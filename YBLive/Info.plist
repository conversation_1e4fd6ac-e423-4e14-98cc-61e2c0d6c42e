<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AWS</key>
	<dict>
		<key>CredentialsProvider</key>
		<dict>
			<key>CognitoIdentity</key>
			<dict>
				<key>Default</key>
				<dict>
					<key>PoolId</key>
					<string>ap-southeast-1:f972630e-cc7d-431a-80c6-581266da25bb</string>
					<key>Region</key>
					<string>AWSRegionAPSoutheast1</string>
				</dict>
			</dict>
		</dict>
		<key>S3TransferUtility</key>
		<dict>
			<key>Default</key>
			<dict>
				<key>Region</key>
				<string>AWSRegionAPSoutheast1</string>
			</dict>
		</dict>
	</dict>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>云豹直播</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleGetInfoString</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>PhoneLive</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLIconFile</key>
			<string>defalut-header-icon</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tencent101551222</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wxe1d27af7f05c9ca4</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>QQ060D8C76</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb2647803221</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fmscms</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>phonelive</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb685742891599488</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>yunbaolive</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>chatsifieds.app.payments</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>gxet13</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fb685742891599488</string>
		<string>iosamap</string>
		<string>comgooglemaps</string>
		<string>baidumap</string>
		<string>qqmap</string>
		<string>fbapi20130214</string>
		<string>WeChat</string>
		<string>Twitter</string>
		<string>twitterauth</string>
		<string>sinaweibo</string>
		<string>fbapi</string>
		<string>sinaweibohd</string>
		<string>kakaokompassauth</string>
		<string>storykompassauth</string>
		<string>kakao48d3f524e4a636b08d81b3ceb50f1003</string>
		<string>kakaolink</string>
		<string>instagram</string>
		<string>pinit</string>
		<string>pocket-oauth-v1</string>
		<string>fbauth2</string>
		<string>rm226427com.mob.demoShareSDK</string>
		<string>renren</string>
		<string>renreniphone</string>
		<string>renrenios</string>
		<string>renrenapi</string>
		<string>mqzone</string>
		<string>mqq</string>
		<string>mqqapi</string>
		<string>wtloginmqq2</string>
		<string>mqqopensdkapiV3</string>
		<string>mqqopensdkapiV2</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>mqzoneopensdkapiV2</string>
		<string>mqzoneopensdkapi19</string>
		<string>mqzoneopensdkapi</string>
		<string>mqzoneopensdk</string>
		<string>alipayshare</string>
		<string>alipay</string>
		<string>yixinopenapi</string>
		<string>yixin</string>
		<string>wechat</string>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>tencentweiboSdkv2</string>
		<string>TencentWeibo</string>
		<string>weibosdk2.5</string>
		<string>weibosdk</string>
		<string>sinaweibohdsso</string>
		<string>sinaweibosso</string>
		<string>com.google.gppconsent.2.4.1</string>
		<string>com.google.gppconsent.2.4.0</string>
		<string>com.google.gppconsent.2.3.0</string>
		<string>com.google.gppconsent.2.2.0</string>
		<string>com.google.gppconsent</string>
		<string>hasgplus4</string>
		<string>googlechrome-x-callback</string>
		<string>googlechrome</string>
		<string>storylink</string>
		<string>alipayshare</string>
		<string>mqqwpa</string>
		<string>line</string>
		<string>whatsapp</string>
		<string>kakaolink</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MOBAppSecret</key>
	<string>e57996c8f3c5dcb251395a37f1edb54b</string>
	<key>MOBAppkey</key>
	<string>2aa0d8be0453d</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>访问您的相机，允许可以开启直播和录制视频</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>访问您的位置，允许可以获取当地的直播和视频</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>访问您的麦克风，允许可以录制声音</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>访问您的相册,允许可以把视频保存在相册</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>读取您的相册，允许可以把相册的内容上传到服务器</string>
	<key>Privacy - Calendars Usage Description </key>
	<string>访问您的日历，允许可以访问您的日程安排</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>remote-notification</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	<key>com.openinstall.APP_KEY</key>
	<string>pkb8o5</string>
</dict>
</plist>
