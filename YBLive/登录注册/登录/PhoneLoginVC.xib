<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina5_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="PhoneLoginVC">
            <connections>
                <outlet property="appNameImg" destination="kFH-bk-kBI" id="GwD-zh-VJT"/>
                <outlet property="doLoginBtn" destination="DJU-ED-B4S" id="czN-rt-CEb"/>
                <outlet property="forgotBtn" destination="Q1W-C3-R7G" id="FbQ-0d-ZLS"/>
                <outlet property="gifImage" destination="ZSg-WC-htq" id="LuF-Sx-Cmn"/>
                <outlet property="iconImg" destination="L54-xc-9oB" id="rcL-9A-9Ax"/>
                <outlet property="leftView" destination="Xzt-o4-bJo" id="2jf-WM-Izp"/>
                <outlet property="logTitleLabel" destination="zSV-HG-21c" id="aJe-th-p00"/>
                <outlet property="nameLabel" destination="cmE-FV-uGt" id="vaS-O9-UbA"/>
                <outlet property="newnRulesL" destination="ftb-U6-ynl" id="8Ag-GO-hJt"/>
                <outlet property="otherLabel" destination="tgB-0o-IyC" id="eVz-iy-QA7"/>
                <outlet property="passWordT" destination="6yD-Ml-H7u" id="fjh-Pb-3C3"/>
                <outlet property="phoneBackView" destination="OG6-3y-XSF" id="Rk9-Nu-U98"/>
                <outlet property="phoneT" destination="pvS-Jq-oJk" id="VOF-ZI-wGy"/>
                <outlet property="platformview" destination="BGk-Nj-xX4" id="uEp-s1-DDP"/>
                <outlet property="privateBtn" destination="wXh-a6-kQq" id="Btz-vZ-YKK"/>
                <outlet property="regBtn" destination="e7U-sM-6FH" id="IST-WN-pCF"/>
                <outlet property="rightView" destination="JYO-RD-rrx" id="JHY-u3-P4I"/>
                <outlet property="view" destination="9Oj-k7-SgM" id="Twj-dN-kn6"/>
                <outlet property="xyBtn" destination="gZy-J1-ccg" id="A9r-YN-qPy"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="9Oj-k7-SgM">
            <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ZSg-WC-htq">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="1036"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Hi~欢迎体验系统" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zSV-HG-21c">
                    <rect key="frame" x="41.333333333333343" y="135.33333333333334" width="331.33333333333326" height="24"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="20"/>
                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OG6-3y-XSF">
                    <rect key="frame" x="41.333333333333343" y="204.33333333333334" width="331.33333333333326" height="50"/>
                    <subviews>
                        <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="+86" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cmE-FV-uGt">
                            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="50" id="mSJ-W4-aew"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请填写手机号" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="pvS-Jq-oJk">
                            <rect key="frame" x="76.000000000000014" y="15.333333333333314" width="245.33333333333337" height="19"/>
                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" type="system" weight="thin" pointSize="14"/>
                            <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                        </textField>
                        <imageView contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="login_下拉.png" translatesAutoresizingMaskIntoConstraints="NO" id="mwA-dy-GGP">
                            <rect key="frame" x="49.999999999999993" y="20" width="16.000000000000007" height="10"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="10" id="oti-Dc-gFK"/>
                                <constraint firstAttribute="width" constant="16" id="pk4-LE-A9i"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6iL-rt-Y0F">
                            <rect key="frame" x="-7" y="49" width="338" height="1"/>
                            <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="7u4-uK-WrY"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="pvS-Jq-oJk" firstAttribute="centerY" secondItem="cmE-FV-uGt" secondAttribute="centerY" id="83q-cW-4Ec"/>
                        <constraint firstAttribute="bottom" secondItem="6iL-rt-Y0F" secondAttribute="bottom" id="PFZ-kd-yF2"/>
                        <constraint firstAttribute="bottom" secondItem="cmE-FV-uGt" secondAttribute="bottom" id="Rf9-jk-O6X"/>
                        <constraint firstItem="6iL-rt-Y0F" firstAttribute="leading" secondItem="OG6-3y-XSF" secondAttribute="leading" constant="-7" id="SI5-95-mxV"/>
                        <constraint firstItem="cmE-FV-uGt" firstAttribute="top" secondItem="OG6-3y-XSF" secondAttribute="top" id="SJ7-KZ-tYA"/>
                        <constraint firstAttribute="trailing" secondItem="6iL-rt-Y0F" secondAttribute="trailing" constant="0.33333333333325754" id="Sgm-C1-8bS"/>
                        <constraint firstItem="mwA-dy-GGP" firstAttribute="centerY" secondItem="cmE-FV-uGt" secondAttribute="centerY" id="UEP-05-Ppv"/>
                        <constraint firstItem="pvS-Jq-oJk" firstAttribute="leading" secondItem="mwA-dy-GGP" secondAttribute="trailing" constant="10" id="YbN-mo-OgB"/>
                        <constraint firstAttribute="height" constant="50" id="ZsA-Pt-4vW"/>
                        <constraint firstItem="cmE-FV-uGt" firstAttribute="leading" secondItem="OG6-3y-XSF" secondAttribute="leading" id="cNr-ik-YFk"/>
                        <constraint firstAttribute="trailing" secondItem="pvS-Jq-oJk" secondAttribute="trailing" constant="10" id="fSP-v2-DOW"/>
                        <constraint firstItem="mwA-dy-GGP" firstAttribute="leading" secondItem="cmE-FV-uGt" secondAttribute="trailing" id="moV-Ef-X6q"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="25"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="25"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jww-gu-aDl">
                    <rect key="frame" x="41.333333333333343" y="264.33333333333331" width="331.33333333333326" height="50"/>
                    <subviews>
                        <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请输入密码" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="6yD-Ml-H7u">
                            <rect key="frame" x="25" y="0.0" width="281.33333333333331" height="50"/>
                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" type="system" weight="thin" pointSize="14"/>
                            <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                        </textField>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gDC-07-VZP">
                            <rect key="frame" x="-6" y="49" width="346" height="1"/>
                            <color key="backgroundColor" red="0.96078431369999995" green="0.96078431369999995" blue="0.96078431369999995" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="yCU-TZ-ZwK"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="6yD-Ml-H7u" firstAttribute="leading" secondItem="jww-gu-aDl" secondAttribute="leading" constant="25" id="4eE-lX-eMP"/>
                        <constraint firstAttribute="trailing" secondItem="6yD-Ml-H7u" secondAttribute="trailing" constant="25" id="6mD-iW-oNV"/>
                        <constraint firstItem="gDC-07-VZP" firstAttribute="leading" secondItem="jww-gu-aDl" secondAttribute="leading" constant="-6" id="YUd-mq-OAJ"/>
                        <constraint firstAttribute="trailing" secondItem="gDC-07-VZP" secondAttribute="trailing" constant="-8.6666666666667425" id="bKd-5p-JPY"/>
                        <constraint firstAttribute="bottom" secondItem="6yD-Ml-H7u" secondAttribute="bottom" id="efB-53-eYa"/>
                        <constraint firstAttribute="bottom" secondItem="gDC-07-VZP" secondAttribute="bottom" id="if0-4k-ETC"/>
                        <constraint firstItem="6yD-Ml-H7u" firstAttribute="top" secondItem="jww-gu-aDl" secondAttribute="top" id="lfn-y0-lcd"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="25"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="25"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DJU-ED-B4S">
                    <rect key="frame" x="41.333333333333343" y="344.33333333333331" width="331.33333333333326" height="40"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="Ju8-0d-qEH"/>
                    </constraints>
                    <state key="normal" title="立即登录" backgroundImage="startLive_back.png">
                        <color key="titleColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    </state>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="20"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="mobileLogin:" destination="-1" eventType="touchUpInside" id="ELR-CA-kAJ"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="e7U-sM-6FH">
                    <rect key="frame" x="61.333333333333343" y="387.33333333333331" width="54" height="30"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="30" id="1ew-Ts-ZOu"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="13"/>
                    <state key="normal" title="立即注册">
                        <color key="titleColor" systemColor="systemGray2Color"/>
                    </state>
                    <connections>
                        <action selector="regist:" destination="-1" eventType="touchUpInside" id="KqL-wH-3Yg"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Q1W-C3-R7G">
                    <rect key="frame" x="298.66666666666669" y="387.33333333333331" width="54" height="30"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="30" id="mGJ-8s-9ZE"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="13"/>
                    <state key="normal" title="忘记密码">
                        <color key="titleColor" systemColor="systemGray2Color"/>
                    </state>
                    <connections>
                        <action selector="forgetPass:" destination="-1" eventType="touchUpInside" id="54U-6s-rgM"/>
                    </connections>
                </button>
                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wXh-a6-kQq" userLabel="隐私">
                    <rect key="frame" x="123" y="670" width="168" height="26"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" name=".PingFangSC-Regular" family=".PingFang SC" pointSize="12"/>
                    <state key="normal" title="登录即代表同意服务和隐私条款">
                        <color key="titleColor" red="0.58823529411764708" green="0.58823529411764708" blue="0.58823529411764708" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="EULA:" destination="-1" eventType="touchUpInside" id="Lw5-11-eXp"/>
                    </connections>
                </button>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="其他登录方式" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tgB-0o-IyC">
                    <rect key="frame" x="164" y="573" width="86" height="17"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <color key="textColor" red="0.58823529411764708" green="0.58823529411764708" blue="0.58823529411764708" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BGk-Nj-xX4">
                    <rect key="frame" x="41.333333333333343" y="610" width="331.33333333333326" height="40"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="Aua-qy-eK4"/>
                    </constraints>
                </view>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xzt-o4-bJo">
                    <rect key="frame" x="71.333333333333314" y="581" width="78.666666666666686" height="1"/>
                    <color key="backgroundColor" red="0.19607843137254902" green="0.19607843137254902" blue="0.19607843137254902" alpha="1" colorSpace="custom" customColorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="YPs-CH-wil"/>
                    </constraints>
                </view>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JYO-RD-rrx">
                    <rect key="frame" x="264" y="581" width="78.666666666666686" height="1"/>
                    <color key="backgroundColor" red="0.19607843137254902" green="0.19607843137254902" blue="0.19607843137254902" alpha="1" colorSpace="custom" customColorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="2xB-OK-Gah"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ftb-U6-ynl" userLabel="newnRulesL" customClass="YYLabel">
                    <rect key="frame" x="207" y="683" width="0.0" height="0.0"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gZy-J1-ccg">
                    <rect key="frame" x="73" y="670.66666666666663" width="25" height="25"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="25" id="ErD-mX-HUC"/>
                        <constraint firstAttribute="height" constant="25" id="uKz-7z-Lkt"/>
                    </constraints>
                    <state key="normal" image="xieyi_normal.png"/>
                    <connections>
                        <action selector="agreementBtnClick:" destination="-1" eventType="touchUpInside" id="hlc-ca-5lO"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9dh-J6-boV">
                    <rect key="frame" x="20" y="30" width="40" height="40"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="40" id="ISQ-rt-rLy"/>
                        <constraint firstAttribute="height" constant="40" id="mLo-ns-X95"/>
                    </constraints>
                    <state key="normal" image="returnback.png"/>
                    <connections>
                        <action selector="returnBtnClick:" destination="-1" eventType="touchUpInside" id="VcC-lF-dn7"/>
                    </connections>
                </button>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="L54-xc-9oB">
                    <rect key="frame" x="41.333333333333343" y="77.333333333333329" width="50" height="50"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="50" id="SAl-Jg-zgs"/>
                        <constraint firstAttribute="height" constant="50" id="fRM-01-lM0"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="10"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="kFH-bk-kBI">
                    <rect key="frame" x="98.333333333333314" y="103.33333333333333" width="86" height="24"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="86" id="CcF-en-UTd"/>
                        <constraint firstAttribute="height" constant="24" id="e9A-tB-jPj"/>
                    </constraints>
                </imageView>
            </subviews>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="DJU-ED-B4S" firstAttribute="leading" secondItem="jww-gu-aDl" secondAttribute="leading" id="1yH-xj-Uti"/>
                <constraint firstItem="kFH-bk-kBI" firstAttribute="leading" secondItem="L54-xc-9oB" secondAttribute="trailing" constant="7" id="2zf-Oi-KbE"/>
                <constraint firstItem="OG6-3y-XSF" firstAttribute="centerX" secondItem="9Oj-k7-SgM" secondAttribute="centerX" id="4An-8W-dA0"/>
                <constraint firstItem="9dh-J6-boV" firstAttribute="top" secondItem="9Oj-k7-SgM" secondAttribute="top" constant="30" id="4FH-dX-VpF"/>
                <constraint firstItem="OG6-3y-XSF" firstAttribute="top" secondItem="zSV-HG-21c" secondAttribute="bottom" constant="45" id="4Z3-lY-NVa"/>
                <constraint firstItem="tgB-0o-IyC" firstAttribute="centerX" secondItem="9Oj-k7-SgM" secondAttribute="centerX" id="5DL-Jm-jiZ"/>
                <constraint firstItem="wXh-a6-kQq" firstAttribute="centerX" secondItem="9Oj-k7-SgM" secondAttribute="centerX" id="7jq-BF-ZTi"/>
                <constraint firstItem="JYO-RD-rrx" firstAttribute="leading" secondItem="tgB-0o-IyC" secondAttribute="trailing" constant="14" id="7mU-Pd-JHY"/>
                <constraint firstItem="zSV-HG-21c" firstAttribute="top" secondItem="L54-xc-9oB" secondAttribute="bottom" constant="8" id="7qM-26-j0O"/>
                <constraint firstItem="wXh-a6-kQq" firstAttribute="top" secondItem="BGk-Nj-xX4" secondAttribute="bottom" constant="20" id="BIf-pR-frH"/>
                <constraint firstItem="JYO-RD-rrx" firstAttribute="trailing" secondItem="BGk-Nj-xX4" secondAttribute="trailing" constant="-30" id="ERE-QF-cTe"/>
                <constraint firstItem="ZSg-WC-htq" firstAttribute="top" secondItem="9Oj-k7-SgM" secondAttribute="top" id="Er2-6n-tYC"/>
                <constraint firstItem="zSV-HG-21c" firstAttribute="width" secondItem="9Oj-k7-SgM" secondAttribute="width" multiplier="0.8" id="G2U-L3-cWR"/>
                <constraint firstItem="9dh-J6-boV" firstAttribute="leading" secondItem="9Oj-k7-SgM" secondAttribute="leading" constant="20" id="HUu-vf-Bft"/>
                <constraint firstItem="Xzt-o4-bJo" firstAttribute="leading" secondItem="BGk-Nj-xX4" secondAttribute="leading" constant="30" id="IDy-2J-TtV"/>
                <constraint firstAttribute="bottom" secondItem="ZSg-WC-htq" secondAttribute="bottom" constant="-300" id="K5Y-F2-NPR"/>
                <constraint firstItem="jww-gu-aDl" firstAttribute="leading" secondItem="OG6-3y-XSF" secondAttribute="leading" id="KeB-Qa-KtQ"/>
                <constraint firstItem="Q1W-C3-R7G" firstAttribute="top" secondItem="e7U-sM-6FH" secondAttribute="top" id="KiK-Oz-Frq"/>
                <constraint firstItem="wXh-a6-kQq" firstAttribute="leading" secondItem="gZy-J1-ccg" secondAttribute="trailing" constant="25" id="LQw-Qd-Tmc"/>
                <constraint firstItem="zSV-HG-21c" firstAttribute="centerY" secondItem="9Oj-k7-SgM" secondAttribute="centerY" multiplier="0.4" id="NIi-mZ-7ks"/>
                <constraint firstItem="ZSg-WC-htq" firstAttribute="leading" secondItem="9Oj-k7-SgM" secondAttribute="leading" id="O3W-Us-OZT"/>
                <constraint firstItem="ftb-U6-ynl" firstAttribute="centerY" secondItem="wXh-a6-kQq" secondAttribute="centerY" id="QpW-Vk-RPq"/>
                <constraint firstItem="DJU-ED-B4S" firstAttribute="top" secondItem="jww-gu-aDl" secondAttribute="bottom" constant="30" id="TAO-Ib-kFN"/>
                <constraint firstItem="L54-xc-9oB" firstAttribute="leading" secondItem="zSV-HG-21c" secondAttribute="leading" id="VXo-0O-Zwr"/>
                <constraint firstItem="OG6-3y-XSF" firstAttribute="width" secondItem="9Oj-k7-SgM" secondAttribute="width" multiplier="0.8" id="aZ6-Sr-PgG"/>
                <constraint firstItem="Q1W-C3-R7G" firstAttribute="trailing" secondItem="DJU-ED-B4S" secondAttribute="trailing" constant="-20" id="aaL-Rc-ber"/>
                <constraint firstItem="ftb-U6-ynl" firstAttribute="centerX" secondItem="wXh-a6-kQq" secondAttribute="centerX" id="bv1-Wc-zJB"/>
                <constraint firstItem="BGk-Nj-xX4" firstAttribute="leading" secondItem="DJU-ED-B4S" secondAttribute="leading" id="dxD-4m-PBU"/>
                <constraint firstItem="JYO-RD-rrx" firstAttribute="centerY" secondItem="tgB-0o-IyC" secondAttribute="centerY" id="jHG-Kp-kch"/>
                <constraint firstItem="BGk-Nj-xX4" firstAttribute="top" secondItem="tgB-0o-IyC" secondAttribute="bottom" constant="20" id="kgE-Xu-fKi"/>
                <constraint firstItem="BGk-Nj-xX4" firstAttribute="trailing" secondItem="DJU-ED-B4S" secondAttribute="trailing" id="l9g-DX-zem"/>
                <constraint firstItem="gZy-J1-ccg" firstAttribute="centerY" secondItem="wXh-a6-kQq" secondAttribute="centerY" id="njo-0K-GXH"/>
                <constraint firstItem="jww-gu-aDl" firstAttribute="top" secondItem="OG6-3y-XSF" secondAttribute="bottom" constant="10" id="nns-fx-9vN"/>
                <constraint firstItem="zSV-HG-21c" firstAttribute="centerX" secondItem="9Oj-k7-SgM" secondAttribute="centerX" id="ntM-AF-OzV"/>
                <constraint firstItem="tgB-0o-IyC" firstAttribute="leading" secondItem="Xzt-o4-bJo" secondAttribute="trailing" constant="14" id="oL2-uL-C5L"/>
                <constraint firstItem="jww-gu-aDl" firstAttribute="height" secondItem="OG6-3y-XSF" secondAttribute="height" id="oQJ-kP-7Vc"/>
                <constraint firstItem="e7U-sM-6FH" firstAttribute="leading" secondItem="DJU-ED-B4S" secondAttribute="leading" constant="20" id="oer-Wx-0Nz"/>
                <constraint firstItem="wXh-a6-kQq" firstAttribute="bottom" secondItem="9Oj-k7-SgM" secondAttribute="bottom" constant="-40" id="p2a-2x-m2T"/>
                <constraint firstAttribute="trailing" secondItem="ZSg-WC-htq" secondAttribute="trailing" id="ps8-LI-D6W"/>
                <constraint firstItem="Xzt-o4-bJo" firstAttribute="centerY" secondItem="tgB-0o-IyC" secondAttribute="centerY" id="rVf-VW-Api"/>
                <constraint firstItem="DJU-ED-B4S" firstAttribute="trailing" secondItem="jww-gu-aDl" secondAttribute="trailing" id="rqB-48-YX8"/>
                <constraint firstItem="kFH-bk-kBI" firstAttribute="bottom" secondItem="L54-xc-9oB" secondAttribute="bottom" id="sHI-ik-HD8"/>
                <constraint firstItem="jww-gu-aDl" firstAttribute="trailing" secondItem="OG6-3y-XSF" secondAttribute="trailing" id="ttH-hI-7RP"/>
                <constraint firstItem="e7U-sM-6FH" firstAttribute="top" secondItem="DJU-ED-B4S" secondAttribute="bottom" constant="3" id="zoJ-Qf-bKB"/>
            </constraints>
            <point key="canvasLocation" x="147.82608695652175" y="214.4021739130435"/>
        </view>
    </objects>
    <resources>
        <image name="login_下拉.png" width="16" height="10"/>
        <image name="returnback.png" width="20" height="20"/>
        <image name="startLive_back.png" width="280" height="40"/>
        <image name="xieyi_normal.png" width="13" height="13"/>
        <systemColor name="systemGray2Color">
            <color red="0.68235294117647061" green="0.68235294117647061" blue="0.69803921568627447" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
