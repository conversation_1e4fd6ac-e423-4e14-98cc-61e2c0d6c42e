#import "PhoneLoginVC.h"
#import "hahazhucedeview.h"
#import "AppDelegate.h"
#import "getpasswordview.h"
#import "ZYTabBarController.h"
#import <ShareSDK/ShareSDK.h>
#import "UserForbiddenView.h"
#import <WXApi.h>

#import "CountryCodeVC.h"
#import "TUIKit.h"

@interface PhoneLoginVC () {
    UIActivityIndicatorView *testActivityIndicator;//菊花
    NSArray *platformsarray;
    NSDictionary *rulesDic;
    BOOL showArea;
    NSString *sendcode_type;
    UIButton *codeSelBtn;
    
    BOOL loginAgreementBool;
}
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *VIewH;
@property (strong, nonatomic) UIWindow *window;
@property(nonatomic,strong)NSString *isreg;
@property (weak, nonatomic) IBOutlet UIButton *privateBtn;
@property (strong, nonatomic) IBOutlet YYLabel *newnRulesL;
@property (weak, nonatomic) IBOutlet UIView *phoneBackView;

- (IBAction)EULA:(id)sender;
#pragma mark ================ 语言包的时候需要修改的label ===============

@property (weak, nonatomic) IBOutlet UILabel *logTitleLabel;
@property (weak, nonatomic) IBOutlet UIButton *rightRegBtn;
@property (weak, nonatomic) IBOutlet UILabel *nameLabel;
@property (weak, nonatomic) IBOutlet UILabel *otherLabel;
@property (weak, nonatomic) IBOutlet UIButton *regBtn;
@property (weak, nonatomic) IBOutlet UIButton *forgotBtn;
@property (weak, nonatomic) IBOutlet UIView *platformview;

@property (nonatomic, strong) UserForbiddenView *forbiddenView;

@property (nonatomic, strong)NSString *countrycode;

@end
@implementation PhoneLoginVC
//获取三方登录方式
-(void)getLoginThird{
    [YBToolClass postNetworkWithUrl:@"Home.getLogin" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *infos = [info firstObject];
            platformsarray = [infos valueForKey:@"login_type_ios"];
            rulesDic = [infos valueForKey:@"login_alert"];
            sendcode_type = minstr([infos valueForKey:@"sendcode_type"]);
            [_appNameImg sd_setImageWithURL:[NSURL URLWithString:minstr([infos valueForKey:@"login_img"])]];
            if ([sendcode_type isEqual:@"0"]) {
                codeSelBtn.userInteractionEnabled = NO;
            }else{
                codeSelBtn.userInteractionEnabled = YES;

            }
            dispatch_async(dispatch_get_main_queue(), ^{
                [self setthirdview];
                [self setNewInfo];

            });
        }
        [testActivityIndicator stopAnimating]; // 结束旋转
        [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏
        
    } fail:^{
        [testActivityIndicator stopAnimating]; // 结束旋转
        [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏
        
    }];
}
-(void)setNewInfo{
    _newnRulesL.hidden = NO;
    _newnRulesL.text =minstr([rulesDic valueForKey:@"login_title"]);// @"登录即代表你同意";
    _newnRulesL.textColor = RGB_COLOR(@"#323232", 1);
    _newnRulesL.font = SYS_Font(15);
    _newnRulesL.numberOfLines = 3;
    _newnRulesL.preferredMaxLayoutWidth = _window_width *0.8;    //设置最大宽度
    NSArray *ppA = [NSArray arrayWithArray:[rulesDic valueForKey:@"message"]];
    
    NSMutableAttributedString *textAtt = [[NSMutableAttributedString alloc]initWithString:[NSString stringWithFormat:@"%@",_newnRulesL.text]];
    [textAtt addAttribute:NSForegroundColorAttributeName value:RGB_COLOR(@"#6F6F6F", 1) range:textAtt.yy_rangeOfAll];
    
    for (int i=0; i<ppA.count; i++) {
        NSDictionary *subDic = ppA[i];
        NSRange clickRange = [[textAtt string]rangeOfString:minstr([subDic valueForKey:@"title"])];
        [textAtt yy_setTextHighlightRange:clickRange color:RGB_COLOR(@"#5C94E7", 1) backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
            NSLog(@"协议");
            if ([PublicObj checkNull:minstr([subDic valueForKey:@"url"])]) {
                [MBProgressHUD showError:@"链接不存在"];
                return;
            }
            NSString *language = [PublicObj getCurrentLanguage];
            NSString *urlssss = minstr([subDic valueForKey:@"url"]);;
            NSString *urlAppend =  [NSString stringWithFormat:@"%@&language=%@",urlssss,language];
            NSString *paths = [urlssss stringByAppendingString:urlAppend];

            YBWebViewController *h5vc = [[YBWebViewController alloc]init];
            h5vc.urls =paths;// minstr([subDic valueForKey:@"url"]);;
            [[MXBADelegate sharedAppDelegate]pushViewController:h5vc animated:YES];
        }];
    }
    _newnRulesL.attributedText = textAtt;
    [_newnRulesL mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_lessThanOrEqualTo(self.view.mas_width).multipliedBy(0.8);
    }];
    _xyBtn.hidden = NO;
    [_xyBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_newnRulesL.mas_left).offset(-5);
        make.centerY.equalTo(_newnRulesL.mas_centerY);
        make.height.width.mas_equalTo(12);
        }];

}

//添加登陆方式
-(void)setthirdview{
    //判断一下是不是数组
    if (![platformsarray isKindOfClass:[NSArray class]]) {
        return;
    }
//    if (platformsarray.count <= 0) {
//        _leftView.hidden = _rightView.hidden = _otherLabel.hidden = YES;
//    }else {
//        _leftView.hidden = _rightView.hidden = _otherLabel.hidden = NO;
//    }
    //进入此方法钱，清除所有按钮，防止重复添加
    for (UIButton *btn in _platformview.subviews) {
        [btn removeFromSuperview];
    }
    //如果返回为空，登陆方式字样隐藏
//    if (platformsarray.count == 0) {
//        _otherviews.hidden = YES;
//    }
//    else{
//        _otherviews.hidden = NO;
//    }
    //注意：此处涉及到精密计算，轻忽随意改动
    CGFloat w = 40;
    CGFloat space = _window_width*0.8-([platformsarray count] - 1)*20-[platformsarray count]*40;
   
    
    for (int i=0; i<platformsarray.count; i++) {
        UIButton *btn = [UIButton buttonWithType:0];
        btn.tag = 1000 + i;
        [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"login_%@",platformsarray[i]]] forState:UIControlStateNormal];
        [btn setTitle:platformsarray[i] forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor clearColor] forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(thirdlogin:) forControlEvents:UIControlEventTouchUpInside];
        btn.frame = CGRectMake(space/2+i*60,0,w,w);
        [_platformview addSubview:btn];
    }
}
//若要添加登陆方式，在此处添加
-(void)thirdlogin:(UIButton *)sender{
    if (loginAgreementBool == NO) {
        [MBProgressHUD showError:YZMsg(@"请仔细阅读用户协议并勾选")];
        return;
    }
    [[NSNotificationCenter defaultCenter]postNotificationName:@"loginEnter" object:nil];

    /*
     1 qq
     2 wx
     3 facebook
     4 twitter
     */
    [self.view endEditing:YES];
    int type;
    if ([sender.titleLabel.text isEqual:@"qq"]) {
        type = 1;
    }else if ([sender.titleLabel.text isEqual:@"wx"]) {
        type = 2;
    }else if ([sender.titleLabel.text isEqual:@"facebook"]) {
        type = 3;
    }else if ([sender.titleLabel.text isEqual:@"twitter"]) {
        type = 4;
    }else if ([sender.titleLabel.text isEqual:@"ios"]){
        type = 5;
    }
    
    switch (type) {
        case 1:
            [self login:@"qq" platforms:SSDKPlatformTypeQQ];
            break;
        case 2:
            [self login:@"wx" platforms:SSDKPlatformTypeWechat];
            break;
        case 3:
            [self login:@"facebook" platforms:SSDKPlatformTypeFacebook];
            break;
        case 4:
            [self login:@"twitter" platforms:SSDKPlatformTypeTwitter];
            break;
        case 5:
            [self login:@"ios" platforms:SSDKPlatformTypeAppleAccount];
            break;
        default:
            break;
    }
}
-(void)login:(NSString *)types platforms:(SSDKPlatformType)platform{
    //取消授权
    [ShareSDK cancelAuthorize:platform];
    
    [testActivityIndicator startAnimating]; // 开始旋转
    [ShareSDK getUserInfo:platform
           onStateChanged:^(SSDKResponseState state, SSDKUser *user, NSError *error)
     {

         if (state == SSDKResponseStateSuccess)
         {
             NSLog(@"uid=%@",user.uid);
             NSLog(@"%@",user.credential);
             NSLog(@"token=%@",user.credential.token);
             NSLog(@"nickname=%@",user.nickname);
             [self RequestLogin:user LoginType:types];
             
         } else if (state == 2 || state == 3) {
             [testActivityIndicator stopAnimating]; // 结束旋转
             [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏
             if ([types isEqual:@"ios"]) {
                 if ([UIDevice currentDevice].systemVersion.floatValue < 13) {
                     [MBProgressHUD showError:YZMsg(@"ios13以下系统暂不支持苹果登录")];
                     return;
                 }

             }
         }
         
     }];
}
-(void)RequestLogin:(SSDKUser *)user LoginType:(NSString *)LoginType
{
    
    [testActivityIndicator startAnimating]; // 结束旋转
    
    NSString *icon = nil;
    NSString *access_token = @"";
    if ([LoginType isEqualToString:@"qq"]) {
        icon = [user.rawData valueForKey:@"figureurl_qq_2"];
        access_token =user.credential.token;
    }
    else if ([LoginType isEqualToString:@"ios"]){

        icon =@"";
    }
    else
    {
        icon = user.icon;
    }
    NSString *unionID;//unionid
    if ([LoginType isEqualToString:@"wx"]){
        
        unionID = [user.rawData valueForKey:@"unionid"];
        access_token =user.credential.token;
    }
    else{
        unionID = user.uid;
    }
    if (!icon || !unionID) {
        [testActivityIndicator stopAnimating]; // 结束旋转
        [MBProgressHUD showError:YZMsg(@"未获取到授权，请重试")];
        return;
    }
    NSDictionary *pDic = @{
                           @"openid":[NSString stringWithFormat:@"%@",unionID],
                           @"type":[NSString stringWithFormat:@"%@",[self encodeString:LoginType]],
                           @"nicename":[NSString stringWithFormat:@"%@",[self encodeString:user.nickname]],
                           @"avatar":[NSString stringWithFormat:@"%@",[self encodeString:icon]],
                           @"source":@"ios",
                           @"sign":[[YBToolClass sharedInstance] md5:[NSString stringWithFormat:@"openid=%@&76576076c1f5f657b634e966c8836a06",unionID]],
                           @"access_token":access_token
                           };
    [YBToolClass postNetworkWithUrl:@"Login.userLoginByThird" andParameter:pDic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *infos = [info firstObject];
            LiveUser *userInfo = [[LiveUser alloc] initWithDic:infos];
            [Config saveProfile:userInfo];
            [self IMLogin];
            //判断第一次登陆
            NSString *isreg = minstr([infos valueForKey:@"isreg"]);
            _isreg = isreg;
            [[NSUserDefaults standardUserDefaults] setObject:minstr([infos valueForKey:@"isagent"]) forKey:@"isagent"];
            [self login];
        }else if(code == 1002){
            //账户禁用
            YBWeakSelf;
            if (self.forbiddenView) {
                [self.forbiddenView removeFromSuperview];
                self.forbiddenView = nil;
            }
            NSDictionary *infos = [info firstObject];

            self.forbiddenView = [[NSBundle mainBundle]loadNibNamed:@"UserForbiddenView" owner:self options:nil].firstObject;
            [self.forbiddenView setInfoData:infos];
            self.forbiddenView.frame = CGRectMake(0, 0, _window_width, _window_height);
            self.forbiddenView.hideSelf = ^{
                [weakSelf.forbiddenView removeFromSuperview];
                weakSelf.forbiddenView = nil;
            };
            [self.view addSubview:self.forbiddenView];
        }else {
            [MBProgressHUD showError:msg];
        }
        [testActivityIndicator stopAnimating]; // 结束旋转
        [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏
        
    } fail:^{
        [testActivityIndicator stopAnimating]; // 结束旋转
        [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏
    }];
}
-(NSString *)getQQunionID:(NSString *)IDID{
    
    //************为了和PC互通，获取QQ的unionID,需要注意的是只有腾讯开放平台的数据打通之后这个接口才有权限访问，不然会报错********
    NSString *url1 = [NSString stringWithFormat:@"https://graph.qq.com/oauth2.0/me?access_token=%@&unionid=1",IDID];
    url1 = [url1 stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    NSString *str = [NSString stringWithContentsOfURL:[NSURL URLWithString:url1] encoding:NSUTF8StringEncoding error:nil];
    NSRange rang1 = [str rangeOfString:@"{"];
    NSString *str2 = [str substringFromIndex:rang1.location];
    NSRange rang2 = [str2 rangeOfString:@")"];
    NSString *str3 = [str2 substringToIndex:rang2.location];
    NSString *str4 = [str3 stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    NSData *data = [str4 dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dic = [data JSONValue];
    NSString *unionID = [dic valueForKey:@"unionid"];
    //************为了和PC互通，获取QQ的unionID********
    
    return unionID;
}
-(NSString*)encodeString:(NSString*)unencodedString{
    NSString*encodedString=(NSString*)
    CFBridgingRelease(CFURLCreateStringByAddingPercentEscapes(kCFAllocatorDefault,
                                                              (CFStringRef)unencodedString,
                                                              NULL,
                                                              (CFStringRef)@"!*'();:@&=+$,/?%#[]",
                                                              kCFStringEncodingUTF8));
    return encodedString;
}

-(void)forwardGround{
    [testActivityIndicator stopAnimating]; // 结束旋转
    [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏
}

-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.navigationController.navigationBarHidden = YES;
    [testActivityIndicator stopAnimating]; // 结束旋转
    [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏
}
-(void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    self.navigationController.navigationBarHidden = YES;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    _newnRulesL.hidden = YES;
    _doLoginBtn.userInteractionEnabled = NO;

    codeSelBtn = [UIButton buttonWithType:0];
    [codeSelBtn addTarget:self action:@selector(codeSelClick) forControlEvents:UIControlEventTouchUpInside];
    codeSelBtn.frame = CGRectMake(0, 0, _nameLabel.width, _nameLabel.height);
    [_nameLabel addSubview:codeSelBtn];
    if (self.VIewH.constant<80) {
        self.VIewH.constant+=statusbarHeight;
    }
    self.countrycode = @"86";
    _iconImg.image = [PublicObj getAppIcon];
    _logTitleLabel.text = YZMsg(@"Hi~欢迎体验系统");
//    _otherLabel.text = YZMsg(@"其他登录方式");
    [_regBtn setTitle:YZMsg(@"立即注册") forState:0];
    [_doLoginBtn setTitle:YZMsg(@"立即登录") forState:0];
    [_forgotBtn setTitle:YZMsg(@"忘记密码") forState:0];
    _doLoginBtn.alpha = 0.5;
    _phoneT.attributedPlaceholder = [[NSAttributedString alloc] initWithString:YZMsg(@" 请输入您的手机号") attributes:@{NSForegroundColorAttributeName:[UIColor lightGrayColor]}];
    _passWordT.attributedPlaceholder = [[NSAttributedString alloc] initWithString:YZMsg(@"请输入密码") attributes:@{NSForegroundColorAttributeName:[UIColor lightGrayColor]}];

    self.navigationController.interactivePopGestureRecognizer.delegate = (id) self;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ChangeBtnBackground) name:UITextFieldTextDidChangeNotification object:nil];
    testActivityIndicator = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
    testActivityIndicator.center = CGPointMake(_window_width/2 - 10, _window_height/2 - 10);
    [self.view addSubview:testActivityIndicator];
    testActivityIndicator.color = [UIColor blackColor];
    
    //隐私
    _privateBtn.titleLabel.textColor = RGB(111, 111, 111);
    NSMutableAttributedString *attStr=[[NSMutableAttributedString alloc]initWithString:YZMsg(@"登录即代表你同意服务和隐私条款")];
    [attStr addAttribute:NSUnderlineStyleAttributeName value:[NSNumber numberWithInteger:NSUnderlineStyleSingle] range:NSMakeRange(8, 7)];
    [_privateBtn setAttributedTitle:attStr forState:0];
    AFNetworkReachabilityManager *netManager = [AFNetworkReachabilityManager sharedManager];
    [netManager startMonitoring];  //开始监听 防止第一次安装不显示
    [netManager setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status){
        if (status == AFNetworkReachabilityStatusNotReachable) {
            [self getLoginThird];
            return;
        }else if (status == AFNetworkReachabilityStatusUnknown || status == AFNetworkReachabilityStatusNotReachable){
            NSLog(@"nonetwork-------");
            [self getLoginThird];
        }else if ((status == AFNetworkReachabilityStatusReachableViaWWAN)||(status == AFNetworkReachabilityStatusReachableViaWiFi)){
            [self getLoginThird];
            NSLog(@"wifi-------");
        }
    }];
//    CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"transform.translation.y"];
//    CGFloat duration = 6.0f;
//    animation.duration = duration;
//    animation.values = @[@0,@-7.5,@-15,@-22.5,@-30,@-37.5,@-45,@-52.5,@-60,@-67.5,@-75,@-82.5,@-90,@-97.5,@-105,@-112.5,@-120,@-127.5,@-134,@-142.5,@-150,@-157.5,@-165,@-172.5,@-180,@-187.5,@-195,@-202.5,@-210,@-210,@-202.5,@-195,@-187.5,@-180,@-172.5,@-165,@-157.5,@-150,@-142.5,@-134,@-127.5,@-120,@-112.5,@-105,@-97.5,@-90,@-82.5,@-75,@-67.5,@-60,@-52.5,@-45,@-37.5,@-30,@-22.5,@-15,@-7.5,@0];
//    animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
//    animation.repeatCount = HUGE_VALF;
//    animation.removedOnCompletion = NO;
//    [_gifImage.layer addAnimation:animation forKey:@"1111"];
    
}
- (IBAction)agreementBtnClick:(UIButton *)sender {
    
    loginAgreementBool = !loginAgreementBool;
    if (loginAgreementBool) {
        [_xyBtn setImage:[UIImage imageNamed:@"xieyi_sel"] forState:0];
    }else{
        [_xyBtn setImage:[UIImage imageNamed:@"xieyi_normal"] forState:0];

//        [_xyBtn setBackgroundImage:[UIImage imageNamed:@"xieyi_normal"]];
    }
}
-(void)ChangeBtnBackground {
    if (_phoneT.text.length >0 && _passWordT.text.length >0)
    {
        _doLoginBtn.alpha = 1;
        _doLoginBtn.userInteractionEnabled = YES;
    }
    else
    {
        _doLoginBtn.alpha = 0.5;
        _doLoginBtn.userInteractionEnabled = NO;
    }
}
-(void)codeSelClick{
    YBWeakSelf;
    CountryCodeVC *codeVC = [[CountryCodeVC alloc]init];
    codeVC.codeEvent = ^(NSDictionary *codeDic) {
        weakSelf.nameLabel.text =[NSString stringWithFormat:@"+%@",minstr([codeDic valueForKey:@"tel"])];
        weakSelf.countrycode = minstr([codeDic valueForKey:@"tel"]);
    };
    [[MXBADelegate sharedAppDelegate]pushViewController:codeVC animated:YES];
}
- (IBAction)mobileLogin:(id)sender {
    if (loginAgreementBool == NO) {
        [MBProgressHUD showError:YZMsg(@"请仔细阅读用户协议并勾选")];
        return;
    }
    [self.view endEditing:YES];
    [testActivityIndicator startAnimating]; // 开始旋转
    NSDictionary *Login = @{
                            @"user_login":_phoneT.text,
                            @"user_pass":_passWordT.text,
                            @"source":@"ios",
                            @"country_code":self.countrycode
                            };
    
    [YBToolClass postNetworkWithUrl:@"Login.userLogin" andParameter:Login success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *infos = [info objectAtIndex:0];
            LiveUser *userInfo = [[LiveUser alloc] initWithDic:infos];
            [Config saveProfile:userInfo];
            [self IMLogin];
            self.navigationController.navigationBarHidden = YES;
            //判断第一次登陆
            NSString *isreg = minstr([infos valueForKey:@"isreg"]);
            _isreg = isreg;
            [[NSUserDefaults standardUserDefaults] setObject:minstr([infos valueForKey:@"isagent"]) forKey:@"isagent"];
            [[NSNotificationCenter defaultCenter]postNotificationName:@"loginEnter" object:nil];

            [self login];

        }else if(code == 1002){
            //账户禁用
            YBWeakSelf;
            if (self.forbiddenView) {
                [self.forbiddenView removeFromSuperview];
                self.forbiddenView = nil;
            }
            NSDictionary *infos = [info firstObject];

            self.forbiddenView = [[NSBundle mainBundle]loadNibNamed:@"UserForbiddenView" owner:self options:nil].firstObject;
            [self.forbiddenView setInfoData:infos];
            self.forbiddenView.frame = CGRectMake(0, 0, _window_width, _window_height);
            self.forbiddenView.hideSelf = ^{
                [weakSelf.forbiddenView removeFromSuperview];
                weakSelf.forbiddenView = nil;
            };
            [self.view addSubview:self.forbiddenView];
            
            
        }else{
            [MBProgressHUD showError:msg];
        }
        [testActivityIndicator stopAnimating]; // 结束旋转
        [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏

    } fail:^{
        [testActivityIndicator stopAnimating]; // 结束旋转
        [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏

    }];
}
- (void)IMLogin{
    [[YBImManager shareInstance] imLogin];
}
-(void)login{

    ZYTabBarController *root = [[ZYTabBarController alloc]init];
    [cityDefault saveisreg:_isreg];
    [[MXBADelegate sharedAppDelegate]pushViewController:root animated:YES];
    
    UIApplication *app =[UIApplication sharedApplication];
    AppDelegate *app2 = (AppDelegate *)app.delegate;
    UINavigationController *nav = [[UINavigationController alloc]initWithRootViewController:root];
    app2.window.rootViewController = nav;
    
    [[YBYoungManager shareInstance]checkYoungStatus:YoungFrom_Home];

}
- (IBAction)regist:(id)sender {
    hahazhucedeview *regist = [[hahazhucedeview alloc]init];
    regist.showAlert = YES;
    regist.rulesDic = rulesDic;
    regist.sendcode_typeStr = sendcode_type;
    [[MXBADelegate sharedAppDelegate]pushViewController:regist animated:YES];

}
- (IBAction)forgetPass:(id)sender {
    getpasswordview *getpass = [[getpasswordview alloc]init];
    getpass.sendcode_typeStr = sendcode_type;
    [[MXBADelegate sharedAppDelegate]pushViewController:getpass animated:YES];
}

- (IBAction)clickBackBtn:(UIButton *)sender {
    
    [self.navigationController popViewControllerAnimated:YES];
    
}
//键盘的隐藏
- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event {
    [self.view endEditing:YES];
    showArea = NO;
}
- (IBAction)EULA:(id)sender {
    YBWebViewController *VC = [[YBWebViewController alloc]init];
    NSString *paths = [h5url stringByAppendingString:@"/portal/page/index?id=4"];
    VC.urls = paths;
    VC.titles = YZMsg(@"服务和隐私条款");
    [[MXBADelegate sharedAppDelegate]pushViewController:VC animated:YES];
}
- (IBAction)returnBtnClick:(UIButton *)sender {
    [[MXBADelegate sharedAppDelegate]popViewController:YES];
}
@end
