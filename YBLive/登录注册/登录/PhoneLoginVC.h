#import <UIKit/UIKit.h>
#import <YYText/YYLabel.h>
#import <YYText/NSAttributedString+YYText.h>

@interface PhoneLoginVC : UIViewController

@property (weak, nonatomic) IBOutlet UITextField *phoneT;
@property (weak, nonatomic) IBOutlet UITextField *passWordT;
@property (weak, nonatomic) IBOutlet UIButton *doLoginBtn;

@property (weak, nonatomic) IBOutlet UIView *leftView;
@property (weak, nonatomic) IBOutlet UIView *rightView;
@property (strong, nonatomic) IBOutlet UIImageView *iconImg;
@property (strong, nonatomic) IBOutlet UIImageView *appNameImg;



- (IBAction)mobileLogin:(id)sender;
- (IBAction)regist:(id)sender;
- (IBAction)forgetPass:(id)sender;
- (IBAction)clickBackBtn:(UIButton *)sender;

@property (weak, nonatomic) IBOutlet UIImageView *gifImage;
@property (weak, nonatomic) IBOutlet UIButton *xyBtn;

@end
