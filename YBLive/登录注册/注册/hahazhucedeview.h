#import <UIKit/UIKit.h>
@interface hahazhucedeview : UIViewController


@property(nonatomic,assign)BOOL showAlert;
@property(nonatomic,strong)NSDictionary *rulesDic;

- (IBAction)doBack:(id)sender;

@property (weak, nonatomic) IBOutlet UITextField *phoneT;


@property (weak, nonatomic) IBOutlet UITextField *yanzhengmaT;

@property (weak, nonatomic) IBOutlet UITextField *passWordT;
@property (weak, nonatomic) IBOutlet UITextField *nPassWordT;

@property (weak, nonatomic) IBOutlet UITextField *password2;
@property (weak, nonatomic) IBOutlet UILabel *sunTLb;
@property (weak, nonatomic) IBOutlet UIImageView *codema;

- (IBAction)doRegist:(id)sender;
- (IBAction)getYanZheng:(id)sender;

@property (weak, nonatomic) IBOutlet UIButton *yanzhengmaBtn;

@property (weak, nonatomic) IBOutlet UIButton *registBTn;

@property (nonatomic, strong)NSString *sendcode_typeStr;
@end
