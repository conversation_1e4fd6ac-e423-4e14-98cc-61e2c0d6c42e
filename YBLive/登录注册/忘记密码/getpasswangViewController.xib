<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="getpasswangViewController">
            <connections>
                <outlet property="VIewH" destination="SAW-ne-REv" id="o1P-h9-iZG"/>
                <outlet property="dochange" destination="B0c-af-5sZ" id="yRY-sk-7CX"/>
                <outlet property="forgotBtn" destination="8ZS-qq-4rn" id="DoN-il-RHc"/>
                <outlet property="forgotTitleLabel" destination="ra5-v2-40K" id="Nxf-DU-fHT"/>
                <outlet property="futurePassWord" destination="DcQ-u0-xWO" id="fk5-k7-ugO"/>
                <outlet property="futurePassWord2" destination="fAJ-YU-H03" id="CgA-Ch-ZkV"/>
                <outlet property="oldPassWord" destination="wqe-V7-cfl" id="WYh-Gq-To6"/>
                <outlet property="oldPwdLabel" destination="ta6-8U-AuJ" id="HbO-Qu-e8U"/>
                <outlet property="surePwdLabel" destination="uBE-25-yWq" id="bA2-fQ-dml"/>
                <outlet property="view" destination="67L-Ge-4pY" id="N8D-2Z-Pg8"/>
                <outlet property="xinPwdLabel" destination="5CT-xZ-eQu" id="07U-uw-Oo7"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="67L-Ge-4pY">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qtK-Sz-8tk">
                    <rect key="frame" x="0.0" y="63" width="375" height="201"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="di5-7O-Lwr">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="64"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="重置密码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ra5-v2-40K">
                            <rect key="frame" x="155" y="32.5" width="65.5" height="19.5"/>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                            <color key="textColor" systemColor="darkTextColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" clipsSubviews="YES" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Fhw-Fx-paR">
                            <rect key="frame" x="20" y="32" width="15" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="GTL-h8-eW5"/>
                                <constraint firstAttribute="width" constant="15" id="Mkl-wg-iwb"/>
                            </constraints>
                            <state key="normal" image="icon_arrow_leftsssa"/>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kit-gH-xYV">
                            <rect key="frame" x="-2.5" y="12" width="60" height="60"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="60" id="OM7-XG-K0n"/>
                                <constraint firstAttribute="height" constant="60" id="TRm-TK-LFd"/>
                            </constraints>
                            <connections>
                                <action selector="doBack:" destination="-1" eventType="touchUpInside" id="pEb-xp-XXM"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P9l-fR-icJ">
                            <rect key="frame" x="0.0" y="63" width="375" height="1"/>
                            <color key="backgroundColor" systemColor="groupTableViewBackgroundColor"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="i6d-D0-wOc"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    <constraints>
                        <constraint firstItem="kit-gH-xYV" firstAttribute="centerY" secondItem="Fhw-Fx-paR" secondAttribute="centerY" id="2yL-Vy-9Lk"/>
                        <constraint firstAttribute="bottom" secondItem="P9l-fR-icJ" secondAttribute="bottom" id="Knf-dB-Jl0"/>
                        <constraint firstItem="kit-gH-xYV" firstAttribute="centerX" secondItem="Fhw-Fx-paR" secondAttribute="centerX" id="PpV-L1-ZDJ"/>
                        <constraint firstAttribute="height" constant="64" id="SAW-ne-REv"/>
                        <constraint firstAttribute="trailing" secondItem="P9l-fR-icJ" secondAttribute="trailing" id="UMD-PK-7Lw"/>
                        <constraint firstItem="ra5-v2-40K" firstAttribute="centerY" secondItem="di5-7O-Lwr" secondAttribute="centerY" constant="10" id="Vfk-ob-TIv"/>
                        <constraint firstItem="Fhw-Fx-paR" firstAttribute="leading" secondItem="di5-7O-Lwr" secondAttribute="leading" constant="20" id="YvH-hZ-K9u"/>
                        <constraint firstItem="ra5-v2-40K" firstAttribute="centerX" secondItem="di5-7O-Lwr" secondAttribute="centerX" id="gln-qO-mZE"/>
                        <constraint firstItem="P9l-fR-icJ" firstAttribute="leading" secondItem="di5-7O-Lwr" secondAttribute="leading" id="ovr-K8-ITD"/>
                        <constraint firstItem="Fhw-Fx-paR" firstAttribute="centerY" secondItem="ra5-v2-40K" secondAttribute="centerY" id="wJZ-KA-4j5"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="旧密码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ta6-8U-AuJ">
                    <rect key="frame" x="20" y="89" width="46" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请输入旧密码" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="wqe-V7-cfl">
                    <rect key="frame" x="86" y="78" width="269" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="Iau-p1-e5L"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                </textField>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZXZ-TD-ELM">
                    <rect key="frame" x="15" y="127" width="340" height="1"/>
                    <color key="backgroundColor" systemColor="groupTableViewBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="kRC-dc-fhe"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="新密码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5CT-xZ-eQu">
                    <rect key="frame" x="20" y="152" width="46" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请填写新密码" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="DcQ-u0-xWO">
                    <rect key="frame" x="86" y="141" width="269" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="j1w-rA-V6e"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="14"/>
                    <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                </textField>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="p5s-ql-dN2">
                    <rect key="frame" x="15" y="195" width="340" height="1"/>
                    <color key="backgroundColor" systemColor="groupTableViewBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="T9Y-5I-Bct"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="B0c-af-5sZ">
                    <rect key="frame" x="37.5" y="283" width="300" height="50"/>
                    <color key="backgroundColor" red="0.81176470590000005" green="0.81176470590000005" blue="0.81176470590000005" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="50" id="MkR-yf-g69"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                    <state key="normal" title="立即修改" backgroundImage="startLive_back.png">
                        <color key="titleColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    </state>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="25"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="doChangePassWord:" destination="-1" eventType="touchUpInside" id="sWZ-ra-JSF"/>
                    </connections>
                </button>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="确认密码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uBE-25-yWq">
                    <rect key="frame" x="20" y="220" width="62" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="确认新密码" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="fAJ-YU-H03">
                    <rect key="frame" x="86" y="209" width="269" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="dgS-nV-ytR"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="14"/>
                    <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                </textField>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9nG-yV-Xfg">
                    <rect key="frame" x="15" y="263" width="340" height="1"/>
                    <color key="backgroundColor" systemColor="groupTableViewBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="tQS-ts-KXo"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8ZS-qq-4rn">
                    <rect key="frame" x="117.5" y="357" width="140" height="31"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="140" id="71d-Ln-ZJX"/>
                        <constraint firstAttribute="height" constant="31" id="ez5-1Z-omZ"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <state key="normal" title="忘记密码？">
                        <color key="titleColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="forgetPassWordClick:" destination="-1" eventType="touchUpInside" id="DD4-Wf-mpa"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" red="0.96862745100000003" green="0.95294117649999999" blue="0.96862745100000003" alpha="1" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="DcQ-u0-xWO" firstAttribute="centerY" secondItem="5CT-xZ-eQu" secondAttribute="centerY" id="26M-4w-ZNb"/>
                <constraint firstItem="5CT-xZ-eQu" firstAttribute="leading" secondItem="ta6-8U-AuJ" secondAttribute="leading" id="2jb-S9-KKA"/>
                <constraint firstItem="p5s-ql-dN2" firstAttribute="trailing" secondItem="DcQ-u0-xWO" secondAttribute="trailing" id="43q-xQ-hKA"/>
                <constraint firstItem="9nG-yV-Xfg" firstAttribute="top" secondItem="uBE-25-yWq" secondAttribute="bottom" constant="25" id="4Ed-Y9-jHo"/>
                <constraint firstItem="wqe-V7-cfl" firstAttribute="leading" secondItem="ta6-8U-AuJ" secondAttribute="trailing" constant="20" id="6h2-j5-TUA"/>
                <constraint firstItem="di5-7O-Lwr" firstAttribute="leading" secondItem="67L-Ge-4pY" secondAttribute="leading" id="6oC-cK-WfK"/>
                <constraint firstItem="wqe-V7-cfl" firstAttribute="centerY" secondItem="ta6-8U-AuJ" secondAttribute="centerY" id="7C2-S5-phe"/>
                <constraint firstItem="fAJ-YU-H03" firstAttribute="leading" secondItem="DcQ-u0-xWO" secondAttribute="leading" id="87o-em-Qg2"/>
                <constraint firstAttribute="trailing" secondItem="qtK-Sz-8tk" secondAttribute="trailing" id="9E6-Qd-2cw"/>
                <constraint firstItem="ZXZ-TD-ELM" firstAttribute="trailing" secondItem="wqe-V7-cfl" secondAttribute="trailing" id="CrU-cj-H8b"/>
                <constraint firstAttribute="trailing" secondItem="wqe-V7-cfl" secondAttribute="trailing" constant="20" id="F6y-7o-XUD"/>
                <constraint firstItem="qtK-Sz-8tk" firstAttribute="leading" secondItem="67L-Ge-4pY" secondAttribute="leading" id="GHD-9e-9Kd"/>
                <constraint firstItem="8ZS-qq-4rn" firstAttribute="centerX" secondItem="67L-Ge-4pY" secondAttribute="centerX" id="IkD-aI-J5m"/>
                <constraint firstItem="DcQ-u0-xWO" firstAttribute="leading" secondItem="wqe-V7-cfl" secondAttribute="leading" id="JZV-Q2-EUM"/>
                <constraint firstItem="5CT-xZ-eQu" firstAttribute="top" secondItem="ZXZ-TD-ELM" secondAttribute="top" constant="25" id="OP2-mR-8UH"/>
                <constraint firstItem="8ZS-qq-4rn" firstAttribute="top" secondItem="B0c-af-5sZ" secondAttribute="bottom" constant="24" id="PPX-ju-dVb"/>
                <constraint firstItem="B0c-af-5sZ" firstAttribute="width" secondItem="67L-Ge-4pY" secondAttribute="width" multiplier="0.8" id="Pfq-6n-jN6"/>
                <constraint firstItem="ta6-8U-AuJ" firstAttribute="leading" secondItem="di5-7O-Lwr" secondAttribute="leading" constant="20" id="Slg-nW-wqF"/>
                <constraint firstItem="9nG-yV-Xfg" firstAttribute="leading" secondItem="p5s-ql-dN2" secondAttribute="leading" id="Viz-Is-vql"/>
                <constraint firstItem="B0c-af-5sZ" firstAttribute="centerX" secondItem="67L-Ge-4pY" secondAttribute="centerX" id="Vsc-Sx-2qD"/>
                <constraint firstItem="qtK-Sz-8tk" firstAttribute="bottom" secondItem="9nG-yV-Xfg" secondAttribute="bottom" id="Vsg-Fz-H8h"/>
                <constraint firstItem="9nG-yV-Xfg" firstAttribute="trailing" secondItem="p5s-ql-dN2" secondAttribute="trailing" id="YFK-LM-KsX"/>
                <constraint firstItem="DcQ-u0-xWO" firstAttribute="trailing" secondItem="wqe-V7-cfl" secondAttribute="trailing" id="aqb-hE-UZe"/>
                <constraint firstItem="ZXZ-TD-ELM" firstAttribute="top" secondItem="ta6-8U-AuJ" secondAttribute="bottom" constant="20" id="dni-B8-UFg"/>
                <constraint firstItem="B0c-af-5sZ" firstAttribute="top" secondItem="9nG-yV-Xfg" secondAttribute="top" constant="20" id="hFw-X1-4jP"/>
                <constraint firstItem="uBE-25-yWq" firstAttribute="leading" secondItem="5CT-xZ-eQu" secondAttribute="leading" id="hSb-vu-tYq"/>
                <constraint firstItem="qtK-Sz-8tk" firstAttribute="top" secondItem="67L-Ge-4pY" secondAttribute="top" constant="63" id="kCi-ZD-fY1"/>
                <constraint firstItem="p5s-ql-dN2" firstAttribute="leading" secondItem="5CT-xZ-eQu" secondAttribute="leading" constant="-5" id="lae-nx-Ke8"/>
                <constraint firstItem="uBE-25-yWq" firstAttribute="top" secondItem="p5s-ql-dN2" secondAttribute="top" constant="25" id="lwi-sl-d2x"/>
                <constraint firstItem="ta6-8U-AuJ" firstAttribute="top" secondItem="di5-7O-Lwr" secondAttribute="bottom" constant="25" id="njx-ke-X2T"/>
                <constraint firstItem="fAJ-YU-H03" firstAttribute="centerY" secondItem="uBE-25-yWq" secondAttribute="centerY" id="qcy-Cs-Sc8"/>
                <constraint firstItem="ZXZ-TD-ELM" firstAttribute="leading" secondItem="ta6-8U-AuJ" secondAttribute="leading" constant="-5" id="sob-rF-YE4"/>
                <constraint firstItem="di5-7O-Lwr" firstAttribute="width" secondItem="67L-Ge-4pY" secondAttribute="width" id="tdM-eq-zN4"/>
                <constraint firstItem="di5-7O-Lwr" firstAttribute="top" secondItem="67L-Ge-4pY" secondAttribute="top" id="uCb-dP-pU9"/>
                <constraint firstItem="p5s-ql-dN2" firstAttribute="top" secondItem="5CT-xZ-eQu" secondAttribute="bottom" constant="25" id="v1o-9P-iDm"/>
                <constraint firstItem="5CT-xZ-eQu" firstAttribute="centerX" secondItem="ta6-8U-AuJ" secondAttribute="centerX" id="yMw-1F-uwL"/>
                <constraint firstItem="fAJ-YU-H03" firstAttribute="trailing" secondItem="DcQ-u0-xWO" secondAttribute="trailing" id="yNt-cz-fwb"/>
            </constraints>
            <point key="canvasLocation" x="-175.19999999999999" y="80.50974512743629"/>
        </view>
    </objects>
    <resources>
        <image name="icon_arrow_leftsssa" width="28" height="28"/>
        <image name="startLive_back.png" width="280" height="40"/>
        <systemColor name="darkTextColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="groupTableViewBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
