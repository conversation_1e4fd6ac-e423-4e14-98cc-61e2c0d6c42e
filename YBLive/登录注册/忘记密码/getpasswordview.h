#import <UIKit/UIKit.h>

@interface getpasswordview : UIViewController


@property (nonatomic, strong)NSString *sendcode_typeStr;
@property (nonatomic, strong)NSString *from_where;

@property (weak, nonatomic) IBOutlet UITextField *phoneT;

@property (weak, nonatomic) IBOutlet UITextField *yanzhengma;

- (IBAction)clickYanzhengma:(id)sender;
@property (weak, nonatomic) IBOutlet UIButton *yanzhengmaBtn;

@property (weak, nonatomic) IBOutlet UITextField *secretT;
@property (weak, nonatomic) IBOutlet UITextField *nsecretT;

@property (weak, nonatomic) IBOutlet UILabel *secretT2;
@property (weak, nonatomic) IBOutlet UITextField *secretTT2;
@property (weak, nonatomic) IBOutlet UIButton *findNowBtn;
@property (weak, nonatomic) IBOutlet UILabel *subTLb;
@property (weak, nonatomic) IBOutlet UIImageView *CodemaImg;

- (IBAction)clickFindBtn:(id)sender;

@end
