#import "getpasswordview.h"
#import "CountryCodeVC.h"
#import "AppDelegate.h"
#import "ZYTabBarController.h"

@interface getpasswordview ()
{
    NSTimer *messsageTimer;
    int messageIssssss;//短信倒计时  60s
    CALayer *layer;
    BOOL showCode;

}

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *VIewH;
#pragma mark ================ 语言包的时候需要修改的label ===============

@property (weak, nonatomic) IBOutlet UILabel *titlelabel;
@property (weak, nonatomic) IBOutlet UILabel *nameLabel;
@property (weak, nonatomic) IBOutlet UILabel *yzmLabel;
@property (weak, nonatomic) IBOutlet UILabel *pwdLabel;
@property (weak, nonatomic) IBOutlet UIButton *codeBtn;
@property (nonatomic, strong)NSString *countrycode;

@end

@implementation getpasswordview

- (void)viewDidLoad {
    [super viewDidLoad];
    if (self.VIewH.constant<80) {
        self.VIewH.constant+=statusbarHeight;
    }
    _titlelabel.text = YZMsg(@"忘记密码");
    _phoneT.placeholder = YZMsg(@"请输入您的手机号");
    _yanzhengma.placeholder = YZMsg(@"请输入验证码");
//    _secretT.placeholder = YZMsg(@"请填写密码");
    _nsecretT.placeholder = YZMsg(@"请填写密码");
    _secretTT2.placeholder = YZMsg(@"请确认密码");
    [_yanzhengmaBtn setTitle:YZMsg(@"获取验证码") forState:0];
    _yanzhengmaBtn.userInteractionEnabled = NO;
    _yanzhengmaBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
    _yanzhengma.adjustsFontSizeToFitWidth = YES;

    _subTLb.text = YZMsg(@"*短信验证保障账户安全的同时短信费用将由平台支付");
    _CodemaImg.image = [UIImage imageNamed:getImagename(@"regist_code")];

    [_findNowBtn setTitle:YZMsg(@"立即找回") forState:0];
    _findNowBtn.alpha = 0.5;
    [_phoneT becomeFirstResponder];
    messageIssssss = 60;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ChangeBtnBackground) name:UITextFieldTextDidChangeNotification object:nil];
    
    
    if ([self.sendcode_typeStr isEqual:@"0"]) {
        self.codeBtn.userInteractionEnabled = NO;
    }else{
        self.codeBtn.userInteractionEnabled = YES;

    }

    self.countrycode = @"86";
}
-(void)ChangeBtnBackground
{
    if (_phoneT.text.length > 0) {
        _yanzhengmaBtn.userInteractionEnabled = YES;
        [_yanzhengmaBtn setTitleColor:normalColors forState:0];
    }else{
        _yanzhengmaBtn.userInteractionEnabled = NO;
        [_yanzhengmaBtn setTitleColor:RGB_COLOR(@"#c8c8c8", 1) forState:0];
    }

    if (_phoneT.text.length > 0 && _yanzhengma.text.length >1 && _nsecretT.text.length > 0 && _secretTT2.text.length >0)
    {
        _findNowBtn.alpha = 1;
        _findNowBtn.enabled = YES;
    }
    else
    {
        _findNowBtn.alpha = 0.5;
        _findNowBtn.enabled = NO;
    }
}
-(void)daojishi{
    [_yanzhengmaBtn setTitle:[NSString stringWithFormat:@"%ds",messageIssssss] forState:UIControlStateNormal];
    _yanzhengmaBtn.userInteractionEnabled = NO;
    if (messageIssssss<=0) {
        [_yanzhengmaBtn setTitle:YZMsg(@"发送验证码") forState:UIControlStateNormal];
        _yanzhengmaBtn.userInteractionEnabled = YES;
        [messsageTimer invalidate];
        messsageTimer = nil;
        messageIssssss = 60;
    }
    messageIssssss-=1;
}
- (IBAction)areaCodeBtnClick:(UIButton *)sender {

    YBWeakSelf;
    CountryCodeVC *codeVC = [[CountryCodeVC alloc]init];
    codeVC.codeEvent = ^(NSDictionary *codeDic) {
        [weakSelf.codeBtn setTitle:[NSString stringWithFormat:@"+%@",minstr([codeDic valueForKey:@"tel"])] forState:0];
        weakSelf.countrycode = minstr([codeDic valueForKey:@"tel"]);
    };

    [[MXBADelegate sharedAppDelegate]pushViewController:codeVC animated:YES];

}
- (IBAction)clickYanzhengma:(id)sender {
    if (_phoneT.text.length == 0){
        [MBProgressHUD showError:YZMsg(@"请输入手机号")];
        return;
    }

    _yanzhengmaBtn.userInteractionEnabled = NO;
    messageIssssss = 60;
    NSDictionary *ForgetCode = @{
                                 @"mobile":_phoneT.text,
                                 @"sign":[[YBToolClass sharedInstance] md5:[NSString stringWithFormat:@"mobile=%@&76576076c1f5f657b634e966c8836a06",_phoneT.text]],
                                 @"country_code":self.countrycode

                                 };

    [YBToolClass postNetworkWithUrl:@"Login.getForgetCode" andParameter:ForgetCode success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            _yanzhengmaBtn.userInteractionEnabled = YES;
            if (messsageTimer == nil) {
                messsageTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(daojishi) userInfo:nil repeats:YES];
            }
            
            [MBProgressHUD showError:YZMsg(@"发送成功")];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [MBProgressHUD hideHUD];
            });

        }else{
            _yanzhengmaBtn.userInteractionEnabled = YES;
            [MBProgressHUD showError:msg];

        }
    } fail:^{
        _yanzhengmaBtn.userInteractionEnabled = YES;

    }];

}
//键盘的隐藏
- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event {
    [self.view endEditing:YES];
}
- (IBAction)doBack:(id)sender {
    
    [self.navigationController popViewControllerAnimated:YES];
}
- (IBAction)clickFindBtn:(id)sender {
    YBWeakSelf;

    NSDictionary *FindPass = @{
                               @"user_login":_phoneT.text,
                               @"user_pass":_nsecretT.text,
                               @"user_pass2":_secretTT2.text,
                               @"code":_yanzhengma.text
                               };
    
    [YBToolClass postNetworkWithUrl:@"Login.userFindPass" andParameter:FindPass success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            [MBProgressHUD showError:YZMsg(@"密码重置成功")];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if ([self.from_where isEqual:@"forget"]) {
                    [weakSelf quitLogin];
                }else{
                    [self.navigationController popViewControllerAnimated:YES];

                }
            });
        }else{
            [MBProgressHUD showError:msg];
        }
    } fail:^{
        
    }];
    

    
}
//退出登录函数
-(void)quitLogin
{
    [YBToolClass postNetworkWithUrl:@"Login.logout" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            [[YBImManager shareInstance]imLogout];
            [Config clearProfile];
            LiveUser *userInfo = [[LiveUser alloc] initWithDic:[PublicObj visitorDic]];
            [Config saveProfile:userInfo];

            UIApplication *app =[UIApplication sharedApplication];
            AppDelegate *app2 = (AppDelegate*)app.delegate;
            ZYTabBarController *root = [[ZYTabBarController alloc]init];
            UINavigationController *nav = [[UINavigationController alloc]initWithRootViewController:root];
            app2.window.rootViewController = nav;

        }else{
            [MBProgressHUD showError:msg];
        }
    } fail:^{
        
    }];
}

@end
