<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="getpasswordview">
            <connections>
                <outlet property="CodemaImg" destination="6GK-tH-sqX" id="gk3-F8-3Je"/>
                <outlet property="VIewH" destination="S0g-mf-0g6" id="hUK-MI-nYx"/>
                <outlet property="codeBtn" destination="Q4f-Mn-srk" id="Msc-WS-bOg"/>
                <outlet property="findNowBtn" destination="kJd-cS-CMW" id="EBP-Pr-MdY"/>
                <outlet property="nameLabel" destination="Rws-8b-Vzj" id="i5P-8B-5I3"/>
                <outlet property="nsecretT" destination="DJF-f2-VKh" id="NPB-Gw-vHw"/>
                <outlet property="phoneT" destination="Tql-OI-JWI" id="khk-Wp-G2M"/>
                <outlet property="pwdLabel" destination="rgm-V5-Pee" id="g3P-d3-ZkP"/>
                <outlet property="secretT" destination="9rE-6I-nDx" id="Yxk-C6-yxd"/>
                <outlet property="secretT2" destination="lql-Jg-BE5" id="ae1-RL-2BJ"/>
                <outlet property="secretTT2" destination="bCf-vE-Set" id="ZlJ-iz-H1I"/>
                <outlet property="subTLb" destination="aZk-mO-d9R" id="OZw-qz-D7H"/>
                <outlet property="titlelabel" destination="NZE-NH-KVq" id="gIq-Ea-8E3"/>
                <outlet property="view" destination="VqB-Eg-QNw" id="beW-yz-pVe"/>
                <outlet property="yanzhengma" destination="dZB-gz-B5T" id="Dtg-U8-qZJ"/>
                <outlet property="yanzhengmaBtn" destination="UaV-4v-lDw" id="GOb-th-mk5"/>
                <outlet property="yzmLabel" destination="Rxy-XQ-B5i" id="Tmh-PP-Yc3"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="VqB-Eg-QNw">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4ti-tw-jny">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="64"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="忘记密码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NZE-NH-KVq">
                            <rect key="frame" x="153" y="31.5" width="69.5" height="21"/>
                            <fontDescription key="fontDescription" type="system" weight="thin" pointSize="17"/>
                            <color key="textColor" systemColor="darkTextColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" clipsSubviews="YES" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JNg-it-9lb">
                            <rect key="frame" x="20" y="32" width="15" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="gDh-Ka-tz2"/>
                                <constraint firstAttribute="width" constant="15" id="mPn-kI-UyP"/>
                            </constraints>
                            <state key="normal" image="icon_arrow_leftsssa"/>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bVC-OK-HP1">
                            <rect key="frame" x="-2.5" y="12" width="60" height="60"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="60" id="Epw-S0-2RF"/>
                                <constraint firstAttribute="height" constant="60" id="OYW-Zv-Xvz"/>
                            </constraints>
                            <connections>
                                <action selector="doBack:" destination="-1" eventType="touchUpInside" id="9UQ-XX-vvN"/>
                            </connections>
                        </button>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hYU-84-3jc">
                            <rect key="frame" x="0.0" y="63" width="375" height="1"/>
                            <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="kEv-A1-Yoq"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    <constraints>
                        <constraint firstItem="bVC-OK-HP1" firstAttribute="centerY" secondItem="JNg-it-9lb" secondAttribute="centerY" id="3p7-cS-vaB"/>
                        <constraint firstAttribute="bottom" secondItem="hYU-84-3jc" secondAttribute="bottom" id="46G-ga-y3w"/>
                        <constraint firstAttribute="height" constant="64" id="S0g-mf-0g6"/>
                        <constraint firstItem="NZE-NH-KVq" firstAttribute="centerX" secondItem="4ti-tw-jny" secondAttribute="centerX" id="XpO-S8-dfX"/>
                        <constraint firstItem="hYU-84-3jc" firstAttribute="leading" secondItem="4ti-tw-jny" secondAttribute="leading" id="XvL-fA-N5Z"/>
                        <constraint firstItem="bVC-OK-HP1" firstAttribute="centerX" secondItem="JNg-it-9lb" secondAttribute="centerX" id="cuV-UI-epq"/>
                        <constraint firstAttribute="trailing" secondItem="hYU-84-3jc" secondAttribute="trailing" id="fhk-2b-cWa"/>
                        <constraint firstItem="JNg-it-9lb" firstAttribute="centerY" secondItem="NZE-NH-KVq" secondAttribute="centerY" id="pSR-Xn-BaI"/>
                        <constraint firstItem="NZE-NH-KVq" firstAttribute="centerY" secondItem="4ti-tw-jny" secondAttribute="centerY" constant="10" id="sdn-gX-DTY"/>
                        <constraint firstItem="JNg-it-9lb" firstAttribute="leading" secondItem="4ti-tw-jny" secondAttribute="leading" constant="20" id="uTA-ZH-IbH"/>
                    </constraints>
                </view>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="账号" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rws-8b-Vzj">
                    <rect key="frame" x="20" y="89" width="31" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请填写手机号" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="Tql-OI-JWI">
                    <rect key="frame" x="107" y="78" width="249" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="C10-Se-jaN"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="14"/>
                    <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                </textField>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GDv-yu-OGv">
                    <rect key="frame" x="15" y="127" width="340" height="1"/>
                    <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="09G-TG-QPn"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="验证" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rxy-XQ-B5i">
                    <rect key="frame" x="20" y="152" width="31" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="regist_code.png" translatesAutoresizingMaskIntoConstraints="NO" id="6GK-tH-sqX">
                    <rect key="frame" x="25.5" y="151" width="20" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="20" id="8Ba-vS-Uns"/>
                        <constraint firstAttribute="height" constant="20" id="zmy-pf-WoV"/>
                    </constraints>
                </imageView>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请输入验证码" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="dZB-gz-B5T">
                    <rect key="frame" x="71" y="141" width="142" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="EpH-i3-a7M"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="14"/>
                    <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                </textField>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UaV-4v-lDw">
                    <rect key="frame" x="245.5" y="146" width="99.5" height="30"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="30" id="BzC-5s-PCc"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="14"/>
                    <state key="normal" title="获取验证码">
                        <color key="titleColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </state>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="15"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="clickYanzhengma:" destination="-1" eventType="touchUpInside" id="saY-gq-rF9"/>
                    </connections>
                </button>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ilg-pf-EFo">
                    <rect key="frame" x="15" y="195" width="340" height="1"/>
                    <color key="backgroundColor" red="0.96078431369999995" green="0.96078431369999995" blue="0.96078431369999995" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="oqe-ys-UnL"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="密码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rgm-V5-Pee">
                    <rect key="frame" x="20" y="220" width="31" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="regist_pwd.png" translatesAutoresizingMaskIntoConstraints="NO" id="CeR-r7-pkd">
                    <rect key="frame" x="25.5" y="219" width="20" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="20" id="AAC-sj-D8d"/>
                        <constraint firstAttribute="height" constant="20" id="Pp2-NF-UGC"/>
                    </constraints>
                </imageView>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="9rE-6I-nDx">
                    <rect key="frame" x="71" y="209" width="284" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="Id7-xL-q3F"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                </textField>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1a5-6L-nGO">
                    <rect key="frame" x="15" y="263" width="340" height="1"/>
                    <color key="backgroundColor" red="0.96078431369999995" green="0.96078431369999995" blue="0.96078431369999995" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="Wu7-0F-TuQ"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="确认" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lql-Jg-BE5">
                    <rect key="frame" x="20" y="288" width="31" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="regist_pwd.png" translatesAutoresizingMaskIntoConstraints="NO" id="krs-9X-bXs">
                    <rect key="frame" x="25.5" y="287" width="20" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="GuF-WH-NZR"/>
                        <constraint firstAttribute="width" constant="20" id="Hxd-gM-OVM"/>
                    </constraints>
                </imageView>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请确认密码" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="bCf-vE-Set">
                    <rect key="frame" x="71" y="277" width="284" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="po9-sw-Rab"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                </textField>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HHz-KH-TMX">
                    <rect key="frame" x="15" y="331" width="340" height="1"/>
                    <color key="backgroundColor" red="0.96078431369999995" green="0.96078431369999995" blue="0.96078431369999995" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="Gof-Ed-xTS"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" enabled="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kJd-cS-CMW">
                    <rect key="frame" x="37.5" y="381" width="300" height="40"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="gDf-Bf-t9e"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="thin" pointSize="15"/>
                    <state key="normal" title="立即找回" backgroundImage="startLive_back.png">
                        <color key="titleColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    </state>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="20"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="clickFindBtn:" destination="-1" eventType="touchUpInside" id="efW-KP-xvo"/>
                    </connections>
                </button>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="regist_phone.png" translatesAutoresizingMaskIntoConstraints="NO" id="6sb-vu-iMQ">
                    <rect key="frame" x="25.5" y="88" width="20" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="LFn-LV-Rs1"/>
                        <constraint firstAttribute="width" constant="20" id="oir-qE-ejM"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="*短信验证保障账户安全的同时短信费用将由平台支付" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aZk-mO-d9R">
                    <rect key="frame" x="25.5" y="342" width="311.5" height="16"/>
                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                    <color key="textColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Q4f-Mn-srk">
                    <rect key="frame" x="65" y="78" width="42" height="40"/>
                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                    <state key="normal" title="+86">
                        <color key="titleColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                    </state>
                    <connections>
                        <action selector="areaCodeBtnClick:" destination="-1" eventType="touchUpInside" id="JNl-zw-3Ee"/>
                    </connections>
                </button>
                <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="请填写密码" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="DJF-f2-VKh">
                    <rect key="frame" x="71" y="209" width="284" height="40"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits" secureTextEntry="YES"/>
                </textField>
            </subviews>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="dZB-gz-B5T" firstAttribute="centerY" secondItem="Rxy-XQ-B5i" secondAttribute="centerY" id="1IP-kR-ZSd"/>
                <constraint firstItem="4ti-tw-jny" firstAttribute="leading" secondItem="VqB-Eg-QNw" secondAttribute="leading" id="1wt-Nl-EfU"/>
                <constraint firstItem="Rws-8b-Vzj" firstAttribute="top" secondItem="4ti-tw-jny" secondAttribute="bottom" constant="25" id="7j5-DM-rfq"/>
                <constraint firstItem="DJF-f2-VKh" firstAttribute="bottom" secondItem="9rE-6I-nDx" secondAttribute="bottom" id="8Xt-wo-ffG"/>
                <constraint firstItem="bCf-vE-Set" firstAttribute="leading" secondItem="9rE-6I-nDx" secondAttribute="leading" id="8ny-7e-Eg2"/>
                <constraint firstItem="kJd-cS-CMW" firstAttribute="top" secondItem="HHz-KH-TMX" secondAttribute="top" constant="50" id="9ZG-gs-o9A"/>
                <constraint firstItem="kJd-cS-CMW" firstAttribute="centerX" secondItem="VqB-Eg-QNw" secondAttribute="centerX" id="BCd-3f-x3F"/>
                <constraint firstItem="krs-9X-bXs" firstAttribute="centerX" secondItem="lql-Jg-BE5" secondAttribute="centerX" id="DS5-V0-UCB"/>
                <constraint firstItem="Ilg-pf-EFo" firstAttribute="leading" secondItem="Rxy-XQ-B5i" secondAttribute="leading" constant="-5" id="E8M-XB-Hl9"/>
                <constraint firstAttribute="trailing" secondItem="Tql-OI-JWI" secondAttribute="trailing" constant="19" id="EHt-zx-4hp"/>
                <constraint firstItem="GDv-yu-OGv" firstAttribute="trailing" secondItem="Tql-OI-JWI" secondAttribute="trailing" constant="-1" id="EZS-Xb-ZD9"/>
                <constraint firstItem="4ti-tw-jny" firstAttribute="width" secondItem="VqB-Eg-QNw" secondAttribute="width" id="Fs8-yD-6bf"/>
                <constraint firstItem="Rxy-XQ-B5i" firstAttribute="top" secondItem="GDv-yu-OGv" secondAttribute="top" constant="25" id="GKW-6V-2W1"/>
                <constraint firstItem="UaV-4v-lDw" firstAttribute="trailing" secondItem="GDv-yu-OGv" secondAttribute="trailing" constant="-10" id="Iej-F7-uWM"/>
                <constraint firstItem="6sb-vu-iMQ" firstAttribute="centerY" secondItem="Rws-8b-Vzj" secondAttribute="centerY" id="Ixc-GV-Ccd"/>
                <constraint firstItem="1a5-6L-nGO" firstAttribute="leading" secondItem="Ilg-pf-EFo" secondAttribute="leading" id="J93-NV-Jvm"/>
                <constraint firstItem="1a5-6L-nGO" firstAttribute="top" secondItem="rgm-V5-Pee" secondAttribute="bottom" constant="25" id="K09-jJ-NeL"/>
                <constraint firstItem="Q4f-Mn-srk" firstAttribute="leading" secondItem="Rws-8b-Vzj" secondAttribute="trailing" constant="14" id="KpF-pB-B5h"/>
                <constraint firstItem="Rxy-XQ-B5i" firstAttribute="leading" secondItem="Rws-8b-Vzj" secondAttribute="leading" id="MT7-0a-DaV"/>
                <constraint firstItem="9rE-6I-nDx" firstAttribute="leading" secondItem="Tql-OI-JWI" secondAttribute="leading" constant="-36" id="MqU-yK-qf9"/>
                <constraint firstItem="6GK-tH-sqX" firstAttribute="centerX" secondItem="Rxy-XQ-B5i" secondAttribute="centerX" id="NPY-1h-Zsc"/>
                <constraint firstItem="Ilg-pf-EFo" firstAttribute="top" secondItem="Rxy-XQ-B5i" secondAttribute="bottom" constant="25" id="OX1-TH-cE0"/>
                <constraint firstAttribute="trailing" secondItem="aZk-mO-d9R" secondAttribute="trailing" constant="38" id="PK7-4i-GAu"/>
                <constraint firstItem="lql-Jg-BE5" firstAttribute="leading" secondItem="rgm-V5-Pee" secondAttribute="leading" id="Pv1-VQ-85H"/>
                <constraint firstItem="rgm-V5-Pee" firstAttribute="top" secondItem="Ilg-pf-EFo" secondAttribute="top" constant="25" id="RUC-OR-Hkd"/>
                <constraint firstItem="Ilg-pf-EFo" firstAttribute="trailing" secondItem="GDv-yu-OGv" secondAttribute="trailing" id="S09-IT-3h8"/>
                <constraint firstItem="CeR-r7-pkd" firstAttribute="centerX" secondItem="rgm-V5-Pee" secondAttribute="centerX" id="TrG-ae-4rT"/>
                <constraint firstItem="lql-Jg-BE5" firstAttribute="top" secondItem="1a5-6L-nGO" secondAttribute="top" constant="25" id="U0F-Mw-YTQ"/>
                <constraint firstItem="HHz-KH-TMX" firstAttribute="top" secondItem="lql-Jg-BE5" secondAttribute="bottom" constant="25" id="WAd-q3-Rlg"/>
                <constraint firstItem="DJF-f2-VKh" firstAttribute="leading" secondItem="9rE-6I-nDx" secondAttribute="leading" id="YXY-E1-tz1"/>
                <constraint firstItem="9rE-6I-nDx" firstAttribute="centerY" secondItem="rgm-V5-Pee" secondAttribute="centerY" id="Yfb-yF-RGp"/>
                <constraint firstItem="GDv-yu-OGv" firstAttribute="leading" secondItem="Rws-8b-Vzj" secondAttribute="leading" constant="-5" id="Z4S-ci-mMO"/>
                <constraint firstItem="Q4f-Mn-srk" firstAttribute="bottom" secondItem="Tql-OI-JWI" secondAttribute="bottom" id="bTb-KP-t74"/>
                <constraint firstItem="CeR-r7-pkd" firstAttribute="centerY" secondItem="rgm-V5-Pee" secondAttribute="centerY" id="c53-0Q-k22"/>
                <constraint firstItem="DJF-f2-VKh" firstAttribute="top" secondItem="9rE-6I-nDx" secondAttribute="top" id="ceb-36-cPG"/>
                <constraint firstItem="rgm-V5-Pee" firstAttribute="leading" secondItem="Rxy-XQ-B5i" secondAttribute="leading" id="cod-ii-qsS"/>
                <constraint firstItem="dZB-gz-B5T" firstAttribute="leading" secondItem="Tql-OI-JWI" secondAttribute="leading" constant="-36" id="dbe-8R-rrq"/>
                <constraint firstItem="aZk-mO-d9R" firstAttribute="top" secondItem="HHz-KH-TMX" secondAttribute="bottom" constant="10" id="eBf-6Q-k2Q"/>
                <constraint firstItem="HHz-KH-TMX" firstAttribute="width" secondItem="1a5-6L-nGO" secondAttribute="width" id="eyp-zQ-Ta7"/>
                <constraint firstItem="GDv-yu-OGv" firstAttribute="top" secondItem="Rws-8b-Vzj" secondAttribute="bottom" constant="20" id="fPm-Sa-F1h"/>
                <constraint firstItem="UaV-4v-lDw" firstAttribute="width" secondItem="dZB-gz-B5T" secondAttribute="width" multiplier="0.7" id="gzG-mn-XQX"/>
                <constraint firstItem="DJF-f2-VKh" firstAttribute="trailing" secondItem="9rE-6I-nDx" secondAttribute="trailing" id="h1i-sm-qcE"/>
                <constraint firstItem="Tql-OI-JWI" firstAttribute="leading" secondItem="Q4f-Mn-srk" secondAttribute="trailing" id="hsk-jf-Z8h"/>
                <constraint firstItem="aZk-mO-d9R" firstAttribute="leading" secondItem="krs-9X-bXs" secondAttribute="leading" id="huY-7L-4Fr"/>
                <constraint firstItem="Q4f-Mn-srk" firstAttribute="top" secondItem="Tql-OI-JWI" secondAttribute="top" id="ipE-Rl-JE5"/>
                <constraint firstItem="bCf-vE-Set" firstAttribute="centerY" secondItem="lql-Jg-BE5" secondAttribute="centerY" id="jXW-Vm-xTJ"/>
                <constraint firstItem="dZB-gz-B5T" firstAttribute="width" secondItem="Tql-OI-JWI" secondAttribute="width" multiplier="0.5" constant="17.5" id="lP2-zP-N7I"/>
                <constraint firstItem="krs-9X-bXs" firstAttribute="centerY" secondItem="lql-Jg-BE5" secondAttribute="centerY" id="lyB-Rd-S7E"/>
                <constraint firstItem="6sb-vu-iMQ" firstAttribute="centerX" secondItem="Rws-8b-Vzj" secondAttribute="centerX" id="nAh-z2-eiD"/>
                <constraint firstItem="bCf-vE-Set" firstAttribute="trailing" secondItem="HHz-KH-TMX" secondAttribute="trailing" id="nEg-Rd-QQs"/>
                <constraint firstItem="Rxy-XQ-B5i" firstAttribute="centerX" secondItem="Rws-8b-Vzj" secondAttribute="centerX" id="nzE-EU-sNV"/>
                <constraint firstItem="Tql-OI-JWI" firstAttribute="leading" secondItem="Rws-8b-Vzj" secondAttribute="trailing" constant="56" id="oU8-yQ-Hfp"/>
                <constraint firstItem="9rE-6I-nDx" firstAttribute="trailing" secondItem="Tql-OI-JWI" secondAttribute="trailing" constant="-1" id="oa2-M7-0dW"/>
                <constraint firstItem="1a5-6L-nGO" firstAttribute="width" secondItem="Ilg-pf-EFo" secondAttribute="width" id="pA9-Qf-7r1"/>
                <constraint firstItem="Rws-8b-Vzj" firstAttribute="leading" secondItem="4ti-tw-jny" secondAttribute="leading" constant="20" id="qEA-jm-33e"/>
                <constraint firstItem="UaV-4v-lDw" firstAttribute="centerY" secondItem="dZB-gz-B5T" secondAttribute="centerY" id="v7B-cA-Kt4"/>
                <constraint firstItem="4ti-tw-jny" firstAttribute="top" secondItem="VqB-Eg-QNw" secondAttribute="top" id="wAK-a7-Lu8"/>
                <constraint firstItem="Tql-OI-JWI" firstAttribute="centerY" secondItem="Rws-8b-Vzj" secondAttribute="centerY" id="wjq-KS-Ghj"/>
                <constraint firstItem="6GK-tH-sqX" firstAttribute="centerY" secondItem="Rxy-XQ-B5i" secondAttribute="centerY" id="x8f-d6-TU7"/>
                <constraint firstItem="HHz-KH-TMX" firstAttribute="leading" secondItem="1a5-6L-nGO" secondAttribute="leading" id="z0p-eE-gOb"/>
                <constraint firstItem="kJd-cS-CMW" firstAttribute="width" secondItem="VqB-Eg-QNw" secondAttribute="width" multiplier="0.8" id="zq3-3j-88y"/>
            </constraints>
            <point key="canvasLocation" x="-202.40000000000001" y="7.6461769115442282"/>
        </view>
    </objects>
    <resources>
        <image name="icon_arrow_leftsssa" width="28" height="28"/>
        <image name="regist_code.png" width="18" height="18"/>
        <image name="regist_phone.png" width="18" height="18"/>
        <image name="regist_pwd.png" width="18" height="18"/>
        <image name="startLive_back.png" width="280" height="40"/>
        <systemColor name="darkTextColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
