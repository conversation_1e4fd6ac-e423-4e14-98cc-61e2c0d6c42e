//
//  MessageListVC.m
//  iphoneLive
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/13.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MessageListVC.h"

#import "MessageHeaderV.h"
#import "MessageListCell.h"
#import "MessageListModel.h"
#import "MessageFansVC.h"
#import "MsgTopPubVC.h"
#import "SelPeopleV.h"
#import "JCHATAlertViewWait.h"
#import "MsgSysVC.h"
//#import "CenterVC.h"
#import "OrderMessageVC.h"

#import "THeader.h"
#import "TUIKit.h"
#import "TChatC2CController.h"

@interface MessageListVC ()<UITableViewDelegate,UITableViewDataSource,V2TIMAdvancedMsgListener,V2TIMConversationListener>
{
    YBNavi *_ybNavi;
    __block NSMutableArray *_conversationArr;
    NSInteger _unreadCount;
    
    SelPeopleV * _selV;

    TConversationCellData *conver_admin1;//官方通知
    TConversationCellData *conver_admin2;//系统通知
    TConversationCellData *_conversation_fans;       //顶部-粉丝
    TConversationCellData *_conversation_zan;        //顶部-赞
    TConversationCellData *_conversation_at;         //顶部-@
    TConversationCellData *_conversation_comment;    //顶部-评论
    TConversationCellData *goodsorder_admin;//订单消息
    
    UILabel *nothingLabel;
    NSDictionary *systemDic;
    int syscount;//是否有系统消息

}
@property(nonatomic,assign)int paging;
@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)MessageHeaderV *headerV;
@property(nonatomic,strong)NSArray *models;
@property(nonatomic,strong)NSMutableArray *userArray;
@property (nonatomic, strong) NSMutableArray *data;
@property(nonatomic, assign) BOOL hideTopBar;

@end

@implementation MessageListVC

- (instancetype)initWithHideTopBar:(BOOL)hideTopBar {
    if (self = [super initWithNibName:nil bundle:nil]) {
        self.hideTopBar = hideTopBar;
    }
    
    return self;
}

-(void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}
- (void)onTotalUnreadMessageCountChanged:(UInt64)totalUnreadCount {
    [self updateConversations];
}
#pragma mark --消息监听
-(void)onRecvNewMessage:(V2TIMMessage *)msg
{
    [self updateConversations];
}
/// 收到消息撤回
- (void)onRecvMessageRevoked:(NSString *)msgID;
{
    [self updateConversations];
}
- (void)updateConversations
{
    YBWeakSelf;
     _data = [NSMutableArray array];
    [[YBImManager shareInstance]getAllConversationList:^(NSMutableArray *CovList, BOOL isSuccess) {
            if(isSuccess){
                weakSelf.data = CovList;
                [self requestUserMessage];
            }
    }];
}

#pragma mark --系统消息
- (void)bbbb:(NSNotification *)noti{
    [self requestSystemMsg];
}
- (void)requestSystemMsg{
    systemDic = @{
                  @"addtime":@"--",
                  @"content":YZMsg(@"暂无消息")
                  };
    [YBToolClass postNetworkWithUrl:@"Message.getList" andParameter:@{@"p":@"1"} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];
        if (code == 0) {
            if ([(NSArray*)info count]>0) {
                syscount = 1;
                systemDic = [info firstObject];
                [_tableView reloadData];

//                dispatch_async(dispatch_get_main_queue(), ^{
//                    [self.tableView beginUpdates];
//                    [self.tableView reloadRowsAtIndexPaths:@[[NSIndexPath indexPathForRow:0 inSection:0]] withRowAnimation:UITableViewRowAnimationNone];
//                    [_tableView endUpdates];
//                });
            }
        }
    } fail:^{
    }];
}
- (void)requestUserMessage{
    
    NSSortDescriptor * sortDes = [NSSortDescriptor sortDescriptorWithKey:@"timestamp" ascending:NO];
    NSArray *sortResult = [_data sortedArrayUsingDescriptors:@[sortDes]];
    NSMutableArray *m_group_array = [NSMutableArray array];
    
    NSMutableArray *m_c2c_array = [NSMutableArray array];

    for (TConversationCellData *sortData in sortResult) {
        //TConversationCellData *sortData = (TConversationCellData*)[sortDic objectForKey:key];
        //NSLog(@"xxxx-chat-have:%@==uid:%@",sortData.timestamp,sortData.convId);
        if(sortData.convType == TConv_Type_Group){
            [m_group_array addObject:sortData];
        }else if (sortData.convType == TConv_Type_C2C){
            NSString *imName = sortData.convId;
            if ([imName isEqual:@"dsp_admin_1"]) {
                // 因为总要admi1在第一位这里暂时不加入,for循环结束再插入
                //[m_c2c_array insertObject:sortData atIndex:0];
                conver_admin1 = sortData;
            }else if ([imName isEqual:@"dsp_admin_2"]){
                // 因为总要admi2在第二位这里暂时不加入,for循环结束再插入
                //[m_c2c_array insertObject:sortData atIndex:1];
                conver_admin2 = sortData;
            }else if ([imName isEqual:@"dsp_fans"]) {
                //粉丝
                _conversation_fans = sortData;
            }else if ([imName isEqual:@"dsp_like"]){
                //赞
                _conversation_zan = sortData;
            }else if ([imName isEqual:@"dsp_at"]){
                //@
                _conversation_at = sortData;
            }else if ([imName isEqual:@"dsp_comment"]){
                //评论
                _conversation_comment = sortData;
            }else if([imName containsString:@"dsp_user_"]){
                // dsp_user_live 【用于推送】
                
            }else if ([imName isEqual:@"goodsorder_admin"]){
                //订单管理员【未要求具体排序这里add即可】
                goodsorder_admin = sortData;
                [m_c2c_array addObject:sortData];
            }else{
                [m_c2c_array addObject:sortData];
            }
        }
    }
    // 检查dsp_admin_1、dsp_admin_2是否缺失
//    if (!conver_admin2) {
//        conver_admin2 = [[YBImManager shareInstance] createEmptyCellDataWithId:@"dsp_admin_2"];
//    }
//    if (!conver_admin1) {
//        conver_admin1 = [[YBImManager shareInstance] createEmptyCellDataWithId:@"dsp_admin_1"];
//    }
    [m_c2c_array insertObject:conver_admin2 atIndex:0];
    [m_c2c_array insertObject:conver_admin1 atIndex:0];
    // 刷新顶部
    [self reloadTopUnread];
    [self requestUserIDMessage:[m_c2c_array mutableCopy]];
}
- (void)requestUserIDMessage:(NSArray *)msgArray{
    NSString *uids = @"";
    for (TConversationCellData *data in msgArray) {
        uids = [uids stringByAppendingFormat:@"%@,",data.convId];
    }
    if (uids.length > 0) {
        //去掉最后一个逗号
        uids = [uids substringToIndex:[uids length] - 1];
    }else{
        _data = [NSMutableArray array];
        [_tableView reloadData];
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];

        return;
    }
    NSString *url = [purl stringByAppendingFormat:@"?service=User.getUidsInfo&uid=%@&uids=%@",[Config getOwnID],uids];

    NSMutableArray *m_array = [NSMutableArray array];
    [YBNetworking postWithUrl:url Dic:nil Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [_tableView.mj_header endRefreshing];
        [_tableView.mj_footer endRefreshing];
        if ([code isEqual:@"0"]) {
            NSArray *info = [data valueForKey:@"info"];
            NSLog(@"------——data:%@ =======\n info:%@",_data, info);

            for (int i = 0; i < [info count]; i ++) {
                TConversationCellData *data = msgArray[i];
                NSDictionary *dic = info[i];
                data.userName = minstr([dic valueForKey:@"user_nickname"]);
                data.userHeader = minstr([dic valueForKey:@"avatar"]);
                data.isauth = minstr([dic valueForKey:@"isauth"]);
                data.level_anchor = minstr([dic valueForKey:@"level_anchor"]);
                data.isAtt = minstr([dic valueForKey:@"utot"]);
                data.isVIP = minstr([dic valueForKey:@"isvip"]);
                data.sixStr = minstr([dic valueForKey:@"sex"]);
                data.levStr = minstr([dic valueForKey:@"level"]);
                data.hostlevStr = minstr([dic valueForKey:@"level_anchor"]);
//                data.isblack = minstr([dic valueForKey:@"isblack"]);
                [m_array addObject:data];

//                [_data replaceObjectAtIndex:i withObject:data];
            }
            _data = [NSMutableArray arrayWithArray:m_array];
            [_tableView reloadData];
        }else {
            [MBProgressHUD showError:msg];
        }
    } Fail:nil];

}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.navigationBar.hidden = YES;
    self.view.backgroundColor = RGB_COLOR(@"#F7F8FA", 1);
    
//    _rk_group = dispatch_group_create();

    self.userArray = [NSMutableArray array];
    self.models = [NSArray array];
    _conversationArr = [NSMutableArray array];
    _headerV = [[[NSBundle mainBundle]loadNibNamed:@"MessageHeaderV" owner:nil options:nil]objectAtIndex:0];

    if (!self.hideTopBar) {
        [self getview];
    }
    [self.view addSubview:self.tableView];
    [[V2TIMManager sharedInstance] addConversationListener:self];

    [self requestSystemMsg];
    [self updateConversations];

    //高级消息监听器
    [[V2TIMManager sharedInstance] addAdvancedMsgListener:self];

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(bbbb:) name:@"system_notificationUpdate" object:nil];

}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    
}
#pragma mark - 数据更新
- (NSArray *)models {
    NSMutableArray *m_array = [NSMutableArray array];
    for (NSDictionary *dic in _userArray) {
        if ([[YBYoungManager shareInstance]isOpenYoung]) {
            if([minstr([dic valueForKey:@"id"]) isEqual:@"goodsorder_admin"]){
                continue;
            }
        }
        MessageListModel *model = [MessageListModel modelWithDic:dic];
        [m_array addObject:model];
    }
    _models = m_array;
    
    return _models;
}
#pragma mark - 点击事件
-(void)goCenter:(NSString *)hostID{
    if ([hostID isEqual:@"dsp_admin_1"]||[hostID isEqual:@"dsp_admin_2"]) {
        return;
    }
    
//    CenterVC *center = [[CenterVC alloc]init];
//    if ([hostID isEqual:[Config getOwnID]]) {
//        center.otherUid =[Config getOwnID];
//    }else{
//        center.otherUid =hostID;
//    }
//    center.isPush = YES;
//    center.hidesBottomBarWhenPushed = YES;
//    [self.navigationController pushViewController:center animated:YES];
    
}
-(void)msgClickEvent:(NSString *)type {
    //rk_顶部红点
    if ([type isEqual:@"粉丝"]) {//=====>不需要翻译
        [[YBImManager shareInstance]clearUnreadConvId:_conversation_fans.convId sendNot:YES];
        _headerV.fansPoint.hidden = YES;
    }else if ([type isEqual:@"赞"]){
        [[YBImManager shareInstance]clearUnreadConvId:_conversation_zan.convId sendNot:YES];
        _headerV.zanPoint.hidden = YES;
    }else if ([type isEqual:@"@我的"]){
        [[YBImManager shareInstance]clearUnreadConvId:_conversation_at.convId sendNot:YES];
        _headerV.atPoint.hidden = YES;
    }else if ([type isEqual:@"评论"]){
        [[YBImManager shareInstance]clearUnreadConvId:_conversation_comment.convId sendNot:YES];
        _headerV.comPoint.hidden = YES;
    }
    if ([type isEqual:@"粉丝"]) {
        MessageFansVC *fansVC = [[MessageFansVC alloc]init];
        fansVC.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:fansVC animated:YES];
        NSLog(@"==粉丝==");
    }else {
        //赞、@我的、评论
        MsgTopPubVC *pubVC = [[MsgTopPubVC alloc]init];
        pubVC.type = type;
        pubVC.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:pubVC animated:YES];
         NSLog(@"==赞==@我的==评论==");
    }
}
-(void)selPeopleFun {
    YBWeakSelf;
    if (!_selV) {
        _selV = [[SelPeopleV alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, _window_height) showType:@"1" selUser:^(NSString *state, MessageListModel *userModel) {
            [weakSelf selCallBack:state uModel:userModel];
        }];
        [self.view addSubview:_selV];
        
    }
    [UIView animateWithDuration:0.3 animations:^{
        _selV.frame = CGRectMake(0, 0, _window_width, _window_height);
        self.tabBarController.tabBar.hidden = YES;
    }];
}
-(void)selCallBack:(NSString *)state uModel:(MessageListModel *)model{
    if ([state isEqual:@"关闭"]) {
        [UIView animateWithDuration:0.3 animations:^{
            _selV.frame = CGRectMake(0, _window_height, _window_width, _window_height);
        } completion:^(BOOL finished) {
            self.tabBarController.tabBar.hidden = NO;
            [_selV removeFromSuperview];
            _selV = nil;
        }];
    }else {
        //单聊
        [UIView animateWithDuration:0.3 animations:^{
            _selV.frame = CGRectMake(0, _window_height, _window_width, _window_height);
        } completion:^(BOOL finished) {
            self.tabBarController.tabBar.hidden = NO;
            [_selV removeFromSuperview];
            _selV = nil;
            //去单聊页面
            NSLog(@"===去单聊页面===%@",model.uidStr);
            TConversationCellData *cov = [[TConversationCellData alloc] init];
            cov.convId = model.uidStr;
            cov.convType = TConv_Type_C2C;
            cov.userHeader = model.iconStr;
            cov.userName = model.unameStr;
            if ([[common letter_switch] isEqual:@"0"]) {
                [MBProgressHUD showError:YZMsg(@"私信对话平台已关闭,暂时无法使用")];
                return;
            }

            TChatC2CController *chat = [[TChatC2CController alloc] init];
            chat.conversation = cov;
            [[MXBADelegate sharedAppDelegate] pushViewController:chat animated:YES];
        }];
        
    }
}
#pragma mark - UITableViewDelegate、UITableViewDataSource
//删除
-(void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.section == 0) {
        [MBProgressHUD showError:YZMsg(@"系统消息无法删除")];
        return;
    }

    TConversationCellData *conv = _data[indexPath.row];
    NSMutableArray *new_a = [NSMutableArray arrayWithArray:_data];
    [new_a removeObjectAtIndex:indexPath.row];
    _data = [NSMutableArray arrayWithArray:new_a];
    [tableView deleteRowsAtIndexPaths:[NSArray arrayWithObjects:indexPath, nil] withRowAnimation:UITableViewRowAnimationNone];
    //rrrrrrrrrrrrrr
//    TIMConversationType type = TIM_C2C;
//    if(conv.convType == TConv_Type_Group){
//        type = TIM_GROUP;
//    }
//    else if(conv.convType == TConv_Type_C2C){
//        type = TIM_C2C;
//    }
//    TIMConversation * convM = [[TIMManager sharedInstance] getConversation:type receiver:conv.convId];
//    [convM setReadMessage:nil succ:^{
//        NSLog(@"++++++++++++++++++++++++++");
//    } fail:^(int code, NSString *msg) {
//        NSLog(@"--------------------------");
//    }];
    [[YBImManager shareInstance]clearUnreadConvId:conv.convId sendNot:YES];
    [[V2TIMManager sharedInstance] deleteConversation:conv.convId succ:^{
        NSLog(@"success");
    } fail:^(int code, NSString *desc) {
        NSLog(@"failure, code:%d, desc:%@", code, desc);
    }];
}
-(CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return 108;
    }
    return 0.01;
}
-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        _headerV.frame = CGRectMake(0, 0, _window_width, 108);
//        _headerV.backgroundColor = [UIColor white];
        _headerV.fansBtn = [PublicObj setUpImgDownText:_headerV.fansBtn space:15];
        _headerV.zanBtn = [PublicObj setUpImgDownText:_headerV.zanBtn space:15];
        _headerV.aiTeBtn = [PublicObj setUpImgDownText:_headerV.aiTeBtn space:15];
        _headerV.commentBtn = [PublicObj setUpImgDownText:_headerV.commentBtn space:15];
        YBWeakSelf;
        _headerV.msgEvent = ^(NSString *type) {
            [weakSelf msgClickEvent:type];
        };
        return _headerV;
    }
    return nil;
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 2;
}
-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    
    return 0.01;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.section == 0) {
        return 80;
    }
    return TConversationCell.getSize.height;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    if (section == 0) {
        return 1;
    }
    return _data.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.section == 0) {
        MessageListCell *cell = [MessageListCell cellWithTab:tableView andIndexPath:indexPath];
        cell.rectCorner = _data.count == 0 ? UIRectCornerAllCorners : UIRectCornerTopLeft | UIRectCornerTopRight;
        cell.nameL.text = YZMsg(@"系统消息");
        cell.detailL.text = minstr([systemDic valueForKey:@"content"]);
        cell.timeL.text = minstr([systemDic valueForKey:@"addtime"]);
        if ([[NSUserDefaults standardUserDefaults] objectForKey:@"notifacationOldTime"]) {
            NSString *oldTime = [[NSUserDefaults standardUserDefaults] objectForKey:@"notifacationOldTime"];
            if ([[YBToolClass sharedInstance] compareDate:oldTime withDate:minstr([systemDic valueForKey:@"addtime"])] == 1) {
                cell.redPoint.hidden = NO;
            }else{
                cell.redPoint.hidden = YES;
            }
        }else{
            if ([minstr([systemDic valueForKey:@"addtime"]) isEqual:@"--"]) {
                cell.redPoint.hidden = YES;
            }else{
                cell.redPoint.hidden = NO;
            }
        }
        cell.iconIV.image = [PublicObj getAppIcon];
        cell.iconEvent = ^(NSString *type) {
            MsgSysVC *sysVC = [[MsgSysVC alloc]init];
            sysVC.hidesBottomBarWhenPushed = YES;
            [[MXBADelegate sharedAppDelegate]pushViewController:sysVC animated:YES];
        };
        return cell;
    }else{
        TConversationCell *cell  = [tableView dequeueReusableCellWithIdentifier:TConversationCell_ReuseId];
        if(!cell){
            cell = [[TConversationCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:TConversationCell_ReuseId];
        }
        if(indexPath.row == _data.count - 1) {
            cell.rectCorner = UIRectCornerBottomLeft | UIRectCornerBottomRight;
        } else {
            cell.rectCorner = 0;
        }
        
        TConversationCellData *celldata =[_data objectAtIndex:indexPath.row];
        [cell setData:celldata];
        return cell;
    }
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (indexPath.section == 0) {
        YBWeakSelf;
        MsgSysVC *sysVC = [[MsgSysVC alloc]init];
        sysVC.hidesBottomBarWhenPushed = YES;
        sysVC.reloadBlock = ^{
            [weakSelf.tableView reloadData];
        };
        [[MXBADelegate sharedAppDelegate]pushViewController:sysVC animated:YES];
    }else{
        TConversationCellData *cov = _data[indexPath.row];
        if ([cov.convId isEqual:@"dsp_admin_1"]||[cov.convId isEqual:@"dsp_admin_2"]) {
        }else if([cov.convId isEqual:@"goodsorder_admin"]){
            if ([[YBYoungManager shareInstance]isOpenYoung]) {
                [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
                return;
            }
            OrderMessageVC *order = [[OrderMessageVC alloc]init];
            order.conversation = cov;
            [[MXBADelegate sharedAppDelegate]pushViewController:order animated:YES];
        }else{
            if ([[common letter_switch] isEqual:@"0"]) {
                [MBProgressHUD showError:YZMsg(@"私信对话平台已关闭,暂时无法使用")];
                return;
            }

            TConversationCellData *cov = _data[indexPath.row];
            TChatC2CController *chat = [[TChatC2CController alloc] init];
            chat.conversation = cov;
            [[MXBADelegate sharedAppDelegate] pushViewController:chat animated:YES];
        }
    }
}
//刷新系统消息红点
-(void)reloadSysUnread{
    NSString *url = [purl stringByAppendingFormat:@"index.php?service=Message.getList&uid=%@&p=%@&token=%@",[Config getOwnID],@"1",[Config getOwnToken]];
    YBWeakSelf;
    [YBNetworking postWithUrl:url Dic:nil Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            NSArray *infoA = [data valueForKey:@"info"];
            if (infoA.count > 0) {
                [[NSUserDefaults standardUserDefaults] setObject:minstr([[infoA firstObject] valueForKey:@"addtime"]) forKey:@"notifacationOldTime"];
            }
            [_tableView reloadData];
        }
    } Fail:^(id fail) {
    }];

}

//rk_顶部红点
#pragma mark - 刷新顶部未读状态
-(void)reloadTopUnread {
    if (_conversation_fans.unRead > 0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            _headerV.fansPoint.hidden = NO;
            _headerV.fansPoint.text = [NSString stringWithFormat:@"%d",_conversation_fans.unRead];
            //个位数显示圆点，两位及以上显示椭圆
            if (_conversation_fans.unRead < 10) {
                _headerV.fanPointWidth.constant = _headerV.fansPoint.frame.size.height;
            }else{
                _headerV.fanPointWidth.constant = _headerV.fansPoint.frame.size.height+8;
            }
        });
    }
    if (_conversation_zan.unRead > 0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            _headerV.zanPoint.hidden = NO;
            _headerV.zanPoint.text = [NSString stringWithFormat:@"%d",_conversation_zan.unRead];
            //个位数显示圆点，两位及以上显示椭圆
            if (_conversation_zan.unRead < 10) {
                _headerV.zanPointWidth.constant = _headerV.zanPoint.frame.size.height;
            }else{
                _headerV.zanPointWidth.constant = _headerV.zanPoint.frame.size.height+8;
            }
        });
    }
    if (_conversation_at.unRead > 0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            _headerV.atPoint.hidden = NO;
            _headerV.atPoint.text = [NSString stringWithFormat:@"%d",_conversation_at.unRead];
            //个位数显示圆点，两位及以上显示椭圆
            if (_conversation_at.unRead < 10) {
                _headerV.atPointWidth.constant = _headerV.atPoint.frame.size.height;
            }else{
                _headerV.atPointWidth.constant = _headerV.atPoint.frame.size.height+8;
            }
        });
    }
    if (_conversation_comment.unRead > 0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            _headerV.comPoint.hidden = NO;
            _headerV.comPoint.text = [NSString stringWithFormat:@"%d",_conversation_comment.unRead];
            //个位数显示圆点，两位及以上显示椭圆
            if (_conversation_comment.unRead < 10) {
                _headerV.comPointWidth.constant = _headerV.comPoint.frame.size.height;
            }else{
                _headerV.comPointWidth.constant = _headerV.comPoint.frame.size.height+8;
            }
        });
    }
}
#pragma mark - set/get
-(UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc]initWithFrame:CGRectMake(0, self.hideTopBar ? 0.0 : 64+statusbarHeight , _window_width, _window_height - 64-statusbarHeight - (self.hideTopBar ? TabBar_Height : 0.0)) style:UITableViewStylePlain];
        _tableView.delegate   = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = RGB_COLOR(@"#F7F8FA", 1);
        _tableView.editing = NO;
        YBWeakSelf;
        _tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            [weakSelf updateConversations];
        }];
        if(@available(iOS 15.0, *)) {
            _tableView.sectionHeaderTopPadding = 0;
        }

    }
    return _tableView;
}

#pragma mark - 导航
-(void)creatNavi {
    _ybNavi = [[YBNavi alloc]init];
    _ybNavi.leftHidden = YES;
    _ybNavi.rightHidden = NO;
    _ybNavi.haveImgR = YES;
    [_ybNavi ybNaviLeft:^(id btnBack) {
    } andRightName:@"msg_linkman" andRight:^(id btnBack) {
        NSLog(@"选择联系人");
        [self selPeopleFun];
        
    } andMidTitle:YZMsg(@"消息")];
    [self.view addSubview:_ybNavi];
}
-(void)getview{
    self.navigationController.navigationBar.hidden = YES;
    self.navigationController.navigationItem.leftBarButtonItem = nil;
    UIView *navtion = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64 + statusbarHeight)];
    navtion.backgroundColor = RGB(256, 256, 256);
    
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2-50, statusbarHeight+24, 100, 40)];
    label.text = YZMsg(@"消息");
    label.textAlignment = NSTextAlignmentCenter;
    label.font = navtionTitleFont;
    [navtion addSubview:label];
    
    UIButton *returnBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    returnBtn.frame = CGRectMake(8,24 + statusbarHeight,40,40);
    returnBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 0, 12.5, 25);
    [returnBtn setImage:[UIImage imageNamed:@"icon_arrow_leftsssa.png"] forState:UIControlStateNormal];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    UIButton *rightBTN = [UIButton buttonWithType:UIButtonTypeSystem];
    [rightBTN setTitle:YZMsg(@"忽略未读") forState:UIControlStateNormal];
    rightBTN.titleLabel.font = [UIFont systemFontOfSize:12];
    [rightBTN addTarget:self action:@selector(weidu:) forControlEvents:UIControlEventTouchUpInside];
    [rightBTN setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    rightBTN.frame = CGRectMake(_window_width - 80,24 + statusbarHeight, 80, 40);
    rightBTN.titleLabel.textAlignment = NSTextAlignmentCenter;
    [navtion addSubview:rightBTN];
    [navtion addSubview:returnBtn];
    UIButton *btnttttt = [UIButton buttonWithType:UIButtonTypeCustom];
    btnttttt.backgroundColor = [UIColor clearColor];
    [btnttttt addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    btnttttt.frame = CGRectMake(0,0,100,64+ statusbarHeight);
    [navtion addSubview:btnttttt];
    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(0, navtion.height-1, _window_width, 1) andColor:RGB(244, 245, 246) andView:navtion];
    [self.view addSubview:navtion];
    
    
    nothingLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, _window_height*0.3, _window_width, 20)];
    nothingLabel.textColor = RGB_COLOR(@"#969696", 1);
    nothingLabel.font = [UIFont systemFontOfSize:13];
    nothingLabel.text = YZMsg(@"你还没有收到任何消息");
    nothingLabel.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:nothingLabel];
    nothingLabel.hidden = YES;
}
//返回
-(void)doReturn{
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
//忽略未读
-(void)weidu:(UIButton *)sender{
 
    int unread = 0;
    for (TConversationCellData *conversation in _data){
        unread += conversation.unRead;
        [[YBImManager shareInstance] clearUnreadConvId:conversation.convId sendNot:NO];
    }
    unread += [_conversation_fans unRead];
    unread += [_conversation_zan unRead];
    unread += [_conversation_at unRead];
    unread += [_conversation_comment unRead];
    
    [[YBImManager shareInstance] clearUnreadConvId:_conversation_fans.convId sendNot:NO];
    [[YBImManager shareInstance] clearUnreadConvId:_conversation_zan.convId sendNot:NO];
    [[YBImManager shareInstance] clearUnreadConvId:_conversation_at.convId sendNot:NO];
    [[YBImManager shareInstance] clearUnreadConvId:_conversation_comment.convId sendNot:NO];
    if (unread == 0) {
        [MBProgressHUD showError:YZMsg(@"当前暂无未读消息")];
    }else {
        [MBProgressHUD showError:YZMsg(@"已经忽略未读消息")];
        UITabBarItem *item = [self.tabBarController.tabBar.items objectAtIndex:2];
        item.badgeValue = nil;
        [self updateConversations];
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [_headerV clearRedPoint];
    });
    [self reloadSysUnread];
}
- (void)playVoice{
    NSURL *soundUrl = [[NSBundle mainBundle] URLForResource:@"messageVioce" withExtension:@"mp3"];
    SystemSoundID soundID;
    AudioServicesCreateSystemSoundID((__bridge CFURLRef)soundUrl,&soundID);
    AudioServicesPlaySystemSound(soundID);
}

@end
