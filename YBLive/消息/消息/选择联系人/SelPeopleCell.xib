<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="72" id="KGk-i7-Jjw" customClass="SelPeopleCell">
            <rect key="frame" x="0.0" y="0.0" width="443" height="72"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="443" height="72"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleAspectFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="axv-xo-Xx3" userLabel="头像">
                        <rect key="frame" x="15" y="11" width="50" height="50"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="axv-xo-Xx3" secondAttribute="height" multiplier="1:1" id="2f5-B6-eTP"/>
                            <constraint firstAttribute="width" constant="50" id="hbW-lp-lJm"/>
                        </constraints>
                        <connections>
                            <action selector="clickIconBtn:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="zXv-lZ-CtK"/>
                        </connections>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fqp-M0-M8j" userLabel="名称">
                        <rect key="frame" x="80" y="11" width="35.5" height="25"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NMb-Se-JUx" userLabel="签名">
                        <rect key="frame" x="80" y="36" width="348" height="25"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" red="0.5490196078431373" green="0.5490196078431373" blue="0.5490196078431373" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KpQ-t8-FCi" userLabel="线">
                        <rect key="frame" x="15" y="71" width="413" height="1"/>
                        <color key="backgroundColor" red="0.96078431372549022" green="0.96470588235294119" blue="0.96862745098039216" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="cXv-9p-DN2"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="fqp-M0-M8j" firstAttribute="leading" secondItem="axv-xo-Xx3" secondAttribute="trailing" constant="15" id="2dk-5W-UjL"/>
                    <constraint firstItem="KpQ-t8-FCi" firstAttribute="centerX" secondItem="H2p-sc-9uM" secondAttribute="centerX" id="8SV-WA-3HO"/>
                    <constraint firstItem="NMb-Se-JUx" firstAttribute="top" secondItem="fqp-M0-M8j" secondAttribute="bottom" id="CrB-AH-fol"/>
                    <constraint firstItem="NMb-Se-JUx" firstAttribute="leading" secondItem="fqp-M0-M8j" secondAttribute="leading" id="ERb-Qk-4JP"/>
                    <constraint firstAttribute="trailing" secondItem="NMb-Se-JUx" secondAttribute="trailing" constant="15" id="SPg-Ez-lrl"/>
                    <constraint firstItem="KpQ-t8-FCi" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" constant="-30" id="UHT-xV-F8p"/>
                    <constraint firstItem="NMb-Se-JUx" firstAttribute="height" secondItem="fqp-M0-M8j" secondAttribute="height" id="WvD-Yr-afP"/>
                    <constraint firstItem="fqp-M0-M8j" firstAttribute="height" secondItem="axv-xo-Xx3" secondAttribute="height" multiplier="1/2" id="Xza-et-mbc"/>
                    <constraint firstItem="fqp-M0-M8j" firstAttribute="top" secondItem="axv-xo-Xx3" secondAttribute="top" id="bE5-8n-Wxi"/>
                    <constraint firstItem="axv-xo-Xx3" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="mDO-4f-Zkp"/>
                    <constraint firstAttribute="bottom" secondItem="KpQ-t8-FCi" secondAttribute="bottom" id="oIp-dh-cAQ"/>
                    <constraint firstItem="axv-xo-Xx3" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="ypr-r6-3Aq"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="iconBtn" destination="axv-xo-Xx3" id="aqW-7R-QrA"/>
                <outlet property="nameL" destination="fqp-M0-M8j" id="0Sz-Wg-Izl"/>
                <outlet property="signatureL" destination="NMb-Se-JUx" id="iJY-KV-VBs"/>
            </connections>
            <point key="canvasLocation" x="85.599999999999994" y="60.26986506746627"/>
        </tableViewCell>
    </objects>
</document>
