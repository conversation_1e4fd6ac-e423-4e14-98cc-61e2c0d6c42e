//
//  MessageHeaderV.m
//  iphoneLive
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/13.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MessageHeaderV.h"

@implementation MessageHeaderV

-(void)awakeFromNib
{
    [super awakeFromNib];
    [_fansBtn setTitle:YZMsg(@"粉丝") forState:0];
    [_zanBtn setTitle:YZMsg(@"赞") forState:0];
    [_aiTeBtn setTitle:YZMsg(@"@我的") forState:0];
    [_commentBtn setTitle:YZMsg(@"评论") forState:0];
}


- (IBAction)clickFansBtn:(UIButton *)sender {
    if (self.msgEvent) {
        self.msgEvent(@"粉丝");//======>不需要翻译
    }
}

- (IBAction)clickZanBtn:(UIButton *)sender {
    if (self.msgEvent) {
        self.msgEvent(@"赞");
    }
}

- (IBAction)clickAiTeBtn:(UIButton *)sender {
    if (self.msgEvent) {
        self.msgEvent(@"@我的");
    }
}

- (IBAction)clickCommentBtn:(UIButton *)sender {
    if (self.msgEvent) {
        self.msgEvent(@"评论");
    }
}
-(void)clearRedPoint;{
    _fansPoint.hidden = YES;
    _zanPoint.hidden = YES;
    _atPoint.hidden = YES;
    _comPoint.hidden = YES;
}
@end
