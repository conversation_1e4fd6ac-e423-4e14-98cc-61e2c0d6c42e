<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="90" id="KGk-i7-Jjw" customClass="MessageListCell">
            <rect key="frame" x="0.0" y="0.0" width="414" height="90"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="414" height="90"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="G0z-2c-If0">
                        <rect key="frame" x="12" y="0.0" width="390" height="90"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                    <imageView contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="msg_sysytemNotifa.png" translatesAutoresizingMaskIntoConstraints="NO" id="QcF-gR-HA3" userLabel="头像">
                        <rect key="frame" x="20" y="24" width="42" height="42"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="QcF-gR-HA3" secondAttribute="height" multiplier="1:1" id="6ar-9j-8ad"/>
                            <constraint firstAttribute="width" constant="42" id="fhG-DC-rkK"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="21"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <imageView hidden="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="msg_gov.png" translatesAutoresizingMaskIntoConstraints="NO" id="SLj-mZ-e6p" userLabel="标识">
                        <rect key="frame" x="36" y="58" width="26" height="13"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="26" id="Fh5-gj-iDr"/>
                            <constraint firstAttribute="width" secondItem="SLj-mZ-e6p" secondAttribute="height" multiplier="26:13" id="qeJ-Vv-OEw"/>
                        </constraints>
                    </imageView>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oWp-Oj-T1v" userLabel="头像点击事件(后期加的)">
                        <rect key="frame" x="10" y="0.0" width="62" height="90"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <connections>
                            <action selector="clickIconBtn:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="Hek-bM-O7f"/>
                        </connections>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jlm-su-NfL" userLabel="名字">
                        <rect key="frame" x="77" y="24" width="42.5" height="21"/>
                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="17"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bTg-1F-S79" userLabel="详情">
                        <rect key="frame" x="77" y="47" width="248.5" height="21"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" red="0.58823529411764708" green="0.58823529411764708" blue="0.58823529411764708" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0QO-f7-8EA" userLabel="时间">
                        <rect key="frame" x="361" y="24" width="33" height="21"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.5490196078431373" green="0.5490196078431373" blue="0.5490196078431373" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="H6V-BT-xBl" userLabel="红点">
                        <rect key="frame" x="376" y="50" width="18" height="18"/>
                        <color key="backgroundColor" red="0.85490196078431369" green="0.0" blue="0.24313725490196078" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="18" identifier="1" id="X7m-Sx-TOX"/>
                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="18" id="zyQ-YQ-kpM"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="9"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mRZ-xq-dXF" userLabel="线">
                        <rect key="frame" x="62" y="89" width="324" height="1"/>
                        <color key="backgroundColor" red="0.95686274509803915" green="0.96078431372549022" blue="0.96470588235294119" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" identifier="1" id="EhB-Ge-7AX"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="AMF-ZE-dPb">
                        <rect key="frame" x="124.5" y="27" width="18" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="SsO-JM-Fba"/>
                            <constraint firstAttribute="width" constant="18" id="jNo-Xq-gXi"/>
                        </constraints>
                    </imageView>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="rlh-CX-XcT">
                        <rect key="frame" x="145.5" y="27" width="30" height="15"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="rlh-CX-XcT" secondAttribute="height" multiplier="2" id="MF8-r2-ya9"/>
                        </constraints>
                    </imageView>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="私聊" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7p6-lT-Kjt">
                        <rect key="frame" x="334" y="30" width="60" height="30"/>
                        <color key="backgroundColor" red="0.9882352941176471" green="0.85882352941176465" blue="0.19607843137254902" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="30" id="1Tr-Aq-pu6"/>
                            <constraint firstAttribute="width" constant="60" id="rUM-k3-ea7"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="15"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </label>
                </subviews>
                <color key="backgroundColor" red="0.96862745098039216" green="0.97254901960784312" blue="0.98039215686274506" alpha="1" colorSpace="calibratedRGB"/>
                <constraints>
                    <constraint firstItem="jlm-su-NfL" firstAttribute="height" secondItem="QcF-gR-HA3" secondAttribute="height" multiplier="1/2" id="0Ds-DV-Qik"/>
                    <constraint firstAttribute="bottom" secondItem="G0z-2c-If0" secondAttribute="bottom" id="24p-l5-zXd"/>
                    <constraint firstItem="QcF-gR-HA3" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="3Cr-vq-6ur"/>
                    <constraint firstItem="bTg-1F-S79" firstAttribute="leading" secondItem="jlm-su-NfL" secondAttribute="leading" id="7RZ-V3-HKr"/>
                    <constraint firstItem="oWp-Oj-T1v" firstAttribute="width" secondItem="QcF-gR-HA3" secondAttribute="width" constant="20" id="97U-Il-IIW"/>
                    <constraint firstAttribute="bottom" secondItem="mRZ-xq-dXF" secondAttribute="bottom" id="9kW-oW-TSK"/>
                    <constraint firstItem="G0z-2c-If0" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="12" id="BJQ-Ia-nbA"/>
                    <constraint firstItem="jlm-su-NfL" firstAttribute="top" secondItem="QcF-gR-HA3" secondAttribute="top" id="Bi8-s9-e7r"/>
                    <constraint firstAttribute="trailing" secondItem="0QO-f7-8EA" secondAttribute="trailing" constant="20" id="Bwl-ze-ccK"/>
                    <constraint firstItem="rlh-CX-XcT" firstAttribute="leading" secondItem="AMF-ZE-dPb" secondAttribute="trailing" constant="3" id="Ez5-zW-vgw"/>
                    <constraint firstItem="QcF-gR-HA3" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="GQ4-vQ-Ifu"/>
                    <constraint firstItem="rlh-CX-XcT" firstAttribute="height" secondItem="AMF-ZE-dPb" secondAttribute="height" id="IEw-HT-Kkk"/>
                    <constraint firstItem="oWp-Oj-T1v" firstAttribute="height" secondItem="H2p-sc-9uM" secondAttribute="height" id="Mlx-UH-Yaa"/>
                    <constraint firstItem="jlm-su-NfL" firstAttribute="width" relation="lessThanOrEqual" secondItem="H2p-sc-9uM" secondAttribute="width" multiplier="0.6" id="Oxy-Ak-PPc"/>
                    <constraint firstAttribute="trailing" secondItem="mRZ-xq-dXF" secondAttribute="trailing" constant="28" id="R1L-bO-9CQ"/>
                    <constraint firstItem="H6V-BT-xBl" firstAttribute="trailing" secondItem="0QO-f7-8EA" secondAttribute="trailing" id="SUf-YS-sIc"/>
                    <constraint firstItem="jlm-su-NfL" firstAttribute="leading" secondItem="QcF-gR-HA3" secondAttribute="trailing" constant="15" id="Utj-R0-8xH"/>
                    <constraint firstItem="SLj-mZ-e6p" firstAttribute="top" secondItem="QcF-gR-HA3" secondAttribute="bottom" constant="-8" id="V86-Wd-en6"/>
                    <constraint firstItem="0QO-f7-8EA" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="rlh-CX-XcT" secondAttribute="trailing" constant="5" id="YJH-Af-jCz"/>
                    <constraint firstItem="oWp-Oj-T1v" firstAttribute="centerX" secondItem="QcF-gR-HA3" secondAttribute="centerX" id="YJj-L3-Dm1"/>
                    <constraint firstItem="bTg-1F-S79" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" multiplier="0.6" id="a2Z-1a-NQ1"/>
                    <constraint firstItem="bTg-1F-S79" firstAttribute="top" secondItem="jlm-su-NfL" secondAttribute="bottom" constant="2" id="aLq-2f-xo2"/>
                    <constraint firstItem="0QO-f7-8EA" firstAttribute="height" secondItem="jlm-su-NfL" secondAttribute="height" id="bhN-ud-kTJ"/>
                    <constraint firstItem="AMF-ZE-dPb" firstAttribute="leading" secondItem="jlm-su-NfL" secondAttribute="trailing" constant="5" id="c4e-Da-1Zm"/>
                    <constraint firstItem="oWp-Oj-T1v" firstAttribute="centerY" secondItem="QcF-gR-HA3" secondAttribute="centerY" id="cGX-ae-HG9"/>
                    <constraint firstItem="7p6-lT-Kjt" firstAttribute="trailing" secondItem="0QO-f7-8EA" secondAttribute="trailing" id="e8a-AS-Wno"/>
                    <constraint firstItem="bTg-1F-S79" firstAttribute="height" secondItem="jlm-su-NfL" secondAttribute="height" id="g8P-6I-Qas"/>
                    <constraint firstItem="mRZ-xq-dXF" firstAttribute="leading" secondItem="QcF-gR-HA3" secondAttribute="trailing" id="hb6-zJ-fKG"/>
                    <constraint firstAttribute="trailing" secondItem="G0z-2c-If0" secondAttribute="trailing" constant="12" id="jyQ-38-1o7"/>
                    <constraint firstItem="7p6-lT-Kjt" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="mE8-Xr-0ss"/>
                    <constraint firstItem="0QO-f7-8EA" firstAttribute="top" secondItem="jlm-su-NfL" secondAttribute="top" id="nvH-w0-JQk"/>
                    <constraint firstItem="rlh-CX-XcT" firstAttribute="centerY" secondItem="AMF-ZE-dPb" secondAttribute="centerY" id="pu6-UX-0ke"/>
                    <constraint firstItem="AMF-ZE-dPb" firstAttribute="centerY" secondItem="jlm-su-NfL" secondAttribute="centerY" id="scz-wg-cSg"/>
                    <constraint firstItem="G0z-2c-If0" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="spu-0d-OVf"/>
                    <constraint firstItem="SLj-mZ-e6p" firstAttribute="trailing" secondItem="QcF-gR-HA3" secondAttribute="trailing" id="yhw-jn-nqQ"/>
                    <constraint firstItem="H6V-BT-xBl" firstAttribute="top" secondItem="0QO-f7-8EA" secondAttribute="bottom" constant="5" id="zkq-pp-SoQ"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="backView" destination="G0z-2c-If0" id="5O8-1v-yje"/>
                <outlet property="detailL" destination="bTg-1F-S79" id="RcN-5D-vxF"/>
                <outlet property="iconIV" destination="QcF-gR-HA3" id="tsP-xg-XlU"/>
                <outlet property="iconTag" destination="SLj-mZ-e6p" id="2Om-Hz-cfD"/>
                <outlet property="levelI" destination="rlh-CX-XcT" id="7rh-5D-IEZ"/>
                <outlet property="nameL" destination="jlm-su-NfL" id="vuH-2u-Exb"/>
                <outlet property="redPoint" destination="H6V-BT-xBl" id="rqF-vS-hUn"/>
                <outlet property="redPointWidth" destination="zyQ-YQ-kpM" id="gVn-JR-Eg1"/>
                <outlet property="sexI" destination="AMF-ZE-dPb" id="lzm-4u-FfD"/>
                <outlet property="siliaoL" destination="7p6-lT-Kjt" id="NiA-LM-Cao"/>
                <outlet property="timeL" destination="0QO-f7-8EA" id="6jp-n9-ZMe"/>
            </connections>
            <point key="canvasLocation" x="179.19999999999999" y="-9.8950524737631191"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="msg_gov.png" width="26" height="13"/>
        <image name="msg_sysytemNotifa.png" width="120" height="120"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
