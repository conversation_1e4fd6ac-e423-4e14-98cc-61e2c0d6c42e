<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="MessageHeaderV">
            <rect key="frame" x="0.0" y="0.0" width="430" height="108"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HXD-TO-KNn" userLabel="背景">
                    <rect key="frame" x="12" y="10" width="406" height="88"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="12"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LZp-Sy-dKC" userLabel="粉丝">
                    <rect key="frame" x="3" y="10" width="101.5" height="88"/>
                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                    <state key="normal" title="粉丝" image="msg_fans.png">
                        <color key="titleColor" red="0.066666666669999999" green="0.050980392159999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="calibratedRGB"/>
                    </state>
                    <connections>
                        <action selector="clickFansBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="xMg-aN-2gO"/>
                    </connections>
                </button>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="S9J-PD-HEA" userLabel="fansPoint">
                    <rect key="frame" x="68" y="15" width="18" height="18"/>
                    <color key="backgroundColor" red="1" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="18" id="knY-wl-wFk"/>
                        <constraint firstAttribute="width" constant="18" id="sdm-QU-Sns"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="9"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XpQ-Wo-Liq" userLabel="赞">
                    <rect key="frame" x="110.5" y="10" width="101.5" height="88"/>
                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                    <state key="normal" title="赞" image="msg_zan.png">
                        <color key="titleColor" red="0.066666666669999999" green="0.050980392159999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="calibratedRGB"/>
                    </state>
                    <connections>
                        <action selector="clickZanBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="Oqq-20-Eng"/>
                    </connections>
                </button>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6GY-OS-VGg" userLabel="zanPoint">
                    <rect key="frame" x="175.5" y="15" width="18" height="18"/>
                    <color key="backgroundColor" red="1" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="18" id="sXK-m7-r2S"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="9"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gjr-AQ-ysS" userLabel="@我的">
                    <rect key="frame" x="218" y="10" width="101.5" height="88"/>
                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                    <state key="normal" title="@我的" image="msg_linkme.png">
                        <color key="titleColor" red="0.066666666669999999" green="0.050980392159999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="calibratedRGB"/>
                    </state>
                    <connections>
                        <action selector="clickAiTeBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="EVx-Hi-N7r"/>
                    </connections>
                </button>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ifT-hO-l37" userLabel="atPoint">
                    <rect key="frame" x="283" y="15" width="18" height="18"/>
                    <color key="backgroundColor" red="1" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="18" id="Szj-8j-S3X"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="9"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xfa-Mf-xAs" userLabel="评论">
                    <rect key="frame" x="325.5" y="10" width="101.5" height="88"/>
                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                    <state key="normal" title="评论" image="msg_comment.png">
                        <color key="titleColor" red="0.066666666669999999" green="0.050980392159999999" blue="0.14117647059999999" alpha="1" colorSpace="custom" customColorSpace="calibratedRGB"/>
                    </state>
                    <connections>
                        <action selector="clickCommentBtn:" destination="iN0-l3-epB" eventType="touchUpInside" id="OZb-Tz-9vw"/>
                    </connections>
                </button>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kH0-mB-S3a" userLabel="comPoint">
                    <rect key="frame" x="390.5" y="15" width="18" height="18"/>
                    <color key="backgroundColor" red="1" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="18" id="o4n-mb-3gc"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="9"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </label>
            </subviews>
            <color key="backgroundColor" red="0.96862745098039216" green="0.97254901960784312" blue="0.98039215686274506" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="xfa-Mf-xAs" firstAttribute="height" secondItem="LZp-Sy-dKC" secondAttribute="height" id="11G-OD-KJx"/>
                <constraint firstItem="kH0-mB-S3a" firstAttribute="centerX" secondItem="xfa-Mf-xAs" secondAttribute="centerX" priority="750" constant="23" id="1MB-vb-IHh"/>
                <constraint firstItem="LZp-Sy-dKC" firstAttribute="centerX" secondItem="HXD-TO-KNn" secondAttribute="centerX" multiplier="0.25" id="3YV-2h-cmH"/>
                <constraint firstItem="HXD-TO-KNn" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="5HT-ws-9mv"/>
                <constraint firstAttribute="bottom" secondItem="HXD-TO-KNn" secondAttribute="bottom" constant="10" id="5Vz-tL-YO6"/>
                <constraint firstItem="LZp-Sy-dKC" firstAttribute="height" secondItem="HXD-TO-KNn" secondAttribute="height" id="87S-Db-jLM"/>
                <constraint firstItem="HXD-TO-KNn" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="10" id="9Gn-5v-jRn"/>
                <constraint firstItem="HXD-TO-KNn" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="12" id="C2D-3h-B5Z"/>
                <constraint firstItem="6GY-OS-VGg" firstAttribute="centerY" secondItem="S9J-PD-HEA" secondAttribute="centerY" id="Cbm-1X-RrY"/>
                <constraint firstItem="XpQ-Wo-Liq" firstAttribute="centerY" secondItem="LZp-Sy-dKC" secondAttribute="centerY" id="HVI-IK-pHj"/>
                <constraint firstItem="gjr-AQ-ysS" firstAttribute="width" secondItem="LZp-Sy-dKC" secondAttribute="width" id="LWz-H6-7iC"/>
                <constraint firstItem="LZp-Sy-dKC" firstAttribute="centerY" secondItem="HXD-TO-KNn" secondAttribute="centerY" id="RGP-eJ-Iwh"/>
                <constraint firstItem="XpQ-Wo-Liq" firstAttribute="centerX" secondItem="HXD-TO-KNn" secondAttribute="centerX" multiplier="0.75" id="Rdq-ax-iK2"/>
                <constraint firstItem="xfa-Mf-xAs" firstAttribute="width" secondItem="LZp-Sy-dKC" secondAttribute="width" id="Rft-y2-CMO"/>
                <constraint firstItem="gjr-AQ-ysS" firstAttribute="centerX" secondItem="HXD-TO-KNn" secondAttribute="centerX" multiplier="1.25" id="WLe-mf-2qu"/>
                <constraint firstItem="kH0-mB-S3a" firstAttribute="centerY" secondItem="S9J-PD-HEA" secondAttribute="centerY" id="XUg-wD-Aih"/>
                <constraint firstItem="ifT-hO-l37" firstAttribute="centerY" secondItem="S9J-PD-HEA" secondAttribute="centerY" id="XX8-B4-Iz6"/>
                <constraint firstItem="xfa-Mf-xAs" firstAttribute="centerY" secondItem="LZp-Sy-dKC" secondAttribute="centerY" id="XYV-Uh-eZl"/>
                <constraint firstItem="XpQ-Wo-Liq" firstAttribute="width" secondItem="LZp-Sy-dKC" secondAttribute="width" id="aia-Bw-HNL"/>
                <constraint firstItem="gjr-AQ-ysS" firstAttribute="height" secondItem="LZp-Sy-dKC" secondAttribute="height" id="awd-ir-Kmc"/>
                <constraint firstItem="S9J-PD-HEA" firstAttribute="centerX" secondItem="LZp-Sy-dKC" secondAttribute="centerX" priority="750" constant="23" id="fcB-Hp-YIl"/>
                <constraint firstAttribute="trailing" secondItem="HXD-TO-KNn" secondAttribute="trailing" constant="12" id="flD-hW-nUW"/>
                <constraint firstItem="kH0-mB-S3a" firstAttribute="height" secondItem="S9J-PD-HEA" secondAttribute="height" id="go7-l9-OMC"/>
                <constraint firstItem="6GY-OS-VGg" firstAttribute="height" secondItem="S9J-PD-HEA" secondAttribute="height" id="kCs-nd-95L"/>
                <constraint firstItem="XpQ-Wo-Liq" firstAttribute="height" secondItem="LZp-Sy-dKC" secondAttribute="height" id="ltt-Jh-rah"/>
                <constraint firstItem="xfa-Mf-xAs" firstAttribute="centerX" secondItem="HXD-TO-KNn" secondAttribute="centerX" multiplier="1.75" id="oUn-xV-Aon"/>
                <constraint firstItem="ifT-hO-l37" firstAttribute="centerX" secondItem="gjr-AQ-ysS" secondAttribute="centerX" priority="750" constant="23" id="p87-bZ-LTI"/>
                <constraint firstItem="6GY-OS-VGg" firstAttribute="centerX" secondItem="XpQ-Wo-Liq" secondAttribute="centerX" priority="750" constant="23" id="s8L-Hb-WWT"/>
                <constraint firstItem="S9J-PD-HEA" firstAttribute="centerY" secondItem="LZp-Sy-dKC" secondAttribute="centerY" constant="-30" id="ssX-sG-DML"/>
                <constraint firstItem="gjr-AQ-ysS" firstAttribute="centerY" secondItem="LZp-Sy-dKC" secondAttribute="centerY" id="vyG-wO-veY"/>
                <constraint firstItem="ifT-hO-l37" firstAttribute="height" secondItem="S9J-PD-HEA" secondAttribute="height" id="wql-6x-qdF"/>
                <constraint firstItem="LZp-Sy-dKC" firstAttribute="width" secondItem="HXD-TO-KNn" secondAttribute="width" multiplier="1/4" id="xIU-OX-4oA"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="aiTeBtn" destination="gjr-AQ-ysS" id="YjA-AU-1dq"/>
                <outlet property="atPoint" destination="ifT-hO-l37" id="Cc3-Al-2QI"/>
                <outlet property="atPointWidth" destination="Szj-8j-S3X" id="jV7-bY-DO8"/>
                <outlet property="comPoint" destination="kH0-mB-S3a" id="er8-Xv-cZj"/>
                <outlet property="comPointWidth" destination="o4n-mb-3gc" id="aQi-vk-1je"/>
                <outlet property="commentBtn" destination="xfa-Mf-xAs" id="7jg-Dd-XgU"/>
                <outlet property="fanPointWidth" destination="sdm-QU-Sns" id="kxL-3s-IP2"/>
                <outlet property="fansBtn" destination="LZp-Sy-dKC" id="wN2-J9-qDy"/>
                <outlet property="fansPoint" destination="S9J-PD-HEA" id="I1Q-YQ-wyI"/>
                <outlet property="headerBgV" destination="HXD-TO-KNn" id="3pe-Im-yUU"/>
                <outlet property="zanBtn" destination="XpQ-Wo-Liq" id="ach-aN-5cC"/>
                <outlet property="zanPoint" destination="6GY-OS-VGg" id="j2D-D2-llq"/>
                <outlet property="zanPointWidth" destination="sXK-m7-r2S" id="8CP-U0-f4K"/>
            </connections>
            <point key="canvasLocation" x="260.80000000000001" y="13.493253373313344"/>
        </view>
    </objects>
    <resources>
        <image name="msg_comment.png" width="46" height="46"/>
        <image name="msg_fans.png" width="46" height="46"/>
        <image name="msg_linkme.png" width="46" height="46"/>
        <image name="msg_zan.png" width="46" height="46"/>
    </resources>
</document>
