//
//  MessageListCell.m
//  iphoneLive
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/13.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MessageListCell.h"
#import "MessageListModel.h"
#import "Live-Swift.h"
@interface MessageListCell()

@end
@implementation MessageListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    
    self.iconTag.image = [UIImage imageNamed:getImagename(@"msg_gov")];

    
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self.backView layoutIfNeeded];
    [self.backView setViewCornerWithPosition:self.rectCorner corner:12.0];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (IBAction)clickIconBtn:(UIButton *)sender {
    if (self.iconEvent) {
        self.iconEvent(@"头像");
    }
}

+(MessageListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath {
    MessageListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MessageListCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"MessageListCell" owner:nil options:nil]objectAtIndex:0];
        cell.siliaoL.backgroundColor = normalColors;
        cell.siliaoL.text = YZMsg(@"私聊");
//        cell.backgroundColor = CellRow_Cor;
    }
    return cell;
}

-(void)setModel:(MessageListModel *)model {
    _model = model;
    [_iconIV sd_setImageWithURL:[NSURL URLWithString:_model.iconStr]];
    //官方标识
    if ([_model.uidStr isEqual:@"dsp_admin_1"] || [_model.uidStr isEqual:@"dsp_admin_2"]) {
        _iconTag.hidden = NO;
    }else{
        _iconTag.hidden = YES;
    }
    if ([_model.uidStr isEqual:@"goodsorder_admin"]) {
        _sexI.hidden = YES;
        _levelI.hidden = YES;
        
        NSDictionary *orderDic = [self dictionaryWithJsonString:_model.contentStr];
        NSLog(@"sssso:%@",orderDic);
        NSString *language = [PublicObj getCurrentLanguage];
        if ([language containsString:@"en"]){
            _model.contentStr = minstr([orderDic valueForKey:@"en"]);// currLan = ZH_CN;
        }else{
            _model.contentStr = minstr([orderDic valueForKey:@"zh-cn"]);// currLan = ZH_CN;
        }
    }
    _nameL.text = _model.unameStr;
    NSString *defaultStr = _model.lastMsgStr;//[NSString stringWithFormat:@"欢迎入驻%@!",app_name];
    if ([_model.contentStr isEqual:@"[位置]"] ||[_model.contentStr isEqual:@"[语音]"]||[_model.contentStr isEqual:@"[商品]"]||[_model.contentStr isEqual:@"[图片]"]) {
        _model.contentStr = YZMsg(_model.contentStr);
    }
    _detailL.text = [PublicObj checkNull:_model.contentStr]?defaultStr:_model.contentStr;
//rrrrrrrrrrrrr
//    if ([[[JCHATSendMsgManager ins] draftStringWithConversation:_model.conversation] isEqualToString:@""]) {
////        _detailL.text = _model.contentStr;
//
//    } else {
//        NSMutableAttributedString *attriString = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@ %@",YZMsg(@"[草稿]"),[[JCHATSendMsgManager ins] draftStringWithConversation:_model.conversation]]];
//        [attriString addAttribute:NSForegroundColorAttributeName
//                            value:[UIColor redColor]
//                            range:NSMakeRange(0, 4)];
//
//        _detailL.attributedText = attriString;
//    }
    
    _timeL.text = _model.timeStr;
    int num = [_model.unReadStr intValue];
    if (num > 0) {
        _redPoint.hidden = NO;
        _redPoint.text = [NSString stringWithFormat:@"%d",num];
    }else{
        _redPoint.hidden = YES;
    }
    //个位数显示圆点，两位及以上显示椭圆
    if (num < 10) {
        _redPointWidth.constant = _redPoint.frame.size.height;
    }else{
        _redPointWidth.constant = _redPoint.frame.size.width + 10;
    }
    if ([_model.sex isEqual:@"1"]) {
        _sexI.image= [ UIImage imageNamed:@"sex_man"];
    }else if ([_model.sex isEqual:@"2"]){
        _sexI.image = [UIImage imageNamed:@"sex_woman"];
    }else{
        _sexI.image= [ UIImage imageNamed:@"sex_woman"];
    }
    NSDictionary *levelDic = [common getUserLevelMessage:_model.level];
    [_levelI sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic valueForKey:@"thumb"])]];

    
}
- (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString {
    if (jsonString == nil) { return nil; }
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err; NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:&err];
    if(err) {
        NSLog(@"json解析失败：%@",err);
        return nil;
        
    } return dic;
    
}

@end
