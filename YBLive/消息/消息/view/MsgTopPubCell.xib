<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="98" id="KGk-i7-Jjw" customClass="MsgTopPubCell">
            <rect key="frame" x="0.0" y="0.0" width="354" height="98"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="354" height="98"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button opaque="NO" clearsContextBeforeDrawing="NO" contentMode="scaleAspectFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JFD-ND-x63" userLabel="头像">
                        <rect key="frame" x="15" y="19" width="60" height="60"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="JFD-ND-x63" secondAttribute="height" multiplier="1:1" id="59v-Qb-crw"/>
                            <constraint firstAttribute="width" constant="60" id="fiC-PQ-8Yv"/>
                        </constraints>
                        <connections>
                            <action selector="clickIconBtn:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="Aes-wn-vbg"/>
                        </connections>
                    </button>
                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleAspectFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7rf-EW-wsx" userLabel="视频封面">
                        <rect key="frame" x="249" y="16.5" width="90" height="65"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="65" id="2bo-ch-1jr"/>
                            <constraint firstAttribute="width" constant="90" id="bN2-Gi-qSY"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                        <connections>
                            <action selector="clickCoverBtn:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="4qL-co-SAF"/>
                        </connections>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FIC-zs-iqr" userLabel="内容">
                        <rect key="frame" x="90" y="16.5" width="144" height="30"/>
                        <constraints>
                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="30" id="vya-4z-DGh"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                        <color key="textColor" red="0.5490196078431373" green="0.5490196078431373" blue="0.5490196078431373" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yhb-jD-y8d" userLabel="时间">
                        <rect key="frame" x="90" y="46.5" width="36" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="G2i-fg-7My"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" red="0.54901960780000003" green="0.54901960780000003" blue="0.54901960780000003" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PyT-SG-oey" userLabel="线">
                        <rect key="frame" x="15" y="97" width="324" height="1"/>
                        <color key="backgroundColor" red="0.96078431372549022" green="0.96470588235294119" blue="0.96862745098039216" alpha="0.059999999999999998" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="xq0-Qa-7La"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="7rf-EW-wsx" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="1E1-dI-phc"/>
                    <constraint firstItem="yhb-jD-y8d" firstAttribute="top" secondItem="FIC-zs-iqr" secondAttribute="bottom" id="CUD-8E-hx4"/>
                    <constraint firstItem="FIC-zs-iqr" firstAttribute="top" secondItem="7rf-EW-wsx" secondAttribute="top" id="CgY-Er-JmR"/>
                    <constraint firstItem="JFD-ND-x63" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="GjA-8g-dGn"/>
                    <constraint firstItem="JFD-ND-x63" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="LJY-uF-LM0"/>
                    <constraint firstItem="FIC-zs-iqr" firstAttribute="trailing" secondItem="7rf-EW-wsx" secondAttribute="leading" constant="-15" id="Uhd-yL-inc"/>
                    <constraint firstAttribute="trailing" secondItem="7rf-EW-wsx" secondAttribute="trailing" constant="15" id="clC-mr-hOk"/>
                    <constraint firstItem="PyT-SG-oey" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" constant="-30" id="nHt-UY-osU"/>
                    <constraint firstItem="PyT-SG-oey" firstAttribute="centerX" secondItem="H2p-sc-9uM" secondAttribute="centerX" id="oqg-QG-BWI"/>
                    <constraint firstItem="FIC-zs-iqr" firstAttribute="leading" secondItem="JFD-ND-x63" secondAttribute="trailing" constant="15" id="vVb-yn-vZm"/>
                    <constraint firstItem="yhb-jD-y8d" firstAttribute="leading" secondItem="FIC-zs-iqr" secondAttribute="leading" id="xC8-QN-8Gv"/>
                    <constraint firstAttribute="bottom" secondItem="PyT-SG-oey" secondAttribute="bottom" id="yeQ-As-9WY"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="contentL" destination="FIC-zs-iqr" id="zEw-sS-KhT"/>
                <outlet property="coverBtn" destination="7rf-EW-wsx" id="hcb-e5-f2w"/>
                <outlet property="iconBtn" destination="JFD-ND-x63" id="oHk-9X-vDf"/>
                <outlet property="timeL" destination="yhb-jD-y8d" id="nhE-FM-6Y1"/>
            </connections>
            <point key="canvasLocation" x="45" y="121"/>
        </tableViewCell>
    </objects>
</document>
