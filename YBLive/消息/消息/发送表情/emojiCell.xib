<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14113" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="emojiCell">
            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="7BN-Ar-Tvs">
                        <rect key="frame" x="10" y="10" width="30" height="30"/>
                    </imageView>
                </subviews>
            </view>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="7BN-Ar-Tvs" secondAttribute="trailing" constant="10" id="ci6-J8-qyX"/>
                <constraint firstAttribute="bottom" secondItem="7BN-Ar-Tvs" secondAttribute="bottom" constant="10" id="lhb-7e-QOU"/>
                <constraint firstItem="7BN-Ar-Tvs" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="10" id="nqa-Fl-QMI"/>
                <constraint firstItem="7BN-Ar-Tvs" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="pI5-3Y-38q"/>
            </constraints>
            <connections>
                <outlet property="emojiImgView" destination="7BN-Ar-Tvs" id="QdB-GF-x5e"/>
            </connections>
            <point key="canvasLocation" x="27" y="50"/>
        </collectionViewCell>
    </objects>
</document>
