//
//  OrderMessageModel.h
//  YBLive
//
//  Created by ybRRR on 2020/3/31.
//  Copyright © 2020 cat. All rights reserved.
//

#import <Foundation/Foundation.h>
#define __TEXT_FONT__ [UIFont systemFontOfSize:16]
#define __TIME_FONT__ [UIFont systemFontOfSize:10]
#define __ICON_WIDTH__ 40
//内边距
#define __EDGE_W__ 15

NS_ASSUME_NONNULL_BEGIN

@interface OrderMessageModel : NSObject
//应该展示的数据
@property(nonatomic,copy)NSString *time;
@property(nonatomic,copy)NSString *text;
@property(nonatomic,copy)NSString *type;
@property(nonatomic,copy)NSString *icon;
@property(nonatomic,copy)NSString *orderid;
@property(nonatomic,copy)NSString *status;
@property(nonatomic,copy)NSString *is_commission;

//应该展示的坐标
@property(nonatomic,assign)CGRect timeR;
@property(nonatomic,assign)CGRect textR;
@property(nonatomic,assign)CGRect iconR;
@property(nonatomic,assign)CGFloat rowH;
-(void)setMessageFrame:(OrderMessageModel *)upMessage;
-(instancetype)initWithDic:(NSDictionary *)subdic;
+(instancetype)messageWithDic:(NSDictionary *)subdic;

@end

NS_ASSUME_NONNULL_END
