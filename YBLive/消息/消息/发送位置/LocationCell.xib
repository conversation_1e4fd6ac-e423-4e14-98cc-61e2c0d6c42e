<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14313.18" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14283.14"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="173" id="KGk-i7-Jjw" customClass="LocationCell">
            <rect key="frame" x="0.0" y="0.0" width="354" height="88"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="354" height="87.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Tdr-Li-mFd" userLabel="地址">
                        <rect key="frame" x="15" y="5" width="299" height="44"/>
                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HIh-zt-Nff" userLabel="详情">
                        <rect key="frame" x="15" y="41" width="299" height="43.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.5490196078431373" green="0.5490196078431373" blue="0.5490196078431373" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="location_sel.png" translatesAutoresizingMaskIntoConstraints="NO" id="ajo-64-ggF" userLabel="选中">
                        <rect key="frame" x="319" y="34" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="D04-ow-fDe"/>
                            <constraint firstAttribute="width" secondItem="ajo-64-ggF" secondAttribute="height" multiplier="1:1" id="NJb-02-pfe"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PTS-4t-Zf6" userLabel="线">
                        <rect key="frame" x="15" y="86.5" width="324" height="0.5"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="0.059999999999999998" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="0.5" id="Q1P-L2-0uU"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="HIh-zt-Nff" firstAttribute="centerX" secondItem="Tdr-Li-mFd" secondAttribute="centerX" id="1WU-t5-exu"/>
                    <constraint firstItem="HIh-zt-Nff" firstAttribute="height" secondItem="Tdr-Li-mFd" secondAttribute="height" id="3pq-Vr-jJ2"/>
                    <constraint firstItem="PTS-4t-Zf6" firstAttribute="centerX" secondItem="H2p-sc-9uM" secondAttribute="centerX" id="5kh-YR-bUT"/>
                    <constraint firstItem="Tdr-Li-mFd" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="77k-o9-L8h"/>
                    <constraint firstItem="HIh-zt-Nff" firstAttribute="width" secondItem="Tdr-Li-mFd" secondAttribute="width" id="7hI-R3-hxe"/>
                    <constraint firstItem="Tdr-Li-mFd" firstAttribute="trailing" secondItem="ajo-64-ggF" secondAttribute="leading" constant="-5" id="Pon-y1-Cai"/>
                    <constraint firstItem="Tdr-Li-mFd" firstAttribute="height" secondItem="H2p-sc-9uM" secondAttribute="height" multiplier="1/2" id="Smb-w6-k60"/>
                    <constraint firstItem="HIh-zt-Nff" firstAttribute="top" secondItem="Tdr-Li-mFd" secondAttribute="bottom" constant="-8" id="Wwl-qX-ihe"/>
                    <constraint firstAttribute="bottom" secondItem="PTS-4t-Zf6" secondAttribute="bottom" constant="0.5" id="jhx-Uo-mH4"/>
                    <constraint firstItem="Tdr-Li-mFd" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="5" id="lF3-1O-kTA"/>
                    <constraint firstItem="PTS-4t-Zf6" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" constant="-30" id="p54-KU-gSB"/>
                </constraints>
            </tableViewCellContentView>
            <constraints>
                <constraint firstItem="ajo-64-ggF" firstAttribute="centerY" secondItem="KGk-i7-Jjw" secondAttribute="centerY" id="9us-kb-C1V"/>
                <constraint firstAttribute="trailing" secondItem="ajo-64-ggF" secondAttribute="trailing" constant="15" id="GZa-Th-OlM"/>
            </constraints>
            <connections>
                <outlet property="addressL" destination="Tdr-Li-mFd" id="E33-pR-7u8"/>
                <outlet property="falgIV" destination="ajo-64-ggF" id="O7k-bi-DpH"/>
                <outlet property="infoL" destination="HIh-zt-Nff" id="ZOF-EE-gwM"/>
            </connections>
            <point key="canvasLocation" x="17" y="35"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="location_sel.png" width="42" height="42"/>
    </resources>
</document>
