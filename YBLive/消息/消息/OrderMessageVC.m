//
//  OrderMessageVC.m
//  YBLive
//
//  Created by ybRRR on 2020/3/31.
//  Copyright © 2020 cat. All rights reserved.
//

#import "OrderMessageVC.h"
#import "OrderMessageModel.h"
#import "chatmessageCell.h"
#import "THeader.h"

@interface OrderMessageVC ()<UITableViewDelegate, UITableViewDataSource>
{
    int pageIndex;
}
@property (nonatomic, strong)UITableView *tableView;
@property(nonatomic,strong)NSMutableArray *allArray;
@property(nonatomic,strong)NSArray *models;

@end

@implementation OrderMessageVC
-(NSArray *)models{
    NSMutableArray *array = [NSMutableArray array];
    for (NSDictionary *dic in self.allArray) {
        OrderMessageModel *model = [OrderMessageModel messageWithDic:dic];
        [model setMessageFrame:[array lastObject]];
        [array addObject:model];
    }
    _models = array;
    return _models;
    
}
- (void)doReturn{
//    TIMConversation * conv = [[TIMManager sharedInstance] getConversation:TIM_C2C receiver:_conversation.convId];
//    [conv setReadMessage:nil succ:^{
//        NSLog(@"++++++++++++++++++++++++++");
//    } fail:^(int code, NSString *msg) {
//        NSLog(@"--------------------------");
//    }];
    [[YBImManager shareInstance]clearUnreadConvId:_conversation.convId sendNot:YES];
    [self.navigationController popViewControllerAnimated:YES];

}

-(void)requestData{
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Message.getShopOrderList"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"p":@(pageIndex)
                          };

    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [self.tableView.mj_header endRefreshing];
        [self.tableView.mj_footer endRefreshing];

        if ([code isEqual:@"0"]) {
            NSArray *infoArr = [data valueForKey:@"info"];
            if (pageIndex == 1) {
                [self.allArray removeAllObjects];
                if (infoArr.count < 1) {
                    [PublicView showImgNoData:self.tableView name:@"shop_无数据" text:@"暂无消息"];
                    [self.tableView reloadData];
                    return ;
                }else{
                    [PublicView hiddenImgNoData:self.tableView];
                }
            }
            [self.allArray addObjectsFromArray:infoArr];
            [self.tableView reloadData];

        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        [self.tableView .mj_header endRefreshing];
        [self.tableView .mj_footer endRefreshing];

    }];

}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"订单消息");
    self.allArray = [NSMutableArray array];
    pageIndex = 1;
    
    self.tableView = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:UITableViewStyleGrouped];
//    UITapGestureRecognizer* tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(Actiondo)];
//    [self.tableView addGestureRecognizer:tapGesture];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.contentOffset = CGPointMake(0, _window_height);
    //去掉分割线
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    //禁止选中
    self.tableView.allowsSelection = NO;
    self.tableView.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
        pageIndex = 1;
        [self requestData];
    }];
    self.tableView.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
        pageIndex ++;
        [self requestData];

    }];
    [self.view addSubview:self.tableView];
    
    [self requestData];
}

-(void)jumpLast
{
    NSUInteger sectionCount = [self.tableView numberOfSections];
    if (sectionCount) {
        NSUInteger rowCount = [self.tableView numberOfRowsInSection:0];
        if (rowCount) {
            NSUInteger ii[2] = {0, rowCount - 1};
            NSIndexPath* indexPath = [NSIndexPath indexPathWithIndexes:ii length:2];
            [self.tableView scrollToRowAtIndexPath:indexPath
                                  atScrollPosition:UITableViewScrollPositionBottom animated:NO];
        }
    }
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.models.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    chatmessageCell *cell = [chatmessageCell cellWithTableView:tableView];
    cell.ordermodel = self.models[indexPath.row];
    return cell;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    OrderMessageModel *model = self.models[indexPath.row];
    return model.rowH;
}
-(void)scrollViewWillBeginDragging:(UIScrollView *)scrollView
{
    [self.view endEditing:YES];
}

@end
