//
//  MessageListModel.m
//  iphoneLive
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/13.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "MessageListModel.h"
//#import "JCHATStringUtils.h"
//rrrrrrrrrrrrrrrrr

@implementation MessageListModel

- (instancetype)initWithDic:(NSDictionary *)dic {
    self = [super init];
    if (self) {
        
        _uidStr = [NSString stringWithFormat:@"%@",[dic valueForKey:@"id"]];
        _unameStr = [NSString stringWithFormat:@"%@",[dic valueForKey:@"user_nickname"]];
        _iconStr = [NSString stringWithFormat:@"%@",[dic valueForKey:@"avatar"]];
//        _conversation = [dic valueForKey:@"conversation"];
        _sex = minstr([dic valueForKey:@"sex"]);
        _level = minstr([dic valueForKey:@"level"]);
        _isAtt = minstr([dic valueForKey:@"utot"]);

//        _unReadStr = [NSString stringWithFormat:@"%@",_conversation.unreadCount];
//        if (_conversation.latestMessage.timestamp != nil ) {
//            double time = [_conversation.latestMessage.timestamp doubleValue];
//
////            NSDate* theDate = [NSDate dateWithTimeIntervalSince1970:time/1000];
////            NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
////            NSString * FORMAT_PAST_SHORT = @"MM-dd HH:mm";
////            [formatter setDateFormat:FORMAT_PAST_SHORT];
////            NSString *dateStr = [formatter stringFromDate:theDate];
////            _timeStr = dateStr;
//            _timeStr = [YBToolClass getFriendlyDateString:time forConversation:YES];
////            _timeStr = [JCHATStringUtils getChatTimeWithDateString:time];
//        } else {
//            _timeStr = @"";
//        }
//        _contentStr = _conversation.latestMessageContentText;
        
//         //消息撤回处理
//         if (_conversation.latestMessage && (_conversation.latestMessage.contentType == kJMSGContentTypePrompt)) {
//             JMSGMessage *latesMessage = _conversation.latestMessage;
//             _contentStr = YZMsg(@"你撤回了一条消息");
//             if (latesMessage.isReceived) {
//                 _contentStr = YZMsg(@"对方撤回了一条消息");
//             }
//         }
//
         _lastMsgStr = @"";
         if (([_uidStr isEqual:@"dsp_admin_1"] || [_uidStr isEqual:@"dsp_admin_2"]) && [dic valueForKey:@"last_msg"]) {
             _lastMsgStr = minstr([dic valueForKey:@"last_msg"]);
             if ([PublicObj checkNull:_timeStr]) {
                 _timeStr = minstr([dic valueForKey:@"last_time"]);
             }
         }

    }
    return self;
}

+(instancetype)modelWithDic:(NSDictionary *)dic {
    return [[self alloc]initWithDic:dic];
}
@end
