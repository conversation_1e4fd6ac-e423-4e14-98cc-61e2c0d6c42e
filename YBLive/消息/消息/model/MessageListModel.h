//
//  MessageListModel.h
//  iphoneLive
//
//  Created by <PERSON><PERSON><PERSON> on 2018/7/13.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TConversationCell.h"
@interface MessageListModel : NSObject

@property(nonatomic,strong)NSString *uidStr;
@property(nonatomic,strong)NSString *unameStr;
@property(nonatomic,strong)NSString *iconStr;
@property(nonatomic,strong)NSString *unReadStr;         //未读消息
@property(nonatomic,strong)NSString *timeStr;
@property(nonatomic,strong)NSString *contentStr;        //内容
@property(nonatomic,strong)NSString *sex;        //内容
@property(nonatomic,strong)NSString *level;        //内容
@property(nonatomic,strong)NSString *isAtt;        //内容
//@property(nonatomic,strong)TConversationCellData *
@property(nonatomic,strong)NSString *lastMsgStr;        //只有admin_1和admin_2这个值是存在的，其他用户为空值

- (instancetype)initWithDic:(NSDictionary *)dic;
+ (instancetype)modelWithDic:(NSDictionary *)dic;

@end
