#import "AppDelegate.h"//cn.weilianliveappstore
#import "YBUserInfoViewController.h"
/******shark sdk *********/
#import <ShareSDK/ShareSDK.h>
#import <ShareSDKConnector/ShareSDKConnector.h>
//腾讯开放平台（对应QQ和QQ空间）SDK头文件
#import <TencentOpenAPI/TencentOAuth.h>
#import <TencentOpenAPI/QQApiInterface.h>
//微信SDK头文件
//#import "LivePlay.h"
/******shark sdk  end*********/
//腾讯bug监控
#import <Bugly/Bugly.h>
#import <WXApi.h>
#import <AlipaySDK/AlipaySDK.h>
#import "PhoneLoginVC.h"
#import "ZYTabBarController.h"
#import <Twitter/Twitter.h>
#import "EBBannerView.h"

#import <QMapKit/QMapKit.h>
#import <QMapSearchKit/QMapSearchKit.h>
#import <UMCommon/UMCommon.h>
#import <UMAnalytics/MobClick.h>

#import "TXUGCBase.h"
#import <TXLiteAVSDK_Professional/TXLiveBase.h>
#import <TXLiteAVSDK_Professional/V2TXLivePremier.h>
#import "GuideViewController.h"
#import "JPVideoPlayerKit.h"
#import "BTAppSwitch.h"
#import "OpenInstallSDK.h"
#import "MessageListVC.h"
#import "TUIKit.h"
#import <TPNS-iOS/XGPush.h>
#import <TPNS-iOS/XGPushPrivate.h>
#import <ZFPlayer/ZFLandscapeRotationManager.h>

@import CoreLocation;
@import CoreTelephony;

@interface AppDelegate ()<CLLocationManagerDelegate,WXApiDelegate,OpenInstallDelegate,XGPushDelegate,V2TXLivePremierObserver>
{
//    CLLocationManager   *_lbsManager;
    NSTimer *youngTimer;
    int timeCount;

}
@property(nonatomic,strong)NSArray *scrollarrays;//轮播
@end
@implementation AppDelegate
{
    NSNotification * sendEmccBack;
}
//- (void)stopLbs {
//    [_lbsManager stopUpdatingHeading];
//    _lbsManager.delegate = nil;
//    _lbsManager = nil;
//}
//- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
//    if (status == kCLAuthorizationStatusRestricted || status == kCLAuthorizationStatusDenied) {
//        [self stopLbs];
//    } else {
//        [_lbsManager startUpdatingLocation];
//    }
//}
//- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error
//{
//    [self stopLbs];
//}
//- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations
//{
//    CLLocation *newLocatioin = locations[0];
//    liveCity *cityU = [cityDefault myProfile];
//    cityU.lat = [NSString stringWithFormat:@"%f",newLocatioin.coordinate.latitude];
//    cityU.lng = [NSString stringWithFormat:@"%f",newLocatioin.coordinate.longitude];
//    CLGeocoder *geocoder = [[CLGeocoder alloc] init];
//    [geocoder reverseGeocodeLocation:newLocatioin completionHandler:^(NSArray *placemarks, NSError *error) {
//        if (!error)
//        {
//            CLPlacemark *placeMark = placemarks[0];
//            NSString *city      = placeMark.locality;
//            NSString *addr = [NSString stringWithFormat:@"%@%@%@%@%@",placeMark.country,placeMark.administrativeArea,placeMark.locality,placeMark.subLocality,placeMark.thoroughfare];
//            LiveUser *user = [Config myProfile];
//            user.city = city;
//            cityU.addr = addr;
//            [Config updateProfile:user];
//            cityU.city = city;
//            [cityDefault saveProfile:cityU];
//        }
//    }];
//     [self stopLbs];
//}
/// 在这里写支持的旋转方向，为了防止横屏方向，应用启动时候界面变为横屏模式
- (UIInterfaceOrientationMask)application:(UIApplication *)application supportedInterfaceOrientationsForWindow:(UIWindow *)window {
    ZFInterfaceOrientationMask orientationMask = [ZFLandscapeRotationManager supportedInterfaceOrientationsForWindow:window];
    if (orientationMask != ZFInterfaceOrientationMaskUnknow) {
        return (UIInterfaceOrientationMask)orientationMask;
    }
    /// 这里是非播放器VC支持的方向
    return UIInterfaceOrientationMaskPortrait;

}

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    [self checkNetwork];

    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"isLiveing"];
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"isPlaying"];
    //三方信息
    [self addThirdKeys];

    if (![Config getOwnID]) {
        LiveUser *userInfo = [[LiveUser alloc] initWithDic:[PublicObj visitorDic]];
        [Config saveProfile:userInfo];
    }
    if ([[NSUserDefaults standardUserDefaults] objectForKey:@"voiceSwitch"] == nil) {
        [common saveSwitch:YES];
    }
    [[SDWebImageDownloader sharedDownloader] setValue:nil forHTTPHeaderField:@"Accept"];
    [[SDWebImageDownloader sharedDownloader] setValue:h5url forHTTPHeaderField:@"referer"];

    if(lagType){
        [[NSUserDefaults standardUserDefaults] setObject:lagType forKey:CurrentLanguage];
    }else{
        NSArray *languages = [NSLocale preferredLanguages];
        NSString *currentLanguage = [languages objectAtIndex:0];
        NSLog( @"啊啊啊啊啊啊啊啊啊%@" , currentLanguage);
//        if ([currentLanguage containsString:@"zh-Hans"] ||[currentLanguage containsString:@"zh-Hant"] ) {
        if ([currentLanguage containsString:@"en-"]) {
            [[NSUserDefaults standardUserDefaults] setObject:EN forKey:CurrentLanguage];
        }else{
            [[NSUserDefaults standardUserDefaults] setObject:ZH_CN forKey:CurrentLanguage];
        }
    }
    
    
//    [[NSUserDefaults standardUserDefaults] setObject:ZH_CN forKey:CurrentLanguage];
    [[RookieTools shareInstance] resetLanguage:[[NSUserDefaults standardUserDefaults] objectForKey:CurrentLanguage] withFrom:@"appdelegate"];
    // 告诉app支持后台播放
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [audioSession setCategory:AVAudioSessionCategoryPlayback error:nil];
    [audioSession setActive:YES error:nil];
    
    self.window = [[UIWindow alloc]initWithFrame:CGRectMake(0,0,_window_width, _window_height)];
//    [self location];
    [[RKLBSManager shareManager]startLocation];
    [self shareSDKThirdPlant];
    //后台运行定时器
    UIApplication*   app = [UIApplication sharedApplication];
    __block    UIBackgroundTaskIdentifier bgTask;
    bgTask = [app beginBackgroundTaskWithExpirationHandler:^{
        dispatch_async(dispatch_get_main_queue(), ^{
            if (bgTask != UIBackgroundTaskInvalid)
            {
                bgTask = UIBackgroundTaskInvalid;
            }
        });
    }];
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            if (bgTask != UIBackgroundTaskInvalid)
            {
                bgTask = UIBackgroundTaskInvalid;
            }
        });
    });
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(openYongnotification) name:@"openYoung_notification" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(closeYongnotification) name:@"closeYoung_notification" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(bannerDidClick:) name:EBBannerViewDidClickNotification object:nil];

    self.window.rootViewController =  [[UINavigationController alloc] initWithRootViewController:[[GuideViewController alloc] init]];
    
     [self.window makeKeyAndVisible];
    
    if ([Config getOwnID] && [[Config getOwnID] intValue]> 0) {
        [self IMLogin];
    }
    //推送
    if ([[UIApplication sharedApplication] respondsToSelector:@selector(registerUserNotificationSettings:)]) {
        UIUserNotificationSettings *settings = [UIUserNotificationSettings settingsForTypes:UIUserNotificationTypeBadge|UIUserNotificationTypeSound|UIUserNotificationTypeAlert categories:nil];
        [[UIApplication sharedApplication] registerUserNotificationSettings:settings];
    }

    //InstallUncaughtExceptionHandler();
    CGFloat currentLight = [[UIScreen mainScreen] brightness];
    [[NSUserDefaults standardUserDefaults]setFloat:currentLight forKey:@"currentLight"];
    //生命周期监听
    [[RKKeepAlive sharedKeepInstance] startAppLifeCycleMonitor];

    return YES;
}
/*
 三方id key等
 */
-(void)addThirdKeys{
//    [BTAppSwitch setReturnURLScheme:@"hailongFengbao.payments"];
    [BTAppSwitch setReturnURLScheme:@"chatsifieds.app.payments"];
    [OpenInstallSDK  initWithDelegate:self];
    //腾讯im
    [[TUIKit sharedInstance]initV2IMSDKWithConfig:[TUIKitConfig defaultConfig]];
    //腾讯地图
    [QMapServices sharedServices].apiKey = TencentKey;
    [[QMSSearchServices sharedServices] setApiKey:TencentKey];
    //Bugly
    [Bugly startWithAppId:BuglyId];
    //友盟
    [UMConfigure initWithAppkey:youmengKey channel:youmengChannel];
    [MobClick setScenarioType:E_UM_NORMAL];
    //腾讯直播短视频鉴权
    [V2TXLivePremier setLicence:TXPushLicenceURL key:TXPushLicenceKey];
    [V2TXLivePremier setObserver:self];
    [TXUGCBase setLicenceURL:LicenceURL key:LicenceKey];    
    //腾讯tpns推送
    [[XGPush defaultManager] configureClusterDomainName:TXPushClusterDomain];
    [[XGPush defaultManager] startXGWithAccessID:TXPushAccessID accessKey:TXPushAccessKey delegate:self];
    [[XGPush defaultManager] setEnableDebug:YES];

}
#pragma mark - V2TXLivePremierObserver
- (void)onLicenceLoaded:(int)result Reason:(NSString *)reason {
    NSLog(@"onLicenceLoaded: result:%d reason:%@", result, reason);
}
#pragma mark -im登录
- (void)IMLogin{
    [[YBImManager shareInstance] imLogin];
}

//-(void)location{
//    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
//    if (kCLAuthorizationStatusDenied == status || kCLAuthorizationStatusRestricted == status) {
//        return;
//     }
//    if (!_lbsManager) {
//        _lbsManager = [[CLLocationManager alloc] init];
//        [_lbsManager setDesiredAccuracy:kCLLocationAccuracyBest];
//        _lbsManager.delegate = self;
//        // 兼容iOS8定位
//        SEL requestSelector = NSSelectorFromString(@"requestWhenInUseAuthorization");
//        CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
//
//        if (kCLAuthorizationStatusDenied == status || kCLAuthorizationStatusRestricted == status) {
//            NSLog(@"请打开您的位置服务!");
//            NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
//
//               UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:nil message:[NSString stringWithFormat:@"打开“定位服务”来允许“%@”确定您的位置",[infoDictionary objectForKey:@"CFBundleDisplayName"]] preferredStyle:UIAlertControllerStyleAlert];
//               UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:@"设置" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//                   [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
//               }];
//               [alertContro addAction:cancleAction];
//               UIAlertAction *sureAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//                   
//               }];
//               [alertContro addAction:sureAction];
//            [[[MXBADelegate sharedAppDelegate]topViewController]presentViewController:alertContro animated:YES completion:nil];
//         }else{
//            if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusNotDetermined && [_lbsManager respondsToSelector:requestSelector]) {
//                [_lbsManager requestWhenInUseAuthorization];  //调用了这句,就会弹出允许框了.
//            } else {
//                [_lbsManager startUpdatingLocation];
//            }
//
//        }
//    }
//}
-(void)shareSDKThirdPlant{
    [ShareSDK registPlatforms:^(SSDKRegister *platformsRegister) {
        [platformsRegister setupQQWithAppId:QQAppId appkey:QQAppKey enableUniversalLink:YES universalLink:QQLinks];
        [platformsRegister setupWeChatWithAppId:WechatAppId appSecret:WechatAppSecret universalLink:WechatUniversalLink];
        [platformsRegister setupFacebookWithAppkey:FacebookApiKey appSecret:FacebookAppSecret displayName:[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleDisplayName"]];
        [platformsRegister setupTwitterWithKey:TwitterKey secret:TwitterSecret redirectUrl:TwitterRedirectUri];
    }];
}
//杀进程
- (void)applicationWillTerminate:(UIApplication *)application{
    [[NSNotificationCenter defaultCenter] postNotificationName:@"shajincheng" object:nil];
    
}
- (void)application:(UIApplication *)application
didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
}
#if __IPHONE_OS_VERSION_MAX_ALLOWED > __IPHONE_7_1
- (void)application:(UIApplication *)application
handleActionWithIdentifier:(NSString *)identifier
forRemoteNotification:(NSDictionary *)userInfo
  completionHandler:(void (^)())completionHandler {
    
     if(application.applicationState == UIApplicationStateInactive)
     {
         if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"1"]) {

             if([[userInfo valueForKey:@"userinfo"] valueForKey:@"uid"] != nil)
             {
                 [[NSNotificationCenter defaultCenter] postNotificationName:@"jinruzhibojiantongzhi" object:[userInfo valueForKey:@"userinfo"]];
             }
         }else if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"2"]) {
             [[NSNotificationCenter defaultCenter] postNotificationName:@"system_notification" object:nil];
             [self goMsgVC];
         }
//         [JPUSHService handleRemoteNotification:userInfo];
     }
    
}
#endif
- (void)application:(UIApplication *)application
didReceiveRemoteNotification:(NSDictionary *)userInfo {
    
     if(application.applicationState == UIApplicationStateInactive)
     {
         if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"1"]) {

             if([[userInfo valueForKey:@"userinfo"] valueForKey:@"uid"] != nil)
             {
                 [[NSNotificationCenter defaultCenter] postNotificationName:@"jinruzhibojiantongzhi" object:[userInfo valueForKey:@"userinfo"]];
             }
         }else if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"2"]) {
             [[NSNotificationCenter defaultCenter] postNotificationName:@"system_notification" object:nil];
             [self goMsgVC];
         }
     
     }
    
}
- (void)application:(UIApplication *)application
didReceiveRemoteNotification:(NSDictionary *)userInfo
fetchCompletionHandler:
(void (^)(UIBackgroundFetchResult))completionHandler {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"system_notificationUpdate" object:nil];
//极光推送 新加附加附加参数 type 消息类型  1表示开播通知，2表示系统消息
//    [EBBannerView showWithContent:minstr([[userInfo valueForKey:@"aps"] valueForKey:@"alert"])];
    if (application.applicationState == UIApplicationStateActive) {
        if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"1"]) {
            if([[userInfo valueForKey:@"userinfo"] valueForKey:@"uid"] != nil)
            {
                [[EBBannerView bannerWithBlock:^(EBBannerViewMaker *make) {
                    make.content = minstr([[userInfo valueForKey:@"aps"] valueForKey:@"alert"]);
                    make.object = [userInfo valueForKey:@"userinfo"];
                }] show];
            }
        }else if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"2"]) {
            [[EBBannerView bannerWithBlock:^(EBBannerViewMaker *make) {
                make.content = minstr([[userInfo valueForKey:@"aps"] valueForKey:@"alert"]);
                make.object = nil;
            }] show];

        }
    }else{
     if(application.applicationState == UIApplicationStateInactive)
     {

         if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"1"]) {

             if([[userInfo valueForKey:@"userinfo"] valueForKey:@"uid"] != nil)
             {
                 if ([[NSUserDefaults standardUserDefaults] boolForKey:@"isLiveing"]) {
                     [MBProgressHUD showError:YZMsg(@"正在直播中，无法退出")];
                     return;
                 }else if ([[NSUserDefaults standardUserDefaults] boolForKey:@"isPlaying"]) {
                     //[NSString stringWithFormat:@"是否进入%@的直播间",minstr([[userInfo valueForKey:@"userinfo"]valueForKey:@"user_nicename"])]
                     UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"是否退出直播间")  preferredStyle:UIAlertControllerStyleAlert];
                     UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                     }];
                     [alertContro addAction:cancleAction];
                     UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                         [[NSNotificationCenter defaultCenter] postNotificationName:@"qiehuanfangjian" object:[userInfo valueForKey:@"userinfo"]];
                         
                     }];
                     [alertContro addAction:sureAction];
                     [self.window.rootViewController presentViewController:alertContro animated:YES completion:nil];
                     
                     return;
                 }else{
                     [[NSNotificationCenter defaultCenter] postNotificationName:@"jinruzhibojiantongzhi" object:[userInfo valueForKey:@"userinfo"]];
                 }

             }
         } else if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"2"]) {
             [[NSNotificationCenter defaultCenter] postNotificationName:@"system_notification" object:nil];
             [self goMsgVC];
         }
     }
     completionHandler(UIBackgroundFetchResultNewData);
     
    }
}
- (void)applicationDidEnterBackground:(UIApplication *)application {
    application.applicationIconBadgeNumber = 0;
    [application cancelAllLocalNotifications];
}
- (void)applicationWillEnterForeground:(UIApplication *)application {
    [application cancelAllLocalNotifications];
}
- (void)applicationDidBecomeActive:(UIApplication *)application{
}
#pragma mark --- 支付宝接入
- (BOOL)application:(UIApplication *)application
            openURL:(NSURL *)url
  sourceApplication:(NSString *)sourceApplication
         annotation:(id)annotation
{
    [OpenInstallSDK handLinkURL:url];

    if ([url.host isEqualToString:@"safepay"]) {
        // 支付跳转支付宝钱包进行支付，处理支付结果
        [[AlipaySDK defaultService] processOrderWithPaymentResult:url standbyCallback:^(NSDictionary *resultDic) {
            NSLog(@"result = %@",resultDic);
        }];
        [[AlipaySDK defaultService] processAuthResult:url standbyCallback:^(NSDictionary *resultDic) {
            NSLog(@"result = %@",resultDic);
            // 解析 auth code
            NSString *result = resultDic[@"result"];
            NSString *authCode = nil;
            if (result.length>0) {
                NSArray *resultArr = [result componentsSeparatedByString:@"&"];
                for (NSString *subResult in resultArr) {
                    if (subResult.length > 10 && [subResult hasPrefix:@"auth_code="]) {
                        authCode = [subResult substringFromIndex:10];
                        break;
                    }
                }
            }
            NSLog(@"授权结果 authCode = %@", authCode?:@"");
            
        }];
    }else{
        [WXApi handleOpenURL:url delegate:self];
    }
    return YES;
}
// NOTE: 9.0以后使用新API接口
- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<NSString*, id> *)options
{
    [OpenInstallSDK handLinkURL:url];

    if ([url.scheme localizedCaseInsensitiveCompare:@"chatsifieds.app.payments"] == NSOrderedSame) {
            return [BTAppSwitch handleOpenURL:url options:options];
        }
//    if ([url.scheme localizedCaseInsensitiveCompare:@"hailongFengbao.payments"] == NSOrderedSame) {
//            return [BTAppSwitch handleOpenURL:url options:options];
//        }

    if ([url.host isEqualToString:@"safepay"]) {
        // 支付跳转支付宝钱包进行支付，处理支付结果
        [[AlipaySDK defaultService] processOrderWithPaymentResult:url standbyCallback:^(NSDictionary *resultDic) {
            NSLog(@"result = %@",resultDic);
            
        }];
        // 授权跳转支付宝钱包进行支付，处理支付结果
        [[AlipaySDK defaultService] processAuthResult:url standbyCallback:^(NSDictionary *resultDic) {
            NSLog(@"result = %@",resultDic);
            // 解析 auth code
            NSString *result = resultDic[@"result"];
            NSString *authCode = nil;
            if (result.length>0) {
                NSArray *resultArr = [result componentsSeparatedByString:@"&"];
                for (NSString *subResult in resultArr) {
                    if (subResult.length > 10 && [subResult hasPrefix:@"auth_code="]) {
                        authCode = [subResult substringFromIndex:10];
                        break;
                    }
                }
            }
            NSLog(@"授权结果 authCode = %@", authCode?:@"");
            [[NSNotificationCenter defaultCenter]postNotificationName:@"RELOADBLANCE" object:nil];
        }];
    }else if ([url.host isEqualToString:@"pay"]){
        return [WXApi handleOpenURL:url delegate:(id<WXApiDelegate>)self];

    }
    return YES;
}
#pragma mark ================ 通知点击事件 ===============
- (void)bannerDidClick:(NSNotification *)notifi{
    NSDictionary *dic = [notifi object];
    
    
    if ([[NSUserDefaults standardUserDefaults] boolForKey:@"isLiveing"]) {
        [MBProgressHUD showError:YZMsg(@"正在直播中，无法退出")];
        return;
    }else if ([[NSUserDefaults standardUserDefaults] boolForKey:@"isPlaying"]) {
        
        UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"是否退出直播间") preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [alertContro addAction:cancleAction];
        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            if ([dic count] > 0) {
                [[NSNotificationCenter defaultCenter] postNotificationName:@"jinruzhibojiantongzhi" object:dic];
            }else{
                [[NSNotificationCenter defaultCenter] postNotificationName:@"system_notification" object:nil];
                [self goMsgVC];
            }

        }];
        [alertContro addAction:sureAction];
        [self.window.rootViewController presentViewController:alertContro animated:YES completion:nil];
        
        return;
    }else{
        if ([dic isKindOfClass:[NSDictionary class]] && [dic count] > 0) {
            [[NSNotificationCenter defaultCenter] postNotificationName:@"jinruzhibojiantongzhi" object:dic];
        }else{
            [[NSNotificationCenter defaultCenter] postNotificationName:@"system_notification" object:nil];
            [self goMsgVC];
        }
    }

}

-(void)goMsgVC {
    if ([[MXBADelegate sharedAppDelegate].topViewController isKindOfClass:[MessageListVC class]]) {
        return;
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        MessageListVC *vc = [[MessageListVC alloc]init];
        [[MXBADelegate sharedAppDelegate] pushViewController:vc animated:YES];
    });
}

-(void)onResp:(BaseResp *)resp
{
     if ([resp isKindOfClass:[PayResp class]])
     {
         PayResp *response = (PayResp *)resp;
         switch (response.errCode)
         {
             case WXSuccess:
                 //服务器端查询支付通知或查询API返回的结果再提示成功
                 NSLog(@"支付成功");
                 [MBProgressHUD showError:YZMsg(@"支付成功")];
//                 [[NSNotificationCenter defaultCenter] postNotificationName:@"wxapiPaySuccess" object:nil userInfo:nil];
                 break;
             case WXErrCodeUserCancel:
                 //服务器端查询支付通知或查询API返回的结果再提示成功
                 //交易取消
                 [MBProgressHUD showError:YZMsg(@"已取消支付")];
                 break;
             default:
                 NSLog(@"支付失败， retcode=%d",resp.errCode);
                 [MBProgressHUD showError:YZMsg(@"支付失败")];
                 break;
         }
     }
}
-(BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray * _Nullable))restorationHandler{
    [OpenInstallSDK continueUserActivity:userActivity];

    NSLog(@"userActivity : %@",userActivity.webpageURL.description);
    return YES;
}
//- (void)getWakeUpParams:(CodeInstallData *)appData{
//}

-(void)getWakeUpParams:(OpeninstallData *)appData
{
    if (appData.data) {//(动态唤醒参数)
        //e.g.如免填邀请码建立邀请关系、自动加好友、自动进入某个群组或房间等
    }
    if (appData.channelCode) {//(通过渠道链接或二维码唤醒会返回渠道编号)
        //e.g.可自己统计渠道相关数据等
    }
    NSLog(@"app delegate-CodeInstallSDK:\n动态参数：%@;\n渠道编号：%@",appData.data,appData.channelCode);

}
#pragma mark------青少年----
-(void)openYongnotification{
    if (!youngTimer) {
        youngTimer = [NSTimer scheduledTimerWithTimeInterval:10 target:self selector:@selector(addTeenagerTime) userInfo:nil repeats:YES];
        [youngTimer fire];
    }
}
-(void)closeYongnotification{
    if (youngTimer) {
        [youngTimer invalidate];
        youngTimer  = nil;
    }
}
-(void)addTeenagerTime{
    timeCount++;
    NSLog(@"zytabbarvc-------time:%d",timeCount);
    NSString *url = [purl stringByAppendingFormat:@"?service=User.addTeenagerTime"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
    };

    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"10010"]) {
            [[YBYoungManager shareInstance]showTipsWindowWithMsg:msg];
        }else if ([code isEqual:@"10011"]){
            [[YBYoungManager shareInstance]showTipsWindowWithMsg:msg];

        }else if ([code isEqual:@"1002"]){
            [self closeYongnotification];
        }
    } Fail:^(id fail) {
        
    }];

}

// 统一接收消息的回调
/// @param notification 消息对象(有2种类型NSDictionary和UNNotification具体解析参考示例代码)
/// @note 此回调为前台收到通知消息及所有状态下收到静默消息的回调（消息点击需使用统一点击回调）
/// 区分消息类型说明：xg字段里的msgtype为1则代表通知消息msgtype为2则代表静默消息
- (void)xgPushDidReceiveRemoteNotification:(nonnull id)notification withCompletionHandler:(nullable void (^)(NSUInteger))completionHandler{
    
    NSLog(@"appdelegate------:%ld",[[XGPush defaultManager]xgApplicationBadgeNumber]);
    if ([notification isKindOfClass:[UNNotificationResponse class]]) {
        NSLog(@"aaaaaaaaaaa[TPNS Demo] click notification: %@", ((UNNotificationResponse *)notification).notification.request.content.userInfo);
    } else if ([notification isKindOfClass:[NSDictionary class]]) {
        NSLog(@"bbbbbbbbb[TPNS Demo] click notification: %@", notification);
    }
 /// code
}
 /// 统一点击回调
/// @param response 如果iOS 10+/macOS 10.14+则为UNNotificationResponse，低于目标版本则为NSDictionary
- (void)xgPushDidReceiveNotificationResponse:(nonnull id)response withCompletionHandler:(nonnull void (^)(void))completionHandler {
    if ([response isKindOfClass:[UNNotificationResponse class]]) {
        
        NSLog(@"appdelegateback------:%ld",[[XGPush defaultManager]xgApplicationBadgeNumber]);

        NSLog(@"[TPNS Demo] click notification: %@", ((UNNotificationResponse *)response).notification.request.content.userInfo);
        NSDictionary *responeDic = ((UNNotificationResponse *)response).notification.request.content.userInfo;
        NSDictionary *userInfo =[PublicObj dictionaryWithJsonString:minstr([responeDic  valueForKey:@"custom"])];
        
        if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"1"]) {

            if([[userInfo valueForKey:@"userinfo"] valueForKey:@"uid"] != nil)
            {
                [[NSNotificationCenter defaultCenter] postNotificationName:@"jinruzhibojiantongzhi" object:[userInfo valueForKey:@"userinfo"]];
            }
        }else if ([minstr([userInfo valueForKey:@"type"]) isEqual:@"2"]) {
            [[NSNotificationCenter defaultCenter] postNotificationName:@"system_notification" object:nil];
            [self goMsgVC];
        }

        
        
    } else if ([response isKindOfClass:[NSDictionary class]]) {
        NSLog(@"[TPNS Demo] click notification: %@", response);
    }

  /// code
}
/**
@brief 注册推送服务回调
@param deviceToken APNs 生成的 Device Token
@param xgToken TPNS 生成的 Token，推送消息时需要使用此值。TPNS 维护此值与 APNs 的 Device Token 的映射关系
@param error 错误信息，若 error 为 nil 则注册推送服务成功
@note TPNS SDK1.2.6.0+
*/
- (void)xgPushDidRegisteredDeviceToken:(nullable NSString *)deviceToken xgToken:(nullable NSString *)xgToken error:(nullable NSError *)error{
    NSLog(@"xgToken------:%@ ---errror:%@",xgToken, error);
    if ([Config getOwnID]) {
        [[XGPushTokenManager defaultTokenManager] upsertAccountsByDict:@{@(0):[Config getOwnID]}];
        NSString *languageStr= [PublicObj getCurrentLanguage];
        [[XGPushTokenManager defaultTokenManager]clearAndAppendTags:@[languageStr,[Config getOwnID]]];
    }
    
}
/// 注册推送服务失败回调
/// @param error 注册失败错误信息
/// @note TPNS SDK1.2.7.1+
-(void)xgPushDidFailToRegisterDeviceTokenWithError:(nullable NSError *)error {
}

//
- (void)checkNetwork{
    AFNetworkReachabilityManager *netManager = [AFNetworkReachabilityManager sharedManager];
    [netManager startMonitoring];  //开始监听 防止第一次安装不显示
    [netManager setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status){
        if (status != AFNetworkReachabilityStatusNotReachable && status != AFNetworkReachabilityStatusUnknown) {
            [V2TXLivePremier setLicence:TXPushLicenceURL key:TXPushLicenceKey];
            [TXUGCBase setLicenceURL:LicenceURL key:LicenceKey];
        }
    }];
}
@end
