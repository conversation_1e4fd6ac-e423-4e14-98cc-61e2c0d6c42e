#import <UIKit/UIKit.h>
#import "huanxinsixinview.h"
#import "chatModel.h"
#import "NSString+StringSize.h"
#import "listModel.h"
#import "UIImageView+Util.h"
#import "listCell.h"
#import "UIImageView+WebCache.h"
#import <CommonCrypto/CommonDigest.h>
#import "fenXiangView.h"
#import "ZFModalTransitionAnimator.h"
#import "PersonHomeVC.h"
#import "chatController.h"
#import "CoinVeiw.h"
#import "setViewM.h"

//连麦推流
#import "catSwitch.h"
#import "GrounderSuperView.h"
//新礼物
#import "PresentView.h"
#import "GiftModel.h"
#import "AnimOperation.h"
#import "AnimOperationManager.h"
#import "GSPChatMessage.h"
#import "socketLivePlay.h"
#import "liwuview.h"
#import "continueGift.h"
//#import "upmessageInfo.h"
#import "expensiveGiftV.h"//礼物
#import "ListCollection.h"//用户列表
#import "userLoginAnimation.h"//进场动画
#import "personList.h"
#import "lastview.h"
#import "viplogin.h"//用户进场动画
#import "TXPlayLinkMic.h"
#import "impressVC.h"
#import "chatMsgCell.h"
#import "shouhuView.h"
#import "guardShowView.h"
#import "redBagView.h"
#import "redListView.h"
#import "anchorOnline.h"
#import "anchorPKView.h"
#import "MsgSysVC.h"
#import "jubaoVC.h"
#import "WinningPrizeView.h"
#import "JackpotButton.h"
#import "JackpotView.h"
#import "UserBulletWindow.h"
#import "roomPayView.h"
#import "turntableView.h"
#import "roomShowGoodsView.h"
#import "YBAlertView.h"
#import "PlatGiftView.h"
#import "gameBottomVC.h"
#import "WPFRotateView.h"
#import "LiveGoodView.h"
#import "DayTaskView.h"
#import "YBPageControl.h"
#import "RKShowPaintedView.h"
#import "SDCycleScrollView.h"
#import "FirstChargeView.h"
#import "FullScreenPlayView.h"
#import "DisableLiveView.h"
#import "TChatC2CController.h"
typedef void(^LivePlayEndEvent)();
typedef NS_ENUM(NSInteger,SliderType) {
    SliderType_Task,
    SliderType_Turn,
    SliderType_Jackpot,
};
@protocol dianjishijian <NSObject>
@optional
-(void)duihaoHidden;
@end
@interface moviePlay : UIViewController
{
    viplogin *vipanimation;//坐骑进场动画
    userLoginAnimation *useraimation;//进场动画(横条飘进)
    UIImageView *buttomimageviews;//背景模糊(一开始进入直播间未加载到视频的时候显示)
    ListCollection *listView;//用户列表
    expensiveGiftV *haohualiwuV;//豪华礼物
    PlatGiftView *platliwuV;//全站礼物

    fenXiangView *fenxiangV;//分享view
    continueGift *continueGifts;//连送礼物
    huanxinsixinview *huanxinviews;//私信列表
    TChatC2CController *tChatsamall;
    
    UIView *chatsmallzhezhao;
    setViewM *setFrontV;//页面UI
//    upmessageInfo *userView;//用户列表弹窗
    liwuview *giftview;//礼物界面
    UIView *_videoPlayView;//视频播放view
    UIView *_swPlayView;
    UIButton *fullScreenBtn;
    ////////////////////////////////////////////////////////////////////////////
    UIScrollView *backScrollView;//
    UIView *regionBack;
    UIScrollView *buttomscrollview;//上下滑屏切换
    UIView *buttomviews;//用来左右滑动;
    UIImageView *buttomimageview;//滑动显示下一个或上一个图片
    BOOL fangKeng;//防坑 ，第一次进房间不滑动
    int scrollvieweight;//判断滑动距离是否够切换
    ////////////////////////////////////////////////////////////////////////////
    socketMovieplay *socketDelegate;//socket监听
    ////////////////////////////////////////////////////////////////////////////
    UIImageView *starImage; //点亮图片
    NSNumber *heartNum;
    int starisok;
    UITapGestureRecognizer *starTap;
    BOOL firstStar;// 第一次点亮
    ////////////////////////////////////////////////////////////////////////////
    UIButton *pushBTN;
    UITextField *keyField;//聊天输入框
    UIView *toolBar;
    catSwitch *cs;//弹幕按钮
    UIButton *keyBTN;//点击弹出键盘
    ////////////////////////////////////////////////////////////////////////////
    LiveUser *myUser;//缓存个人信息
    CGFloat listcollectionviewx;//用户列表的x
    NSString *haohualiwu;//判断豪华礼物
    long long userCount;//用户数量
    CGFloat www;
    NSString *titleColor;//判断 回复颜色
    NSString *level;
    
    BOOL    _canScrollToBottom;//判断tableview可不可以滑动
    UIPanGestureRecognizer *panGestureRecognizer;
    UIView* _winRtcView;
    int userlist_time;//定时刷新用户列表时间
    BOOL isRegistLianMai;//判断连麦注册成功
    UIView *liansongliwubottomview;
    BOOL haslianmai;//是不是可以连麦
    
    lastview *lastv;
    
    int coasttime;//扣费计时
    BOOL giftViewShow;//是否显示礼物view
    UIPanGestureRecognizer *videopan;//视频拖拽手势
    UITapGestureRecognizer *videotap;
    
    int isshow;//连麦相关
    BOOL ksynotconnected;
    BOOL ksyclosed;
    
    NSString *speak_limit;
    NSString *barrage_limit;
    NSString *jackpot_level;
    NSString *turntable_switch;
    
    NSTimer *starMove;//点亮计时器
    NSTimer *listTimer;//定时刷新用户列表
    NSTimer *lianmaitimer;//主播同意连麦后超时响应时间
    NSTimer *timecoast;//计时扣费
    
    TXPlayLinkMic *_tx_playrtmp;
    NSString *_myplayurl;
    NSString *usertype;
    NSString *votesTatal;
    shouhuView *guardView;
    guardShowView *gShowView;
    NSDictionary *guardInfo;
    redBagView *redBview;
    UIButton *redBagBtn;
    redListView *redList;
    UIButton *sendBagBtn;
    anchorOnline *anchorView;
    anchorPKView *pkView;
    MsgSysVC *sysView;
//    NSString *lianmaiLevel;

    WinningPrizeView *winningView;
    JackpotButton *JackpotBtn;
    JackpotView *jackV;
    
    UserBulletWindow *buttleView;
    roomPayView *payView;

    UIButton *turntableBtn;
    turntableView *turntableV;
    roomShowGoodsView *roomGoodsV;
    UIButton *goodsShowBtn;//展示商品的按钮
    
    CGFloat lastContenOffset;
    CGFloat endDraggingOffset;

    NSMutableArray *scrollImgArr;
    
    gameBottomVC *gameVC;//炸金花
    WPFRotateView *rotationV;//转盘
    NSDictionary *zhuangstartdic;//庄家初始化信息
    NSDictionary *showGoodsDic;//直播间展示小窗口
    
    UIPageControl *sysPageControl;
    SDCycleScrollView * _cycleScroll;
    
    UIImageView *titleBackImgView;//标题飘屏
    
    NSArray *_sliderA;
    UIImageView *_headImg;
    UILabel *_nameLb;
    UIButton *_attBtn;
    NSString *pkuserUid;
}
@property(nonatomic,strong)UILabel *unReadLabel;

@property(nonatomic, copy)LivePlayEndEvent endEvent;
@property(nonatomic,strong)NSString *livetype;
@property(nonatomic,strong)NSString *type_val;
@property(nonatomic,strong)GrounderSuperView *danmuView;//弹幕
@property(nonatomic,copy)NSString *tanChuangID;//弹窗用户的id
@property(nonatomic,strong)NSString *danmuprice;//弹幕价格
@property(nonatomic,strong)UITableView *tableView;
@property(nonatomic,strong)NSMutableArray *listArray;
@property(nonatomic,strong)NSMutableArray *chatModels;//模型数组
@property(nonatomic,copy)NSString *content;//聊天内容
@property(nonatomic, strong)NSArray *chatWordArr;
@property (strong, nonatomic) NSURL *url;
@property(nonatomic,strong)NSArray *models;

@property(nonatomic,strong)UIButton *returnCancle;//退出
@property(nonatomic,strong)UIButton *moreBTN;
@property(nonatomic,strong)UIButton *messageBTN;//消息
@property(nonatomic,strong)UIButton *fenxiangBTN;//分享
@property(nonatomic,strong)UIButton *liwuBTN;//礼物
@property(nonatomic,strong)UIButton *gameBtn;

@property(nonatomic,strong)UIButton *connectVideo;//连麦
@property(nonatomic,strong)UIButton *firstChargeBtn;//首充
@property(nonatomic,strong)FirstChargeView *chargeView;
@property(nonatomic,copy)NSString *dailytask_switch;//每日任务开关 0 关 1 开
/*
 *
 **总的主播数组
 */
@property(nonatomic,strong)NSArray *scrollarray;
/*
 *
 **获取第几个主播
 */
@property(nonatomic,strong)NSDictionary *lastHostDic;      //上一个
@property(nonatomic,strong)NSDictionary *hostdic;          //当前
@property(nonatomic,strong)NSDictionary *nextHostDic;      //下一个
@property(nonatomic,assign) NSInteger scrollindex;         //页面传值

@property(nonatomic,strong)NSString *isJpush;
@property(nonatomic,strong)NSDictionary *playDoc;
@property(nonatomic,assign)int hiddenN;
@property(nonatomic,assign)id<dianjishijian>liwuDelegate;

@property(nonatomic,strong)NSString *sdkType;//0-金山   1-腾讯
@property(nonatomic, assign) CGFloat scrollViewOffsetYOnStartDrag;

@property(nonatomic,strong)YBAlertView *alert;

@property(nonatomic,strong)LiveGoodView *liveGoodsView;
@property(nonatomic,strong)DayTaskView *taskView;
@property(nonatomic,strong)RKShowPaintedView *paintedShowRegion;//手绘礼物显示区域

@property(nonatomic, strong)NSMutableArray *liveGoodTipArr;
@property(nonatomic, strong)UILabel *liveTipLabel;
@property(nonatomic, strong)NSMutableArray *viploginArray;
@property(nonatomic, strong)FullScreenPlayView *fullscreenView;
@property(nonatomic, strong)DisableLiveView *disLiveView;

@property(nonatomic, strong)UIView *attView;

@end
