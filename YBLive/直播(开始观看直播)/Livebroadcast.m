#import "Livebroadcast.h"
/********************  MHSDK添加 开始 ********************/
#import "MHMeiyanMenusView.h"
#import <MHBeautySDK/MHBeautyManager.h>
#import "MHBeautyParams.h"
/********************  MHSDK添加 结束 ********************/
#import "startLiveClassVC.h"

/***********************  腾讯SDK start ********************/
#import "YBLiveRTCManager.h"

#import <CWStatusBarNotification/CWStatusBarNotification.h>
#import "V8HorizontalPickerView.h"

#import <CoreTelephony/CTCallCenter.h>
#import <CoreTelephony/CTCall.h>
#import <MHBeautySDK/MHBeautyManager.h>
#import <SSZipArchive/SSZipArchive.h>
#import "sproutCommon.h"
#import "OrderMessageVC.h"
#import "TChatC2CController.h"
#import "THeader.h"
#import "OnlineUserView.h"
#import <Accelerate/Accelerate.h>
#import "AgordLinkMic.h"
#import "PKAnchorInfoView.h"
#import "YBPageControl.h"
typedef NS_ENUM(NSInteger,TCLVFilterType) {
    FilterType_None         = 0,
    FilterType_white        ,   //美白滤镜
    FilterType_langman         ,   //浪漫滤镜
    FilterType_qingxin         ,   //清新滤镜
    FilterType_weimei         ,   //唯美滤镜
    FilterType_fennen         ,   //粉嫩滤镜
    FilterType_huaijiu         ,   //怀旧滤镜
    FilterType_landiao         ,   //蓝调滤镜
    FilterType_qingliang     ,   //清凉滤镜
    FilterType_rixi         ,   //日系滤镜
};
/***********************  腾讯SDK end **********************/

//#import <GPUImage/GPUImage.h>
#define upViewW  _window_width*0.8
@import CoreTelephony;
@interface Livebroadcast ()<UITextViewDelegate,UITableViewDataSource,UITableViewDelegate,UIActionSheetDelegate,UITextFieldDelegate,catSwitchDelegate,haohuadelegate,socketLiveDelegate,listDelegate,tx_play_linkmic,UIImagePickerControllerDelegate,UINavigationControllerDelegate,UITextViewDelegate,adminDelegate,guardShowDelegate,anchorOnlineDelegate,redListViewDelegate,anchorPKViewDelegate,anchorPKAlertDelegate,TXVideoCustomProcessDelegate,TXLivePushListener,V8HorizontalPickerViewDelegate,V8HorizontalPickerViewDataSource,JackpotViewDelegate,UserBulletWindowDelegate,MHMeiyanMenusViewDelegate,NSURLSessionDelegate,SSZipArchiveDelegate,platDelgate,gameDelegate,WPFRotateViewDelegate,shangzhuangdelegate,gameselected,SDCycleScrollViewDelegate,V2TIMConversationListener,YBLiveRTCDelegate,YbAgoraDelegate>

/********************  MHSDK添加 开始 ********************/
@property (nonatomic, strong) MHMeiyanMenusView *menusView;
@property (nonatomic, strong) MHBeautyManager *beautyManager;
/******************** MHSDK添加 结束 ********************/
@property (nonatomic,strong)UIView *preFrontView;
//@property(nonatomic, strong) GPUImageView *gpuPreviewView;
//@property (nonatomic, strong) GPUImageStillCamera *videoCamera;
//@property (nonatomic, strong) CIImage *outputImage;
//@property (nonatomic, assign) size_t outputWidth;
//@property (nonatomic, assign) size_t outputheight;

/***********************  腾讯SDK start **********************/

@property(nonatomic,strong)NSMutableArray *filterArray;//美颜数组
@property (nonatomic,strong)UIView     *vBeauty;
@property (nonatomic,strong)CTCallCenter     *callCenter;

/***********************  腾讯SDK end **********************/
@property (nonatomic,strong)OnlineUserView *onlineView;
@property (nonatomic, strong)AgordLinkMic *agordLinkView;
@property(nonatomic,strong)PKAnchorInfoView *pkAnchorView;

@end
@implementation Livebroadcast
{
    NSMutableArray *msgList;
    CGSize roomsize;
    UILabel *roomID;
    UIView *bgmView;
    //预览的视图
    UIButton *preThumbBtn;//上传封面按钮
    UILabel *thumbLabel;//上传封面状态的label
    UIImage *thumbImage;
    NSString *thumbUrlStr;
    UILabel *liveClassLabel;
    UITextView *liveTitleTextView;
    UILabel *textPlaceholdLabel;
    
    NSMutableArray *preShareBtnArray;//分享按钮数组
    NSString *selectShareName;//选择分享的名称
    
    UIButton *preTypeBtn;
    UIScrollView *roomTypeView;//选择房间类型
    NSMutableArray *roomTypeBtnArray;//房间类型按钮
    NSString *roomType;//房间类型
    NSString *roomTypeValue;//房间价值
    
    NSString *liveClassID;
    //预览的视图结束
    //直播时长
    UIView *liveTimeBGView;
    UILabel *liveTimeLabel;
    int liveTime;
    NSTimer *liveTimer;
    BOOL isTorch;
    BOOL _isFront;
    BOOL userCanLink;
    /***********************  腾讯SDK start **********************/
    float  _tx_beauty_level;
    float  _tx_whitening_level;
    float  _tx_eye_level;
    float  _tx_face_level;
    UIButton              *_beautyBtn;
    UIButton              *_filterBtn;
    UILabel               *_beautyLabel;
    UILabel               *_whiteLabel;
    UILabel               *_bigEyeLabel;
    UILabel               *_slimFaceLabel;
    UISlider              *_sdBeauty;
    UISlider              *_sdWhitening;
    UISlider              *_sdBigEye;
    UISlider              *_sdSlimFace;
    V8HorizontalPickerView  *_filterPickerView;
    NSInteger    _filterType;

    /***********************  腾讯SDK end **********************/
    UIImageView *loactionImgView;
    UILabel *locationLabel;
    BOOL locationSwitch;

    UIButton *openGoodsCar;
    
    BOOL isShowSticker;
    BOOL isMirror;
    OrderMessageVC *orderChat;
    
    NSString *live_thumb;
    BOOL _needScale;
    BOOL _isFrontCamera;
    NSArray *_funcTypeArr;
    UIImageView *bGimg;
    UIButton *_returnCancle;
}
#pragma mark ================ tiuivew代理 ===============
-(void)showPreFrontView{
    if (_preFrontView) {
        _preFrontView.hidden = NO;
    }
}
#pragma mark ============定位开关=============
- (void)locationSwitchBtnClick{
    if ([locationLabel.text isEqual:YZMsg(@"开定位")]) {
        loactionImgView.image = [UIImage imageNamed:@"pre_location"];
        locationLabel.text = [cityDefault getMyCity];
        locationSwitch = YES;
    }else{
        UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"关闭定位，直播不会被附近的人看到，直播间人数可能会减少，确认关闭吗？") preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            locationSwitch = YES;
        }];
        [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];

        [alertContro addAction:cancleAction];
        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"坚决关闭") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            loactionImgView.image = [UIImage imageNamed:@"pre_location_off"];
            locationLabel.text = YZMsg(@"开定位");
            locationSwitch = NO;
         }];
        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
        [alertContro addAction:sureAction];
        [self presentViewController:alertContro animated:YES completion:nil];

    }
}
#pragma mark ================ 直播开始之前的预览 ===============
- (void)creatPreFrontView{
    _preFrontView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    _preFrontView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:_preFrontView];
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hidePreTextView)];
    [_preFrontView addGestureRecognizer:tap];
    
    UIView *locationLabelView = [[UIView alloc]init];
    locationLabelView.backgroundColor = RGB_COLOR(@"#000000", 0.3);
    locationLabelView.layer.cornerRadius = 8;
    locationLabelView.layer.masksToBounds = YES;
    [_preFrontView addSubview:locationLabelView];
    [locationLabelView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerY.equalTo(loactionImgView);
//        make.left.equalTo(loactionImgView.mas_right).offset(-8);
        make.top.equalTo(_preFrontView.mas_top).offset(42+statusbarHeight);
        make.height.mas_equalTo(16);
        make.right.equalTo(_preFrontView.mas_right).offset(-16);
    }];
    loactionImgView = [[UIImageView alloc]init];//WithFrame:CGRectMake(10, 42+statusbarHeight, 16, 16)];
    loactionImgView.image = [UIImage imageNamed:@"pre_location"];
    [_preFrontView addSubview:loactionImgView];
    [loactionImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(locationLabelView.mas_centerY);
        make.right.equalTo(locationLabelView.mas_left).offset(10);
        make.height.width.mas_equalTo(16);
    }];
    locationLabel = [[UILabel alloc]init];
//    WithFrame:CGRectMake(loactionImgView.right-10, loactionImgView.top+3, [[YBToolClass sharedInstance] widthOfString:[cityDefault getMyCity] andFont:[UIFont systemFontOfSize:13] andHeight:16]+18, 14)
    locationLabel.font = [UIFont systemFontOfSize:11];
    locationLabel.textColor = [UIColor whiteColor];
    [locationLabelView addSubview:locationLabel];
    [locationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(locationLabelView);
        make.height.mas_equalTo(16);
        make.left.equalTo(locationLabelView).offset(10);
        make.right.equalTo(locationLabelView).offset(-10);
    }];
    [[RKLBSManager shareManager]startLocation];
    [[RKLBSManager shareManager]locationUpdae:^(NSString *Province, NSString *City, NSString *District) {
        locationLabel.text = [cityDefault getMyCity];
    }];

    [_preFrontView insertSubview:locationLabelView belowSubview:loactionImgView];
    UIButton *locationSwitchBtn = [UIButton buttonWithType:0];
    [locationSwitchBtn addTarget:self action:@selector(locationSwitchBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [_preFrontView addSubview:locationSwitchBtn];
    [locationSwitchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.height.equalTo(loactionImgView);
        make.right.equalTo(locationLabelView);
    }];
    
    [self.view layoutIfNeeded];
    UIButton *preCloseBtn = [UIButton buttonWithType:0];
    preCloseBtn.frame = CGRectMake(16, loactionImgView.top, loactionImgView.height, loactionImgView.height);
    [preCloseBtn setImage:[UIImage imageNamed:@"personBack"] forState:0];
    [preCloseBtn addTarget:self action:@selector(doClosePreView) forControlEvents:UIControlEventTouchUpInside];
    [_preFrontView addSubview:preCloseBtn];
        
    UIView *preMiddleView = [[UIView alloc]initWithFrame:CGRectMake(14, preCloseBtn.bottom+50, _window_width-28, (_window_width-24)*0.33)];//-(_window_width-24)*0.33
    preMiddleView.backgroundColor = RGB_COLOR(@"#0000000", 0.3);
    preMiddleView.layer.cornerRadius = 10;
    [_preFrontView addSubview:preMiddleView];
    
    preThumbBtn = [UIButton buttonWithType:0];
    preThumbBtn.frame = CGRectMake(10, 10, preMiddleView.height-20, preMiddleView.height-20);
    [preThumbBtn setImage:[UIImage imageNamed:@"pre_uploadThumb"] forState:0];
    [preThumbBtn addTarget:self action:@selector(doUploadPicture) forControlEvents:UIControlEventTouchUpInside];
    preThumbBtn.layer.cornerRadius = 10;
    preThumbBtn.layer.masksToBounds = YES;
    [preMiddleView addSubview:preThumbBtn];
    
    thumbLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, preThumbBtn.height*0.7, preThumbBtn.width, preThumbBtn.height/4)];
    thumbLabel.textColor = [UIColor whiteColor];//RGB_COLOR(@"#c8c8c8", 1);
    thumbLabel.textAlignment = NSTextAlignmentCenter;
    thumbLabel.text = YZMsg(@"直播封面");
    thumbLabel.font = [UIFont systemFontOfSize:13];
    [preThumbBtn addSubview:thumbLabel];
    
    UILabel *preTitlelabel = [[UILabel alloc]initWithFrame:CGRectMake(preThumbBtn.right+5, preThumbBtn.top, 100, preThumbBtn.height/4)];
    preTitlelabel.font = [UIFont systemFontOfSize:13];
    preTitlelabel.textColor = [UIColor whiteColor];//RGB_COLOR(@"#c8c8c8", 1);
    preTitlelabel.text = YZMsg(@"直播标题");
    [preMiddleView addSubview:preTitlelabel];
    liveTitleTextView = [[UITextView alloc]initWithFrame:CGRectMake(preTitlelabel.left, preTitlelabel.bottom, preMiddleView.width-10-preThumbBtn.right, 30)];
    liveTitleTextView.delegate = self;
    liveTitleTextView.font = [UIFont systemFontOfSize:20];
    liveTitleTextView.textColor = [UIColor whiteColor];
    liveTitleTextView.backgroundColor = [UIColor clearColor];
    [preMiddleView addSubview:liveTitleTextView];
    textPlaceholdLabel = [[UILabel alloc]initWithFrame:CGRectMake(5, 4, liveTitleTextView.width, 22)];
    textPlaceholdLabel.font = [UIFont systemFontOfSize:20];
    textPlaceholdLabel.textColor = [UIColor whiteColor];//RGB_COLOR(@"#c8c8c8", 1);
    textPlaceholdLabel.text = YZMsg(@"给直播写个标题吧");
    [liveTitleTextView addSubview:textPlaceholdLabel];

    UIButton *liveClassBtn = [UIButton buttonWithType:0];
    liveClassBtn.frame = CGRectMake(liveTitleTextView.left, liveTitleTextView.bottom+5, liveTitleTextView.width*0.7, 30);
    liveClassBtn.backgroundColor = RGBA(0, 0, 0, 0.3);
    liveClassBtn.layer.cornerRadius = 5;
    liveClassBtn.layer.masksToBounds = YES;
    [liveClassBtn addTarget:self action:@selector(showAllClassView) forControlEvents:UIControlEventTouchUpInside];
    [preMiddleView addSubview:liveClassBtn];
    
    UIImageView *classImg = [[UIImageView alloc]init];
    classImg.frame = CGRectMake(10, 8, 20, 14);
    classImg.image = [UIImage imageNamed:@"live_class"];
    classImg.contentMode = UIViewContentModeScaleAspectFit;
    [liveClassBtn addSubview:classImg];
    
    liveClassLabel = [[UILabel alloc]initWithFrame:CGRectMake(classImg.right+5, 0, liveClassBtn.width-classImg.right-5, 30)];
    liveClassLabel.text = YZMsg(@"请选择直播内容");
    liveClassLabel.textColor = [UIColor whiteColor];
    liveClassLabel.font = [UIFont systemFontOfSize:14];
    [liveClassBtn addSubview:liveClassLabel];

    
    /**_live_isban是否禁播**/
    if ([_live_isban isEqual:@"1"]) {
        UILabel *ban_titleLb = [[UILabel alloc]init];
        ban_titleLb.frame = CGRectMake(preMiddleView.left+5, preMiddleView.bottom+10, preMiddleView.width, 40);
        ban_titleLb.font = [UIFont systemFontOfSize:14];
        ban_titleLb.textColor = normalColors;
        ban_titleLb.text = _liveban_title;
        ban_titleLb.numberOfLines = 0;
        NSLog(@"-----livebroadcast-----:%@ \n %@",_live_isban, _liveban_title);
        [_preFrontView addSubview:ban_titleLb];
    }
    
    //开播按钮
    UIButton *startLiveBtn = [UIButton buttonWithType:0];
    startLiveBtn.size = CGSizeMake(_window_width*0.8,40);
    startLiveBtn.center = CGPointMake(_window_width*0.5, _window_height*0.8);
    startLiveBtn.layer.cornerRadius = 20.0;
    startLiveBtn.layer.masksToBounds = YES;
//    [startLiveBtn setBackgroundColor:normalColors];
    [startLiveBtn setBackgroundImage:[UIImage imageNamed:@"startLive_back"]];
    [startLiveBtn addTarget:self action:@selector(doHidden:) forControlEvents:UIControlEventTouchUpInside];
    [startLiveBtn setTitle:YZMsg(@"开始直播") forState:0];
    startLiveBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [_preFrontView addSubview:startLiveBtn];
    
    locationSwitch = YES;
    NSArray *functionArr;
    NSArray *functionImgArr;

    if(_isGameLive){
        functionArr = @[YZMsg(@"分享"),YZMsg(@"房间类型")];
        functionImgArr = @[@"pre_fun_share",@"pre_fun_roomclass"];
        _funcTypeArr = @[@(FunctionType_share),@(FunctionType_roomType)];
    }else{
        if ([_isshop isEqual:@"1"]) {
            functionArr = @[YZMsg(@"购物车"),YZMsg(@"翻转"),YZMsg(@"美颜"),YZMsg(@"分享"),YZMsg(@"房间类型")];
            functionImgArr = @[@"pre_fun_shop",@"pre_fun_camera",@"pre_fun_meiyan",@"pre_fun_share",@"pre_fun_roomclass"];
            _funcTypeArr = @[@(FunctionType_shop),@(FunctionType_camera),@(FunctionType_meiyan),@(FunctionType_share),@(FunctionType_roomType)];
        }else{
            functionArr = @[YZMsg(@"翻转"),YZMsg(@"美颜"),YZMsg(@"分享"),YZMsg(@"房间类型")];
            functionImgArr = @[@"pre_fun_camera",@"pre_fun_meiyan",@"pre_fun_share",@"pre_fun_roomclass"];
            _funcTypeArr = @[@(FunctionType_camera),@(FunctionType_meiyan),@(FunctionType_share),@(FunctionType_roomType)];
        }

    }
    
    UIView *funView = [[UIView alloc]init];
    [_preFrontView addSubview:funView];
    [funView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(startLiveBtn.mas_top).offset(-20);
        make.centerX.equalTo(startLiveBtn.mas_centerX);
    }];
    
    for(int i = 0; i < functionArr.count; i ++){
        UIButton *funbtn = [UIButton buttonWithType:0];
        [funbtn addTarget:self action:@selector(funBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        funbtn.tag = 10000+i;
        [funView addSubview:funbtn];
        if(i == 0){
            [funbtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(funView.mas_left).offset(5);
                make.width.height.mas_equalTo(70);
                make.bottom.equalTo(funView.mas_bottom).offset(-10);
                make.top.equalTo(funView.mas_top);
            }];
            openGoodsCar = funbtn;
            openGoodsCar.selected = NO;
        }else{
            if(i == functionArr.count -1){
                [funbtn mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(funView.mas_left).offset(i * 70);
                    make.right.equalTo(funView.mas_right).offset(-5);
                    make.top.equalTo(funView.mas_top);
                    make.width.mas_equalTo(70);
                }];
                preTypeBtn = funbtn;
            }else{
                [funbtn mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(funView.mas_left).offset(i * 70);
                    make.top.equalTo(funView.mas_top);
                    make.width.mas_equalTo(70);
                }];
            }
        }
        
        UIImageView *img = [[UIImageView alloc]init];
//        img.frame = CGRectMake(0, funbtn.width/2-15, 30, 30);
        img.image =[UIImage imageNamed:functionImgArr[i]];
        img.tag = 20000+i;
        [funbtn addSubview:img];
        [img mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(funbtn.mas_centerX);
            make.top.equalTo(funbtn.mas_top);
            make.width.height.mas_equalTo(30);
        }];
        UILabel *titleLb = [[UILabel alloc]init];
//        titleLb.frame = CGRectMake(0, img.bottom+5, funbtn.width, 30);
        titleLb.textAlignment = NSTextAlignmentCenter;
        titleLb.font = [UIFont systemFontOfSize:14];
        titleLb.textColor  = UIColor.whiteColor;
        titleLb.text = functionArr[i];
        titleLb.tag = 30000+i;
        [funbtn addSubview:titleLb];
        [titleLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(funbtn.mas_centerX);
            make.top.equalTo(img.mas_bottom).offset(5);

        }];
    }
}
-(void)funBtnClick:(UIButton *)sender{
    NSInteger index = sender.tag - 10000;
    id aa = _funcTypeArr[index];
    FunctionType type = [aa integerValue];
    if (type == FunctionType_shop) {
        openGoodsCar.selected = !openGoodsCar.selected;
        if(openGoodsCar.selected){
            for (UIImageView *subImg in sender.subviews) {
                if(subImg.tag == sender.tag +10000){
                    subImg.image =[UIImage imageNamed:@"pre_fun_shopsel"];
                }
            }
            for (UILabel *subLb in sender.subviews) {
                if(subLb.tag == sender.tag +20000){
                    subLb.textColor = normalColors;
                }
            }

        }else{
            for (UIImageView *subImg in sender.subviews) {
                if(subImg.tag == sender.tag +10000){
                    subImg.image =[UIImage imageNamed:@"pre_fun_shop"];
                }
            }
            for (UILabel *subLb in sender.subviews) {
                if(subLb.tag == sender.tag +20000){
                    subLb.textColor = UIColor.whiteColor;
                }
            }

        }
    }else if (type == FunctionType_camera){
        [self rotateCamera];
    }else if (type == FunctionType_meiyan){
        [self showFitterView];
    }else if (type == FunctionType_share){
        [self doShareViewShow];
    }else if (type == FunctionType_roomType){
        [self dochangelivetype];
    }
}
-(void)dochangelivetype{
    NSArray *arrays = [common live_type];
    NSMutableArray *roomTypeArr = [NSMutableArray array];
    
    for (NSArray *arr in arrays) {
        NSString *typestring = arr[0];
        int types = [typestring intValue];
        switch (types) {
            case 0:
                [roomTypeArr addObject:@"普通"];
                break;
            case 1:
                [roomTypeArr addObject:@"密码"];
                break;
            case 2:
                [roomTypeArr addObject:@"门票"];
                break;
            case 3:
                [roomTypeArr addObject:@"计时"];
                break;
            default:
                break;
        }
    }
    if (!roomTypeView) {
        roomTypeView = [[UIScrollView alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, _window_height*0.15)];
        roomTypeView.backgroundColor = RGB_COLOR(@"#000000", 0.7);
        roomTypeView.contentSize = CGSizeMake(_window_width/4*roomTypeArr.count, 0);
        [_preFrontView addSubview:roomTypeView];
        CGFloat speace;
        if (roomTypeArr.count > 3) {
            speace = 0;
        }else{
            speace = (_window_width-_window_width/4*roomTypeArr.count)/2;
        }
        roomTypeBtnArray = [NSMutableArray array];
        for (int i = 0; i < roomTypeArr.count; i++) {
            UIButton *btn = [UIButton buttonWithType:0];
            btn.frame = CGRectMake(speace+i*_window_width/4, 0, _window_width/4, roomTypeView.height);
            [btn addTarget:self action:@selector(doRoomType:) forControlEvents:UIControlEventTouchUpInside];
            NSString *roomeName = [NSString stringWithFormat:@"%@房间",roomTypeArr[i]];
            [btn setTitle:YZMsg(roomeName) forState:UIControlStateSelected];
            [btn setTitle:YZMsg(roomeName) forState:UIControlStateNormal];

            [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
            [btn setTitleColor:normalColors forState:UIControlStateSelected];
            [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"room_%@_nor",roomTypeArr[i]]] forState:UIControlStateNormal];
            [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"room_%@_sel",roomTypeArr[i]]] forState:UIControlStateSelected];
            btn.titleLabel.font = [UIFont systemFontOfSize:13];
            btn.imageEdgeInsets = UIEdgeInsetsMake(7.5, _window_width/8-10, 22.5, 10);
            btn.titleEdgeInsets = UIEdgeInsetsMake(30, -22.5, 0, 0);

            if (i == 0) {
                btn.selected = YES;
            }else{
                btn.selected = NO;
            }
            [roomTypeView addSubview:btn];
            [roomTypeBtnArray addObject:btn];
        }
        [UIView animateWithDuration:0.5 animations:^{
            roomTypeView.y = _window_height*0.85;
        }];
    }else{
        [UIView animateWithDuration:0.5 animations:^{
            roomTypeView.y = _window_height*0.85;
        }];
    }
}
- (void)doRoomType:(UIButton *)sender{
    NSLog(@"%@",sender.titleLabel.text);
    if ([sender.titleLabel.text isEqual:YZMsg(@"普通房间")]) {
        [self changeRoomBtnState:YZMsg(@"普通房间")];
        roomType = @"0";
        roomTypeValue = @"";
    }
    if ([sender.titleLabel.text isEqual:YZMsg(@"密码房间")]) {
        UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"请设置房间密码")   message:@"" preferredStyle:UIAlertControllerStyleAlert];
        [alertContro addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {                textField.placeholder = YZMsg(@"请输入密码");
            textField.keyboardType = UIKeyboardTypeNumberPad;
            textField.secureTextEntry = YES;
        }];
        
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
        [alertContro addAction:cancleAction];
        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            UITextField *envirnmentNameTextField = alertContro.textFields.firstObject;
            if (envirnmentNameTextField.text == nil || envirnmentNameTextField.text == NULL || envirnmentNameTextField.text.length == 0) {
                [MBProgressHUD showError:YZMsg(@"请输入正确的密码")];
                [self presentViewController:alertContro animated:YES completion:nil];
            }else{
                roomTypeValue = envirnmentNameTextField.text;
                roomType = @"1";
                [self changeRoomBtnState:YZMsg(@"密码房间")];
            }
        }];
        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
        [alertContro addAction:sureAction];
        [self presentViewController:alertContro animated:YES completion:nil];
    }
    if ([sender.titleLabel.text isEqual:YZMsg(@"门票房间")]) {
        NSString *titleStr = [NSString stringWithFormat:@"%@\n%@",YZMsg(@"请设置收费金额"),YZMsg(@"(收益以直播结束显示为准)")];
        UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:titleStr    message:@"" preferredStyle:UIAlertControllerStyleAlert];

        [alertContro addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {                textField.placeholder = YZMsg(@"请输入价格");
            textField.keyboardType = UIKeyboardTypeNumberPad;
        }];
        
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
        [alertContro addAction:cancleAction];
        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            UITextField *envirnmentNameTextField = alertContro.textFields.firstObject;
            if (envirnmentNameTextField.text == nil || envirnmentNameTextField.text == NULL || envirnmentNameTextField.text.length == 0) {
                [MBProgressHUD showError:YZMsg(@"请输入正确的门票价格")];
                [self presentViewController:alertContro animated:YES completion:nil];
            }else{
                roomTypeValue = envirnmentNameTextField.text;
                roomType = @"2";
                [self changeRoomBtnState:YZMsg(@"门票房间")];
            }
        }];
        [sureAction setValue:normalColors forKey:@"_titleTextColor"];

        [alertContro addAction:sureAction];
        [self presentViewController:alertContro animated:YES completion:nil];

    }
    if ([sender.titleLabel.text isEqual:YZMsg(@"计时房间")]) {
        [self doupcoast];
    }
}

- (void)changeRoomBtnState:(NSString *)roomName{
    for (UIButton *btn  in roomTypeBtnArray) {
        if ([btn.titleLabel.text isEqual:roomName]) {
            btn.selected = YES;
        }else{
            btn.selected = NO;
        }
    }
    [UIView animateWithDuration:0.5 animations:^{
        roomTypeView.y = _window_height*2;
    } completion:^(BOOL finished) {
//        [preTypeBtn setTitle:roomName forState:0];
        for (UILabel *subLb in preTypeBtn.subviews) {
            if(subLb.tag == preTypeBtn.tag +20000){
                subLb.text = roomName;
            }
        }

    }];

}
- (void)showFitterView{
    _preFrontView.hidden = YES;
        if (![[common getIsTXfiter]isEqual:@"1"]) {
            [self.menusView showMenuView:YES];
        }else{
             if ([_sdkType isEqual:@"1"]) {
                [self userTXBase];
            }else{
                [self OnChoseFilter:nil];//美颜
            }
        }
}
- (void)doOpenGoodsCar:(UIButton *)sender{
    openGoodsCar.selected = !openGoodsCar.selected;
    if (openGoodsCar.selected) {
        [openGoodsCar setBackgroundColor:[UIColor whiteColor]];
    }else{
        [openGoodsCar setBackgroundColor:[[UIColor blackColor] colorWithAlphaComponent:0.4]];
    }
}
//创建房间
-(void)createroom{
    if (thumbImage) {
        YBWeakSelf;
        [[YBStorageManage shareManage]getCOSInfo:^(int code) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (code == 0) {
                    [weakSelf startCreateRoomImage];
                }
            });
        }];

    }else{
        [self startCreateRoom];
    }
}
-(void)startCreateRoomImage{
//    [MBProgressHUD showMessage:@""];
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    YBWeakSelf;
    if (thumbImage) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"duibinaf.png"];
            [[YBStorageManage shareManage]yb_storageImg:thumbImage andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                thumbUrlStr = minstr(key);
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf startCreateRoom];
        });
        NSLog(@"任务完成执行");
    });

}
-(void)startCreateRoom{
    /*
    if(_isGameLive){
        liveClassID = @"0";
    }*/
    NSString *deviceinfo = [NSString stringWithFormat:@"%@_%@_%@",[YBNetworking iphoneType],[[UIDevice currentDevice] systemVersion],[YBNetworking getNetworkType]];

    NSString *title = [liveTitleTextView.text stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    AFHTTPSessionManager *session = [AFHTTPSessionManager manager];
    
    NSString *languageStr= [PublicObj getCurrentLanguage];
    NSLog(@"ybtoo=========Str:%@", languageStr);

    NSString *url = [purl stringByAppendingFormat:@"?service=Live.createRoom&uid=%@&token=%@&user_nicename=%@&title=%@&province=%@&city=%@&lng=%@&lat=%@&type=%@&type_val=%@&liveclassid=%@&deviceinfo=%@&isshop=%d&thumb=%@&live_type=%@&language=%@",[Config getOwnID],[Config getOwnToken],[Config getOwnNicename],title,@"",            locationSwitch ? [cityDefault getMyCity] : @"",locationSwitch ? [cityDefault getMylng] : @"",locationSwitch ? [cityDefault getMylat] : @"",roomType,roomTypeValue,liveClassID,deviceinfo,openGoodsCar.selected,thumbUrlStr,@"0",languageStr];
    url = [url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    NSDictionary *subDic = @{
                             @"avatar":minstr([Config getavatar]),
                             @"avatar_thumb":minstr([Config getavatarThumb])
                             };
    [session POST:url parameters:subDic headers:nil constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
//        if (thumbImage) {
//            [formData appendPartWithFileData:[Utils compressImage:thumbImage] name:@"file" fileName:@"duibinaf.png" mimeType:@"image/jpeg"];
//        }
    } progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        [MBProgressHUD hideHUD];

        NSDictionary *data = [responseObject valueForKey:@"data"];
        NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
        if ([code isEqual:@"0"]) {
            NSDictionary *info = [[data valueForKey:@"info"] firstObject];
            jackpot_level = minstr([info valueForKey:@"jackpot_level"]);
            live_thumb = minstr([info valueForKey:@"thumb"]);
            NSString *coin = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_coin"]];
            NSString *game_banker_limit = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_limit"]];
            NSString *uname = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_name"]];
            NSString *uhead = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_avatar"]];
            NSString *uid = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_bankerid"]];
            NSDictionary *zhuangdic = @{
                                        @"coin":coin,
                                        @"game_banker_limit":game_banker_limit,
                                        @"user_nicename":uname,
                                        @"avatar":uhead,
                                        @"id":uid
                                        };
            _zhuangDic = zhuangdic;
            //声网
            _user_sw_token = minstr([info valueForKey:@"user_sw_token"]);
            NSArray *game_switch = [info valueForKey:@"game_switch"];//游戏开关
            _game_switch = game_switch;

            NSString *agorakitid = [NSString stringWithFormat:@"%@",[info valueForKey:@"agorakitid"]];
            [common saveagorakitid:agorakitid];//保存声网ID
            NSString *tx_appid = minstr([info valueForKey:@"tx_appid"]);
            [common saveTXSDKAppID:tx_appid];
            //保存靓号和vip信息
            NSDictionary *liang = [info valueForKey:@"liang"];
            NSString *liangnum = minstr([liang valueForKey:@"name"]);
            NSDictionary *vip = [info valueForKey:@"vip"];
            NSString *type = minstr([vip valueForKey:@"type"]);
            NSDictionary *subdic = [NSDictionary dictionaryWithObjects:@[type,liangnum] forKeys:@[@"vip_type",@"liang"]];
            [Config saveVipandliang:subdic];
            self.chatWordArr = [info valueForKey:@"sensitive_words"];
            _type = [roomType intValue];
            _roomDic = info;
            if (_type == 0 || _type == 2) {
                _canFee = YES;
            }else{
                _canFee = NO;
            }
            _dailytask_switch = minstr([info valueForKey:@"dailytask_switch"]);

            //移除预览UI
            [_preFrontView removeFromSuperview];
            _preFrontView = nil;
            //注册通知
            [self nsnotifition];
            //开始直播
            [self startUI];
        }else if ([code isEqual:@"700"]) {
            UIAlertController *quitAlert = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"您的登录状态失效，请重新登录！") preferredStyle:UIAlertControllerStyleAlert];
             UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
                  [[NSNotificationCenter defaultCenter] postNotificationName:@"denglushixiao" object:nil];

                 [[YBToolClass sharedInstance] quitLogin];

             }];
             [cancelAction setValue:normalColors forKey:@"_titleTextColor"];
             [quitAlert addAction:cancelAction];
             [[[MXBADelegate sharedAppDelegate] topViewController] presentViewController:quitAlert animated:YES completion:nil];
         }else{
            [MBProgressHUD showError:[data valueForKey:@"msg"]];
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showError:YZMsg(@"网络错误")];
    }];

}

//分享
- (void)share:(UIButton *)sender{
    if ([selectShareName isEqual:sender.titleLabel.text]) {
        sender.selected = NO;
        selectShareName = @"";
    }else{
        sender.selected = YES;
        selectShareName = sender.titleLabel.text;
        for (UIButton *btn in preShareBtnArray) {
            if (btn != sender) {
                btn.selected = NO;
            }
        }
    }
}
-(void)doHidden:(UIButton *)sender{
    if ([liveClassID isEqual:@"-99999999"]) {
        [MBProgressHUD showError:YZMsg(@"请选择直播内容")];
        return;
    }
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        NSLog(@"相机权限受限");
        UIAlertView *alert = [[UIAlertView alloc]initWithTitle:YZMsg(@"权限受阻") message:YZMsg(@"请在设置中开启相机权限") delegate:self cancelButtonTitle:YZMsg(@"确定") otherButtonTitles:nil, nil];
        [alert show];
        return;
    }
    [[AVAudioSession sharedInstance] requestRecordPermission:^(BOOL granted) {
        if (granted) {
            // 用户同意获取麦克风
        } else {
            UIAlertView *alert = [[UIAlertView alloc]initWithTitle:YZMsg(@"权限受阻") message:YZMsg(@"请在设置中开启麦克风权限") delegate:self cancelButtonTitle:YZMsg(@"确定") otherButtonTitles:nil, nil];
            [alert show];
            return ;
        }
    }];
    [MBProgressHUD showMessage:@""];
    if ([selectShareName isEqual:@""]) {
        [self createroom];
    }else if([selectShareName isEqual:@"qq"]){
        [self simplyShare:SSDKPlatformSubTypeQQFriend];
    }else if([selectShareName isEqual:@"qzone"]){
        [self simplyShare:SSDKPlatformSubTypeQZone];
    }else if([selectShareName isEqual:@"wx"]){
        [self simplyShare:SSDKPlatformSubTypeWechatSession];
    }else if([selectShareName isEqual:@"wchat"]){
        [self simplyShare:SSDKPlatformSubTypeWechatTimeline];
    }else if([selectShareName isEqual:@"facebook"]){
        [self simplyShare:SSDKPlatformTypeFacebook];
    }else if([selectShareName isEqual:@"twitter"]){
        [self simplyShare:SSDKPlatformTypeTwitter];
    }else if([selectShareName isEqual:@"weibo"]){
        [self simplyShare:SSDKPlatformTypeSinaWeibo];
    }
}
- (void)simplyShare:(int)SSDKPlatformType
{
    [self shareLiveRoom];

    /**
     * 在简单分享中，只要设置共有分享参数即可分享到任意的社交平台
     **/
    
    int SSDKContentType = SSDKContentTypeAuto;
    
    NSURL *ParamsURL;
    
    
    if(SSDKPlatformType == SSDKPlatformTypeSinaWeibo)
    {
        SSDKContentType = SSDKContentTypeImage;
    }
    else if((SSDKPlatformType == SSDKPlatformSubTypeWechatSession || SSDKPlatformType == SSDKPlatformSubTypeWechatTimeline))
    {
        NSString *strFullUrl = [[common wx_siteurl] stringByAppendingFormat:@"%@",[Config getOwnID]];
        ParamsURL = [NSURL URLWithString:strFullUrl];
    }else{
        
        ParamsURL = [NSURL URLWithString:[common app_ios]];
    }
    //获取我的头像
    LiveUser *user = [Config myProfile];
    //创建分享参数
    NSMutableDictionary *shareParams = [NSMutableDictionary dictionary];
//    [shareParams SSDKSetupShareParamsByText:[NSString stringWithFormat:@"%@%@",[Config getOwnNicename],[common share_des]]
//                                     images:user.avatar_thumb
//                                        url:ParamsURL
//                                      title:[common share_title]
//                                       type:SSDKContentType];
    NSString *shareDes;
    if (liveTitleTextView.text.length < 1) {
        shareDes = [common share_des];
    }else{
        shareDes = liveTitleTextView.text;
    }
    NSString *shareTitle = [common share_title];
    if (shareTitle.length > 0) {
        if ([shareTitle containsString:@"username"]) {
            shareTitle = [shareTitle stringByReplacingOccurrencesOfString:@"username" withString:[Config getOwnNicename]];
            shareTitle = [shareTitle stringByReplacingOccurrencesOfString:@"{" withString:@""];
            shareTitle = [shareTitle stringByReplacingOccurrencesOfString:@"}" withString:@""];
        }
    }

    [shareParams SSDKSetupShareParamsByText:shareDes
                                     images:user.avatar_thumb
                                        url:ParamsURL
                                      title:shareTitle
                                       type:SSDKContentType];

    [MBProgressHUD hideHUD];

    [ShareSDK share:SSDKPlatformType parameters:shareParams onStateChanged:^(SSDKResponseState state, NSDictionary *userData, SSDKContentEntity *contentEntity, NSError *error) {
        
        if (state == SSDKResponseStateSuccess) {
            [MBProgressHUD showError:YZMsg(@"分享成功")];
        }
        else if (state == SSDKResponseStateFail){
            [MBProgressHUD showError:YZMsg(@"分享失败")];
        }
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self createroom];
        });
    }];
}
-(void)shareLiveRoom{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.shareLiveRoom"];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken]
    };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];

}

- (void)hidePreTextView{
    [liveTitleTextView resignFirstResponder];
    if (roomTypeView) {
        [UIView animateWithDuration:0.5 animations:^{
            roomTypeView.y = _window_height;
        }];

    }
}
- (void)textViewDidChange:(UITextView *)textView

{
    if (textView.text.length == 0) {
        textPlaceholdLabel.text = YZMsg(@"给直播写个标题吧");
    }else{
        textPlaceholdLabel.text = @"";
    }
    
}


//选择频道
- (void)showAllClassView{
    startLiveClassVC *vc = [[startLiveClassVC alloc]init];
    vc.classID = liveClassID;
    vc.block = ^(NSDictionary * _Nonnull dic) {
        liveClassID = minstr([dic valueForKey:@"id"]);
        liveClassLabel.text = minstr([dic valueForKey:@"name"]);
    };
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    self.animator = [[ZFModalTransitionAnimator alloc] initWithModalViewController:vc];
    self.animator.bounces = NO;
    self.animator.behindViewAlpha = 1;
    self.animator.behindViewScale = 0.5f;
    self.animator.transitionDuration = 0.4f;
    vc.transitioningDelegate = self.animator;
    self.animator.dragable = YES;
    self.animator.direction = ZFModalTransitonDirectionRight;
    [self presentViewController:vc animated:YES completion:nil];

}
//选择封面
-(void)doUploadPicture{
    UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"选择上传方式") message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
    UIAlertAction *picAction = [UIAlertAction actionWithTitle:YZMsg(@"相册") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self selectThumbWithType:UIImagePickerControllerSourceTypePhotoLibrary];
    }];
    [alertContro addAction:picAction];
//    UIAlertAction *photoAction = [UIAlertAction actionWithTitle:YZMsg(@"拍照") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        if ([_sdkType isEqual:@"1"]) {
//            [_txLivePublisher stopPreview];
//        }else{
//            [_gpuStreamer stopPreview];
//        }
//        [self selectThumbWithType:UIImagePickerControllerSourceTypeCamera];
//    }];
//    [alertContro addAction:photoAction];
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
    }];
    [alertContro addAction:cancleAction];

    [self presentViewController:alertContro animated:YES completion:nil];
}
- (void)selectThumbWithType:(UIImagePickerControllerSourceType)type{
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.delegate = self;
    imagePickerController.sourceType = type;
    imagePickerController.allowsEditing = YES;
    if (type == UIImagePickerControllerSourceTypeCamera) {
        imagePickerController.showsCameraControls = YES;
        imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    }
    [self presentViewController:imagePickerController animated:YES completion:nil];
}
-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"])
    {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerEditedImage"];
        thumbImage = image;
        [preThumbBtn setImage:image forState:UIControlStateNormal];
        thumbLabel.text = YZMsg(@"更换封面");
        thumbLabel.backgroundColor = RGB_COLOR(@"#0000000", 0.3);
    }
    [picker dismissViewControllerAnimated:YES completion:nil];
    if ([_sdkType isEqual:@"1"]) {
    }else{
        //ray-声网
//        [_gpuStreamer startPreview:_pushPreview];
    }

}
-(void)imagePickerControllerDidCancel:(UIImagePickerController *)picker{
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    [picker dismissViewControllerAnimated:YES completion:nil];
    if ([_sdkType isEqual:@"1"]) {
    }else{
        //ray-声网
//        [_gpuStreamer startPreview:_pushPreview];
    }

}
- (void)navigationController:(UINavigationController *)navigationController didShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if ([UIDevice currentDevice].systemVersion.floatValue < 11) {
        return;
    }
    if ([viewController isKindOfClass:NSClassFromString(@"PUPhotoPickerHostViewController")]) {
        [viewController.view.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.frame.size.width < 42) {
                [viewController.view sendSubviewToBack:obj];
                *stop = YES;
            }
        }];
    }
}

//退出预览
- (void)doClosePreView{
    if ([_sdkType isEqual:@"1"]) {
        [self txStopRTC];
    }else{
        [[YBAgoraManager shareInstance]leaveChannel:nil];
    }
    self.isRedayCloseRoom = YES;
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"isLiveing"];
    [self.navigationController popViewControllerAnimated:YES];
}
#pragma mark ================ 预览结束 ===============

#pragma mark ================ 直播结束 ===============
- (void)requestLiveAllTimeandVotes{
    NSString *url = [NSString stringWithFormat:@"Live.stopInfo&stream=%@",minstr([_roomDic valueForKey:@"stream"])];
    [YBToolClass postNetworkWithUrl:url andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *subdic = [info firstObject];
            [self lastview:subdic];
        }else{
            [self lastview:nil];
        }
    } fail:^{
        [self lastview:nil];
    }];

}
-(void)lastview:(NSDictionary *)dic{
    //无数据都显示0
    if (!dic) {
        dic = @{@"votes":@"0",@"nums":@"0",@"length":@"0"};
    }
    UIImageView *lastView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    lastView.userInteractionEnabled = YES;
    [lastView sd_setImageWithURL:[NSURL URLWithString:[Config getavatar]]];
    UIBlurEffect *blur = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
    UIVisualEffectView *effectview = [[UIVisualEffectView alloc] initWithEffect:blur];
    effectview.frame = CGRectMake(0, 0,_window_width,_window_height);
    [lastView addSubview:effectview];
    
    
    UILabel *labell= [[UILabel alloc]initWithFrame:CGRectMake(0,24+statusbarHeight, _window_width, _window_height*0.17)];
    labell.textColor = normalColors;
    labell.text = YZMsg(@"直播已结束");
    labell.textAlignment = NSTextAlignmentCenter;
    labell.font = [UIFont fontWithName:@"Helvetica-Bold" size:20];
    [lastView addSubview:labell];
    
    UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(_window_width*0.1, labell.bottom+50, _window_width*0.8, _window_width*0.8*8/13)];
    backView.backgroundColor = RGB_COLOR(@"#000000", 0.2);
    backView.layer.cornerRadius = 5.0;
    backView.layer.masksToBounds = YES;
    [lastView addSubview:backView];
    
    UIImageView *headerImgView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width/2-50, labell.bottom, 100, 100)];
    [headerImgView sd_setImageWithURL:[NSURL URLWithString:[Config getavatar]] placeholderImage:[UIImage imageNamed:@"icon_avatar_placeholder"]];
    headerImgView.layer.masksToBounds = YES;
    headerImgView.layer.cornerRadius = 50;
    [lastView addSubview:headerImgView];

    
    UILabel *nameL= [[UILabel alloc]initWithFrame:CGRectMake(0,50, backView.width, backView.height*0.55-50)];
    nameL.textColor = [UIColor whiteColor];
    nameL.text = [Config getOwnNicename];
    nameL.textAlignment = NSTextAlignmentCenter;
    nameL.font = [UIFont fontWithName:@"Helvetica-Bold" size:18];
    [backView addSubview:nameL];

    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(10, nameL.bottom, backView.width-20, 1) andColor:RGB_COLOR(@"#585452", 1) andView:backView];
    
    NSArray *labelArray = @[YZMsg(@"直播时长"),[NSString stringWithFormat:@"%@",YZMsg(@"收获")],YZMsg(@"观看人数")];
    for (int i = 0; i < labelArray.count; i++) {
        UILabel *topLabel = [[UILabel alloc]initWithFrame:CGRectMake(i*backView.width/3, nameL.bottom, backView.width/3, backView.height/4)];
        topLabel.font = [UIFont boldSystemFontOfSize:18];
        topLabel.textColor = [UIColor whiteColor];
        topLabel.textAlignment = NSTextAlignmentCenter;
        topLabel.adjustsFontSizeToFitWidth = YES;
        topLabel.numberOfLines = 0;
        if (i == 0) {
            topLabel.text = minstr([dic valueForKey:@"length"]);
        }
        if (i == 1) {
            topLabel.text = minstr([dic valueForKey:@"votes"]);
        }
        if (i == 2) {
            topLabel.text = minstr([dic valueForKey:@"nums"]);
        }
        [backView addSubview:topLabel];
        UILabel *footLabel = [[UILabel alloc]initWithFrame:CGRectMake(topLabel.left, topLabel.bottom, topLabel.width, 14)];
        footLabel.font = [UIFont systemFontOfSize:13];
        footLabel.textColor = RGB_COLOR(@"#cacbcc", 1);
        footLabel.textAlignment = NSTextAlignmentCenter;
        footLabel.text = labelArray[i];
        [backView addSubview:footLabel];
    }
    
    
    
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.frame = CGRectMake(_window_width*0.1,_window_height *0.75, _window_width*0.8,50);
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [button addTarget:self action:@selector(docancle) forControlEvents:UIControlEventTouchUpInside];
//    [button setBackgroundColor:normalColors];
    [button setBackgroundImage:[UIImage imageNamed:@"startLive_back"]];

    [button setTitle:YZMsg(@"返回首页") forState:0];
    button.titleLabel.font = [UIFont systemFontOfSize:15];
    button.layer.cornerRadius = 25;
    button.layer.masksToBounds  =YES;
    [lastView addSubview:button];
    [self.view addSubview:lastView];
    
}
- (void)docancle{
    self.isRedayCloseRoom = YES;
//    [self.menusView clearFaceSelectedStatus];
    [UIApplication sharedApplication].idleTimerDisabled = NO;

    [self.navigationController popViewControllerAnimated:YES];

}
#pragma mark ================ 直播结束 ===============

-(void)sendBarrage:(NSDictionary *)msg{
    NSString *text = [NSString stringWithFormat:@"%@",[[msg valueForKey:@"ct"] valueForKey:@"content"]];
    NSString *name = [msg valueForKey:@"uname"];
    NSString *icon = [msg valueForKey:@"uhead"];
    NSDictionary *levelDic = [common getUserLevelMessage:[msg valueForKey:@"level"]];
    NSString *colorStr = minstr([levelDic valueForKey:@"colour"]);

    NSDictionary *userinfo = [[NSDictionary alloc] initWithObjectsAndKeys:text,@"title",name,@"name",icon,@"icon",colorStr,@"nameColor",nil];
    [danmuview setModel:userinfo];
}
-(void)sendMessage:(NSDictionary *)dic{
    dic = [YBToolClass roomChatInsertTime:dic];
    [msgList addObject:dic];
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];
}
-(void)sendDanMu:(NSDictionary *)dic{
    NSString *text = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"content"]];
    NSString *name = [dic valueForKey:@"uname"];
    NSString *icon = [dic valueForKey:@"uhead"];
    NSDictionary *levelDic = [common getUserLevelMessage:[dic valueForKey:@"level"]];
    NSString *colorStr = minstr([levelDic valueForKey:@"colour"]);

    NSDictionary *userinfo = [[NSDictionary alloc] initWithObjectsAndKeys:text,@"title",name,@"name",icon,@"icon",colorStr,@"nameColor",nil];
    if (_tx_playrtmp) {
        [self.view insertSubview:danmuview aboveSubview:_tx_playrtmp];
    }else {
        //ray-声网
        [self.view insertSubview:danmuview aboveSubview:_agordLinkView];
    }
    [danmuview setModel:userinfo];
    long totalcoin = [self.danmuPrice intValue];//
    [self addCoin:totalcoin];
}
-(void)getZombieList:(NSArray *)list{
    NSArray *arrays =[list firstObject];
    userCount += arrays.count;
//    onlineLabel.text = [NSString stringWithFormat:@"%lld",userCount];
    if(userCount > 99){
        [onlineBtn setTitle:@"99+" forState:0];
    }else{
        [onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    }

    if (!listView) {
        listView = [[ListCollection alloc]initWithListArray:list andID:[Config getOwnID] andStream:[NSString stringWithFormat:@"%@",[self.roomDic valueForKey:@"stream"]]andFixWidth:0];
        listView.delegate = self;
        listView.frame = CGRectMake(130, 20+statusbarHeight, _window_width-130-50-50-50, 40);
        listView.listCollectionview.frame =CGRectMake(0,0,_window_width- 130-50-60-50,40);
        [frontView addSubview:listView];
    }
      [listView listarrayAddArray:[list firstObject]];
}
-(void)jumpLast:(UITableView *)tableView
{
    if (_canScrollToBottom) {
    NSUInteger sectionCount = [tableView numberOfSections];
    if (sectionCount) {
        
        NSUInteger rowCount = [tableView numberOfRowsInSection:0];
        if (rowCount) {
            
            NSUInteger ii[2] = {sectionCount-1, 0};
            NSIndexPath* indexPath = [NSIndexPath indexPathWithIndexes:ii length:2];
            [tableView scrollToRowAtIndexPath:indexPath
                             atScrollPosition:UITableViewScrollPositionBottom animated:YES];
        }
    }
    
    }
}
-(void)quickSort1:(NSMutableArray *)userlist
{
    for (int i = 0; i<userlist.count; i++)
    {
        for (int j=i+1; j<[userlist count]; j++)
        {
            int aac = [[[userlist objectAtIndex:i] valueForKey:@"level"] intValue];
            int bbc = [[[userlist objectAtIndex:j] valueForKey:@"level"] intValue];
            NSDictionary *da = [NSDictionary dictionaryWithDictionary:[userlist objectAtIndex:i]];
            NSDictionary *db = [NSDictionary dictionaryWithDictionary:[userlist objectAtIndex:j]];
            if (aac >= bbc)
            {
                [userlist replaceObjectAtIndex:i withObject:da];
                [userlist replaceObjectAtIndex:j withObject:db];
            }else{
                [userlist replaceObjectAtIndex:j withObject:da];
                [userlist replaceObjectAtIndex:i withObject:db];
            }
        }
    }
}
-(void)socketUserLive:(NSString *)ID and:(NSDictionary *)msg{
    userCount -= 1;
    if(userCount > 99){
        [onlineBtn setTitle:@"99+" forState:0];
    }else{
        [onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    }

    if (listView) {
        [listView userLive:msg];
    }
    //ray-声网
//    if (_js_playrtmp) {
//        if ([ID integerValue] == _js_playrtmp.tag-1500) {
//            [_js_playrtmp stopConnect];
//            [_js_playrtmp stopPush];
//            [_js_playrtmp removeFromSuperview];
//            _js_playrtmp = nil;
//        }
//    }

}
-(void)socketUserLogin:(NSString *)ID andDic:(NSDictionary *)dic{
    userCount += 1;
    if (listView) {
        [listView userAccess:dic];
    }
    if(userCount > 99){
        [onlineBtn setTitle:@"99+" forState:0];
    }else{
        [onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    }

    NSString *vipType = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"vip_type"]];
    NSString *guard_type = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"guard_type"]];
    if ([vipType isEqual:@"1"] || [guard_type isEqual:@"1"] || [guard_type isEqual:@"2"]) {
        [useraimation addUserMove:dic];
        useraimation.frame = CGRectMake(10,self.tableView.top - 40,_window_width,20);
    }
    NSString *car_id = minstr([[dic valueForKey:@"ct"] valueForKey:@"car_id"]);
    if (![car_id isEqual:@"0"]) {
        [_viploginArray addObject:dic];
        [self addVipLogin:dic];
    }
    [self userLoginSendMSG:dic];
}
-(void)addVipLogin:(NSDictionary *)msg{
      YBWeakSelf;
      if (!vipanimation) {
          vipanimation = [[viplogin alloc]initWithFrame:CGRectMake(0,80,_window_width,_window_width*0.8) andBlock:^{
              [weakSelf.viploginArray removeObjectAtIndex:0];
              [vipanimation removeFromSuperview];
              vipanimation = nil;
              if (weakSelf.viploginArray.count > 0) {
                  [weakSelf addVipLogin:weakSelf.viploginArray[0]];
              }
          }];
          vipanimation.frame =CGRectMake(0,80,_window_width,_window_width*0.8);
          [self.view insertSubview:vipanimation atIndex:10];
          [self.view bringSubviewToFront:vipanimation];
          [vipanimation addUserMove:msg];
    }
}

//用户进入直播间发送XXX进入了直播间
-(void)userLoginSendMSG:(NSDictionary *)dic {
    titleColor = @"userLogin";
    NSString *uname = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"user_nickname"]];
    NSString *levell = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"level"]];
    NSString *ID = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"id"]];
    NSString *vip_type = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"vip_type"]];
    NSString *liangname = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"liangname"]];
    NSString *usertype = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"usertype"]];
    NSString *guard_type = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"guard_type"]];

    NSString *conttt = YZMsg(@" 进入了直播间");
    NSString *isadmin;
    if ([[NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"usertype"]] isEqual:@"40"]) {
        isadmin = @"1";
    }else{
        isadmin = @"0";
    }
    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",conttt,@"contentChat",levell,@"levelI",ID,@"id",titleColor,@"titleColor",vip_type,@"vip_type",liangname,@"liangname",usertype,@"usertype",guard_type,@"guard_type",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];
}
-(void)socketSystem:(NSString *)ct{
    titleColor = @"firstlogin";
    NSString *uname = YZMsg(@"直播间消息");
    NSString *levell = @" ";
    NSString *ID = @" ";
    NSString *vip_type = @"0";
    NSString *liangname = @"0";
    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",levell,@"levelI",ID,@"id",titleColor,@"titleColor",vip_type,@"vip_type",liangname,@"liangname",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];
}
-(void)socketLight{
    starX =_window_width - www- 10;// closeLiveBtn.frame.origin.x ;
    starY = _window_height - 45-ShowDiff-30;//closeLiveBtn.frame.origin.y - 30;
    starImage = [[UIImageView alloc]initWithFrame:CGRectMake(starX, starY, 30, 30)];
    starImage.contentMode = UIViewContentModeScaleAspectFit;
    NSMutableArray *array = [NSMutableArray arrayWithObjects:@"plane_heart_cyan.png",@"plane_heart_pink.png",@"plane_heart_red.png",@"plane_heart_yellow.png",@"plane_heart_heart.png", nil];
    NSInteger random = arc4random()%array.count;
    starImage.image = [UIImage imageNamed:[array objectAtIndex:random]];
    [UIView animateWithDuration:0.2 animations:^{
        starImage.alpha = 1.0;
        starImage.frame = CGRectMake(starX+random - 10, starY-random - 30, 30, 30);
        CGAffineTransform transfrom = CGAffineTransformMakeScale(1.3, 1.3);
        starImage.transform = CGAffineTransformScale(transfrom, 1, 1);
    }];
    [self.view insertSubview:starImage atIndex:10];
    CGFloat finishX = _window_width - round(arc4random() % 200);
    //  动画结束点的Y值
    CGFloat finishY = 200;
    //  imageView在运动过程中的缩放比例
    CGFloat scale = round(arc4random() % 2) + 0.7;
    // 生成一个作为速度参数的随机数
    CGFloat speed = 1 / round(arc4random() % 900) + 0.6;
    //  动画执行时间
    NSTimeInterval duration = 4 * speed;
    //  如果得到的时间是无穷大，就重新附一个值（这里要特别注意，请看下面的特别提醒）
    if (duration == INFINITY) duration = 2.412346;
    //  开始动画
    [UIView beginAnimations:nil context:(__bridge void *_Nullable)(starImage)];
    //  设置动画时间
    [UIView setAnimationDuration:duration];
    
    
    //  设置imageView的结束frame
    starImage.frame = CGRectMake( finishX, finishY, 30 * scale, 30 * scale);
    
    //  设置渐渐消失的效果，这里的时间最好和动画时间一致
    [UIView animateWithDuration:duration animations:^{
        starImage.alpha = 0;
    }];
    
    //  结束动画，调用onAnimationComplete:finished:context:函数
    [UIView setAnimationDidStopSelector:@selector(onAnimationComplete:finished:context:)];
    //  设置动画代理
    [UIView setAnimationDelegate:self];
    [UIView commitAnimations];
}
-(void)setStickerWithInfo:(NSDictionary *)infos andCt:(NSDictionary *)ct{
    
    isShowSticker = YES;
    NSURL *downLoadurl =[NSURL URLWithString:minstr([infos valueForKey:@"resource"])];
    NSString *name  = minstr([infos valueForKey:@"name"]);
    NSString *uptime = minstr([infos valueForKey:@"uptime"]);
    CGFloat times =[[ct valueForKey:@"swftime"] floatValue];

    // 已经下载的贴纸
    NSString *key = [NSString stringWithFormat:@"%@:%@",name,uptime];
    if ([[NSUserDefaults standardUserDefaults]objectForKey:key]) {
        [self setStickerWithKey:key andtimes:times];
    }else{
            [[self.session downloadTaskWithURL:downLoadurl completionHandler:^(NSURL *_Nullable location, NSURLResponse *_Nullable response, NSError *_Nullable error) {
                if (error) {
                } else {
                    // unzip
                    NSString *path = [[StickerManager sharedManager] unzipPath];
                    BOOL unZipSuccess = [SSZipArchive unzipFileAtPath:location.path toDestination:path delegate:self];
                    if (unZipSuccess) {
                        NSString *stickerPath = [NSString stringWithFormat:@"%@/%@/config.json",path,name];
                        NSString *content = [NSString stringWithContentsOfFile:stickerPath encoding:NSUTF8StringEncoding error:nil];
                        [[NSUserDefaults standardUserDefaults] setObject:content forKey:key];
                        [[NSUserDefaults standardUserDefaults] synchronize];
                        [self setStickerWithKey:key andtimes:times];
                    }
                }
            }] resume];
    }
}
//添加贴纸
-(void)setStickerWithKey:(NSString *)keyStr andtimes:(CGFloat)times{
    if ([[NSUserDefaults standardUserDefaults]boolForKey:HAVESTICKER]) {
        BOOL isHaveSticker = [[NSUserDefaults standardUserDefaults]boolForKey:HAVESTICKER];
        if (isHaveSticker) {
//            [self.beautyManager setSticker:keyStr];
            [self.beautyManager setSticker:keyStr withLevel:0];

            [[NSUserDefaults standardUserDefaults]setBool:YES forKey:STICKERNOTUSE];
            [[NSNotificationCenter defaultCenter] postNotificationName:STICKERNOTUSE object:nil];
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(times * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                
                if (self.sendStickerArr.count > 0) {
                    [self continueShowSticker];
                }else{
                    isShowSticker = NO;

                    NSString *key  = [[NSUserDefaults standardUserDefaults]objectForKey:STICKERKEY];
                    [self.beautyManager setSticker:key withLevel:0];
                    [[NSNotificationCenter defaultCenter] postNotificationName:STICKERCANUSE object:nil];
                    [[NSUserDefaults standardUserDefaults]setBool:NO forKey:STICKERNOTUSE];
                }
            });
        }else{
            [self.beautyManager setSticker:keyStr withLevel:0];
            [[NSNotificationCenter defaultCenter] postNotificationName:STICKERNOTUSE object:nil];
            [[NSUserDefaults standardUserDefaults]setBool:YES forKey:STICKERNOTUSE];

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(times * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if (self.sendStickerArr.count > 0) {
                    [self continueShowSticker];
                }else{
                    isShowSticker = NO;

                    [self.beautyManager setSticker:@"" withLevel:0];
                [[NSNotificationCenter defaultCenter] postNotificationName:STICKERCANUSE object:nil];
                [[NSUserDefaults standardUserDefaults]setBool:NO forKey:STICKERNOTUSE];
                }
            });
        }
    }else{
        [self.beautyManager setSticker:keyStr withLevel:0];
        [[NSUserDefaults standardUserDefaults]setBool:YES forKey:STICKERNOTUSE];
        [[NSNotificationCenter defaultCenter] postNotificationName:STICKERNOTUSE object:nil];

        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(times * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (self.sendStickerArr.count > 0) {
                [self continueShowSticker];
            }else{
                isShowSticker = NO;

                [self.beautyManager setSticker:@"" withLevel:0];
            [[NSNotificationCenter defaultCenter] postNotificationName:STICKERCANUSE object:nil];
            [[NSUserDefaults standardUserDefaults]setBool:NO forKey:STICKERNOTUSE];
            }
        });
    }
}
-(void)continueShowSticker{
    NSDictionary *dic =  [self.sendStickerArr firstObject];
    NSDictionary *ct = [self.sendStickerDic firstObject];

    [self.sendStickerArr removeObjectAtIndex:0];
    [self.sendStickerDic removeObjectAtIndex:0];
    
    [self setStickerWithInfo:dic andCt:ct];
}
//全站礼物
-(void)sendAllStationsGift:(NSDictionary *)msg
{
//    [self expensiveGift:msg isPlatGift:YES];
    [self platGift:msg];
}
-(void)sendGift:(NSDictionary *)msg{
    titleColor = @"2";

    NSString *haohualiwuss =  [NSString stringWithFormat:@"%@",[msg valueForKey:@"evensend"]];
    NSDictionary *ct = [msg valueForKey:@"ct"];
    NSString *stickerid = minstr([ct valueForKey:@"sticker_id"]);
    _voteNums = minstr([ct valueForKey:@"votestotal"]);
    [self changeState];

    if (![stickerid isEqual:@"0"]) {
        for (NSDictionary *dic in self.stickerArr) {
            if ([[dic valueForKey:@"id"] isEqual:stickerid]) {
                
                [self.sendStickerArr addObject:dic];
                [self.sendStickerDic addObject:ct];
            }
        }
        if (!isShowSticker) {
            NSDictionary *dic =  [self.sendStickerArr firstObject];
            NSDictionary *ct = [self.sendStickerDic firstObject];

            [self.sendStickerArr removeObjectAtIndex:0];
            [self.sendStickerDic removeObjectAtIndex:0];

            [self setStickerWithInfo:dic andCt:ct];
        }

    }else{
        NSDictionary *GiftInfo = @{@"uid":[msg valueForKey:@"uid"],
                                   @"nicename":[msg valueForKey:@"uname"],
                                   @"giftname":[ct valueForKey:@"giftname"],
                                   @"gifticon":[ct valueForKey:@"gifticon"],
                                   @"giftcount":[ct valueForKey:@"giftcount"],
                                   @"giftid":[ct valueForKey:@"giftid"],
                                   @"level":[msg valueForKey:@"level"],
                                   @"avatar":[msg valueForKey:@"uhead"],
                                   @"type":[ct valueForKey:@"type"],
                                   @"swf":minstr([ct valueForKey:@"swf"]),
                                   @"swftime":minstr([ct valueForKey:@"swftime"]),
                                   @"swftype":minstr([ct valueForKey:@"swftype"]),
                                   @"isluck":minstr([ct valueForKey:@"isluck"]),
                                   @"lucktimes":minstr([ct valueForKey:@"lucktimes"]),
                                   @"mark":minstr([ct valueForKey:@"mark"]),
                                   @"isplatgift":minstr([ct valueForKey:@"isplatgift"]),
                                   @"paintedWidth":minstr([msg valueForKey:@"paintedWidth"]),
                                   @"paintedHeight":minstr([msg valueForKey:@"paintedHeight"]),
                                   @"paintedPath":[msg valueForKey:@"paintedPath"],
                                   };

        if ([minstr([ct valueForKey:@"type"]) isEqual:@"1"]) {
            [self expensiveGift:GiftInfo isPlatGift:NO];
        }else{
            if (!continueGifts) {
                continueGifts = [[continueGift alloc]init];
                [liansongliwubottomview addSubview:continueGifts];
                //初始化礼物空位
                [continueGifts initGift];
                YBWeakSelf;
                continueGifts.rkPaintedEvent = ^(NSDictionary *giftDic) {
                    [weakSelf showPaintedGift:giftDic];
                };
            }
            if (isAnchorLink) {
                //ray-声网
                [self.view insertSubview:liansongliwubottomview aboveSubview:[_sdkType isEqual:@"1"]?_tx_playrtmp:_agordLinkView];
            }

            if (pkView) {
                [self.view insertSubview:liansongliwubottomview aboveSubview:pkView];
            }
            [continueGifts GiftPopView:GiftInfo andLianSong:haohualiwuss ischat:NO andUser:@"" andTouser:@""];
        }
    }
//聊天区域显示送礼物去除
    NSString *ctt;
    if([[PublicObj getCurrentLanguage]isEqual:EN]){
        ctt= [NSString stringWithFormat:@"%@ %@ %@ %@",YZMsg(@"送"),[ct valueForKey:@"giftcount"],YZMsg(@"个"),[ct valueForKey:@"giftname_en"]];
    }else{
        ctt= [NSString stringWithFormat:@"%@%@%@%@",YZMsg(@"送"),[ct valueForKey:@"giftcount"],YZMsg(@"个"),[ct valueForKey:@"giftname"]];
    }
    NSString* uname = [msg valueForKey:@"uname"];
    NSString *levell = [msg valueForKey:@"level"];
    NSString *ID = [msg valueForKey:@"uid"];
    NSString *avatar = [msg valueForKey:@"uhead"];
    NSString *vip_type =minstr([msg valueForKey:@"vip_type"]);
    NSString *liangname =minstr([msg valueForKey:@"liangname"]);

    NSDictionary *chat6 = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ctt,@"contentChat",levell,@"levelI",ID,@"id",@"#FF6131",@"titleColor",avatar,@"avatar",vip_type,@"vip_type",liangname,@"liangname",nil];
    chat6 = [YBToolClass roomChatInsertTime:chat6];
    [msgList addObject:chat6];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];
    
}

-(void)showPaintedGift:(NSDictionary *)giftDic {
    [self.view bringSubviewToFront:_paintedShowRegion];
    //手绘显示动画
    _paintedShowRegion.giftPathStr = minstr([giftDic valueForKey:@"gifticon"]);
    _paintedShowRegion.paintedWidth = [minstr([giftDic valueForKey:@"paintedWidth"]) floatValue];
    _paintedShowRegion.paintedHeight = [minstr([giftDic valueForKey:@"paintedHeight"]) floatValue];
    _paintedShowRegion.paintedPointArray = [NSArray arrayWithArray:[giftDic valueForKey:@"paintedPath"]];
}

//懒加载
-(NSArray *)chatModels{
    NSMutableArray *array = [NSMutableArray array];
    for (NSDictionary *dic in msgList) {
        chatModel *model = [chatModel modelWithDic:dic];
        [model setChatFrame:[_chatModels lastObject]];
        [array addObject:model];
    }
    _chatModels = array;
    return _chatModels;
}
-(void)socketShutUp:(NSString *)name andID:(NSString *)ID andType:(NSString *)type{
    [socketL shutUp:ID andName:name andType:type];
}
-(void)socketkickuser:(NSString *)name andID:(NSString *)ID{
    [socketL kickuser:ID andName:name];
}
static int startKeyboard = 0;

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView{
    _canScrollToBottom = NO;
}
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    _canScrollToBottom = YES;
}
-(void)chushihua{

    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"isLiveing"];
    [gameState saveProfile:@"0"];//重置游戏状态
    self.isRedayCloseRoom = NO;
    backTime = 0;
    _canScrollToBottom = YES;
    _voteNums = @"0";//主播一开始的收获数
    userCount = 0;//用户人数计算
    haohualiwuV.expensiveGiftCount = [NSMutableArray array];//豪华礼物数组
    platliwuV.expensiveGiftCount = [NSMutableArray array];
    titleColor = @"0";//用此字段来判别文字颜色
    msgList = [[NSMutableArray alloc] init];//聊天数组
    _chatModels = [NSArray array];//聊天模型
    _chatWordArr = [NSArray array];//屏蔽词集合
    ismessgaeshow = NO;
    isLianmai = NO;
    //预览界面的信息
    liveClassID = @"-99999999";
    roomType = @"0";
    roomTypeValue = @"";
    selectShareName = @"";
    thumbUrlStr = @"";
    thumbImage = nil;
    isAnchorLink = NO;
    isTorch = NO;
    _isFront = YES;
    _isFrontCamera = YES;
    userCanLink = NO;
    self.stickerArr = [NSArray array];
    self.sendStickerArr = [NSMutableArray array];
    self.sendStickerDic = [NSMutableArray array];
    _liveGoodTipArr = [NSMutableArray array];
    _viploginArray = [NSMutableArray array];
    if (![[common getIsTXfiter]isEqual:@"1"]) {
        [self initMeiyanFaceUI];
    }
    _user_sw_token = @"";
}
-(void)viewWillAppear:(BOOL)animated{
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    self.navigationController.navigationBarHidden = YES;
    self.navigationController.interactivePopGestureRecognizer.delegate =nil;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        self.unRead =[[YBImManager shareInstance]getAllUnreadNum];
        [self labeiHid];
    });
    if (huanxinviews != nil && tChatsamall != nil) {
        huanxinviews.view.frame = CGRectMake(0, _window_height*5, _window_width, _window_height*0.4);
        tChatsamall.view.frame = CGRectMake(0, _window_height*5, _window_width, _window_height*0.4);
    }
}
-(void)getUnreadCount{
    [self labeiHid];
}

-(void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}
-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    self.view.backgroundColor = [UIColor whiteColor];
    //弹出相机权限
    [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
     
    }];
    //弹出麦克风权限
    [AVCaptureDevice requestAccessForMediaType:AVMediaTypeAudio completionHandler:^(BOOL granted) {
      
    }];
    isclosenetwork = NO;

    [self chushihua];
    [self setBgAndPreview];

    if ([_sdkType isEqual:@"1"]) {
        //腾讯
        [self txRTCPush];
    }else{
        [YBAgoraManager shareInstance].broadcastIDStr  = [Config getOwnID];
        //声网
        isMirror = YES;
        [YBAgoraManager shareInstance].AgoraRole = AgoraClientRoleBroadcaster;
        [[YBAgoraManager shareInstance] showWithCanvasView:_pushPreview andUserId:[Config getOwnID]isBroadcaster:AgoraClientRoleBroadcaster RenderMode:AgoraVideoRenderModeHidden];
        [YBAgoraManager shareInstance].delegate = self;
    }
    __block Livebroadcast *weakself = self;
    managerAFH = [AFNetworkReachabilityManager sharedManager];
    [managerAFH setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
        switch (status) {
            case AFNetworkReachabilityStatusUnknown:
                NSLog(@"未识别的网络");
                isclosenetwork = YES;
                [weakself backGround];
                break;
            case AFNetworkReachabilityStatusNotReachable:
                NSLog(@"不可达的网络(未连接)");
                isclosenetwork = YES;
                [weakself backGround];
                break;
            case  AFNetworkReachabilityStatusReachableViaWWAN:
                isclosenetwork = NO;
                [weakself forwardGround];
                if (weakself.roomDic) {
                    [weakself checkLiveingStatus];
                }

                break;
            case AFNetworkReachabilityStatusReachableViaWiFi:
                isclosenetwork = NO;
                if (weakself.roomDic) {
                    [weakself checkLiveingStatus];
                }
                [weakself forwardGround];
                break;
            default:
                break;
        }
    }];
    [managerAFH startMonitoring];
#pragma mark 回到后台+来电话
    self.callCenter = [CTCallCenter new];
    
    self.callCenter.callEventHandler = ^(CTCall *call) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([call.callState isEqualToString:CTCallStateDialing]) {
                NSLog(@"电话主动拨打电话");
                [weakself reciverPhoneCall];
            } else if ([call.callState isEqualToString:CTCallStateConnected]) {
                NSLog(@"电话接通");
                [weakself reciverPhoneCall];
            } else if ([call.callState isEqualToString:CTCallStateDisconnected]) {
                NSLog(@"电话挂断");
                [weakself phoneCallEnd];
            } else if ([call.callState isEqualToString:CTCallStateIncoming]) {
                NSLog(@"电话被叫");
                [weakself reciverPhoneCall];
            } else {
                NSLog(@"电话其他状态");
            }
        });
    };
    
    [MBProgressHUD hideHUD];
    [self creatPreFrontView];
    //获取所有未读消息
    [[V2TIMManager sharedInstance] addConversationListener:self];

    NSInteger deviceType = [PublicObj getDeviceType];
    //iPhone6s
    if (deviceType >= 8){
        _needScale = NO;
    }else{
        _needScale = YES;
    }
}
//杀进程
-(void)shajincheng{
    if (self.menusView) {
        [self.menusView removeFromSuperview];
        self.menusView = nil;
//        [self.beautyManager destroy];
//        self.beautyManager = nil;
    }
    [[YBLiveRTCManager shareInstance]endlive];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self getCloseShow];
        [socketL closeRoom];
        [socketL colseSocket];
        [self clearSticker];
    });
}
-(void)backgroundselector{
    backTime +=1;
    NSLog(@"返回后台时间%d",backTime);
    if (backTime > 60) {
        [self hostStopRoom];
    }
}
-(void)backGround{
    if (!_isGameLive) {
        //进入后台
        if (!backGroundTimer) {
            [self sendEmccBack];
            backGroundTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(backgroundselector) userInfo:nil repeats:YES];
        }
    }
}
-(void)forwardGround{
    if (backTime != 0) {
        [socketL phoneCall:@"主播回来了"];
    }
    //进入前台
    if (backTime > 60) {
        [self hostStopRoom];
    }
    if (isclosenetwork == NO) {
        [backGroundTimer invalidate];
        backGroundTimer  = nil;
        backTime = 0;
    }
}
-(void)appactive{
    if(!_isGameLive){
        NSLog(@"哈哈哈哈哈哈哈哈哈哈哈哈 app回到前台");
        if ([_sdkType isEqual:@"1"]) {
            [[YBLiveRTCManager shareInstance]resumePush];
        }else {
            //ray-声网
            [[YBAgoraManager shareInstance]resumePush];
        }
        [self forwardGround];
    }
}
-(void)appnoactive{
    if(!_isGameLive){
        if ([_sdkType isEqual:@"1"]) {
            [[YBLiveRTCManager shareInstance]pausePush];
        }else{
            [[YBAgoraManager shareInstance]pausePush];
        }

        [self backGround];
        NSLog(@"0000000000000000000 app进入后台");
    }
}
//来电话
-(void)sendEmccBack {
    [socketL phoneCall:@"主播离开一下，精彩不中断，不要走开哦"];
}
-(void)startUI{
    frontView = [[UIView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height)];
    frontView.clipsToBounds = YES;
    [self.view addSubview:frontView];
    [self.view insertSubview:frontView atIndex:3];

    listView = [[ListCollection alloc]initWithListArray:nil andID:[Config getOwnID] andStream:[NSString stringWithFormat:@"%@",[_roomDic valueForKey:@"stream"]]andFixWidth:0];
    listView.frame = CGRectMake(130,20+statusbarHeight,_window_width-130-50-50,40);
    listView.listCollectionview.frame =CGRectMake(0,0,_window_width- 130-60-50,40);

    listView.delegate = self;
    [frontView addSubview:listView];
    dispatch_async(dispatch_get_main_queue(), ^{
        [self setView];//加载信息页面
    });
    //倒计时动画
    backView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    backView.opaque = YES;
    label1 = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2 -100, _window_height/2-200, 100, 100)];
    label1.textColor = [UIColor whiteColor];
    label1.font = [UIFont systemFontOfSize:90];
    label1.text = @"3";
    label1.textAlignment = NSTextAlignmentCenter;
    label1.center = backView.center;
    label2 = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2 -100, _window_height/2-200, 100, 100)];
    label2.textColor = [UIColor whiteColor];
    label2.font = [UIFont systemFontOfSize:90];
    label2.text = @"2";
    label2.textAlignment = NSTextAlignmentCenter;
    label2.center = backView.center;
    label3 = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2 -100, _window_height/2-200, 100, 100)];
    label3.textColor = [UIColor whiteColor];
    label3.font = [UIFont systemFontOfSize:90];
    label3.text = @"1";
    label3.textAlignment = NSTextAlignmentCenter;
    label3.center = backView.center;
    label1.hidden = YES;
    label2.hidden = YES;
    label3.hidden = YES;
    [backView addSubview:label3];
    [backView addSubview:label1];
    [backView addSubview:label2];
    [frontView addSubview:backView];
    [self creatLiveTimeView];
    [self kaishidonghua];
    self.view.backgroundColor = [UIColor clearColor];
}
//开始321
-(void)kaishidonghua{
    [self hideBTN];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        label1.hidden = NO;
        [self donghua:label1];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        label1.hidden = YES;
        label2.hidden = NO;
        [self donghua:label2];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        label2.hidden = YES;
        label3.hidden = NO;
        [self donghua:label3];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        label3.hidden = YES;
        backView.hidden = YES;
        [backView removeFromSuperview];
        [self showBTN];
        [self getStartShow];//请求直播
    });
}

-(void)setAgoMeiYanData:(NSString *)type{
    NSLog(@"基础美颜=======：%@",type);
}
/* ray-声网
//设置美颜
-(void)setMeiYanData:(int)buttonIndex
{
    switch (buttonIndex) {
        case 0:
        {
            _filter = [[KSYGPUBeautifyExtFilter alloc] init];
        }
            break;
        case 1:
        {
            _filter = [[KSYGPUBeautifyFilter alloc] init];
        }
            break;
        case 2:
        {
            _filter = [[KSYGPUDnoiseFilter alloc] init];
        }
            break;
        case 3:
        {
            _filter = [[KSYGPUBeautifyPlusFilter alloc] init];
        }
            break;
        default:
            _filter = nil;
            _filter = [[KSYGPUFilter alloc] init];
            break;
    }

    [_gpuStreamer setupFilter:_filter];

}
 */
-(void)sliderValueChanged:(float)slider
{
    [[YBAgoraManager shareInstance]changeBeautyValue:slider/10];
}
-(void)hidebeautifulgirl
{
    beautifulgirl.hidden = YES;
}
//ray-声网
//- (void) addObservers {
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(onStreamStateChange:)
//                                                 name:KSYStreamStateDidChangeNotification
//                                               object:nil];
//    
//    
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(onNetStateEvent:)
//                                                 name:KSYNetStateEventNotification
//                                               object:nil];
//}
- (void)dealloc
{
    //ray-声网
//    [[NSNotificationCenter defaultCenter] removeObserver:self];
//    [[NSNotificationCenter defaultCenter] removeObserver:self name:kKSYReachabilityChangedNotification object:nil];
    [backGroundTimer invalidate];
    backGroundTimer  = nil;
    NSLog(@"1212121212121212121");

}
//释放通知
- (void) rmObservers {
    //ray-声网
//    [[NSNotificationCenter defaultCenter] removeObserver:self
//                                                    name:KSYStreamStateDidChangeNotification
//                                                  object:nil];
//    [[NSNotificationCenter defaultCenter] removeObserver:self
//                                                    name:KSYNetStateEventNotification
//                                                  object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
    
//    [[NSNotificationCenter defaultCenter] removeObserver:@"wangminxindemusicplay"];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"wangminxindemusicplay" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"shajincheng" object:nil];
    //shajincheng
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"sixinok" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"fenxiang" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"denglushixiao" object:nil];
    
    [[NSNotificationCenter defaultCenter]removeObserver:self name:ybChatRefresh object:nil];

}
#pragma mark - UI responde
- (void)onQuit:(id)sender {
    //ray-声网
//    if (_filter) {
//        _filter = nil;
//    }
//    [_gpuStreamer stopPreview];
//    _gpuStreamer = nil;
    if (![_sdkType isEqual:@"1"]) {
        [[YBAgoraManager shareInstance]leaveChannel:nil];
    }
}
////美颜按钮点击事件
-(void)OnChoseFilter:(id)sender {
    [[YBAgoraManager shareInstance] setBeautyEffectOptions:YES options:nil];

    if (!beautifulgirl) {
        __weak Livebroadcast *weakself = self;
           beautifulgirl = [[beautifulview alloc]initWithFrame:self.view.bounds andhide:^(NSString *type) {
               [weakself hidebeautifulgirl];
               if (_preFrontView) {
                   _preFrontView.hidden = NO;
               }
          } andslider:^(NSString *type) {
               [weakself sliderValueChanged:[type floatValue]];
          } andtype:^(NSString *type) {
              //ray-声网
//              [weakself setAgoMeiYanData:type];
              if ([type intValue] == 5) {
                  [weakself sliderValueChanged:0];
              }
        }];
        [self.view addSubview:beautifulgirl];
    }
    beautifulgirl.hidden = NO;
    [self.view bringSubviewToFront:beautifulgirl];
}
//推流成功后更新直播状态 1开播
-(void)changePlayState:(int)status{
    NSDictionary *changelive = @{
                                 @"stream":urlStrtimestring,
                                 @"status":[NSString stringWithFormat:@"%d",status]
                                 };
    [YBToolClass postNetworkWithUrl:@"Live.changeLive" andParameter:changelive success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
//            [self creatLiveTimeView];
        }
    } fail:^{
        
    }];
}
#pragma mark ================ 直播时长的view ===============
- (void)creatLiveTimeView{
    
    liveTimeBGView = [[UIView alloc]initWithFrame:CGRectMake(10, 30+leftW +statusbarHeight+25, 60, 20)];
    liveTimeBGView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.2];
    liveTimeBGView.layer.cornerRadius = 10.0;
    liveTimeBGView.layer.masksToBounds = YES;
    [frontView addSubview:liveTimeBGView];
    UIView *pointView = [[UIView alloc]initWithFrame:CGRectMake(10, 8.5, 3, 3)];
    pointView.backgroundColor = normalColors;
    pointView.layer.cornerRadius = 1.5;
    pointView.layer.masksToBounds = YES;
    [liveTimeBGView addSubview:pointView];

    liveTimeLabel = [[UILabel alloc]initWithFrame:CGRectMake(pointView.right+3, 0, 31, 20)];
    liveTimeLabel.textColor = [UIColor whiteColor];
    liveTimeLabel.font = [UIFont systemFontOfSize:10];
    liveTimeLabel.textAlignment = NSTextAlignmentCenter;
    liveTimeLabel.text = @"00:00";
    [liveTimeBGView addSubview:liveTimeLabel];
    liveTime = 0;
    if (!liveTimer) {
        liveTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(liveTimeChange) userInfo:nil repeats:YES];
    }
}
- (void)liveTimeChange{
    liveTime ++;
    if (liveTime < 3600) {
        liveTimeLabel.text = [NSString stringWithFormat:@"%02d:%02d",liveTime/60,liveTime%60];
    }else{
        if (liveTimeBGView.width < 73) {
            liveTimeBGView.width = 73;
            liveTimeLabel.width = 44;
        }
        liveTimeLabel.text = [NSString stringWithFormat:@"%02d:%02d:%02d",liveTime/3600,(liveTime%3600)/60,(liveTime%3600)%60];
    }
}
#pragma mark - state handle
/* ray-声网
- (void) onStreamError {
    if (1 ) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [_gpuStreamer.streamerBase stopStream];
            [_gpuStreamer.streamerBase startStream:[NSURL URLWithString:_hostURL]];
        });
    }
}
- (void) onNetStateEvent:(NSNotification *)notification {
    KSYNetStateCode netEvent = _gpuStreamer.streamerBase.netStateCode;
    //NSLog(@"net event : %ld", (unsigned long)netEvent );
    if ( netEvent == KSYNetStateCode_SEND_PACKET_SLOW ) {
      
        NSLog(@"发送包时间过长，( 单次发送超过 500毫秒 ）");
    }
    else if ( netEvent == KSYNetStateCode_EST_BW_RAISE ) {
  
        NSLog(@"估计带宽调整，上调" );
    }
    else if ( netEvent == KSYNetStateCode_EST_BW_DROP ) {

        
        NSLog(@"估计带宽调整，下调" );
    }
    else if ( netEvent == KSYNetStateCode_KSYAUTHFAILED ) {
        
        NSLog(@"SDK 鉴权失败 (暂时正常推流5~8分钟后终止推流)" );
    }
}
- (void) onStreamStateChange:(NSNotification *)notification {
    if ( _gpuStreamer.streamerBase.streamState == KSYStreamStateIdle) {
        NSLog(@"推流状态:初始化时状态为空闲");
    }
    else if ( _gpuStreamer.streamerBase.streamState == KSYStreamStateConnected){
        NSLog(@"推流状态:已连接");
        [self changePlayState:1];//推流成功后改变直播状态
        if (_gpuStreamer.streamerBase.streamErrorCode == KSYStreamErrorCode_KSYAUTHFAILED ) {
            //(obsolete)
            NSLog(@"推流错误:(obsolete)");
        }
    }
    else if (_gpuStreamer.streamerBase.streamState == KSYStreamStateConnecting ) {
        NSLog(@"推流状态:连接中");
    }
    else if (_gpuStreamer.streamerBase.streamState == KSYStreamStateDisconnecting ) {
        NSLog(@"推流状态:断开连接中");
        [self onStreamError];
    }
    else if (_gpuStreamer.streamerBase.streamState == KSYStreamStateError ) {
        NSLog(@"推流状态:推流出错");
        [self onStreamError];
        return;
    }
}
 */
//直播结束选择 alertview
- (void)onQuit {
    
    
    UIAlertController  *alertlianmaiVCtc = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"是否要结束直播？") preferredStyle:UIAlertControllerStyleAlert];
    //修改按钮的颜色，同上可以使用同样的方法修改内容，样式
    UIAlertAction *defaultActionss = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
         [self hostStopRoom];
    }];

    UIAlertAction *cancelActionss = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }];

    
    NSString *version = [UIDevice currentDevice].systemVersion;
    
    
    if (version.doubleValue < 9.0) {
        
    }
    else{
        [defaultActionss setValue:normalColors forKey:@"_titleTextColor"];
        [cancelActionss setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    }
    
    [alertlianmaiVCtc addAction:defaultActionss];
    [alertlianmaiVCtc addAction:cancelActionss];
    [self presentViewController:alertlianmaiVCtc animated:YES completion:nil];

    
}

//关闭直播做的操作
-(void)hostStopRoom{
    [self getCloseShow];//请求关闭直播接口
}
//直播结束时 停止所有计时器
-(void)liveOver{
    if (lrcTimer) {
        [lrcTimer invalidate];
        lrcTimer = nil;
    }
    if (backGroundTimer) {
        [backGroundTimer invalidate];
        backGroundTimer  = nil;
    }
    if (listTimer) {
        [listTimer invalidate];
        listTimer = nil;
    }
    if (liveTimer) {
        [liveTimer invalidate];
        liveTimer = nil;
    }
    if (hartTimer) {
        [hartTimer invalidate];
        hartTimer = nil;
    }
    if (liveingTimer) {
        [liveingTimer invalidate];
        liveingTimer  = nil;
    }
}
//直播结束时退出房间
-(void)dismissVC{
    if ([_sdkType isEqual:@"1"]) {
//        [self txStopRtmp];
        [[YBLiveRTCManager shareInstance]endlive];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self txStopRTC];
        });
    }else{
        //ray-声网
        if (_isGameLive) {
            [[YBAgoraManager shareInstance]stopScreenCapture];
        }        
        [[YBAgoraManager shareInstance]stopPushView];
        [[YBAgoraManager shareInstance]leaveChannel:nil];
    }
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"isLiveing"];
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationDidBecomeActiveNotification object:nil];
     [managerAFH stopMonitoring];
    if (bottombtnV) {
        [bottombtnV removeFromSuperview];
        bottombtnV = nil;
    }
    [buttleView removeFromSuperview];
    buttleView = nil;
    [managerAFH stopMonitoring];
    managerAFH = nil;
    if (continueGifts) {
        [continueGifts stopTimerAndArray];
        [continueGifts initGift];
        [continueGifts removeFromSuperview];
        continueGifts = nil;
    }
    if (_paintedShowRegion) {
        [_paintedShowRegion destroyPaitend];
    }

    if (haohualiwuV) {
        [haohualiwuV stopHaoHUaLiwu];
        [haohualiwuV removeFromSuperview];
        haohualiwuV.expensiveGiftCount = nil;
    }
    if (platliwuV) {
        [platliwuV stopHaoHUaLiwu];
        [platliwuV removeFromSuperview];
        platliwuV.expensiveGiftCount = nil;
    }
//    if (_js_playrtmp) {
//        [_js_playrtmp stopConnect];
//        [_js_playrtmp stopPush];
//        [_js_playrtmp removeFromSuperview];
//        _js_playrtmp = nil;
//    }
    if (_tx_playrtmp) {
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
//    if (self.beautyManager) {
//        [self.beautyManager destroy];
//    }
    if (self.menusView) {
        [self.menusView removeFromSuperview];
        self.menusView = nil;
    }
    if (zhuangVC) {
        [zhuangVC dismissroom];
        [zhuangVC removeall];
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (gameVC) {
        [gameVC stopGame];
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
    }
    if (rotationV) {
        [rotationV stopRotatipnGameInt];
        [rotationV stoplasttimer];
        [socketL stopRotationGame];//关闭游戏socket
        [rotationV removeFromSuperview];
        [rotationV removeall];
        rotationV = nil;
    }
    
    NSDictionary *subdic = [NSDictionary dictionaryWithObjects:@[minstr(urlStrtimestring)] forKeys:@[@"stream"]];
    [[NSNotificationCenter defaultCenter]  postNotificationName:@"coin" object:nil userInfo:subdic];
}
/***********  以上推流  *************/
/***************     以下是信息页面          **************/
//加载信息页面
-(void)zhuboMessage{
    [self showButtleView:[Config getOwnID]];
}
-(void)GetInformessage:(NSDictionary *)subdic{
    [self showButtleView:[NSString stringWithFormat:@"%@",[subdic valueForKey:@"id"]]];
}
/// 动画完后销毁iamgeView
- (void)onAnimationComplete:(NSString *)animationID finished:(NSNumber *)finished context:(void *)context{
    UIImageView *imageViewsss = (__bridge UIImageView *)(context);
    [imageViewsss removeFromSuperview];
    imageViewsss = nil;
}
-(void)setView{
  
    //左上角 直播live
    leftView = [[UIView alloc]initWithFrame:CGRectMake(10,25+statusbarHeight,115,leftW)];
    leftView.layer.cornerRadius = leftW/2;
    leftView.backgroundColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.6];
    //主播头像button
    UIButton *IconBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    [IconBTN addTarget:self action:@selector(zhuboMessage) forControlEvents:UIControlEventTouchUpInside];
    IconBTN.frame = CGRectMake(1, 1, leftW-2, leftW-2);
    IconBTN.layer.masksToBounds = YES;
//    IconBTN.layer.borderWidth = 1;
//    IconBTN.layer.borderColor = normalColors.CGColor;
    IconBTN.layer.cornerRadius = leftW/2-1;
    UITapGestureRecognizer *tapleft = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(zhuboMessage)];
    tapleft.numberOfTapsRequired = 1;
    tapleft.numberOfTouchesRequired = 1;
    [leftView addGestureRecognizer:tapleft];
    LiveUser *user = [[LiveUser alloc]init];
    user = [Config myProfile];
    NSString *path = user.avatar;
    NSURL *url = [NSURL URLWithString:path];
    [IconBTN sd_setBackgroundImageWithURL:url forState:UIControlStateNormal placeholderImage:[UIImage imageNamed:@"default_head.png"]];
    
    
    UIImageView *levelimage = [[UIImageView alloc]initWithFrame:CGRectMake(IconBTN.right - 15,IconBTN.bottom - 15,15,15)];
    NSDictionary *levelDic = [common getAnchorLevelMessage:[Config level_anchor]];
    [levelimage sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic valueForKey:@"thumb_mark"])]];

    
    //直播live
    UILabel *liveLabel = [[UILabel alloc]initWithFrame:CGRectMake(leftW+4,0,65,leftW/2)];
    liveLabel.textAlignment = NSTextAlignmentLeft;
    liveLabel.text = [Config getOwnNicename];
    liveLabel.textColor = [UIColor whiteColor];
    //liveLabel.shadowColor = [UIColor lightGrayColor];
    liveLabel.shadowOffset = CGSizeMake(1,1);//设置阴影
    liveLabel.font =[UIFont systemFontOfSize:13];// fontMT(13);
    //在线人数
    onlineLabel = [[UILabel alloc]init];
    onlineLabel.frame = CGRectMake(leftW+4,leftW/2,65,leftW/2);
    onlineLabel.textAlignment = NSTextAlignmentLeft;
    onlineLabel.textColor = [UIColor whiteColor];
    onlineLabel.font = [UIFont systemFontOfSize:10];//fontMT(10);
    NSString *liangname = [NSString stringWithFormat:@"%@",[[_roomDic valueForKey:@"liang"] valueForKey:@"name"]];
    if ([liangname isEqual:@"0"]) {
        onlineLabel.text = [NSString stringWithFormat:@"ID:%@",[Config getOwnID]];
    }else{
        onlineLabel.text = [NSString stringWithFormat:@"%@:%@",YZMsg(@"靓"),liangname];
    }
    
    onlineBtn = [UIButton buttonWithType:0];
    onlineBtn.frame = CGRectMake(_window_width-50-50, 20+statusbarHeight, 40, 40);
    onlineBtn.backgroundColor = RGBA(1, 1, 1, 0.4);
    [onlineBtn setTitleColor:UIColor.whiteColor forState:0];
    onlineBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    onlineBtn.layer.cornerRadius = 20;
    onlineBtn.layer.masksToBounds = YES;
    onlineBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
    [onlineBtn setTitle:@"0" forState:0];
    [onlineBtn addTarget:self action:@selector(onlineBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [frontView addSubview:onlineBtn];
    
    //聊天
    self.tableView = [[UITableView alloc]initWithFrame:CGRectMake(10, _window_height - _window_height*0.2 - 50 - ShowDiff,tableWidth,_window_height*0.2) style:UITableViewStylePlain];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.clipsToBounds = YES;
    self.tableView.estimatedRowHeight = 80.0;
    //输入框
    keyField = [[UITextField alloc]initWithFrame:CGRectMake(70,7,_window_width-90 - 50, 30)];
    keyField.returnKeyType = UIReturnKeySend;
    keyField.delegate  = self;
    keyField.borderStyle = UITextBorderStyleNone;
    keyField.placeholder = YZMsg(@"和大家说些什么");
    keyField.backgroundColor = [UIColor whiteColor];
    keyField.layer.cornerRadius = 15.0;
    keyField.layer.masksToBounds = YES;
    UIView *fieldLeft = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 15, 30)];
    fieldLeft.backgroundColor = [UIColor whiteColor];
    keyField.leftView = fieldLeft;
    keyField.leftViewMode = UITextFieldViewModeAlways;
    keyField.font = [UIFont systemFontOfSize:15];

    www = 30;
    //键盘出现
    keyBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    keyBTN.tintColor = [UIColor whiteColor];
    keyBTN.userInteractionEnabled = YES;
    [keyBTN addTarget:self action:@selector(showkeyboard:) forControlEvents:UIControlEventTouchUpInside];
    [keyBTN setTitle:YZMsg(@"  说点什么...") forState:0];
    keyBTN.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    keyBTN.titleLabel.font= [UIFont systemFontOfSize:13];
    [keyBTN setBackgroundColor:RGBA(1, 1, 1, 0.4)];
    keyBTN.layer.cornerRadius = 15;
    keyBTN.layer.masksToBounds = YES;
    faceIcon =[[UIImageView alloc]init];
    faceIcon.image = [UIImage imageNamed:@"msg_face"];
    [keyBTN addSubview:faceIcon];

    //发送按钮
    pushBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    [pushBTN setImage:[UIImage imageNamed:@"chat_send_gray"] forState:UIControlStateNormal];
    [pushBTN setImage:[UIImage imageNamed:@"chat_send_yellow"] forState:UIControlStateSelected];
    pushBTN.imageView.contentMode = UIViewContentModeScaleAspectFit;

    pushBTN.layer.masksToBounds = YES;
    pushBTN.layer.cornerRadius = 5;
    [pushBTN addTarget:self action:@selector(pushMessage:) forControlEvents:UIControlEventTouchUpInside];
    pushBTN.frame = CGRectMake(_window_width-55,7,50,30);
    cs = [[catSwitch alloc] initWithFrame:CGRectMake(6,11,44,22)];
    cs.delegate = self;
    //退出页面按钮
    closeLiveBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    closeLiveBtn.tintColor = [UIColor whiteColor];
    [closeLiveBtn setImage:[UIImage imageNamed:@"cancleliveshow"] forState:UIControlStateNormal];
    [closeLiveBtn addTarget:self action:@selector(onQuit) forControlEvents:UIControlEventTouchUpInside];
    closeLiveBtn.frame =CGRectMake(_window_width-50, 20+statusbarHeight, 40, 40);
    
    //CGRectMake(_window_width - www- 10,bottombtnH, www, www);

    //消息按钮
    messageBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    [messageBTN setImage:[UIImage imageNamed:@"live_私信"] forState:UIControlStateNormal];
    [messageBTN addTarget:self action:@selector(doMessage) forControlEvents:UIControlEventTouchUpInside];
    self.unReadLabel = [[UILabel alloc]initWithFrame:CGRectMake(13, -5, 16, 16)];
    self.unReadLabel.textAlignment = NSTextAlignmentCenter;
    self.unReadLabel.textColor = [UIColor whiteColor];
    self.unReadLabel.layer.masksToBounds = YES;
    self.unReadLabel.layer.cornerRadius = 8;
    self.unReadLabel.font = [UIFont systemFontOfSize:9];
    self.unReadLabel.backgroundColor = [UIColor redColor];
    self.unReadLabel.hidden = YES;
    [messageBTN addSubview:self.unReadLabel];
    //camera按钮
    moreBTN = [UIButton buttonWithType:UIButtonTypeSystem];
    moreBTN.tintColor = [UIColor whiteColor];
    [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能"] forState:UIControlStateNormal];
    [moreBTN addTarget:self action:@selector(showmoreview) forControlEvents:UIControlEventTouchUpInside];
    keyField.frame = CGRectMake(cs.right+10,7,_window_width-90 - 30, 30);

    
    if (openGoodsCar.selected) {
        //camera按钮
        goodsShowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [goodsShowBtn setBackgroundImage:[UIImage imageNamed:@"live_店铺"] forState:UIControlStateNormal];
        [goodsShowBtn addTarget:self action:@selector(showgoodsShowView) forControlEvents:UIControlEventTouchUpInside];

    }
    
//    //camera按钮
//    linkSwitchBtn = [UIButton buttonWithType:0];
//    [linkSwitchBtn setBackgroundImage:[UIImage imageNamed:getImagename(@"允许连麦")] forState:UIControlStateNormal];
//    [linkSwitchBtn setBackgroundImage:[UIImage imageNamed:getImagename(@"禁止连麦")] forState:UIControlStateSelected];
//    [linkSwitchBtn addTarget:self action:@selector(linkSwitchBtnClick:) forControlEvents:UIControlEventTouchUpInside];
//    linkSwitchBtn.selected = NO;
    
    //PK按钮
    startPKBtn = [UIButton buttonWithType:0];
    [startPKBtn setBackgroundImage:[UIImage imageNamed:getImagename(@"发起pk")] forState:UIControlStateNormal];
    [startPKBtn addTarget:self action:@selector(startPKBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    //PK按钮
    redBagBtn = [UIButton buttonWithType:0];
    [redBagBtn setBackgroundImage:[UIImage imageNamed:@"红包-右上角"] forState:UIControlStateNormal];
    [redBagBtn addTarget:self action:@selector(redBagBtnClick) forControlEvents:UIControlEventTouchUpInside];
    redBagBtn.hidden = YES;
    redBagBtn.frame = CGRectMake(_window_width-50, 170+statusbarHeight, 50, 50);
    /*==================  连麦  ================*/
    //tool绑定键盘
    toolBar = [[UIView alloc]initWithFrame:CGRectMake(0,_window_height+10, _window_width, 44)];
    toolBar.backgroundColor = [UIColor clearColor];
    UIView *tooBgv = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 44)];
    tooBgv.backgroundColor = [UIColor whiteColor];
    tooBgv.alpha = 0.7;
    [toolBar addSubview:tooBgv];
    [toolBar addSubview:pushBTN];
    [toolBar addSubview:keyField];
    [toolBar addSubview:cs];
    [frontView addSubview:keyBTN];
    //关闭连麦按钮
    //直播间按钮（竞拍，游戏，扣费，后台控制隐藏,createroom接口传进来）
    [self changeBtnFrame:_window_height - 45];
    [leftView addSubview:onlineLabel];
    [leftView addSubview:liveLabel];
    [leftView addSubview:IconBTN];
    [leftView addSubview:levelimage];
    [frontView addSubview:leftView];
    [frontView addSubview:moreBTN];
    [frontView addSubview:goodsShowBtn];
    [frontView addSubview:messageBTN];
    [frontView addSubview:closeLiveBtn];
//    [frontView addSubview:linkSwitchBtn];
    [self.view addSubview:redBagBtn];

    [self hideBTN];
    /*==================  连麦  ================*/
    [self.view addSubview:toolBar];
    //增加监听，当键盘出现或改变时收出消息
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ChangePushBtnState) name:UITextFieldTextDidChangeNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(denglushixiao) name:@"denglushixiao" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(hideBroadcastChat) name:@"hideBroadcastChat" object:nil];
    
    
    [self.view insertSubview:self.tableView atIndex:4];
    useraimation = [[userLoginAnimation alloc]init];
    useraimation.frame = CGRectMake(10,self.tableView.top - 40,_window_width,20);
    [self.view insertSubview:useraimation atIndex:4];
    danmuview = [[GrounderSuperView alloc] initWithFrame:CGRectMake(0, 100, self.view.frame.size.width, 140)];
    
    //手绘礼物
    [self.view  addSubview:self.paintedShowRegion];

    [frontView insertSubview:danmuview atIndex:5];
    liansongliwubottomview = [[UIView alloc]init];
    [self.view insertSubview:liansongliwubottomview belowSubview:frontView];
    liansongliwubottomview.frame = CGRectMake(0, self.tableView.top-150,_window_width/2,140);
//    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideSixin:)];
//
//    [self.view addGestureRecognizer:tapGesture];

}
#pragma mark -------------在线人数--------------
-(void)onlineBtnClick{
    NSDictionary *dataDic = @{@"uid":[Config getOwnID],
                              @"stream":minstr([self.roomDic valueForKey:@"stream"])
    };
    YBWeakSelf;
    _onlineView  =  [[OnlineUserView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)withInfoDic:dataDic];
    _onlineView.btnEvent = ^(NSString *status, NSDictionary *userDic) {
        if([status isEqual:@"关闭"]){
            [weakSelf.onlineView removeFromSuperview];
            weakSelf.onlineView = nil;
        }else{
            [weakSelf showButtleView:minstr([userDic valueForKey:@"id"])];
        }
    };
    [self.view addSubview:_onlineView];
}
- (void)hideSixin:(UIButton *)sender{
//    [keyField resignFirstResponder];
    [self getweidulabel];

    if (tChatsamall||sysView) {
        if (tChatsamall) {
//            [tChatsamall dropToolBar];
            [tChatsamall.view endEditing:YES];
        }
        return;
    }
    if (huanxinviews) {
        [huanxinviews.view removeFromSuperview];
        huanxinviews.view = nil;
        huanxinviews = nil;
        [self showBTN];
    }
    [sender removeFromSuperview];
    sender = nil;
}

-(void)hideBroadcastChat{
    if ([self respondsToSelector:@selector(hideSixin:)]) {
        [self hideSixin:hidechatbtn];
    }
}

-(void)hidecoastview{
    [UIView animateWithDuration:0.3 animations:^{
        coastview.frame = CGRectMake(0, -_window_height, _window_width, _window_height);
    }];
}
//弹出收费弹窗
-(void)doupcoast{

    if (!coastview) {
        coastview = [[coastselectview alloc]initWithFrame:CGRectMake(0, -_window_height, _window_width, _window_height) andsureblock:^(NSString *type) {
            if (_preFrontView) {
                roomType = @"3";
                roomTypeValue = type;
                [self changeRoomBtnState:YZMsg(@"计时房间")];
                [self hidecoastview];
            }else{
                coastmoney = type;
                [MBProgressHUD showMessage:@""];
                //Live.changeLiveType
                NSDictionary *subdic = @{
                                         @"stream":urlStrtimestring,
                                         @"type":@"3",
                                         @"type_val":coastmoney
                                         };
                [YBToolClass postNetworkWithUrl:@"Live.changeLiveType" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
                    [MBProgressHUD hideHUD];

                    if (code == 0) {
                        
                        [MBProgressHUD hideHUD];
                        [MBProgressHUD showError:msg];
                        [socketL changeLiveType:coastmoney];
                        //收费金额
                        [self hidecoastview];
                    }

                } fail:^{
                    [MBProgressHUD hideHUD];
                }];
            }
        } andcancleblock:^(NSString *type) {
            //取消
            [self hidecoastview];
        }];
        [self.view addSubview:coastview];
    }
    [UIView animateWithDuration:0.3 animations:^{
        coastview.frame = CGRectMake(0,0, _window_width, _window_height);
    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:0.1 animations:^{
            coastview.frame = CGRectMake(0,20,_window_width, _window_height);
        }];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:0.1 animations:^{
            coastview.frame = CGRectMake(0, 0, _window_width, _window_height);
        }];
    });
    coastview.userInteractionEnabled = YES;
}
- (void)doShareViewShow{
    if (!fenxiangV) {
        if (liveTitleTextView.text.length < 1) {
            liveTitleTextView.text = @"";
        }
        //分享弹窗
        fenxiangV = [[fenXiangView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height)];
        NSDictionary *mudic = @{
                                @"user_nickname":[Config getOwnNicename],
                                @"avatar_thumb":[Config getavatarThumb],
                                @"uid":[Config getOwnID],
                                @"thumb":live_thumb,
                                @"title":liveTitleTextView.text
                                };
        
        [fenxiangV GetDIc:mudic];
//        [fenxiangV dofromLogin];
        [self.view addSubview:fenxiangV];
    }else{
        [self.view bringSubviewToFront:fenxiangV];
        [fenxiangV show];
    }
}
-(void)toolbarHidden
{
    [self showBTN];
    toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
    [UIView animateWithDuration:0.5 animations:^{
        tChatsamall.view.frame = CGRectMake(0, _window_height*3, _window_width, _window_height*0.4);
    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (tChatsamall) {
            [tChatsamall.view removeFromSuperview];
            tChatsamall.view = nil;
            tChatsamall = nil;
        }
    });
}
-(void)toolbarClick:(id)sender
{
    [keyField resignFirstResponder];
    toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
}

-(void)changeState{
    if (!yingpiaoLabel) {
        //魅力值//魅力值
        //修改 魅力值 适应字体 欣
        UIFont *font1 = [UIFont systemFontOfSize:12];
        NSString *str = [NSString stringWithFormat:@"%@ %@ >",[common name_votes],_voteNums];
        CGFloat width = [[YBToolClass sharedInstance] widthOfString:str andFont:font1 andHeight:20];
        yingpiaoLabel  = [[UILabel alloc]init];
        yingpiaoLabel.backgroundColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.6];
        yingpiaoLabel.font = font1;
        yingpiaoLabel.text = str;
        yingpiaoLabel.frame = CGRectMake(10,30+leftView.frame.size.height +statusbarHeight, width+30,20);
        yingpiaoLabel.textAlignment = NSTextAlignmentCenter;
        yingpiaoLabel.textColor = [UIColor whiteColor];
        yingpiaoLabel.layer.cornerRadius = 10.0;
        yingpiaoLabel.layer.masksToBounds  =YES;
        UITapGestureRecognizer *yingpiaoTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(yingpiao)];
        [yingpiaoLabel addGestureRecognizer:yingpiaoTap];
        yingpiaoLabel.userInteractionEnabled = YES;
        [frontView addSubview:yingpiaoLabel];
    }else{
        UIFont *font1 = [UIFont systemFontOfSize:12];
        NSString *str = [NSString stringWithFormat:@"%@ %@ >",[common name_votes],_voteNums];
        CGFloat width = [[YBToolClass sharedInstance] widthOfString:str andFont:font1 andHeight:20]+30;
        yingpiaoLabel.width = width;
        yingpiaoLabel.text = str;
        guardBtn.frame = CGRectMake(yingpiaoLabel.right+5, yingpiaoLabel.top, guardWidth+20, yingpiaoLabel.height);
    }
}
- (void)changeGuardNum:(NSString *)nums{
    if (!guardBtn) {
        guardWidth = [[YBToolClass sharedInstance] widthOfString:YZMsg(@"守护 虚位以待>") andFont:[UIFont systemFontOfSize:12] andHeight:20];
        guardBtn = [UIButton buttonWithType:0];
        guardBtn.frame = CGRectMake(yingpiaoLabel.right+5, yingpiaoLabel.top, guardWidth+20, yingpiaoLabel.height);
        guardBtn.backgroundColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.6];
        [guardBtn addTarget:self action:@selector(guardBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [guardBtn setTitle:YZMsg(@"守护 虚位以待>") forState:0];
        guardBtn.layer.cornerRadius = 10;
        guardBtn.layer.masksToBounds = YES;
        guardBtn.titleLabel.font = [UIFont systemFontOfSize:12];
        [frontView addSubview:guardBtn];
    }
    if (![nums isEqual:@"0"]) {
        [guardBtn setTitle:[NSString stringWithFormat:@"%@ %@%@ >",YZMsg(@"守护"),nums,YZMsg(@"人")] forState:0];
        guardWidth = [[YBToolClass sharedInstance] widthOfString:guardBtn.titleLabel.text andFont:[UIFont systemFontOfSize:12] andHeight:20];
        guardBtn.frame = CGRectMake(yingpiaoLabel.right+5, yingpiaoLabel.top, guardWidth+20, yingpiaoLabel.height);
    }
}
//跳往魅力值界面
-(void)yingpiao{
    NSString *language = [PublicObj getCurrentLanguage];

    YBWebViewController *jumpC = [[YBWebViewController alloc]init];
    jumpC.urls = [NSString stringWithFormat:@"%@/appapi/contribute/index?uid=%@&language=%@",h5url,[Config getOwnID],language];
    [[MXBADelegate sharedAppDelegate]pushViewController:jumpC animated:YES];

}
-(void)changeMusic:(NSNotification *)notifition{
    _count = 0;
    [musicV removeFromSuperview];
    musicV = nil;

    _isPlayLrcing = NO;
    if (lrcTimer) {
        [lrcTimer invalidate];
        lrcTimer =nil;
    }
    NSDictionary *dic = [notifition userInfo];
    muaicPath  = [dic valueForKey:@"music"];
    //NSString *lrcId = [dic valueForKey:@"lrc"];
    passStr = [dic valueForKey:@"lrc"];
    NSFileManager *managers=[NSFileManager defaultManager];
    
    if ([_sdkType isEqual:@"1"]) {
        [[YBLiveRTCManager shareInstance]stopBGM];
        if ([managers fileExistsAtPath:muaicPath]) {
            [[YBLiveRTCManager shareInstance]playBGMWithPath:muaicPath];
            musicV = [[UIView alloc]initWithFrame:CGRectMake(50, _window_height*0.4, _window_width-50, 100)];
            musicV.backgroundColor = [UIColor clearColor];
            [self.view addSubview:musicV];
            UIPanGestureRecognizer *aPan = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(musicPan:)];
            aPan.minimumNumberOfTouches = 1;
            aPan.maximumNumberOfTouches = 1;
            [musicV addGestureRecognizer:aPan];
        }else{
            NSLog(@"歌曲不存在");
            musicV = [[UIView alloc]initWithFrame:CGRectMake(50, _window_height*0.4, _window_width-50, 100)];
            musicV.backgroundColor = [UIColor clearColor];
            [self.view addSubview:musicV];
            UIPanGestureRecognizer *aPan = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(musicPan:)];
            aPan.minimumNumberOfTouches = 1;
            aPan.maximumNumberOfTouches = 1;
            [musicV addGestureRecognizer:aPan];

        }
        NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
        NSString *docDir = [paths objectAtIndex:0];
        lrcPath = [docDir stringByAppendingFormat:@"/%@.lrc",passStr];//lrcId
        if ([managers fileExistsAtPath:lrcPath]) {
            lrcView = [[YLYOKLRCView alloc]initWithFrame:CGRectMake(0,40,_window_width-50, 30)];
            lrcView.lrcLabel.font = [UIFont fontWithName:@"Helvetica-Bold" size:17];//OKlrcLabel
            lrcView.OKlrcLabel.font = [UIFont fontWithName:@"Helvetica-Bold" size:17];
            lrcView.backgroundColor = [UIColor clearColor];
            [musicV addSubview:lrcView];
            YLYMusicLRC *lrc = [[YLYMusicLRC alloc]initWithLRCFile:lrcPath];
            if(lrc.lrcList.count == 0 || !lrc.lrcList) {
                [MBProgressHUD showError:@"暂无歌词"];
                buttonmusic = [UIButton buttonWithType:UIButtonTypeCustom];
                buttonmusic.frame = CGRectMake(80,0,50,30);
                [buttonmusic addTarget:self action:@selector(musicPlay) forControlEvents:UIControlEventTouchUpInside];
                [buttonmusic setTitle:YZMsg(@"结束") forState:UIControlStateNormal];
                buttonmusic.backgroundColor = [UIColor clearColor];
                [buttonmusic setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
                buttonmusic.layer.masksToBounds = YES;
                buttonmusic.layer.cornerRadius = 10;
                buttonmusic.layer.borderWidth = 1;
                buttonmusic.layer.borderColor = [UIColor whiteColor].CGColor;
                [musicV addSubview:buttonmusic];
                return;
            }
            self.lrcList = lrc.lrcList;
            self.timeLabel = [[UILabel alloc]initWithFrame:CGRectMake(140,0,50,30)];
            self.timeLabel.backgroundColor = [UIColor clearColor];
            self.timeLabel.textAlignment = NSTextAlignmentCenter;
            self.timeLabel.textColor = [UIColor whiteColor];
            self.timeLabel.layer.cornerRadius = 10;
            self.timeLabel.layer.borderWidth = 1;
            self.timeLabel.layer.borderColor = [UIColor whiteColor].CGColor;
            self.timeLabel.text = [NSString stringWithFormat:@"%d:0%d",0,0];
            [musicV addSubview:self.timeLabel];
        }else{
            NSLog(@"歌词不存在");
        }
        buttonmusic = [UIButton buttonWithType:UIButtonTypeCustom];
        buttonmusic.frame = CGRectMake(80,0,50,30);
        [buttonmusic addTarget:self action:@selector(musicPlay) forControlEvents:UIControlEventTouchUpInside];
        [buttonmusic setTitle:YZMsg(@"结束") forState:UIControlStateNormal];
        buttonmusic.backgroundColor = [UIColor clearColor];
        [buttonmusic setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        buttonmusic.layer.masksToBounds = YES;
        buttonmusic.layer.cornerRadius = 10;
        buttonmusic.layer.borderWidth = 1;
        buttonmusic.layer.borderColor = [UIColor whiteColor].CGColor;
        [musicV addSubview:buttonmusic];

    }
    else{
        //ray-声网
        
        [[YBAgoraManager shareInstance]stopBGM];
        if ([managers fileExistsAtPath:muaicPath]) {
            [[YBAgoraManager shareInstance]playBGMWithPath:muaicPath];
            musicV = [[UIView alloc]initWithFrame:CGRectMake(50, _window_height*0.4, _window_width-50, 100)];
            musicV.backgroundColor = [UIColor clearColor];
            [self.view addSubview:musicV];
            UIPanGestureRecognizer *aPan = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(musicPan:)];
            aPan.minimumNumberOfTouches = 1;
            aPan.maximumNumberOfTouches = 1;
            [musicV addGestureRecognizer:aPan];
        }else{
            NSLog(@"歌曲不存在");
            musicV = [[UIView alloc]initWithFrame:CGRectMake(50, _window_height*0.4, _window_width-50, 100)];
            musicV.backgroundColor = [UIColor clearColor];
            [self.view addSubview:musicV];
            UIPanGestureRecognizer *aPan = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(musicPan:)];
            aPan.minimumNumberOfTouches = 1;
            aPan.maximumNumberOfTouches = 1;
            [musicV addGestureRecognizer:aPan];

        }
        NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
        NSString *docDir = [paths objectAtIndex:0];
        lrcPath = [docDir stringByAppendingFormat:@"/%@.lrc",passStr];//lrcId
        if ([managers fileExistsAtPath:lrcPath]) {
            lrcView = [[YLYOKLRCView alloc]initWithFrame:CGRectMake(0,40,_window_width-50, 30)];
            lrcView.lrcLabel.font = [UIFont fontWithName:@"Helvetica-Bold" size:17];//OKlrcLabel
            lrcView.OKlrcLabel.font = [UIFont fontWithName:@"Helvetica-Bold" size:17];
            lrcView.backgroundColor = [UIColor clearColor];
            [musicV addSubview:lrcView];
            YLYMusicLRC *lrc = [[YLYMusicLRC alloc]initWithLRCFile:lrcPath];
            if(lrc.lrcList.count == 0 || !lrc.lrcList) {
                [MBProgressHUD showError:@"暂无歌词"];
                buttonmusic = [UIButton buttonWithType:UIButtonTypeCustom];
                buttonmusic.frame = CGRectMake(80,0,50,30);
                [buttonmusic addTarget:self action:@selector(musicPlay) forControlEvents:UIControlEventTouchUpInside];
                [buttonmusic setTitle:YZMsg(@"结束") forState:UIControlStateNormal];
                buttonmusic.backgroundColor = [UIColor clearColor];
                [buttonmusic setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
                buttonmusic.layer.masksToBounds = YES;
                buttonmusic.layer.cornerRadius = 10;
                buttonmusic.layer.borderWidth = 1;
                buttonmusic.layer.borderColor = [UIColor whiteColor].CGColor;
                [musicV addSubview:buttonmusic];
                return;
            }
            self.lrcList = lrc.lrcList;
            self.timeLabel = [[UILabel alloc]initWithFrame:CGRectMake(140,0,50,30)];
            self.timeLabel.backgroundColor = [UIColor clearColor];
            self.timeLabel.textAlignment = NSTextAlignmentCenter;
            self.timeLabel.textColor = [UIColor whiteColor];
            self.timeLabel.layer.cornerRadius = 10;
            self.timeLabel.layer.borderWidth = 1;
            self.timeLabel.layer.borderColor = [UIColor whiteColor].CGColor;
            self.timeLabel.text = [NSString stringWithFormat:@"%d:0%d",0,0];
            [musicV addSubview:self.timeLabel];
        }else{
            NSLog(@"歌词不存在");
        }
        buttonmusic = [UIButton buttonWithType:UIButtonTypeCustom];
        buttonmusic.frame = CGRectMake(80,0,50,30);
        [buttonmusic addTarget:self action:@selector(musicPlay) forControlEvents:UIControlEventTouchUpInside];
        [buttonmusic setTitle:YZMsg(@"结束") forState:UIControlStateNormal];
        buttonmusic.backgroundColor = [UIColor clearColor];
        [buttonmusic setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        buttonmusic.layer.masksToBounds = YES;
        buttonmusic.layer.cornerRadius = 10;
        buttonmusic.layer.borderWidth = 1;
        buttonmusic.layer.borderColor = [UIColor whiteColor].CGColor;
        [musicV addSubview:buttonmusic];
        
//        [_gpuStreamer.bgmPlayer stopPlayBgm];
//        if ([managers fileExistsAtPath:muaicPath]) {
//            [_gpuStreamer.bgmPlayer startPlayBgm:muaicPath isLoop:NO];
//            _gpuStreamer.bgmPlayer.bgmVolume = 0.2;
//            [_gpuStreamer.aMixer setMixVolume:0.2 of:_gpuStreamer.bgmTrack];
//            [_gpuStreamer.aMixer setMixVolume:1.0 of:_gpuStreamer.micTrack];
//
//        }else{
//            NSLog(@"歌曲不存在");
//        }
    }
}
-(void)musicStateChanged:(AgoraAudioMixingStateType)state;
{
    if (state == AgoraAudioMixingStateTypePlaying) {
        
    }else if (state == AgoraAudioMixingStateTypeStopped){
        [self musicPlay];

    }
}
-(void)musicPositionChanged:(NSInteger)position
{
    //音乐播放进度
    int progress = (int)position/1000;
    dispatch_async(dispatch_get_main_queue(), ^{
        if ((int)progress % 60 < 10) {
            self.timeLabel.text = [NSString stringWithFormat:@"%d:0%d",(int)progress / 60, (int)progress % 60];
        } else {
            self.timeLabel.text = [NSString stringWithFormat:@"%d:%d",(int)progress / 60, (int)progress % 60];
        }
//        if (progressMs == durationMs) {
//            [self musicPlay];
//        }
    });
    NSDictionary *dic = self.lrcList[_count];
    NSArray *array = [dic[@"lrctime"] componentsSeparatedByString:@":"];//把时间转换成秒
    NSUInteger currentTime = [array[0] intValue] * 60 + [array[1] intValue];
    //判断是否播放歌词
    if (position >= currentTime && _isPlayLrcing == NO) {
        [lrcView beganLrc:self.lrcList];
        _isPlayLrcing = YES;
    }

}
//播放音乐
-(void)playMusicBegin{
    socketL.hasBgm = YES;
}
//播放音乐进度
-(void)playMusicProgress:(NSInteger)progressMs Duration:(NSInteger)durationMs
{
    //音乐播放进度
    int progress = (int)progressMs/1000;
    dispatch_async(dispatch_get_main_queue(), ^{
        if ((int)progress % 60 < 10) {
            self.timeLabel.text = [NSString stringWithFormat:@"%d:0%d",(int)progress / 60, (int)progress % 60];
        } else {
            self.timeLabel.text = [NSString stringWithFormat:@"%d:%d",(int)progress / 60, (int)progress % 60];
        }
        if (progressMs == durationMs) {
            [self musicPlay];
        }
    });
    NSDictionary *dic = self.lrcList[_count];
    NSArray *array = [dic[@"lrctime"] componentsSeparatedByString:@":"];//把时间转换成秒
    NSUInteger currentTime = [array[0] intValue] * 60 + [array[1] intValue];
    //判断是否播放歌词
    if (progressMs >= currentTime && _isPlayLrcing == NO) {
        [lrcView beganLrc:self.lrcList];
        _isPlayLrcing = YES;
    }
}

//手指拖拽音乐移动
-(void)musicPan:(UIPanGestureRecognizer *)sender{
    CGPoint point = [sender translationInView:sender.view];
    CGPoint center = sender.view.center;
    center.x += point.x;
    center.y += point.y;
    musicV.center = center;
    //清空
    [sender setTranslation:CGPointZero inView:sender.view];
}
//TODO:更新ing歌曲播放时间
-(void)updateMusicTimeLabel{
    //ray-声网
//    if ((int)_gpuStreamer.bgmPlayer.bgmPlayTime % 60 < 10) {
//        self.timeLabel.text = [NSString stringWithFormat:@"%d:0%d",(int)_gpuStreamer.bgmPlayer.bgmPlayTime / 60, (int)_gpuStreamer.bgmPlayer.bgmPlayTime % 60];
//    }else {
//        self.timeLabel.text = [NSString stringWithFormat:@"%d:%d",(int)_gpuStreamer.bgmPlayer.bgmPlayTime / 60, (int)_gpuStreamer.bgmPlayer.bgmPlayTime % 60];
//    }
//    NSDictionary *dic = self.lrcList[_count];
//    NSArray *array = [dic[@"lrctime"] componentsSeparatedByString:@":"];//把时间转换成秒
//    NSUInteger currentTime = [array[0] intValue] * 60 + [array[1] intValue];
//    //判断是否播放歌词
//    if (_gpuStreamer.bgmPlayer.bgmPlayTime >= currentTime && _isPlayLrcing == NO) {
//        [lrcView beganLrc:self.lrcList];
//        _isPlayLrcing = YES;
//    }
//    if (_gpuStreamer.bgmPlayer.bgmPlayerState != KSYBgmPlayerStatePlaying) {
//        [self musicPlay];
//    }
}
//关闭音乐
-(void)musicPlay{
    if (lrcView.timelrc) {
        [lrcView.timelrc invalidate];
        lrcView.timelrc = nil;
    }
    if (lrcView) {
        [lrcView removeFromSuperview];
        lrcView = nil;
    }
    _count = 0;
    if ([_sdkType isEqual:@"1"]) {
        [[YBLiveRTCManager shareInstance]stopBGM];
    }else{
        [[YBAgoraManager shareInstance]stopBGM];
    }
    
    [musicV removeFromSuperview];
    musicV = nil;
    socketL.hasBgm = NO;
}

-(void)showmoreviews{
    //添加的镜像，闪光灯。。。
    __weak Livebroadcast *weakself = self;
    if (!bottombtnV) {
        bottombtnV = [[bottombuttonv alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height) music:^(NSString *type) {
            if (isLianmai) {
                [MBProgressHUD showError:YZMsg(@"连麦时无法开启伴奏")];
                return;
            }
            [weakself justplaymusic];//播放音乐
        } meiyan:^(NSString *type) {
            if (![[common getIsTXfiter]isEqual:@"1"]) {
                [self.menusView showMenuView:YES];
            }else{
                if ([_sdkType isEqual:@"1"]) {
                    [weakself userTXBase];
                }else{
                    [weakself OnChoseFilter:nil];//美颜
                }
            }

        } coast:^(NSString *type) {
            [weakself doShareViewShow];//分享
        } light:^(NSString *type) {
            [weakself toggleTorch];//闪光灯
        } camera:^(NSString *type) {
            [weakself rotateCamera];//切换摄像头
        } game:^(NSString *type) {
            if (isLianmai) {
                [MBProgressHUD showError:YZMsg(@"连麦状态下不能进行游戏哦")];
                return;
            }
            [weakself startgamepagev];//选择游戏
        } jingpai:^(NSString *type) {
            //开始竞拍
            
        } hongbao:^(NSString *type) {
            //红包
            [weakself showRedView];
        } lianmai:^(NSString *type) {
            //连麦
            if (isLianmai) {
                [MBProgressHUD showError:YZMsg(@"当前正在进行连麦")];
                return;
            }
            [weakself showAnchorView];
        }mirror:^(NSString *type){
            isMirror = !isMirror;
            if ([_sdkType isEqual:@"1"]) {
                [[YBLiveRTCManager shareInstance]changeMirror:isMirror];
            }else{
                [[YBAgoraManager shareInstance]changeMirror:isMirror];
            }
            if (isMirror) {
                [MBProgressHUD showError:YZMsg(@"您已关闭镜像")];
            }else {
                [MBProgressHUD showError:YZMsg(@"您已开启镜像")];
            }

        } showjingpai:@"0" showgame:_game_switch showcoase:_type hideself:^(NSString *type) {
            [UIView animateWithDuration:0.4 animations:^{
                bottombtnV.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
            }];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能"] forState:UIControlStateNormal];
                [bottombtnV removeFromSuperview];
                bottombtnV = nil;
            });
        } andIsTorch:isTorch andUserLink:^(NSString *type) {
            [weakself linkSwitchBtnClick];
        } canUserLink:userCanLink isGamePush:_isGameLive];
        UIWindow *window = [UIApplication sharedApplication].delegate.window;
        [window addSubview:bottombtnV];
        bottombtnV.hidden = YES;
        [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能"] forState:UIControlStateNormal];
    }
}
-(void)toggleTorch{
    if (_isFront == YES) {
            [MBProgressHUD showError:YZMsg(@"只有后置摄像头才能开启闪光灯")];
            return;
        }
    if ([_sdkType isEqual:@"1"]) {
        isTorch = !isTorch;
        [[YBLiveRTCManager shareInstance]cameraTorch:isTorch];
    }else{
        isTorch = !isTorch;
        [[YBAgoraManager shareInstance]cameraTorch:isTorch];
    }
}
-(void)rotateCamera{
    if ([_sdkType isEqual:@"1"]) {
        _isFrontCamera = !_isFrontCamera;
        [[YBLiveRTCManager shareInstance]changeCamera:_isFrontCamera];
    }else{
        _isFrontCamera = !_isFrontCamera;
        [[YBAgoraManager shareInstance]changeCamera:_isFrontCamera];
    }
    _isFront = !_isFront;
}
-(void)justplaymusic{
    musicView *music = [[musicView alloc]init];
    music.modalPresentationStyle = UIModalPresentationFullScreen;
    self.animator = [[ZFModalTransitionAnimator alloc] initWithModalViewController:music];
    self.animator.bounces = NO;
    self.animator.behindViewAlpha = 1;
    self.animator.behindViewScale = 0.5f;
    self.animator.transitionDuration = 0.4f;
    music.transitioningDelegate = self.animator;
    self.animator.dragable = YES;
    self.animator.direction = ZFModalTransitonDirectionRight;
    [self presentViewController:music animated:YES completion:nil];
}
-(void)showmoreview{
    if (!bottombtnV) {
        [self showmoreviews];
    }
    if (bottombtnV.hidden == YES) {
        bottombtnV.hidden = NO;
        [UIView animateWithDuration:0.4 animations:^{
            bottombtnV.frame = CGRectMake(0,0, _window_width, _window_height);
        }];
    }else{
        [UIView animateWithDuration:0.4 animations:^{
            bottombtnV.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
        }];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            bottombtnV.hidden = YES;
        });
    }
}
-(void)donghua:(UILabel *)labels{
    CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"transform"];
    animation.duration = 0.8;
    NSMutableArray *values = [NSMutableArray array];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(4.0, 4.0, 4.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(3.0, 3.0, 3.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(2.0, 2.0, 2.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(1.0, 1.0, 1.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(0.1, 0.1, 0.1)]];
    animation.values = values;
    animation.removedOnCompletion = NO;//是不是移除动画的效果
    animation.fillMode = kCAFillModeForwards;//保持最新状态
    [labels.layer addAnimation:animation forKey:nil];
}
#pragma mark ---- 私信方法
-(void)nsnotifition{
    //注册进入后台的处理
    NSNotificationCenter* notification = [NSNotificationCenter defaultCenter];
    [notification addObserver:self
           selector:@selector(appactive)
               name:UIApplicationDidBecomeActiveNotification
             object:nil];
    [notification addObserver:self
           selector:@selector(appnoactive)
               name:UIApplicationWillResignActiveNotification
             object:nil];

    [notification addObserver:self selector:@selector(changeMusic:) name:@"wangminxindemusicplay" object:nil];
    [notification addObserver:self selector:@selector(shajincheng) name:@"shajincheng" object:nil];
    //@"shajincheng"
    [notification addObserver:self selector:@selector(forsixin:) name:@"sixinok" object:nil];
    [notification addObserver:self selector:@selector(getweidulabel) name:@"gengxinweidu" object:nil];
    [notification addObserver:self selector:@selector(toolbarHidden) name:@"toolbarHidden" object:nil];
    //ray-声网
//    [notification addObserver:self selector:@selector(onAudioStateChange:)name:KSYAudioStateDidChangeNotification object:nil];
    [notification addObserver:self selector:@selector(getChatRefresh:) name:ybChatRefresh object:nil];
}
-(void)getChatRefresh:(NSNotification*)noti {
    NSDictionary *notiDic = noti.userInfo;
    NSString *add_time = minstr([notiDic valueForKey:@"add_time"]);
    NSArray *curArray = [NSArray arrayWithArray:msgList];
    BOOL have = NO;
    for (int i = 0; i<curArray.count; i++) {
        NSDictionary *subDic = curArray[i];
        if ([minstr([subDic valueForKey:@"add_time"]) isEqual:add_time]) {
            have = YES;
            NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:subDic];
            [m_dic setObject:@"0" forKey:@"is_first"];
            [msgList replaceObjectAtIndex:i withObject:m_dic.mutableCopy];
        }
    }
    if (have) {
        NSLog(@"======图片加载......refresh");
        [_tableView reloadData];
    }
}
//更新未读消息
-(void)getweidulabel{
    [self labeiHid];
}
// 收到所有会话的未读总数变更通知
- (void)onTotalUnreadMessageCountChanged:(UInt64)totalUnreadCount {
    [self labeiHid];
}
-(void)labeiHid{
    __block NSInteger unRead = 0;
        
    [[YBImManager shareInstance]getAllUnredNumExceptUser:@[@"dsp_fans",@"dsp_like",@"dsp_at",@"dsp_comment"] complete:^(int allUnread) {
        unRead = allUnread;
        dispatch_async(dispatch_get_main_queue(), ^{
            self.unReadLabel.text = [NSString stringWithFormat:@"%ld",unRead];
            if ([self.unReadLabel.text isEqual:@"0"] || unRead <= 0) {
                self.unReadLabel.hidden =YES;
            }else {
                self.unReadLabel.hidden = NO;
            }
        });
    }];
}
//跳往消息列表
-(void)doMessage{
    [self hideBTN];
    [tChatsamall.view removeFromSuperview];
    tChatsamall = nil;
    tChatsamall.view = nil;
    [huanxinviews.view removeFromSuperview];
    huanxinviews = nil;
    huanxinviews.view = nil;
    if (!huanxinviews) {
        hidechatbtn = [UIButton buttonWithType:0];
        hidechatbtn.frame = CGRectMake(0, 0, _window_width, _window_height*0.6);
        [hidechatbtn addTarget:self action:@selector(hideSixin:) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:hidechatbtn];
        huanxinviews = [[huanxinsixinview alloc]init];
        huanxinviews.view.frame = CGRectMake(0, _window_height*3, _window_width, _window_height*0.4);
        huanxinviews.zhuboID = @"";
        [huanxinviews forMessage];
        [self.view insertSubview:huanxinviews.view atIndex:9];

    }
    [UIView animateWithDuration:0.2 animations:^{
        huanxinviews.view.frame = CGRectMake(0, _window_height - _window_height*0.4,_window_width, _window_height*0.4);
    }];
}
//点击用户聊天
-(void)forsixin:(NSNotification *)ns{
    
    NSMutableDictionary *dic = [[ns userInfo] mutableCopy];
    if (sysView.view) {
        [sysView.view removeFromSuperview];
        sysView = nil;
        sysView.view = nil;
        
    }
    __weak Livebroadcast *wSelf = self;

    if ([[dic valueForKey:@"id"] isEqual:@"1"]) {
        if (liansongliwubottomview) {
            [self.view insertSubview:liansongliwubottomview belowSubview:frontView];
        }

        sysView = [[MsgSysVC alloc]init];
        sysView.view.frame = CGRectMake(_window_width, _window_height-_window_height*0.4, _window_width, _window_height*0.4);
        sysView.block = ^(int type) {
            if (type == 0) {
                [wSelf hideSysTemView];
            }
        };
        [sysView reloadSystemView];
        
        [self.view insertSubview:sysView.view atIndex:10];
        [UIView animateWithDuration:0.5 animations:^{
            sysView.view.frame = CGRectMake(0, _window_height-_window_height*0.4, _window_width, _window_height*0.4);
        }];
        return;
    }
    if([[dic valueForKey:@"id"] isEqual:@"goodsorder_admin"]){
        orderChat = [[OrderMessageVC alloc]init];
        [[MXBADelegate sharedAppDelegate]pushViewController:orderChat animated:YES];
        return;

    }
    if ([[common letter_switch] isEqual:@"0"]) {
                   [MBProgressHUD showError:YZMsg(@"私信对话平台已关闭,暂时无法使用")];
                   return;
               }
    [tChatsamall.view removeFromSuperview];
    tChatsamall = nil;
    tChatsamall.view = nil;
    
    if (!tChatsamall) {
        tChatsamall = [[TChatC2CController alloc]init];
        [dic setObject:minstr([dic valueForKey:@"name"]) forKey:@"user_nickname"];
        TConversationCellData *conv =[dic valueForKey:@"conversation"];
        tChatsamall.conversation = conv;
        tChatsamall.block = ^(int type) {
            if (type == 0) {
                [wSelf hideChatMall];
            }
            if (type == 1) {
//                if ([conv.convId isEqual:minstr([wSelf.playDoc valueForKey:@"uid"])]) {
//                    [wSelf isAttentionLive:@"1"];
//                }
            }
        };
        [tChatsamall reloadSamllChtaView:@"0"];
        [self.view insertSubview:tChatsamall.view atIndex:10];
        [self.view bringSubviewToFront:tChatsamall.view];
        if (liansongliwubottomview) {
            [self.view insertSubview:liansongliwubottomview atIndex:8];
        }

    }
    tChatsamall.view.hidden = NO;
}
- (void)hideSysTemView{
    [sysView.view removeFromSuperview];
    sysView = nil;
    sysView.view = nil;
}

-(void)siXin:(NSString *)icon andName:(NSString *)name andID:(NSString *)ID andIsatt:(NSString *)isatt{
    [tChatsamall.view removeFromSuperview];
    tChatsamall = nil;
    tChatsamall.view = nil;
    [huanxinviews.view removeFromSuperview];
    huanxinviews = nil;
    huanxinviews.view = nil;
    YBWeakSelf;
    if (!tChatsamall) {
        tChatsamall = [[TChatC2CController alloc]init];
        TConversationCellData *data = [[TConversationCellData alloc] init];
        data.convId = minstr(ID);
        data.convType = TConv_Type_C2C;
        data.userHeader = minstr(icon);
        data.userName = minstr(name);
        tChatsamall.conversation = data;

        tChatsamall.block = ^(int type) {
            if (type == 0) {
                [weakSelf hideChatMall];
            }
//            if (type == 1) {
//                if ([data.convId isEqual:minstr([wSelf.playDoc valueForKey:@"uid"])]) {
//                    [weakSelf isAttentionLive:@"1"];
//                }
//            }
        };
        [tChatsamall reloadSamllChtaView:@"0"];

        [self.view insertSubview:tChatsamall.view atIndex:10];
        if (liansongliwubottomview) {
            [self.view insertSubview:liansongliwubottomview atIndex:8];
        }
    }
    tChatsamall.view.hidden = NO;
}
- (void)hideChatMall{
    if (huanxinviews) {
        [huanxinviews forMessage];
        CATransition *transition = [CATransition animation];    //创建动画效果类
        transition.duration = 0.3; //设置动画时长
        transition.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];  //设置动画淡入淡出的效果
        transition.type = kCATransitionPush;//{kCATransitionMoveIn, kCATransitionPush, kCATransitionReveal, kCATransitionFade};设置动画类型，移入，推出等
        //更多私有{@"cube",@"suckEffect",@"oglFlip",@"rippleEffect",@"pageCurl",@"pageUnCurl",@"cameraIrisHollowOpen",@"cameraIrisHollowClose"};
        transition.subtype = kCATransitionFromLeft;//{kCATransitionFromLeft, kCATransitionFromRight, kCATransitionFromTop, kCATransitionFromBottom};
        [tChatsamall.view.layer addAnimation:transition forKey:nil];       //在图层增加动画效果
        [tChatsamall.view removeFromSuperview];
        tChatsamall.view = nil;
        tChatsamall = nil;
        
    }else{
        [UIView animateWithDuration:0.3 animations:^{
            tChatsamall.view.frame = CGRectMake(_window_width, _window_height*0.6, _window_width, _window_height*0.4);
        } completion:^(BOOL finished) {
            [tChatsamall.view removeFromSuperview];
            tChatsamall.view = nil;
            tChatsamall = nil;
        }];
    }
}

-(void)pushZhuYe:(NSString *)IDS{
    PersonHomeVC  *person = [[PersonHomeVC alloc]init];
    person.userID = IDS;
    [[MXBADelegate sharedAppDelegate]pushViewController:person animated:YES];

}
-(void)sendAtMsgClick:(NSString *)nameStr
{
    if (tChatsamall) {
        tChatsamall.view.hidden = YES;
        [tChatsamall.view removeFromSuperview];
        tChatsamall.view = nil;
        tChatsamall = nil;
    }
    ismessgaeshow = YES;

    [keyField becomeFirstResponder];
    keyField.text = [NSString stringWithFormat:@"@ %@",nameStr];
}

//键盘弹出隐藏下面四个按钮
-(void)hideBTN{
//    closeLiveBtn.hidden = YES;
    keyBTN.hidden = YES;
    messageBTN.hidden = YES;
    moreBTN.hidden = YES;
    bottombtnV.hidden = YES;
//    linkSwitchBtn.hidden = YES;
    if (goodsShowBtn) {
        goodsShowBtn.hidden = YES;
    }
}
-(void)showBTN{
//    closeLiveBtn.hidden = NO;
    keyBTN.hidden = NO;
    messageBTN.hidden = NO;
    moreBTN.hidden = NO;
//    linkSwitchBtn.hidden = NO;
    if (goodsShowBtn) {
        goodsShowBtn.hidden = NO;
    }
}
- (void)keyboardWillShow:(NSNotification *)aNotification
{
    //防止填写竞拍信息的时候弹出私信
    startPKBtn.hidden = YES;
    if (!ismessgaeshow) {
        
        return;
    }
    if (startKeyboard == 1) {
        return;
    }
    if (gameVC) {
        gameVC.hidden = YES;
    }
    if (rotationV) {
        rotationV.hidden = YES;
    }

    [self hideBTN];
    //获取键盘的高度
    NSDictionary *userInfo = [aNotification userInfo];
    NSValue *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardRect = [aValue CGRectValue];
    CGFloat height = keyboardRect.origin.y;
    [UIView animateWithDuration:0.3 animations:^{
        toolBar.frame = CGRectMake(0,height-44,_window_width,44);
        frontView.frame = CGRectMake(0,-height, _window_width, _window_height);
        [self tableviewheight:_window_height - _window_height*0.2 - keyboardRect.size.height - 40];
        [self.view bringSubviewToFront:toolBar];
        [self.view insertSubview:self.tableView atIndex:4];
        [self changecontinuegiftframe];
        if (zhuangVC) {
            zhuangVC.frame =  CGRectMake(10,20, _window_width/4, _window_width/4 + 20 + _window_width/8);
        }

    }];
}
- (void)keyboardWillHide:(NSNotification *)aNotification
{
    startPKBtn.hidden = NO;

    ismessgaeshow = NO;
    [self showBTN];
    if (gameVC) {
        gameVC.hidden = NO;
    }
    if (rotationV) {
        rotationV.hidden = NO;
    }

    [UIView animateWithDuration:0.1 animations:^{
        toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
        if (gameVC) {
            [self tableviewheight:_window_height - _window_height*0.2 - 240-www];

        }else if (rotationV){
            [self tableviewheight:_window_height - _window_height*0.2 - _window_width/1.8 - www];
        }
        else{
            [self tableviewheight:_window_height - _window_height*0.2 - 50 - ShowDiff];
        }
        frontView.frame = CGRectMake(0, 0, _window_width, _window_height);
        [self changecontinuegiftframe];
        if (zhuangVC) {
            zhuangVC.frame =  CGRectMake(10,90, _window_width/4, _window_width/4 + 20 + _window_width/8);
        }

    }];
}
-(void)adminZhezhao{
    zhezhaoList.view.hidden = YES;
    self.tableView.hidden = NO;
    [UIView animateWithDuration:0.3 animations:^{
        adminlist.view.frame = CGRectMake(0,_window_height*2, _window_width, _window_height*0.3);
    }];
}
//管理员列表
-(void)adminList{
    if (!adminlist) {
        //管理员列表
        zhezhaoList  = [[UIViewController alloc]init];
        zhezhaoList.view.frame = CGRectMake(0, 0, _window_width, _window_height);
        [self.view addSubview:zhezhaoList.view];
        zhezhaoList.view.hidden = YES;
        adminlist = [[adminLists alloc]init];
        adminlist.delegate = self;
        adminlist.view.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
        [self.view addSubview:adminlist.view];
        UITapGestureRecognizer *tapAdmin = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(adminZhezhao)];
        [zhezhaoList.view addGestureRecognizer:tapAdmin];
    }
    [[NSNotificationCenter defaultCenter]postNotificationName:@"adminlist" object:nil];
    self.tableView.hidden = YES;
    [UIView animateWithDuration:0.3 animations:^{
        zhezhaoList.view.hidden = NO;
        adminlist.view.frame = CGRectMake(0,0, _window_width, _window_height);
    }];
}
-(void)setAdminSuccess:(NSString *)isadmin andName:(NSString *)name andID:(NSString *)ID{
//    NSString *cts;
//    if ([isadmin isEqual:@"0"]) {
//        //不是管理员
//         cts = @"被取消管理员";
//        [MBProgressHUD showError:YZMsg(@"取消管理员成功")];
//    }else{
//        //是管理员
//          cts = @"被设为管理员";
//        [MBProgressHUD showError:@"设置管理员成功"];
//    }
     [socketL setAdminID:ID andName:name andCt:isadmin];
}
-(void)superStopRoom:(NSString *)state{
    [self hostStopRoom];
}
//发送消息
-(void)sendBarrage
{
    //屏蔽词过滤
    NSString *sendMsg = keyField.text;
    for (NSString *str in self.chatWordArr) {
        if ([sendMsg containsString:str]) {
           sendMsg =  [sendMsg  stringByReplacingOccurrencesOfString:str withString:@"***"];
        }
    }

    /*******发送弹幕开始 **********/
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.sendBarrage"];
    NSDictionary *subdic = @{
                             @"liveuid":[Config getOwnID],
                             @"stream":urlStrtimestring,
                             @"giftid":@"1",
                             @"giftcount":@"1",
                             @"content":sendMsg
                             };
    [YBToolClass postNetworkWithUrl:@"Live.sendBarrage" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSString *barragetoken = [[info firstObject] valueForKey:@"barragetoken"];
            //刷新本地魅力值
            LiveUser *liveUser = [Config myProfile];
            liveUser.coin = [NSString stringWithFormat:@"%@",[[info firstObject] valueForKey:@"coin"]];
            [Config updateProfile:liveUser];
            [socketL sendBarrage:barragetoken];

        }
    } fail:^{
        
    }];
    
    /*********************发送礼物结束 ************************/
}
-(void)pushMessage:(UITextField *)sender{
    if (keyField.text.length >50) {
        [MBProgressHUD showError:YZMsg(@"字数最多50字")];
        return;
    }
    pushBTN.enabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        pushBTN.enabled = YES;
    });
    NSCharacterSet *set = [NSCharacterSet whitespaceAndNewlineCharacterSet];
    NSString *trimedString = [keyField.text stringByTrimmingCharactersInSet:set];
    if ([trimedString length] == 0) {
        
        return ;
    }
    if(cs.state == YES)//发送弹幕
    {
        
        if (keyField.text.length <=0) {
            return;
        }
        [self sendBarrage];
        keyField.text = @"";
        pushBTN.selected = NO;
        return;
    }
    //屏蔽词过滤
    NSString *sendMsg = keyField.text;
    for (NSString *str in self.chatWordArr) {
        if ([sendMsg containsString:str]) {
           sendMsg =  [sendMsg  stringByReplacingOccurrencesOfString:str withString:@"***"];
        }
    }

    [socketL sendMessage:sendMsg];
    keyField.text = @"";
    pushBTN.selected = NO;
}
//聊天输入框
-(void)showkeyboard:(UIButton *)sender{
    if (tChatsamall) {
        tChatsamall.view.hidden = YES;
        [tChatsamall.view removeFromSuperview];
        tChatsamall.view = nil;
        tChatsamall = nil;
    }
    ismessgaeshow = YES;
    [keyField becomeFirstResponder];
    
}
// 以下是 tableview的方法
///*******    连麦 注意下面的tableview方法    *******/
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return UITableViewAutomaticDimension;
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return self.chatModels.count;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return 1;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
       [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    chatMsgCell *cell = [tableView dequeueReusableCellWithIdentifier:@"chatMsgCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"chatMsgCell" owner:nil options:nil] lastObject];
    }
    cell.model = self.chatModels[indexPath.section];
    return cell;
}
- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 5;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 5)];
    view.backgroundColor = [UIColor clearColor];
    return view;
}

-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (self.menusView) {
        [self.menusView showMenuView:NO];
    }

    chatModel *model = self.chatModels[indexPath.section];
    [keyField resignFirstResponder];
    if ([model.userName isEqual:YZMsg(@"直播间消息")]) {
        return;
    }
    NSString *IsUser = [NSString stringWithFormat:@"%@",model.userID];
    if (IsUser.length > 1) {
        NSDictionary *subdic = @{@"id":model.userID,
                             @"name":model.userName
                             };
        [self GetInformessage:subdic];
    }
}
//请求直播
-(void)getStartShow
{
    _hostURL = minstr([_roomDic valueForKey:@"push"]);
    urlStrtimestring = [_roomDic valueForKey:@"stream"];
    _socketUrl = [_roomDic valueForKey:@"chatserver"];
    _danmuPrice = [_roomDic valueForKey:@"barrage_fee"];
//    if (![jackpot_level isEqual:@"-1"]) {
//    }
    [self JackpotLevelUp:@{@"uplevel":jackpot_level}];

    
    _voteNums = [NSString stringWithFormat:@"%@",[_roomDic valueForKey:@"votestotal"]];
    [self changeState];
    [self changeGuardNum:minstr([_roomDic valueForKey:@"guard_nums"])];
    socketL = [[socketLive alloc]init];
    socketL.delegate = self;
    socketL.zhuboDic = _roomDic;
    [socketL getshut_time:_shut_time];//获取禁言时间
    [socketL addNodeListen:_socketUrl andTimeString:urlStrtimestring];
    userlist_time = [[_roomDic valueForKey:@"userlist_time"] intValue];
    if (!listTimer) {
        listTimer = [NSTimer scheduledTimerWithTimeInterval:userlist_time target:self selector:@selector(reloadUserList) userInfo:nil repeats:YES];
    }
    if (!hartTimer) {
        hartTimer = [NSTimer scheduledTimerWithTimeInterval:0.8 target:self selector:@selector(socketLight) userInfo:nil repeats:YES];
    }

    if (!liveingTimer) {
        liveingTimer = [NSTimer scheduledTimerWithTimeInterval:30 target:self selector:@selector(checkLiveingStatus) userInfo:nil repeats:YES];
    }

    if ([_sdkType isEqual:@"1"]) {
        [self txStartRTC];
    }else{
        //声网
        [[YBAgoraManager shareInstance]joinChannelWithChannelId:urlStrtimestring andUserToken:_user_sw_token WithModel:RtcMode_Living isBroadcaster:AgoraClientRoleBroadcaster isGameLive:_isGameLive];
    }
    YBWeakSelf;
    //获取道具礼物
    [self getStickerList:^(NSArray *stickerlist) {
        weakSelf.stickerArr = stickerlist.copy;
        NSLog(@"livebroadcast-------stickerArr:%@", self.stickerArr);
    }];
}
#pragma mark -声网start
#pragma mark - 加入频道成功
-(void)joinChannelSuc{
//    [[YBAgoraManager shareInstance]changeMirror:isMirror];
    [self changePlayState:1];
}
//改变tableview高度
-(void)tableviewheight:(CGFloat)h{
    self.tableView.frame = CGRectMake(10,h,tableWidth,_window_height*0.2);
    useraimation.frame = CGRectMake(10,self.tableView.top - 40,_window_width,20);
}
//改变连送礼物的frame
-(void)changecontinuegiftframe{
    liansongliwubottomview.frame = CGRectMake(0, self.tableView.top - 150,_window_width/2,140);
}
-(void)changeBtnFrame:(CGFloat)bottombtnH{
    //bottombtnH = bottombtnH-ShowDiff;
    if(bottombtnH == _window_height - 45){
        bottombtnH = bottombtnH-ShowDiff;
    }
    keyBTN.frame = CGRectMake(10, bottombtnH, 130, www);
    faceIcon.frame = CGRectMake(keyBTN.width-26, 4, 22, 22);

    messageBTN.frame = CGRectMake(_window_width - www*2-20,bottombtnH, www, www);
    moreBTN.frame =CGRectMake(_window_width - www - 10,bottombtnH, www, www);
    if (goodsShowBtn) {
        goodsShowBtn.frame = CGRectMake(_window_width - www*3-30,bottombtnH, www, www);
        startPKBtn.frame = CGRectMake(_window_width - www*4-40 - 20,bottombtnH, www*2, www);

    }else{
        startPKBtn.frame = CGRectMake(_window_width - www*3-30 - 20,bottombtnH, www*2, www);
    }
    [frontView insertSubview:keyBTN atIndex:6];
    [frontView insertSubview:messageBTN atIndex:6];
    [frontView insertSubview:moreBTN atIndex:6];
    [self showBTN];
}
-(void)pushCoinV{
    CoinVeiw *coin = [[CoinVeiw alloc] init];
    [self presentViewController:coin animated:YES completion:nil];
}
//********************************转盘*******************************************************************//
-(void)reloadUserList{
    if (listView) {
        [listView listReloadNoew];
    }
}
- (void)loginOnOtherDevice{
    
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:YZMsg(@"当前账号已在其他设备登录") message:nil preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self hostStopRoom];
    }]];
    [self presentViewController:alert animated:YES completion:nil];
}

//请求关闭直播
-(void)getCloseShow
{
    if (isAnchorLink) {
        [socketL anchor_DuankaiLink];
    }
    [self musicPlay];
    [self clearSticker];
    [self liveOver];//停止计时器
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],@"stream":urlStrtimestring};
    NSString *sign = [YBToolClass dynamicSortString:signdic];

    NSString *url = [NSString stringWithFormat:@"Live.stopRoom&uid=%@&token=%@&stream=%@&time=%@&sign=%@",[Config getOwnID],[Config getOwnToken],urlStrtimestring,[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],sign];
    [YBToolClass postNetworkWithUrl:url andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD hideHUD];
        [socketL closeRoom];//发送关闭直播的socket

        [self dismissVC];
        [socketL colseSocket];//注销socket
        socketL = nil;//注销socket
        //直播结束
        [self onQuit:nil];//停止音乐、停止推流
        [self rmObservers];//释放通知
        //            [self.navigationController popViewControllerAnimated:YES];
        [self requestLiveAllTimeandVotes];
    } fail:^{
        [MBProgressHUD hideHUD];
        [socketL closeRoom];//发送关闭直播的socket
        [self dismissVC];
        [socketL colseSocket];//注销socket
        socketL = nil;//注销socket
        //直播结束
        [self onQuit:nil];//停止音乐、停止推流
        [self rmObservers];//释放通知
        //        [self.navigationController popViewControllerAnimated:YES];
        [self requestLiveAllTimeandVotes];

    }];
}

-(void)clearSticker{
    [[NSUserDefaults standardUserDefaults]setBool:NO forKey:HAVESTICKER];
    [[NSUserDefaults standardUserDefaults]setObject:@"" forKey:STICKERKEY];
    [[NSUserDefaults standardUserDefaults]setBool:NO forKey:STICKERNOTUSE];

}
//礼物效果
/************ 礼物弹出及队列显示开始 *************/
//红包
-(void)redbag{
    
}

//全站飘屏礼物
-(void)platGiftdelegate:(NSDictionary *)giftData
{
    
    if (!platliwuV) {
        platliwuV = [[PlatGiftView alloc]initWithIsPlat:YES];
        platliwuV.delegate = self;
//        [self.view insertSubview:platliwuV atIndex:8];
        if (_tx_playrtmp) {
            [self.view insertSubview:platliwuV aboveSubview:_tx_playrtmp];
        }else{
            //ray-声网
            [self.view insertSubview:platliwuV aboveSubview:_agordLinkView];

        }
//        [self.view insertSubview:platliwuV atIndex:8];
//        CGAffineTransform t = CGAffineTransformMakeTranslation(_window_width, 0);
//        platliwuV.transform = t;
    }
    if (giftData == nil) {
    }
    else
    {
        [platliwuV addArrayCount:giftData];
    }
    if(platliwuV.haohuaCount == 0){
        [platliwuV enGiftEspensive:YES];
    }
}
-(void)platGift:(NSDictionary *)giftData{
    if (!platliwuV) {
        platliwuV = [[PlatGiftView alloc]initWithIsPlat:YES];
        platliwuV.delegate = self;
        [self.view insertSubview:platliwuV atIndex:8];
    }
    if (giftData == nil) {
        
    }else
    {
        [platliwuV addArrayCount:giftData];
    }
    if(platliwuV.haohuaCount == 0){
        [platliwuV enGiftEspensive:YES];
    }

}

-(void)expensiveGiftdelegate:(NSDictionary *)giftData{
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]initWithIsPlat:NO];
        haohualiwuV.delegate = self;
        [self.view insertSubview:haohualiwuV atIndex:8];
        if([_sdkType isEqual:@"1"]){
            if (_tx_playrtmp) {
                [self.view insertSubview:haohualiwuV aboveSubview:_tx_playrtmp];
            }
        }else{
            [self.view insertSubview:haohualiwuV aboveSubview:_agordLinkView];
        }
    }
    if (giftData == nil) {
    }
    else
    {
        [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive:NO];
    }
    if (pkView) {
        [self.view insertSubview:haohualiwuV aboveSubview:pkView];
    }
}
-(void)expensiveGift:(NSDictionary *)giftData isPlatGift:(BOOL)isPlat{
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]initWithIsPlat:isPlat];
        haohualiwuV.delegate = self;
        [self.view insertSubview:haohualiwuV atIndex:8];
        if (_tx_playrtmp) {
            [self.view insertSubview:haohualiwuV aboveSubview:_tx_playrtmp];
        }
        //ray-声网
//        if (_js_playrtmp) {
//            [self.view insertSubview:haohualiwuV aboveSubview:_js_playrtmp];
//        }
    }
    if (giftData == nil) {
    }
    else
    {
        [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive:isPlat];
    }
}
/*
 *添加魅力值数
 */
-(void)addCoin:(long)coin
{
    long long ordDate = [_voteNums longLongValue];
    long long newDate = ordDate + coin;
    _voteNums = [NSString stringWithFormat:@"%lld",newDate];
    [self changeState];
}
-(void)addvotesdelegate:(NSString *)votes{
    [self addCoin:[votes longLongValue]];
}
-(void)switchState:(BOOL)state
{
    NSLog(@"%d",state);
    if(!state)
    {
        keyField.placeholder = YZMsg(@"和大家说些什么");
    }else{
        keyField.placeholder = [NSString stringWithFormat:@"%@，%@%@/%@",YZMsg(@"开启弹幕"),_danmuPrice,[common name_coin],YZMsg(@"条")];
    }
}
- (BOOL)shouldAutorotate {
    return YES;
}
/*==================  连麦  ================*/
-(void)setBgAndPreview {
    pushbottomV = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    pushbottomV.backgroundColor = [UIColor clearColor];
    [self.view addSubview:pushbottomV];
    if(_isGameLive){
        bGimg = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        bGimg.contentMode = UIViewContentModeScaleToFill;
        [self.view addSubview:bGimg];
        [bGimg yb_setImageWithUrlStr:minstr([Config getavatar]) placeholder:nil complete:^(UIImage *image) {//[model.vip_info valueForKey:@"avatar_thumb"]
            if (image) {
                UIBlurEffect *blur = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
                UIVisualEffectView *effectview = [[UIVisualEffectView alloc] initWithEffect:blur];
                effectview.frame = CGRectMake(0, 0,_window_width,_window_height);
                [bGimg addSubview:effectview];
            }
        }];
    }

    pkBackImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0,0,_window_width,_window_height)];
    pkBackImgView.image = [UIImage imageNamed:@"pk背景"];
    pkBackImgView.userInteractionEnabled = YES;
    pkBackImgView.hidden = YES;
    [pushbottomV addSubview:pkBackImgView];
    
    _pushPreview = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    _pushPreview.backgroundColor = [UIColor clearColor];
    [pushbottomV addSubview:_pushPreview];
}
//设置videoview拖拽点击
-(void)addvideoswipe{
    [self showBTN];
    if (!videopan) {
        videopan = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(handlepanss:)];
        [pushbottomV addGestureRecognizer:videopan];
    }
    if (!videotap) {
        videotap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(videotap)];
    }
}
-(void)videotap{
    useraimation.hidden = NO;
    vipanimation.hidden = NO;
    [pushbottomV removeGestureRecognizer:videopan];
    videopan = nil;
    [UIView beginAnimations:nil context:nil];
    [UIView setAnimationDuration:0.1];
    pushbottomV.transform = CGAffineTransformScale(CGAffineTransformIdentity, 1.0f, 1.0f);
    [UIView commitAnimations];
    pushbottomV.frame = CGRectMake(0,0,_window_width,_window_height);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.tableView.hidden = NO;
        listView.hidden       = NO;
        frontView.hidden      = NO;
        danmuview.hidden      = NO;
        liansongliwubottomview.hidden = NO;
        haohualiwuV.hidden = NO;
        platliwuV.hidden = NO;
        [self showBTN];
    });
}
- (void) handlepanss: (UIPanGestureRecognizer *)sender{
    CGPoint point = [sender translationInView:sender.view];
    CGPoint center = sender.view.center;
    center.x += point.x/3;
    center.y += point.y/3;
    if (center.x <0 ) {
        center.x = 0;
    }
    if (center.x >_window_width) {
        center.x = _window_width - _window_width*0.3;
    }
    if (center.y <0) {
        center.y = 0;
    }
    if ( center.y > _window_height ) {
        center.y = _window_height - _window_height*0.3;
    }
    pushbottomV.center = center;
    //清空
    [sender setTranslation:CGPointZero inView:sender.view];
}
- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self.view endEditing:YES];
    CGPoint origin = [[touches anyObject] locationInView:self.view];
    CGPoint location;
    location.x = origin.x/self.view.frame.size.width;
    location.y = origin.y/self.view.frame.size.height;
    [self onSwitchRtcView:location];
    //腾讯基础美颜
    if (_vBeauty && _vBeauty.hidden == NO) {
        _vBeauty.hidden = YES;
         _preFrontView.hidden = NO;
    }
    if (self.menusView.isShow) {
        [self.menusView showMenuView:NO];
        if (![self.menusView isDescendantOfView:self.view]) {
            _preFrontView.hidden = NO;
        }
    }
    _preFrontView.hidden = self.menusView.isShow;
}
- (BOOL)textFieldShouldReturn:(UITextField *)textField;
{
    if (textField == keyField) {
        [self pushMessage:nil];
    }
    return YES;
}
-(void) onSwitchRtcView:(CGPoint)location
{
    
}

-(void)closeBgm
{
    [self musicPlay];
}
- (void)showBGMView{
    if (!bgmView) {
        bgmView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        bgmView.backgroundColor = [UIColor clearColor];
        [self.view addSubview:bgmView];
        UIButton *bgmHideBtn = [UIButton buttonWithType:0];
        bgmHideBtn.frame = CGRectMake(0, 0, _window_width, _window_height-150);
        [bgmHideBtn addTarget:self action:@selector(hideBgmView) forControlEvents:UIControlEventTouchUpInside];
        [bgmView addSubview:bgmHideBtn];
        UIView *whiteView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-150, _window_width, 150)];
        whiteView.backgroundColor = [UIColor whiteColor];
        [bgmView addSubview:whiteView];
        NSArray *titleArr = @[YZMsg(@"麦克风音量"),YZMsg(@"背景音乐音量")];
        for (int i = 0; i<titleArr.count; i++) {
            UILabel *label =[[ UILabel alloc]initWithFrame:CGRectMake(10, i*50, 100, 50)];
            label.font = [UIFont systemFontOfSize:12];
            label.text = titleArr[i];
            [whiteView addSubview:label];
            UISlider *slider = [[UISlider alloc] initWithFrame:CGRectMake(110, label.top+15, _window_width-130, 20)];
            slider.tag = 18526+i;
            slider.minimumValue = 0;// 设置最小值
            slider.maximumValue = 1;// 设置最大值
            if (i == 0) {
                slider.value = 1;// 设置初始值
            }else{
                slider.value = 0.2;// 设置初始值
            }
            slider.minimumTrackTintColor = [UIColor greenColor]; //滑轮左边颜色，如果设置了左边的图片就不会显示
            slider.maximumTrackTintColor = [UIColor grayColor]; //滑轮右边颜色，如果设置了右边的图片就不会显示
            [slider setThumbImage:[UIImage imageNamed:@"play_seekbar_icon"] forState:0];
            [slider addTarget:self action:@selector(sliderValueChangeddddddd:) forControlEvents:UIControlEventValueChanged];// 针对值变化添加响应方法
            [whiteView addSubview:slider];
        }
        [UIView animateWithDuration:0.3 animations:^{
            bgmView.frame = CGRectMake(0, 0, _window_width, _window_height);
        }];
    }else{
        [UIView animateWithDuration:0.3 animations:^{
            bgmView.frame = CGRectMake(0, 0, _window_width, _window_height);
        } completion:^(BOOL finished) {
            [self.view bringSubviewToFront:bgmView];
        }];
    }
}
- (void)hideBgmView{
    [UIView animateWithDuration:0.3 animations:^{
        bgmView.frame = CGRectMake(0, _window_height, _window_width, _window_height);
    }];
}
- (void)sliderValueChangeddddddd:(UISlider *)slider{
    //ray-声网
//    if (slider.tag == 18526) {
//        [_gpuStreamer.aMixer setMixVolume:slider.value of:_gpuStreamer.micTrack];
//    }else{
//        [_gpuStreamer.aMixer setMixVolume:slider.value of:_gpuStreamer.bgmTrack];
//    }
}
- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
}

#pragma mark ================ 连麦 ===============
/**
 连麦成功，拉取连麦用户的流
 
 @param playurl 流地址
 @param userid 用户ID
 */
-(void)getSmallRTMP_URL:(NSString *)playurl andUserID:(NSString *)userid{
    isAnchorLink = NO;
    if ([_sdkType isEqual:@"1"]) {
        if (_tx_playrtmp) {
            [_tx_playrtmp stopConnect];
            [_tx_playrtmp stopPush];
            [_tx_playrtmp removeFromSuperview];
            _tx_playrtmp = nil;
        }
        _tx_playrtmp = [[TXPlayLinkMic alloc]initWithRTMPURL:@{@"playurl":playurl,@"pushurl":@"0",@"userid":userid} andFrame:CGRectMake(_window_width - 100, _window_height - 110 -statusbarHeight - 150 , 100, 150) andisHOST:YES andAnToAn:NO];
        _tx_playrtmp.delegate = self;
        _tx_playrtmp.tag = 1500 + [userid intValue];
        [self.view addSubview:_tx_playrtmp];
        [self.view insertSubview:_tx_playrtmp aboveSubview:self.tableView];
        //混流
        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":playurl};
        [_tx_playrtmp hunliu:hunDic andHost:NO];
        [self huanCunLianMaiMessage:playurl andUserID:userid];
    }else{
        //ray-声网
//        if (_js_playrtmp) {
//            [_js_playrtmp stopConnect];
//            [_js_playrtmp stopPush];
//            [_js_playrtmp removeFromSuperview];
//            _js_playrtmp = nil;
//        }
//        _js_playrtmp = [[JSPlayLinkMic alloc]initWithRTMPURL:@{@"playurl":playurl,@"pushurl":@"0",@"userid":userid} andFrame:CGRectMake(_window_width - 100, _window_height - 110 -statusbarHeight - 150 , 100, 150) andisHOST:YES];
//        _js_playrtmp.delegate = self;
//        _js_playrtmp.tag = 1500 + [userid intValue];
//        [self.view addSubview:_js_playrtmp];
//        [self.view insertSubview:_js_playrtmp aboveSubview:self.tableView];
//        [self huanCunLianMaiMessage:playurl andUserID:userid];
    }
    
    
    
}
#pragma mark -  腾讯连麦start
-(void)tx_closeuserconnect:(NSString *)uid{
//    if (pkAlertView) {
//        return;
//    }
    if ([_sdkType isEqual:@"1"] && _tx_playrtmp) {
        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":@""};
        [_tx_playrtmp hunliu:hunDic andHost:YES];
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
    //主播端
    if (isAnchorLink) {
        [socketL anchor_DuankaiLink];
    }else{
        [socketL closeconnectuser:uid];
    }
    [self changeLivebroadcastLinkState:NO];
}

#pragma mark -  腾讯连麦end

/**
 有人下麦
 
 @param uid UID
 */
-(void)usercloseConnect:(NSString *)uid{
//    [[YBAgoraManager shareInstance]leaveChannel:uid];
//    if (_js_playrtmp) {
//        [_js_playrtmp stopConnect];
//        [_js_playrtmp stopPush];
//        [_js_playrtmp removeFromSuperview];
//        _js_playrtmp = nil;
//    }
    if([_sdkType isEqual:@"1"]){
        if (_tx_playrtmp) {
            NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":@""};
            [_tx_playrtmp hunliu:hunDic andHost:NO];
            [_tx_playrtmp stopConnect];
            [_tx_playrtmp stopPush];
            [_tx_playrtmp removeFromSuperview];
            _tx_playrtmp = nil;
        }
    }else{
        //ray-声网
        [[YBAgoraManager shareInstance]disConnectLinkMic:uid];
        [_agordLinkView removeFromSuperview];
        _agordLinkView = nil;
        
        [_returnCancle removeFromSuperview];
        _returnCancle = nil;
    }
    [UIApplication sharedApplication].idleTimerDisabled = YES;
}
//主播关闭某人的连麦
-(void)js_closeuserconnect:(NSString *)uid{
    if (pkAlertView) {
        return;
    }
    if ([_sdkType isEqual:@"1"] && _tx_playrtmp) {
        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":@""};
        [_tx_playrtmp hunliu:hunDic andHost:YES];
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
    if (isAnchorLink) {
        [socketL anchor_DuankaiLink];
    }else{
        [socketL closeconnectuser:uid];
    }
    [self changeLivebroadcastLinkState:NO];
}
//请求接口，服务器缓存连麦者信息
- (void)huanCunLianMaiMessage:(NSString *)playurl andUserID:(NSString *)touid{
    NSDictionary *parameterDic = @{
                                   @"pull_url":playurl,
                                   @"touid":touid
                                   };
    [YBToolClass postNetworkWithUrl:@"Live.showVideo" andParameter:parameterDic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
        }
    } fail:^{
    }];
}
/**
 更改Livebroadcast中的连麦状态
 
 @param islianmai 是否在连麦
 */
- (void)changeLivebroadcastLinkState:(BOOL)islianmai{
    isLianmai = islianmai;
}
#pragma mark ================ 改变发送按钮图片 ===============
- (void)ChangePushBtnState{
    if (keyField.text.length > 0) {
        pushBTN.selected = YES;
    }else{
        pushBTN.selected = NO;
    }
}
#pragma mark ================ 守护 ===============
- (void)guardBtnClick{
    gShowView = [[guardShowView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andUserGuardMsg:nil andLiveUid:[Config getOwnID]];
    gShowView.delegate = self;
    [self.view addSubview:gShowView];
    [gShowView show];
}
- (void)removeShouhuView{
    if (gShowView) {
        [gShowView removeFromSuperview];
        gShowView = nil;
    }
    if (anchorView) {
        [anchorView removeFromSuperview];
        anchorView = nil;
    }
    if (redList) {
        [redList removeFromSuperview];
        redList = nil;
    }
    if (pkAlertView) {
        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
//        startPKBtn.hidden = NO;
        [frontView addSubview:startPKBtn];

    }

}
- (void)updateGuardMsg:(NSDictionary *)dic{
    _voteNums = minstr([dic valueForKey:@"votestotal"]);
    [self changeState];
    [self changeGuardNum:minstr([dic valueForKey:@"guard_nums"])];
    if (listView) {
        [listView listReloadNoew];
    }
    
}
#pragma mark ================ 主播连麦 ===============
- (void)showAnchorView{
    anchorView = [[anchorOnline alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    anchorView.delegate = self;
    anchorView.myStream = minstr([_roomDic valueForKey:@"stream"]);
    [self.view addSubview:anchorView];
    [anchorView show];
}
- (void)startLink:(NSDictionary *)dic andMyInfo:(NSDictionary *)myInfo{
    if (musicV) {
        YBWeakSelf;
        UIAlertController *bgmAlert = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"连麦时需要关闭背景音乐") preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        }];
        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [weakSelf closeBgm];
            [socketL anchor_startLink:dic andMyInfo:myInfo];

        }];
        [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
        [bgmAlert addAction:cancelAction];
        [bgmAlert addAction:sureAction];
        [[[MXBADelegate sharedAppDelegate]topViewController] presentViewController:bgmAlert animated:YES completion:nil];
    }else{
        [MBProgressHUD showError:YZMsg(@"连麦请求已发送")];
        [socketL anchor_startLink:dic andMyInfo:myInfo];

    }

}
#pragma mark ---声网主播同意用户连麦
-(void)broadcastAgreeUserLink:(NSString *)toUid
{
    if (![_sdkType isEqual:@"1"]) {
        if(!_agordLinkView){
            _agordLinkView = [[AgordLinkMic alloc]initWithFrame:CGRectMake(_window_width - 100, _window_height - 110 - ShowDiff - 150 , 100, 150)];
            _agordLinkView.connUserId =toUid;
            [self.view addSubview:_agordLinkView];
            
            _returnCancle = [UIButton buttonWithType:UIButtonTypeCustom];
            _returnCancle.tintColor = [UIColor whiteColor];
            [_returnCancle setImage:[UIImage imageNamed:@"直播间观众—关闭"] forState:UIControlStateNormal];
            _returnCancle.backgroundColor = [UIColor clearColor];
            [_returnCancle setTitle:toUid forState:UIControlStateNormal];
            [_returnCancle setTitleColor:[UIColor clearColor] forState:UIControlStateNormal];
            [_returnCancle addTarget:self action:@selector(returnCancles:) forControlEvents:UIControlEventTouchUpInside];
            _returnCancle.frame = CGRectMake(_window_width-37, _window_height - 110 - ShowDiff - 150+5, 34, 34);
            _returnCancle.imageView.contentMode = UIViewContentModeScaleAspectFit;
            [self.view addSubview:_returnCancle];
        }
        NSDictionary *parDic = @{@"method":@"ConnectVideo",@"uid":toUid};
        [[YBAgoraManager shareInstance]sendStreamMsg:parDic andStreamId:[_roomDic valueForKey:@"stream"]];

        [YBAgoraManager shareInstance].isLiveLinkMic = NO;
        [[YBAgoraManager shareInstance]showWithCanvasView:_agordLinkView andUserId:toUid isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
    }
}
-(void)returnCancles:(UIButton *)sender{
    
    NSString *touid =sender.titleLabel.text;
    if(![_sdkType isEqual:@"1"]){
        [[YBAgoraManager shareInstance]disConnectLinkMic:touid];
        [YBAgoraManager shareInstance].isLiveLinkMic = NO;
        [[YBAgoraManager shareInstance]stopChannelMediaRelay];

        [_agordLinkView removeFromSuperview];
        _agordLinkView = nil;
        [_returnCancle removeFromSuperview];
        _returnCancle = nil;
            
        [UIView animateWithDuration:0.3 animations:^{
            _pushPreview.frame = CGRectMake(0, 0, _window_width, _window_height);
            bGimg.frame = _pushPreview.frame;
        }];
        [self destroyPkAnchorView];
    }
    //主播端
    if (isAnchorLink) {
        [socketL anchor_DuankaiLink];
        
        NSDictionary *parDic = @{@"method":@"LiveConnect",@"pkuid":@"0"};
        [[YBAgoraManager shareInstance]sendStreamMsg:parDic andStreamId:[_roomDic valueForKey:@"stream"]];
    }else{
        [socketL closeconnectuser:touid];
        NSDictionary *parDic = @{@"method":@"ConnectVideo",@"uid":@"0"};
        [[YBAgoraManager shareInstance]sendStreamMsg:parDic andStreamId:[_roomDic valueForKey:@"stream"]];

    }
    [self changeLivebroadcastLinkState:NO];

}
- (void)anchor_agreeLink:(NSDictionary *)dic{
    [MBProgressHUD showError:YZMsg(@"对方主播接受了您的连麦请求，开始连麦")];

    isAnchorLink = YES;
    pkBackImgView.hidden = NO;
    isLianmai = YES;
    [UIView animateWithDuration:0.3 animations:^{
        _pushPreview.frame = CGRectMake(0, 130+statusbarHeight, _window_width/2, _window_width*2/3);
        bGimg.frame = _pushPreview.frame;
    }];
    if ([_sdkType isEqual:@"1"]) {
        if (_tx_playrtmp) {
            [_tx_playrtmp stopConnect];
            [_tx_playrtmp stopPush];
            [_tx_playrtmp removeFromSuperview];
            _tx_playrtmp = nil;
        }
        _tx_playrtmp = [[TXPlayLinkMic alloc]initWithRTMPURL:@{@"playurl":minstr([dic valueForKey:@"pkpull"]),@"pushurl":@"0",@"userid":minstr([dic valueForKey:@"pkuid"])} andFrame:CGRectMake(_window_width/2, 130+statusbarHeight , _window_width/2, _window_width*2/3) andisHOST:YES andAnToAn:YES];
        _tx_playrtmp.delegate = self;
        _tx_playrtmp.tag = 1500 + [minstr([dic valueForKey:@"pkuid"]) intValue];
        [self.view addSubview:_tx_playrtmp];
        [self.view insertSubview:redBagBtn aboveSubview:_tx_playrtmp];
        [self.view insertSubview:toolBar aboveSubview:_tx_playrtmp];
        if (musicV) {
            [self.view insertSubview:musicV aboveSubview:_tx_playrtmp];
        }
        if (haohualiwuV) {
            [self.view insertSubview:haohualiwuV aboveSubview:_tx_playrtmp];
        }

        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":minstr([dic valueForKey:@"pkpull"])};
        [_tx_playrtmp hunliu:hunDic andHost:YES];
    }else{
        [self getSwRtcPKToken:dic];
        NSDictionary *parDic = @{@"method":@"LiveConnect",@"pkuid":minstr([dic valueForKey:@"pkuid"])};
        [[YBAgoraManager shareInstance]sendStreamMsg:parDic andStreamId:[_roomDic valueForKey:@"stream"]];
    }
    [self closeBgm];

    startPKBtn.hidden = NO;
    [frontView addSubview:startPKBtn];

}
-(void)getSwRtcPKToken:(NSDictionary *)infoDic{
    NSString *url = [purl stringByAppendingFormat:@"?service=Linkmic.getSwRtcPKToken"];

    NSDictionary *parDic = @{@"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             @"stream":urlStrtimestring,
                             @"tostream":minstr([infoDic valueForKey:@"tostream"])
    };
    [YBNetworking postWithUrl:url Dic:parDic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSDictionary *resultDic = [[data valueForKey:@"info"] firstObject];
            NSLog(@"-------:%@",resultDic);
            if ([code isEqual:@"0"]) {
                if(!_agordLinkView){
                    _agordLinkView = [[AgordLinkMic alloc]initWithFrame:CGRectMake(_window_width/2, 130+statusbarHeight, _window_width/2, _window_width*2/3)];
                    _agordLinkView.connUserId = minstr([infoDic valueForKey:@"pkuid"]);
                    [self.view addSubview:_agordLinkView];
                    
                    _returnCancle = [UIButton buttonWithType:UIButtonTypeCustom];
                    _returnCancle.tintColor = [UIColor whiteColor];
                    [_returnCancle setImage:[UIImage imageNamed:@"直播间观众—关闭"] forState:UIControlStateNormal];
                    _returnCancle.backgroundColor = [UIColor clearColor];
                    [_returnCancle setTitle:minstr([infoDic valueForKey:@"pkuid"]) forState:UIControlStateNormal];
                    [_returnCancle setTitleColor:[UIColor clearColor] forState:UIControlStateNormal];
                    [_returnCancle addTarget:self action:@selector(returnCancles:) forControlEvents:UIControlEventTouchUpInside];
                    _returnCancle.frame = CGRectMake(_window_width-37, 130+statusbarHeight+5, 34, 34);
                    _returnCancle.imageView.contentMode = UIViewContentModeScaleAspectFit;
                    [self.view addSubview:_returnCancle];
                    //
                    [self showPkAnchorView:minstr([infoDic valueForKey:@"pkuid"])];
                }
                [MBProgressHUD showError:YZMsg(@"对方主播接受了您的连麦请求，开始连麦")];
                isAnchorLink = YES;
                pkBackImgView.hidden = NO;
                isLianmai = YES;
                [UIView animateWithDuration:0.3 animations:^{
                    _pushPreview.frame = CGRectMake(0, 130+statusbarHeight, _window_width/2, _window_width*2/3);
                    bGimg.frame = _pushPreview.frame;
                }];
                [self.view insertSubview:redBagBtn aboveSubview:_agordLinkView];
                [self.view insertSubview:toolBar aboveSubview:_agordLinkView];
                if (musicV) {
                    [self.view insertSubview:musicV aboveSubview:_agordLinkView];
                }
                if (haohualiwuV) {
                    [self.view insertSubview:haohualiwuV aboveSubview:_agordLinkView];
                }
                [[YBAgoraManager shareInstance]startOrUpdateJoinChannelMediaRelayWithDic:resultDic andChannelName:minstr([infoDic valueForKey:@"tostream"])];
                [[YBAgoraManager shareInstance]showWithCanvasView:_agordLinkView andUserId:minstr([infoDic valueForKey:@"pkuid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
            }
        } Fail:^(id fail) {
            
        }];
}
//断开连麦
- (void)anchor_stopLink:(NSDictionary *)dic{
    startPKBtn.hidden = YES;
    if (pkAlertView) {
        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
//    [socketL notAgreeClick];
    [startPKBtn removeFromSuperview];
    //ray-声网
    if(_agordLinkView){
        [[YBAgoraManager shareInstance]stopChannelMediaRelay];
        [_agordLinkView removeFromSuperview];
        _agordLinkView = nil;
        
        [_returnCancle removeFromSuperview];
        _returnCancle = nil;
    }
    [self destroyPkAnchorView];
    if (_tx_playrtmp) {
        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":@""};
        [_tx_playrtmp hunliu:hunDic andHost:YES];
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
    if (pkView) {
        [pkView removeTimer];
        [pkView removeFromSuperview];
        pkView = nil;
    }

    [UIView animateWithDuration:0.3 animations:^{
        _pushPreview.frame = CGRectMake(0, 0, _window_width, _window_height);
        bGimg.frame = _pushPreview.frame;
        //ray-声网
//        if (![_sdkType isEqual:@"1"]) {
//             _gpuStreamer.preview.size = CGSizeMake(_window_width, _window_height);
//        }
    }];
    isAnchorLink = NO;
    [self changeLivebroadcastLinkState:NO];
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    
}
#pragma mark ================ PK ===============
-(void)removePKView{
    if (pkView) {
        [pkView removeTimer];
        [pkView removeFromSuperview];
        pkView = nil;
        if (isAnchorLink) {
            startPKBtn.hidden = NO;
            [frontView addSubview:startPKBtn];
            
        }
    }
}
- (void)startPKBtnClick{
//    startPKBtn.hidden = YES;
    [startPKBtn removeFromSuperview];
    if (pkAlertView) {
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
    pkAlertView = [[anchorPKAlert alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight+_window_width*2/6, _window_width, 70) andIsStart:YES];
    pkAlertView.delegate = self;
    [self.view addSubview:pkAlertView];
    [socketL launchPK];
    [socketL setWaitPKTime:10];
    [MBProgressHUD showError:YZMsg(@"PK请求已发送")];
}
-(void)sendWaitPkTime:(int)time
{
    [socketL setWaitPKTime:time];
}
- (void)showPKView{
    if (pkAlertView) {
        [socketL setWaitPKTime:0];
        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
//    startPKBtn.hidden = YES;
    [startPKBtn removeFromSuperview];
    if (pkView) {
        [pkView removeFromSuperview];
        pkView = nil;
    }
    pkView = [[anchorPKView alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight, _window_width, _window_width*2/3+20) andTime:@"300"];
    pkView.delegate = self;
    [self.view addSubview:pkView];
}
- (void)showPKButton{
    if (pkAlertView) {
        [socketL setWaitPKTime:0];

        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
    [frontView addSubview:startPKBtn];
    startPKBtn.hidden = NO;

}
-(void)hidePKButton{
    if (pkAlertView) {
        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
    startPKBtn.hidden = YES;

}
- (void)showPKResult:(NSDictionary *)dic{
    int win;
    if ([minstr([dic valueForKey:@"win_uid"]) isEqual:@"0"]) {
        win = 0;
    }else if ([minstr([dic valueForKey:@"win_uid"]) isEqual:[Config getOwnID]]) {
        win = 1;
    }else{
        win = 2;
    }
    [pkView showPkResult:dic andWin:win];
}
- (void)changePkProgressViewValue:(NSDictionary *)dic{
    NSString *blueNum;
    NSString *redNum;
    CGFloat progress = 0.0;
    if ([minstr([dic valueForKey:@"pkuid1"]) isEqual:[Config getOwnID]]) {
        blueNum = minstr([dic valueForKey:@"pktotal1"]);
        redNum = minstr([dic valueForKey:@"pktotal2"]);
    }else{
        redNum = minstr([dic valueForKey:@"pktotal1"]);
        blueNum = minstr([dic valueForKey:@"pktotal2"]);
    }
    if ([blueNum isEqual:@"0"]) {
        progress = 0.2;
    }else if ([redNum isEqual:@"0"]) {
        progress = 0.8;
    }else{
        CGFloat ppp = [blueNum floatValue]/([blueNum floatValue] + [redNum floatValue]);
        if (ppp < 0.2) {
            progress = 0.2;
        }else if (ppp > 0.8){
            progress = 0.8;
        }else{
            progress = ppp;
        }
    }

    [pkView updateProgress:progress withBlueNum:blueNum withRedNum:redNum];
}
- (void)duifangjujuePK{
    if (pkAlertView) {
        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
//    startPKBtn.hidden = NO;
    [frontView addSubview:startPKBtn];

}
#pragma mark ================ 红包 ===============
- (void)showRedView{
    redBview = [[redBagView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    __weak Livebroadcast *wSelf = self;
    redBview.block = ^(NSString *type) {
        [wSelf sendRedBagSuccess:type];
    };
    redBview.zhuboDic = _roomDic;
    [self.view addSubview:redBview];
}
- (void)sendRedBagSuccess:(NSString *)type{
    [redBview removeFromSuperview];
    redBview = nil;
    if ([type isEqual:@"909"]) {
        return;
    }
    [socketL fahongbaola];
}
- (void)showRedbag:(NSDictionary *)dic{
    redBagBtn.hidden = NO;
    NSString *uname;
    if ([minstr([dic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
        uname = YZMsg(@"主播");
    }else{
        uname = minstr([dic valueForKey:@"uname"]);
    }
    NSString *levell = @" ";
    NSString *ID = @" ";
    NSString *vip_type = @"0";
    NSString *liangname = @"0";
    
    NSString *ct;
    NSString *languageStr= [PublicObj getCurrentLanguage];
    if ([languageStr isEqual:@"en"]) {
        ct = minstr([dic valueForKey:@"ct_en"]);
    }else{
        ct =minstr([dic valueForKey:@"ct"]);
    }
    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",levell,@"levelI",ID,@"id",@"redbag",@"titleColor",vip_type,@"vip_type",liangname,@"liangname",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];

}
- (void)redBagBtnClick{
    redList = [[redListView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) withZHuboMsg:_roomDic];
    redList.delegate =self;
    [self.view addSubview:redList];
}

#pragma mark ============连麦开关n按钮点击=============
- (void)linkSwitchBtnClick:(UIButton *)sender{
}
- (void)linkSwitchBtnClick{
    [YBToolClass postNetworkWithUrl:@"Linkmic.setMic" andParameter:@{@"ismic":@(!userCanLink)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            userCanLink = !userCanLink;
        }
        [MBProgressHUD showError:msg];
        
    } fail:^{
    }];
}
#pragma mark ===========================   腾讯推流start   =======================================
-(void)txBaseBeauty {
    _filterArray = [NSMutableArray new];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"原图");
        v.face = [UIImage imageNamed:getImagename(@"orginal")];
        v;
    })];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"美白");
        v.face = [UIImage imageNamed:getImagename(@"fwhite")];
        v;
    })];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"浪漫");
        v.face = [UIImage imageNamed:getImagename(@"langman")];
        v;
    })];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"清新");
        v.face = [UIImage imageNamed:getImagename(@"qingxin")];
        v;
    })];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"唯美");
        v.face = [UIImage imageNamed:getImagename(@"weimei")];
        v;
    })];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"粉嫩");
        v.face = [UIImage imageNamed:getImagename(@"fennen")];
        v;
    })];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"怀旧");
        v.face = [UIImage imageNamed:getImagename(@"huaijiu")];
        v;
    })];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"蓝调");
        v.face = [UIImage imageNamed:getImagename(@"landiao")];
        v;
    })];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"清凉");
        v.face = [UIImage imageNamed:getImagename(@"qingliang")];
        v;
    })];
    [_filterArray addObject:({
        V8LabelNode *v = [V8LabelNode new];
        v.title = YZMsg(@"日系");
        v.face = [UIImage imageNamed:getImagename(@"rixi")];
        v;
    })];
    
    
    
    //美颜拉杆浮层
    float   beauty_btn_width  = 65;
    float   beauty_btn_height = 30;//19;
    
    float   beauty_btn_count  = 2;
    
    float   beauty_center_interval = (self.view.width - 30 - beauty_btn_width)/(beauty_btn_count - 1);
    float   first_beauty_center_x  = 15 + beauty_btn_width/2;
    int ib = 0;
    _vBeauty = [[UIView  alloc] init];
    _vBeauty.frame = CGRectMake(0, self.view.height-185-statusbarHeight, self.view.width, 185+statusbarHeight);
    [_vBeauty setBackgroundColor:[UIColor whiteColor]];
    float   beauty_center_y = _vBeauty.height - 30;//35;
    _beautyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _beautyBtn.center = CGPointMake(first_beauty_center_x, beauty_center_y);
    _beautyBtn.bounds = CGRectMake(0, 0, beauty_btn_width, beauty_btn_height);
    [_beautyBtn setImage:[UIImage imageNamed:@"white_beauty"] forState:UIControlStateNormal];
    [_beautyBtn setImage:[UIImage imageNamed:@"white_beauty_press"] forState:UIControlStateSelected];
    [_beautyBtn setTitle:YZMsg(@"美颜") forState:UIControlStateNormal];
    [_beautyBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [_beautyBtn setTitleColor:normalColors forState:UIControlStateSelected];
    _beautyBtn.titleEdgeInsets = UIEdgeInsetsMake(0, 3, 0, 0);
    _beautyBtn.titleLabel.font = [UIFont systemFontOfSize:16];
    _beautyBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
    _beautyBtn.tag = 0;
    _beautyBtn.selected = YES;
    [_beautyBtn addTarget:self action:@selector(selectBeauty:) forControlEvents:UIControlEventTouchUpInside];
    ib++;
    _filterBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _filterBtn.center = CGPointMake(first_beauty_center_x + ib*beauty_center_interval, beauty_center_y);
    _filterBtn.bounds = CGRectMake(0, 0, beauty_btn_width, beauty_btn_height);
    [_filterBtn setImage:[UIImage imageNamed:@"beautiful"] forState:UIControlStateNormal];
    [_filterBtn setImage:[UIImage imageNamed:@"beautiful_press"] forState:UIControlStateSelected];
    [_filterBtn setTitle:YZMsg(@"滤镜") forState:UIControlStateNormal];
    [_filterBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [_filterBtn setTitleColor:normalColors forState:UIControlStateSelected];
    _filterBtn.titleEdgeInsets = UIEdgeInsetsMake(0, 3, 0, 0);
    _filterBtn.titleLabel.font = [UIFont systemFontOfSize:16];
    _filterBtn.tag = 1;
    [_filterBtn addTarget:self action:@selector(selectBeauty:) forControlEvents:UIControlEventTouchUpInside];
    ib++;
    _beautyLabel = [[UILabel alloc] initWithFrame:CGRectMake(10,  _beautyBtn.top - 95, 40, 20)];
    _beautyLabel.text = YZMsg(@"美白");
    _beautyLabel.font = [UIFont systemFontOfSize:12];
    _beautyLabel.adjustsFontSizeToFitWidth = YES;
    _sdBeauty = [[UISlider alloc] init];
    _sdBeauty.frame = CGRectMake(_beautyLabel.right, _beautyBtn.top - 95, self.view.width - _beautyLabel.right - 10, 20);
    _sdBeauty.minimumValue = 0;
    _sdBeauty.maximumValue = 9;
    _sdBeauty.value = 6.3;
    [_sdBeauty setThumbImage:[UIImage imageNamed:@"slider"] forState:UIControlStateNormal];
    [_sdBeauty setMinimumTrackImage:[PublicObj getImgWithColor:normalColors] forState:UIControlStateNormal];
    [_sdBeauty setMaximumTrackImage:[UIImage imageNamed:@"gray"] forState:UIControlStateNormal];
    [_sdBeauty addTarget:self action:@selector(txsliderValueChange:) forControlEvents:UIControlEventValueChanged];
    _sdBeauty.tag = 0;
    
    
    _whiteLabel = [[UILabel alloc] initWithFrame:CGRectMake(10, _beautyBtn.top - 55, 40, 20)];
    
    _whiteLabel.text = YZMsg(@"美颜");
    _whiteLabel.font = [UIFont systemFontOfSize:12];
    _whiteLabel.adjustsFontSizeToFitWidth = YES;
    _sdWhitening = [[UISlider alloc] init];
    
    _sdWhitening.frame =  CGRectMake(_whiteLabel.right, _beautyBtn.top - 55, self.view.width - _whiteLabel.right - 10, 20);
    
    _sdWhitening.minimumValue = 0;
    _sdWhitening.maximumValue = 9;
    _sdWhitening.value = 2.7;
    [_sdWhitening setThumbImage:[UIImage imageNamed:@"slider"] forState:UIControlStateNormal];
    [_sdWhitening setMinimumTrackImage:[PublicObj getImgWithColor:normalColors] forState:UIControlStateNormal];//[UIImage imageNamed:@"green"]
    [_sdWhitening setMaximumTrackImage:[UIImage imageNamed:@"gray"] forState:UIControlStateNormal];
    [_sdWhitening addTarget:self action:@selector(txsliderValueChange:) forControlEvents:UIControlEventValueChanged];
    _sdWhitening.tag = 1;
    
    _filterPickerView = [[V8HorizontalPickerView alloc] initWithFrame:CGRectMake(0, 10, self.view.width, 115)];
    _filterPickerView.textColor = [UIColor grayColor];
    _filterPickerView.elementFont = [UIFont fontWithName:@"" size:14];
    _filterPickerView.delegate = self;
    _filterPickerView.dataSource = self;
    _filterPickerView.hidden = YES;
    
    UIImageView *sel = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"filter_selected"]];
    
    _filterPickerView.selectedMaskView = sel;
    _filterType = 0;
    
    [_vBeauty addSubview:_beautyLabel];
    [_vBeauty addSubview:_whiteLabel];
    [_vBeauty addSubview:_sdWhitening];
    [_vBeauty addSubview:_sdBeauty];
    [_vBeauty addSubview:_beautyBtn];
    [_vBeauty addSubview:_bigEyeLabel];
    [_vBeauty addSubview:_sdBigEye];
    [_vBeauty addSubview:_slimFaceLabel];
    [_vBeauty addSubview:_sdSlimFace];
    [_vBeauty addSubview:_filterPickerView];
    [_vBeauty addSubview:_filterBtn];
    _vBeauty.hidden = YES;
    [self.view addSubview: _vBeauty];
}
-(void)userTXBase {
    if (!_vBeauty) {
        [self txBaseBeauty];
    }
    _preFrontView.hidden = YES;
    _vBeauty.hidden = NO;
    [self.view bringSubviewToFront:_vBeauty];
}
#pragma mark  --RTC推流
-(void)txRTCPush{

    [[YBLiveRTCManager shareInstance]initWithLiveMode:V2TXLiveMode_RTC andPushData:_pushSettings];
    [[YBLiveRTCManager shareInstance]setPushView:_pushPreview];
    [[YBLiveRTCManager shareInstance] setBeautyLevel:_tx_beauty_level WhitenessLevel:_tx_whitening_level IsTXfiter:[common getIsTXfiter]];
    if ([[common getIsTXfiter]isEqual:@"1"]) {
    }else{
        isMirror = YES;
    }
    [YBLiveRTCManager shareInstance].delegate = self;
    _notification = [CWStatusBarNotification new];
    _notification.notificationLabelBackgroundColor = [UIColor redColor];
    _notification.notificationLabelTextColor = [UIColor whiteColor];
}
#pragma mark  --RTC推流回调
/**
 * 推流器连接状态回调通知
 *
 * @param status    推流器连接状态 {@link V2TXLivePushStatus}。
 * @param msg       连接状态信息。
 * @param extraInfo 扩展信息。
 */
-(void)ybRTCPushStatusUpdate:(V2TXLivePushStatus)status message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (status == V2TXLivePushStatusDisconnected) {
            /// 与服务器断开连接
            NSLog(@"PUSH_EVT_PUSH_BEGIN网络断连,且经多次重连抢救无效,可以放弃治疗,更多重试请自行重启推流");
            [_notification displayNotificationWithMessage:@"网络断连" forDuration:5];
            [self hostStopRoom];

        }else if(status == V2TXLivePushStatusConnecting){
            /// 正在连接服务器
             if (isPUSH_WARNING_RECONNECT) {
                isPUSH_WARNING_RECONNECT = NO;
                [self checkLiveingStatus];
             }

        }else if(status == V2TXLivePushStatusConnectSuccess){
            /// 连接服务器成功
            [self changePlayState:1];

        }else if(status == V2TXLivePushStatusConnectSuccess){
            ///  重连服务器中
            [_notification displayNotificationWithMessage:@"网络断连, 已启动自动重连" forDuration:5];
        }
    });
}
#pragma mark -美狐回调
-(void)MHBeautyBlock:(V2TXLiveVideoFrame *)srcFrame dstFrame:(V2TXLiveVideoFrame *)dstFrame
{
    dstFrame.textureId= [self.beautyManager getTextureProcessWithTexture:srcFrame.textureId width:(GLint)srcFrame.width height:(GLint)srcFrame.height mirror:YES];
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.menusView) {
            if (!isLoadWebSprout) {
                isLoadWebSprout = YES;
                [self.menusView setupDefaultBeautyAndFaceValue];

            }
        }
    });

}
#pragma mark -RTC推流
-(void)txStartRTC{
    [[YBLiveRTCManager shareInstance]startPush:_hostURL isGameLive:_isGameLive];
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
}
-(void)ybPushLiveStatus:(V2TXLiveCode)pushStatus
{
    if (pushStatus == V2TXLIVE_OK) {
        NSLog(@"LIVEBROADCAST --:推流成功、停止推流");
    }else if (pushStatus == V2TXLIVE_ERROR_INVALID_PARAMETER){
        [_notification displayNotificationWithMessage:@"操作失败，url 不合法" forDuration:5];
        NSLog(@"推流器启动失败");
    }else if (pushStatus == V2TXLIVE_ERROR_INVALID_LICENSE){
        [_notification displayNotificationWithMessage:@"操作失败，license 不合法，鉴权失败" forDuration:5];
        NSLog(@"推流器启动失败");
    }else if (pushStatus == V2TXLIVE_ERROR_REFUSED){
        [_notification displayNotificationWithMessage:@"操作失败，RTC 不支持同一设备上同时推拉同一个 StreamId" forDuration:5];
        NSLog(@"推流器启动失败");
    }else if (pushStatus == V2TXLIVE_WARNING_NETWORK_BUSY){
        [_notification displayNotificationWithMessage:
            @"您当前的网络环境不佳，请尽快更换网络保证正常直播" forDuration:5];
    }
}
#pragma mark -停止RTC推流
-(void)txStopRTC{
    [[YBLiveRTCManager shareInstance]stopPushIsGameLive:_isGameLive];
    [[UIApplication sharedApplication] setIdleTimerDisabled:NO];
    for (TXPlayLinkMic *playv in self.view.subviews) {
        if ([playv isKindOfClass:[TXPlayLinkMic class]]) {
            [playv stopConnect];
            [playv stopPush];
            [playv removeFromSuperview];
        }
    }

}
#pragma tx_play_linkmic 代理
-(void)tx_closeUserbyVideo:(NSDictionary *)subdic{
    [MBProgressHUD showError:@"播放失败"];
}
-(void) onNetStatus:(NSDictionary*) param{
    
}
-(void) txsliderValueChange:(UISlider*) obj {
    if (obj.tag == 1) { //美颜
        _tx_beauty_level = obj.value;
        [[YBLiveRTCManager shareInstance]setBeautyLevel:_tx_beauty_level WhitenessLevel:_tx_whitening_level];
    } else if (obj.tag == 0) { //美白
        _tx_whitening_level = obj.value;
        [[YBLiveRTCManager shareInstance]setBeautyLevel:_tx_beauty_level WhitenessLevel:_tx_whitening_level];
    } else if (obj.tag == 2) { //大眼
        _tx_eye_level = obj.value;
        [[YBLiveRTCManager shareInstance]setYBEyeScaleLevel:_tx_eye_level];
    } else if (obj.tag == 3) { //瘦脸
        _tx_face_level = obj.value;
        [[YBLiveRTCManager shareInstance]setYBFaceScaleLevel:_tx_face_level];
    } else if (obj.tag == 4) {// 背景音乐音量
        [[YBLiveRTCManager shareInstance]setYBBGMVolume:(obj.value/obj.maximumValue)];
    } else if (obj.tag == 5) { // 麦克风音量
//        [_txLivePublisher setMicVolume:(obj.value/obj.maximumValue)];
    }
}

-(void)selectBeauty:(UIButton *)button{
    switch (button.tag) {
        case 0: {
            _sdWhitening.hidden = NO;
            _sdBeauty.hidden    = NO;
            _beautyLabel.hidden = NO;
            _whiteLabel.hidden  = NO;
            _bigEyeLabel.hidden = NO;
            _sdBigEye.hidden    = NO;
            _slimFaceLabel.hidden = NO;
            _sdSlimFace.hidden    = NO;
            _beautyBtn.selected  = YES;
            _filterBtn.selected = NO;
            _filterPickerView.hidden = YES;
            _vBeauty.frame = CGRectMake(0, self.view.height-185-statusbarHeight, self.view.width, 185+statusbarHeight);
        }break;
        case 1: {
            _sdWhitening.hidden = YES;
            _sdBeauty.hidden    = YES;
            _beautyLabel.hidden = YES;
            _whiteLabel.hidden  = YES;
            _bigEyeLabel.hidden = YES;
            _sdBigEye.hidden    = YES;
            _slimFaceLabel.hidden = YES;
            _sdSlimFace.hidden    = YES;
            _beautyBtn.selected  = NO;
            _filterBtn.selected = YES;
            _filterPickerView.hidden = NO;
            [_filterPickerView scrollToElement:_filterType animated:NO];
        }
            _beautyBtn.center = CGPointMake(_beautyBtn.center.x, _vBeauty.frame.size.height - 35-statusbarHeight);
            _filterBtn.center = CGPointMake(_filterBtn.center.x, _vBeauty.frame.size.height - 35-statusbarHeight);
    }
}
//设置美颜滤镜
#pragma mark - HorizontalPickerView DataSource Methods/Users/<USER>/Work/RTMPDemo_PituMerge/RTMPSDK/webrtc
- (NSInteger)numberOfElementsInHorizontalPickerView:(V8HorizontalPickerView *)picker {
    return [_filterArray count];
}
#pragma mark - HorizontalPickerView Delegate Methods
- (UIView *)horizontalPickerView:(V8HorizontalPickerView *)picker viewForElementAtIndex:(NSInteger)index {
    
    V8LabelNode *v = [_filterArray objectAtIndex:index];
    return [[UIImageView alloc] initWithImage:v.face];
    
}
- (NSInteger) horizontalPickerView:(V8HorizontalPickerView *)picker widthForElementAtIndex:(NSInteger)index {
    
    return 90;
}
- (void)horizontalPickerView:(V8HorizontalPickerView *)picker didSelectElementAtIndex:(NSInteger)index {
    _filterType = index;
    [self filterSelected:index];
}
- (void)filterSelected:(NSInteger)index {
    NSString* lookupFileName = @"";
    switch (index) {
        case FilterType_None:
            break;
        case FilterType_white:
            lookupFileName = @"filter_white";
            break;
        case FilterType_langman:
            lookupFileName = @"filter_langman";
            break;
        case FilterType_qingxin:
            lookupFileName = @"filter_qingxin";
            break;
        case FilterType_weimei:
            lookupFileName = @"filter_weimei";
            break;
        case FilterType_fennen:
            lookupFileName = @"filter_fennen";
            break;
        case FilterType_huaijiu:
            lookupFileName = @"filter_huaijiu";
            break;
        case FilterType_landiao:
            lookupFileName = @"filter_landiao";
            break;
        case FilterType_qingliang:
            lookupFileName = @"filter_qingliang";
            break;
        case FilterType_rixi:
            lookupFileName = @"filter_rixi";
            break;
        default:
            break;
    }
    NSString * path = [[NSBundle mainBundle] pathForResource:lookupFileName ofType:@"png"];
    if (path != nil && index != FilterType_None ) {
        UIImage *image = [UIImage imageWithContentsOfFile:path];
        [[YBLiveRTCManager shareInstance]setYBFilter:image];
    }
    else {
        [[YBLiveRTCManager shareInstance]setYBFilter:nil];
    }
}

#pragma mark ================ TXVideoProcessDelegate ===============
- (MHBeautyManager *)beautyManager {
    if (!_beautyManager) {
        _beautyManager = [[MHBeautyManager alloc] init];
        [_beautyManager setWatermarkRect:CGRectMake(0.2, 0.1, 0.1, 0)];
    }
    return _beautyManager;
}
#pragma mark - MHMenuViewDelegate
- (void)beautyEffectWithLevel:(NSInteger)beauty whitenessLevel:(NSInteger)white ruddinessLevel:(NSInteger)ruddiness {
    //暂时用腾讯的美颜
    _tx_beauty_level = 9;
    _tx_whitening_level = 3;
    
//    NSString *mopi =  [sproutCommon getYBskin_smooth];//磨皮数值
//    NSString *white = [sproutCommon getYBskin_whiting];//美白数值
//    NSString *hongrun = [sproutCommon getYBskin_tenderness];//红润数值

//    [_txLivePublisher setBeautyStyle:0 beautyLevel:beauty whitenessLevel:white ruddinessLevel:ruddiness];
}

#pragma mark -初始化美颜UI
- (void)initMeiyanFaceUI{
    _menusView = [[MHMeiyanMenusView alloc] initWithFrame:CGRectMake(0, window_height - MHMeiyanMenuHeight - BottomIndicatorHeight, window_width, MHMeiyanMenuHeight) superView:self.view  beautyManager:self.beautyManager];
}


- (void)onTextureDestoryed{
    NSLog(@"[self.tiSDKManager destroy];");
}
#pragma mark ===========================   腾讯推流end   =======================================

-(void)addCycleScroll:(NSArray *)titleArr{
    NSArray * sliderMuArr;
    
    //奖池关闭
    if ([jackpot_level isEqual:@"-1"]) {
        if ([_dailytask_switch isEqual:@"0"]) {
            return;
        }else{
            sliderMuArr= @[getImagename(@"每日任务")];
        }

    }else{
        if ([_dailytask_switch isEqual:@"0"]) {
            sliderMuArr= @[getImagename(@"Jackpot_btnBack")];
        }else{
            sliderMuArr= @[getImagename(@"每日任务"),getImagename(@"Jackpot_btnBack")];
        }

    }
    if (_cycleScroll) {
        [_cycleScroll removeFromSuperview];
        _cycleScroll = nil;
    }
    _cycleScroll = [[SDCycleScrollView alloc]initWithFrame: CGRectMake(10, statusbarHeight + 135, 80, 60)];
    _cycleScroll.backgroundColor = [UIColor clearColor];
    _cycleScroll.bannerImageViewContentMode = UIViewContentModeScaleAspectFit;
    _cycleScroll.delegate = self;
    _cycleScroll.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
    [self.view addSubview:_cycleScroll];
    _cycleScroll.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    _cycleScroll.autoScrollTimeInterval = 3.0;//轮播时间间隔，默认1.0秒，可自定义
    _cycleScroll.currentPageDotColor = [UIColor whiteColor];
    _cycleScroll.pageDotColor = [[UIColor whiteColor] colorWithAlphaComponent:0.4];
    _cycleScroll.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
    _cycleScroll.imageURLStringsGroup = sliderMuArr;
    _cycleScroll.titlesGroup = titleArr;
    _cycleScroll.titleLabelBackgroundColor = [UIColor clearColor];
    _cycleScroll.titleLabelHeight = 40;
    _cycleScroll.titleLabelTextFont = SYS_Font(12);
    
    if (sysPageControl) {
        [sysPageControl removeFromSuperview];
        sysPageControl =  nil;
    }
    sysPageControl  = [[YBPageControl alloc]init];
    sysPageControl.pageIndicatorTintColor = [UIColor grayColor];
    sysPageControl.currentPageIndicatorTintColor = [UIColor whiteColor];
    sysPageControl.numberOfPages = sliderMuArr.count;
    sysPageControl.transform=CGAffineTransformScale(CGAffineTransformIdentity, 0.7, 0.7);
    [self.view addSubview:sysPageControl];
    [sysPageControl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(10);
        if (@available(iOS 14.0,*)) {
            make.width.mas_equalTo(200);
        }else {
            make.width.mas_equalTo(80);
        }
        make.centerX.equalTo(_cycleScroll);
        make.top.equalTo(_cycleScroll.mas_bottom).offset(0);
    }];
}
/** 点击图片回调 */
- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index;
{
    NSLog(@"点击轮播----------index:%ld",index);
    if ([_dailytask_switch isEqual:@"0"]) {
        [self showJackpotView];

    }else{
    if (index == 0) {
            YBWeakSelf;
            if (!_taskView) {
                _taskView = [[DayTaskView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)andLiveUid:[Config getOwnID]];
                _taskView.closeEvent = ^{
                    [weakSelf.taskView removeFromSuperview];
                    weakSelf.taskView = nil;
                };
                [self.view addSubview:_taskView];
            }
        }else if (index == 1) {
            [self showJackpotView];
        }
    }
}
/** 图片滚动回调 */
- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didScrollToIndex:(NSInteger)index;
{
    sysPageControl.currentPage = index;

}
#pragma mark ============奖池View=============
- (void)JackpotLevelUp:(NSDictionary *)dic{
//    NSString *level =  [NSString stringWithFormat:@"    Lv.%@",minstr([dic valueForKey:@"uplevel"])];
    NSArray *title = @[@"",@""];
    [self addCycleScroll:title];

}
- (void)WinningPrize:(NSDictionary *)dic{
    if (winningView) {
        [winningView removeFromSuperview];
        winningView = nil;
    }
    winningView = [[WinningPrizeView alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight, _window_width, _window_width) andMsg:dic];
    [self.view addSubview:winningView];
    [self.view bringSubviewToFront:winningView];
    
}
- (void)showJackpotView{
    if (jackV) {
        [jackV removeFromSuperview];
        jackV = nil;
    }
    [YBToolClass postNetworkWithUrl:@"Jackpot.getJackpot" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            jackV = [[[NSBundle mainBundle]loadNibNamed:NSStringFromClass([JackpotView class]) owner:nil options:nil] lastObject];
            jackV.delegate = self;
            jackV.levelL.text = [NSString stringWithFormat:@"Lv.%@",minstr([infoDic valueForKey:@"level"])];
            jackV.coinL.text = minstr([infoDic valueForKey:@"total"]);
            jackV.frame = CGRectMake(_window_width*0.2, 135+statusbarHeight, _window_width*0.6+20, _window_width*0.6);
            [self.view addSubview:jackV];
        }else{
            [MBProgressHUD  showError:msg];
        }
    } fail:^{
        
    }];
}
-(void)jackpotViewClose{
    [jackV removeFromSuperview];
    jackV = nil;
}
#pragma mark ============幸运礼物全站效果=============
- (void)showAllLuckygift:(NSDictionary *)dic{
    if (!luckyGift) {
        luckyGift = [[AllRoomShowLuckyGift alloc]initWithFrame:CGRectMake(guardBtn.right+5, guardBtn.top-2.5, _window_width-(guardBtn.right+5), guardBtn.height+5)];
        [frontView addSubview:luckyGift];
    }
    [luckyGift addLuckyGiftMove:dic];
}


#pragma mark ============显示用户弹窗选项卡=============
- (void)showButtleView:(NSString *)touid{
    buttleView = [[UserBulletWindow alloc]initWithUserID:touid andIsAnchor:YES andAnchorID:[Config getOwnID]];
    buttleView.delegate  = self;
    [self.view addSubview:buttleView];
}
- (void)removeButtleView{
    [buttleView removeFromSuperview];
    buttleView = nil;
    self.tableView.userInteractionEnabled = YES;

}

#pragma mark ============检查开播状态=============
- (void)checkLiveingStatus{
    [YBToolClass postNetworkWithUrl:@"Live.checkLiveing" andParameter:@{@"stream":minstr([_roomDic valueForKey:@"stream"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *dic = [info firstObject];
            NSLog(@"info=%@",info);
            if ([minstr([dic valueForKey:@"status"]) isEqual:@"0"]) {
                [MBProgressHUD showError:@"连接已断开，请重新开播"];
                [self getCloseShow];
            }
        }
    } fail:^{

    }];
}
#pragma mark ============登录失效=============
- (void)denglushixiao{
    dispatch_async(dispatch_get_main_queue(), ^{
        [self getCloseShow];
    });
}

#pragma mark ============电话监听=============
- (void)reciverPhoneCall{
    if(!_isGameLive){
        if ([_sdkType isEqual:@"1"]) {
            [self appnoactive];
        }else{
            //ray-声网
//            [_gpuStreamer stopPreview];
        }

    }
}
- (void)phoneCallEnd{
    if ([_sdkType isEqual:@"1"]) {
        [self appactive];
    }else{
        //ray-声网
//        [_gpuStreamer startPreview:_pushPreview];
//        if (_roomDic) {
//            [_gpuStreamer.streamerBase startStream: [NSURL URLWithString:_hostURL]];
//        }
    }

}

#pragma mark ============店铺相关=============
- (void)showgoodsShowView{
    if (roomGoodsV) {
        [roomGoodsV removeFromSuperview];
        roomGoodsV = nil;
    }
    roomGoodsV = [[roomShowGoodsView alloc]initWithFrom:YES andZhuboMsg:_roomDic];
    roomGoodsV.showEvent = ^(NSDictionary * _Nonnull infoDic, RelationGoodsModel * _Nonnull models) {
        NSLog(@"-----broadcast:%@", infoDic);
        if ([minstr([infoDic valueForKey:@"status"]) isEqual:@"1"]) {
            [socketL goodsLiveShow:models.goodsid andThumb:models.thumb andName:models.name andPrice:models.price andType:models.type saleNums:models.sale_nums];
        }else{
            [socketL hideLiveGoodsShow];
        }
    };
    [self.view addSubview:roomGoodsV];
}

#pragma mark -----获取道具礼物------------------
-(void)getStickerList:(void(^)(NSArray * stickerlist))success{
    NSDictionary *dic = @{@"package_name":[self bundleName],
                          @"source":@"ios",

    };
    
    NSString *sign = [YBToolClass stickerSortString:dic];

    NSString *urlssss =[NSString stringWithFormat:@"%@package_name=%@&source=%@&sign=%@",PropUrl,[self bundleName],@"ios",sign] ;

    
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:urlssss] cachePolicy:NSURLRequestReloadIgnoringLocalCacheData timeoutInterval:10];
    request.HTTPMethod = @"POST";
    [request setValue:@"application/json" forHTTPHeaderField:@"content-Type"];
    [request setValue:@"application/json;charset=utf-8" forHTTPHeaderField:@"Accept"];

    [[self.session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if (error) {
            //failed(self.sticker, self.index, self);
            dispatch_async(dispatch_get_main_queue(), ^{
                [MBProgressHUD showError:YZMsg(@"获取道具礼物失败")];
            });
        } else {
            NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
            NSArray *infoarr = [dic valueForKey:@"info"];
            if (success) {
                success(infoarr);
            }
        }

    }] resume];

}
#pragma mark - NSURLSession delegate
-(void)URLSession:(NSURLSession *)session didReceiveChallenge:(NSURLAuthenticationChallenge *)challenge completionHandler:(void (^)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler {
    SecTrustRef serverTrust = challenge.protectionSpace.serverTrust;
    SecCertificateRef certificate = SecTrustGetCertificateAtIndex(serverTrust, 0);
    
    // Set SSL policies for domain name check
    NSMutableArray *policies = [NSMutableArray array];
    [policies addObject:(__bridge_transfer id)SecPolicyCreateSSL(true, (__bridge CFStringRef)challenge.protectionSpace.host)];
    SecTrustSetPolicies(serverTrust, (__bridge CFArrayRef)policies);
    
    // Evaluate server certificate
    SecTrustResultType result;
    SecTrustEvaluate(serverTrust, &result);
    // BOOL certificateIsValid = (result == kSecTrustResultUnspecified || result == kSecTrustResultProceed);
    
    // Get local and remote cert data
    NSData *remoteCertificateData = CFBridgingRelease(SecCertificateCopyData(certificate));
    NSString *pathToCert = [[NSBundle mainBundle] pathForResource:@"server" ofType:@"cer"];
    NSData *localCertificate = [NSData dataWithContentsOfFile:pathToCert];
    
    // The pinnning check
    if ([remoteCertificateData isEqualToData:localCertificate]) {
        NSURLCredential *credential = [NSURLCredential credentialForTrust:serverTrust];
        completionHandler(NSURLSessionAuthChallengeUseCredential, credential);
    } else {
        completionHandler(NSURLSessionAuthChallengeCancelAuthenticationChallenge, NULL);
    }

}
- (NSURLSession *)session {
    if (!_session) {
        NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
        // config.requestCachePolicy = NSURLRequestReloadIgnoringLocalCacheData;
        _session =
        [NSURLSession sessionWithConfiguration:config delegate:self delegateQueue:[[NSOperationQueue alloc] init]];
    }
    return _session;
}

- (NSString *)bundleName {
    NSString *bundleName = [[NSBundle mainBundle] bundleIdentifier];
    
    return bundleName;
}
//********************************炸金花*******************************************************************//
-(void)startgamepagev{
    [UIView animateWithDuration:0.4 animations:^{
        bottombtnV.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
        
    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        bottombtnV.hidden = YES;
    });
    self.tableView.hidden = YES;
    if (!gameselectedVC) {
        gameselectedVC = [[gameselected alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height) andArray:_game_switch];
        gameselectedVC.delegate = self;
        [self.view insertSubview:gameselectedVC atIndex:10];
    }
    gameselectedVC.hidden = NO;
    
}
-(void)reloadcoinsdelegate{
    if (gameVC) {
        [gameVC reloadcoins];
    }
}
//更换庄家信息
-(void)changebank:(NSDictionary *)subdic{
    [zhuangVC getNewZhuang:subdic];
}
-(void)changebankall:(NSDictionary *)subdic{
      [socketL getzhuangjianewmessagem:subdic];
      [zhuangVC getNewZhuang:subdic];
}
-(void)getzhuangjianewmessagedelegate:(NSDictionary *)subdic{
    [zhuangVC getNewZhuang:subdic];
}
-(void)gameselect:(int)action{
    //1炸金花  2海盗  3转盘  4牛牛  5二八贝
    self.tableView.hidden = NO;
    switch (action) {
        case 1:
            _method = @"startGame";
            _msgtype = @"15";
            [self startGame];
            break;
        case 2:
            _method = @"startLodumaniGame";
            _msgtype = @"18";
            [self startGame];
            break;
        case 3:
             [self getRotation];
            break;
        case 4:
            _method = @"startCattleGame";
            _msgtype = @"17";
            [self startGame];
            break;
        case 5:
//            _method = @"startShellGame";
//            _msgtype = @"19";
//            [self startsheelGame];
            break;
            
        default:
            break;
    }
    gameselectedVC.hidden = YES;
}
-(void)hideself{

    self.tableView.hidden = NO;
    gameselectedVC.hidden = YES;
}
//********************************转盘*******************************************************************//
-(void)stopRotationgameBySelf{
    [rotationV stopRotatipnGameInt];
     [rotationV stoplasttimer];
    [socketL stopRotationGame];//关闭游戏socket
    [rotationV removeFromSuperview];
    [rotationV removeall];
    rotationV = nil;
    [gameState saveProfile:@"0"];
    [self changeBtnFrame:_window_height - 45];
    [self tableviewheight: _window_height - _window_height*0.2 - 50 - ShowDiff];
}
-(void)getRotation{
  
    NSString *games = [NSString stringWithFormat:@"%@",[gameState getGameState]];
    if ([games isEqual:@"1"] ) {
        [MBProgressHUD showError:YZMsg(@"请等待游戏结束")];
        return;
    }
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (gameVC) {
        [gameVC stopGame];
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
        [gameState saveProfile:@"0"];//保存游戏开始状态
    }
    if (rotationV) {
        [rotationV stopRotatipnGameInt];
         [rotationV stoplasttimer];
        [socketL stopRotationGame];//关闭游戏socket
        [rotationV removeFromSuperview];
        [rotationV removeall];
        rotationV = nil;
        [gameState saveProfile:@"0"];
    }
    rotationV = [WPFRotateView rotateView];
    [rotationV setlayoutview];
    [rotationV isHost:YES andHostDic:[_roomDic valueForKey:@"stream"]];
    [rotationV hostgetstart];
    rotationV.delegate = self;
    rotationV.frame = CGRectMake(0, _window_height - _window_width/1.8, _window_width, _window_width);
    [self.view insertSubview:rotationV atIndex:6];
    [self changeBtnFrame:_window_height - _window_width/1.8 - www];
    [self tableviewheight: _window_height - _window_height*0.2 - _window_width/1.8 - www];
    [socketL prepRotationGame];
    [self changecontinuegiftframe];
}
//更新押注数量
-(void)getRotationCoin:(NSString *)type andMoney:(NSString *)money{
    [rotationV getRotationCoinType:type andMoney:money];
}
-(void)getRotationResult:(NSArray *)array{
    [rotationV getRotationResult:array];
}
//开始倒计时
-(void)startRotationGameSocketToken:(NSString *)token andGameID:(NSString *)ID andTime:(NSString *)time{
    [socketL RotatuonGame:ID andTime:time androtationtoken:token];
}
//二八贝游戏********************************************************************************************
-(void)startsheelGame{

//    NSString *games = [NSString stringWithFormat:@"%@",[gameState getGameState]];
//    if ([games isEqual:@"1"] ) {
//        [MBProgressHUD showError:YZMsg(@"请等待游戏结束")];
//        return;
//    }
//    if (zhuangVC) {
//        [zhuangVC remopokers];
//        [zhuangVC removeFromSuperview];
//        zhuangVC = nil;
//    }
//    if (rotationV) {
//        [rotationV stopRotatipnGameInt];
//         [rotationV stoplasttimer];
//        [socketL stopRotationGame];//关闭游戏socket
//        [rotationV removeFromSuperview];
//        [rotationV removeall];
//        rotationV = nil;
//        [gameState saveProfile:@"0"];
//    }
//    if (shell) {
//        [shell stopGame];
//        [shell releaseAll];
//        [shell gameOver];
//        [shell removeFromSuperview];
//        shell = nil;
//        [gameState saveProfile:@"0"];//保存游戏开始状态
//    }
//    if (gameVC) {
//        [gameVC stopGame];
//        [gameVC releaseAll];
//        [gameVC removeFromSuperview];
//        gameVC = nil;
//        [gameState saveProfile:@"0"];//保存游戏开始状态
//    }
//
//    if (!shell) {
//        shell = [[shellGame alloc]initWIthDic:_roomDic andIsHost:YES andMethod:@"startShellGame" andMsgtype:@"19" andandBanklist:nil];
//        shell.delagate = self;
//        [self.view insertSubview:shell atIndex:5];
//        [socketL prepGameandMethod:_method andMsgtype:_msgtype];
//        shell.frame = CGRectMake(0, _window_height - 260, _window_width,260);
//        [self changeBtnFrame:_window_height - 260-www];
//        [self tableviewheight:_window_height - _window_height*0.2 - 260-www];
//        [self changecontinuegiftframe];
//    }
}
//********************************转盘*******************************************************************//
//********************************炸金花   牛仔*******************************************************************//
-(void)startGame{
 
    NSString *games = [NSString stringWithFormat:@"%@",[gameState getGameState]];
    if ([games isEqual:@"1"] ) {
        [MBProgressHUD showError:YZMsg(@"请等待游戏结束")];
        return;
    }
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (rotationV) {
        [rotationV stopRotatipnGameInt];
         [rotationV stoplasttimer];
        [socketL stopRotationGame];//关闭游戏socket
        [rotationV removeFromSuperview];
        [rotationV removeall];
        rotationV = nil;
    }
    if (gameVC) {
        [gameVC stopGame];
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
    }
    [gameState saveProfile:@"0"];//保存游戏开始状态
    //出现游戏界面
    gameVC = [[gameBottomVC alloc]initWIthDic:_roomDic andIsHost:YES andMethod:_method andMsgtype:_msgtype];
    [socketL prepGameandMethod:_method andMsgtype:_msgtype];
    //判断开始哪个游戏
    gameVC.delagate = self;
    gameVC.frame = CGRectMake(0, _window_height - 230, _window_width,230);
    [self.view insertSubview:gameVC atIndex:5];
    [self changeBtnFrame:_window_height - 230-www];
    [self tableviewheight:_window_height - _window_height*0.2 -230-www];
    [self changecontinuegiftframe];
    if ([_method isEqual:@"startCattleGame"]) {
        //上庄玩法
        zhuangVC = [[shangzhuang alloc]initWithFrame:CGRectMake(10,90, _window_width/4, _window_width/4 + 20 + _window_width/8) ishost:YES withstreame:[_roomDic valueForKey:@"stream"]];
        zhuangVC.deleagte = self;
        [self.view insertSubview:zhuangVC atIndex:6];
        [zhuangVC addPoker];
        [zhuangVC addtableview];
        [zhuangVC getbanksCoin:_zhuangDic];
        [self changecontinuegiftframe];
    }
}
//主播广播准备开始游戏
-(void)prepGame:(NSString *)gameid ndMethod:(NSString *)method andMsgtype:(NSString *)msgtype andBanklist:(NSDictionary *)banklist{
    [socketL takePoker:gameid ndMethod:method andMsgtype:msgtype andBanklist:banklist];
}
//游戏开始，开始倒数计时
-(void)startGameSocketToken:(NSString *)token andGameID:(NSString *)ID andTime:(NSString *)time ndMethod:(NSString *)method andMsgtype:(NSString *)msgtype{
    [socketL zhaJinHua:ID andTime:time andJinhuatoken:token ndMethod:method andMsgtype:msgtype];
}
-(void)skate:(NSString *)type andMoney:(NSString *)money andMethod:(NSString *)method andMsgtype:(NSString *)msgtype{
    [socketL stakePoke:type andMoney:money andMethod:method andMsgtype:msgtype];
}
-(void)getCoin:(NSString *)type andMoney:(NSString *)money{
    [gameVC getCoinType:type andMoney:money];
}
//得到游戏结果
-(void)getResult:(NSArray *)array{
    [gameVC getResult:array];
    if (zhuangVC) {
        [zhuangVC getresult:array];
    }
}
-(void)stopGamendMethod:(NSString *)method andMsgtype:(NSString *)msgtype{
    [socketL stopGamendMethod:method andMsgtype:msgtype];
 
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (gameVC) {
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
    }
    [self changeBtnFrame:_window_height - 45];
    [self tableviewheight:_window_height - _window_height*0.2 - 50 - ShowDiff];
    [self changecontinuegiftframe];
}
-(void)showXQTBMsgList:(NSDictionary *)msg;
{
    NSArray *getList = [msg valueForKey:@"list"];
    for (int i = 0; i <getList.count;i ++ ){
        titleColor = @"firstlogin";
        NSString *ct;
        NSString *languageStr= [PublicObj getCurrentLanguage];
        if ([languageStr isEqual:@"en"]) {
            ct = minstr([getList[i] valueForKey:@"title_en"]);
        }else{
            ct = minstr([getList[i] valueForKey:@"title"]);
        }
        NSString *uname = YZMsg(@"直播间消息");
        NSString *ID = @"";
        NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",ID,@"id",titleColor,@"titleColor",nil];
        chat = [YBToolClass roomChatInsertTime:chat];
        [msgList addObject:chat];
        titleColor = @"0";
        if(msgList.count>30)
        {
            [msgList removeObjectAtIndex:0];
        }
        [self.tableView reloadData];
        [self jumpLast:self.tableView];
    }
}
-(void)showXYDZPMsgList:(NSDictionary *)msg
{
    NSArray *getList = [msg valueForKey:@"list"];
    for (int i = 0; i <getList.count;i ++ ){
        titleColor = @"firstlogin";
        NSString *ct;
        NSString *languageStr= [PublicObj getCurrentLanguage];
        if ([languageStr isEqual:@"en"]) {
            ct = minstr([getList[i] valueForKey:@"title_en"]);
        }else{
            ct = minstr([getList[i] valueForKey:@"title"]);
        }
        NSString *uname = YZMsg(@"直播间消息");
        NSString *ID = @"";
        NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",ID,@"id",titleColor,@"titleColor",nil];
        chat = [YBToolClass roomChatInsertTime:chat];
        [msgList addObject:chat];
        titleColor = @"0";
        if(msgList.count>30)
        {
            [msgList removeObjectAtIndex:0];
        }
        [self.tableView reloadData];
        [self jumpLast:self.tableView];

    }
}
- (RKShowPaintedView *)paintedShowRegion {
    if (!_paintedShowRegion) {
        _paintedShowRegion = [[RKShowPaintedView alloc]init];
        _paintedShowRegion.frame = CGRectMake(0, 0, _window_width, _window_height*0.6);
        //_paintedShowRegion.backgroundColor = UIColor.redColor;
    }
    return _paintedShowRegion;
}

//点击购物车展示
-(void)showLiveGooodTips:(NSDictionary *)msg{
    NSString *userName = minstr([msg valueForKey:@"uname"]);
    NSDictionary *userDic = @{@"uname":userName};
    [_liveGoodTipArr addObject:userDic];
    [self beginShowLiveGoodTips];    
}
-(void)beginShowLiveGoodTips{
    YBWeakSelf;
    if (_liveGoodTipArr.count > 0) {
        NSDictionary *infoDic = [_liveGoodTipArr firstObject];
        NSString *userName = minstr([infoDic valueForKey:@"uname"]);
        NSString*newStr = [userName substringWithRange:NSMakeRange(0,1)];
        NSString *ssss = [NSString stringWithFormat:@"***%@",YZMsg(@"前去购买")];
        newStr = [newStr stringByAppendingString:ssss];
        CGSize sizee = [PublicObj sizeWithString:newStr andFont:SYS_Font(14)];
        
        if (!_liveTipLabel) {
            _liveTipLabel = [[UILabel alloc]init];
            _liveTipLabel.frame = CGRectMake(_window_width, _tableView.frame.origin.y-50, sizee.width+20, 32);
            _liveTipLabel.font = [UIFont systemFontOfSize:14];
            _liveTipLabel.textColor = [UIColor whiteColor];
            _liveTipLabel.textAlignment = NSTextAlignmentCenter;
            _liveTipLabel.text = newStr;
            _liveTipLabel.layer.cornerRadius = 16;
            _liveTipLabel.layer.masksToBounds = YES;
            _liveTipLabel.backgroundColor = RGBA(248,152,38, 0.55);
            [self.view addSubview:_liveTipLabel];
            
            [UIView animateWithDuration:1.5 animations:^{
                _liveTipLabel.frame = CGRectMake(20, _tableView.frame.origin.y-50, sizee.width+20, 32);
            } completion:^(BOOL finished) {
                
                [UIView animateWithDuration:1 animations:^{
                    _liveTipLabel.frame = CGRectMake(0, _tableView.frame.origin.y-50, sizee.width+20, 32);

                } completion:^(BOOL finished) {
                    
                    [UIView animateWithDuration:1 animations:^{
                        _liveTipLabel.frame = CGRectMake(-_window_width, _tableView.frame.origin.y-50, sizee.width+20, 32);

                    } completion:^(BOOL finished) {
                        [_liveTipLabel removeFromSuperview];
                        _liveTipLabel = nil;
                        [_liveGoodTipArr removeObjectAtIndex:0];
                        [weakSelf beginShowLiveGoodTips];
                    }];
                }];
                
            }];
        }
    }
}
-(void)kickUser:(NSDictionary *)msg
{
    if ([_sdkType isEqual:@"1"]) {
        [[YBLiveRTCManager shareInstance]kickUser:minstr([msg valueForKey:@"touid"])];;
    }
}
#pragma mark -声网美颜
-(void)beautyCaptureVideoFrame:(AgoraOutputVideoFrame *)videoFrame sourceType:(AgoraVideoSourceType)sourceType
{
    if(!_isGameLive){
        [self.beautyManager processAgoraWithPixelBuffer:videoFrame.pixelBuffer];
//        [self.menusView setupDefaultBeautyAndFaceValue];
        videoFrame.pixelBuffer = videoFrame.pixelBuffer;
    }
}

#pragma mark - 声网主播连麦-对方信息
-(void)showPkAnchorView:(NSString *)pkuid {
    [self destroyPkAnchorView];
    [self.view addSubview:self.pkAnchorView];
    _pkAnchorView.anchorUid = pkuid;
    [_pkAnchorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(_agordLinkView.mas_bottom).offset(-10);
        make.right.equalTo(_agordLinkView.mas_right).offset(-10);
        make.height.mas_equalTo([_pkAnchorView pkHeight]);
    }];
}

- (PKAnchorInfoView *)pkAnchorView {
    if (!_pkAnchorView) {
        _pkAnchorView = [[PKAnchorInfoView alloc]init];
    }
    return _pkAnchorView;
}

-(void)destroyPkAnchorView {
    if (_pkAnchorView) {
        [_pkAnchorView removeFromSuperview];
        _pkAnchorView = nil;
    }
}

@end
