//
//  YBLiveRTCManager.h
//  YBLive
//
//  Created by ybRRR on 2023/1/29.
//  Copyright © 2023 cat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <TXLiteAVSDK_Professional/V2TXLivePusher.h>


@protocol YBLiveRTCDelegate <NSObject>
#pragma mark -直播返回信息
-(void)ybPushLiveStatus:(V2TXLiveCode)pushStatus;
#pragma mark -推流状态
-(void)ybRTCPushStatusUpdate:(V2TXLivePushStatus)status message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo;
#pragma mark -播放音乐
-(void)playMusicBegin;
#pragma mark -播放音乐进度
-(void)playMusicProgress:(NSInteger)progressMs Duration:(NSInteger)durationMs;
#pragma mark -美狐回调
-(void)MHBeautyBlock:(V2TXLiveVideoFrame *)srcFrame dstFrame:(V2TXLiveVideoFrame *)dstFrame;
@end


@interface YBLiveRTCManager : NSObject
@property(nonatomic, assign)id<YBLiveRTCDelegate>delegate;

+(instancetype)shareInstance;


#pragma mark -初始化
-(void)initWithLiveMode:(V2TXLiveMode)liveMode andPushData:(NSDictionary *)pushDic;
-(void)initWithChatLiveMode:(V2TXLiveMode)liveMode;
#pragma mark -初始化语音视频聊天室 txsdk
-(void)initWithChatVideoLiveModel:(V2TXLiveMode)liveMode andPushData:(NSDictionary *)pushDic;
#pragma mark -设置展示界面
-(void)setPushView:(UIView *)pushView;
#pragma mark -开始推流
//-(void)startPush:(NSString *)pusUrl;
-(void)startPush:(NSString *)pusUrl isGameLive:(BOOL)isgamelive;
#pragma mark -停止推流
//-(void)stopPush;
-(void)stopPushIsGameLive:(BOOL)isgamelive;
#pragma mark -暂停推流
-(void)pausePush;
#pragma mark -恢复推流
-(void)resumePush;
#pragma mark -静音
-(void)pauseAudio;
#pragma mark -取消静音
-(void)resumeAudio;

#pragma mark -切换摄像头
-(void)changeCamera:(BOOL)isFront;
#pragma mark -切换镜像
-(void)changeMirror:(BOOL)isMirror;
#pragma mark -闪光灯
-(void)cameraTorch:(BOOL)isTorch;
#pragma mark -播放音乐
-(void)playBGMWithPath:(NSString *)musicPath;
#pragma mark -停止播放音乐
-(void)stopBGM;
#pragma mark -发起云端混流
-(void)MixTranscoding:(V2TXLiveTranscodingConfig *)config;
#pragma mark -设置基础美颜参数
-(void)setBeautyLevel:(float)beautyLevel WhitenessLevel:(float)whitenessLevel IsTXfiter:(NSString *)isTXfiter;
-(void)setBeautyLevel:(float)beautyLevel WhitenessLevel:(float)whitenessLevel;
#pragma mark -设置大眼级别
-(void)setYBEyeScaleLevel:(float)eyeScaleLevel;
#pragma mark -设置瘦脸级别
-(void)setYBFaceScaleLevel:(float)faceSlimLevel;
#pragma mark -设置红润级别
-(void)setYBRuddyLevel:(float)ruddyLevel;
#pragma mark -背景音乐音量
-(void)setYBBGMVolume:(float)bgmVolume;
#pragma mark -设置色彩滤镜效果
-(void)setYBFilter:(UIImage *)image;

#pragma mark -踢人
-(void)kickUser:(NSString *)userid;
#pragma mark -结束直播
-(void)endlive;
@end

