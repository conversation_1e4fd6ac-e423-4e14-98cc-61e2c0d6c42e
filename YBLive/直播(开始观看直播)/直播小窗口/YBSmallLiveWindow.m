//
//  YBSmallLiveWindow.m
//  YBLive
//
//  Created by ybRRR on 2023/6/28.
//  Copyright © 2023 cat. All rights reserved.
//

#import "YBSmallLiveWindow.h"
#import <TXLiteAVSDK_Professional/V2TXLivePlayer.h>
#import <ZFPlayer/ZFPlayer.h>
#import <ZFPlayer/ZFPlayerControlView.h>
#import <ZFPlayer/ZFIJKPlayerManager.h>
#import "SmallTXView.h"
#import "LivePlay.h"
@interface YBSmallLiveWindow ()<V2TXLivePlayerObserver>
{
    SmallTXView *_videoPlayView;
    SmallTXView *_linkPlayView;

    NSDictionary *_playDic;
    CGFloat vWidth;// = _window_width *0.33;
    CGFloat vHeight;// = vWidth *1.42;
    UIButton *closeBtn;
    NSString *_sdkType;
}
@property (nonatomic, strong)V2TXLivePlayer *txLivePlayer;
@property (nonatomic, strong) ZFPlayerController *videoPlayer;

@end
@implementation YBSmallLiveWindow

static YBSmallLiveWindow *_smallWindowManager = nil;

+(instancetype)shareInstance{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _smallWindowManager = [[super allocWithZone:NULL]init];
    });
    return _smallWindowManager;
}
+ (instancetype)allocWithZone:(struct _NSZone *)zone {
    return [self shareInstance];
}
#pragma mark -初始化直播 txsdk
-(void)initWithLiveData:(NSDictionary *)playDic  andSDKType:(NSString *)sdkType{
    _playDic = playDic;
    _sdkType = sdkType;
    vWidth = _window_width *0.33;
    vHeight = vWidth *1.5;
    YBWeakSelf;
    _videoPlayView = [[SmallTXView alloc] init];
    _videoPlayView.backgroundColor = [UIColor clearColor];
    _videoPlayView.frame = CGRectMake(_window_width-vWidth-10,_window_height-50-vHeight, vWidth, vHeight);
    _videoPlayView.tag = 10005;
    _videoPlayView.tapEvent = ^{
        [weakSelf reEnter];
    };
    [[UIApplication sharedApplication].keyWindow addSubview:_videoPlayView];

    if (![_sdkType isEqual:@"1"]) {
        CGFloat linkW =_videoPlayView.width *0.4;
        CGFloat linkH =_videoPlayView.height *0.4;
        _linkPlayView = [[SmallTXView alloc] init];
        _linkPlayView.backgroundColor = [UIColor clearColor];
        _linkPlayView.frame = CGRectMake(_videoPlayView.width-linkW,_videoPlayView.height-linkH, linkW, linkH);
        _linkPlayView.tag = 10006;
        _linkPlayView.hidden = YES;
        [_videoPlayView addSubview:_linkPlayView];
        
        [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(showAgoraLink:) name:@"showAgoraLink" object:nil];
    }
    
    
    if ([minstr([playDic valueForKey:@"isvideo"]) isEqual:@"1"]) {
        // 0  代表横向  1 代表竖向
        if ([minstr([playDic valueForKey:@"anyway"]) isEqual:@"1"]) {
            [self changeViewFream:@"0"];
        }
        [self playWithVideoPlayer];
        [self.videoPlayer setContainerView:_videoPlayView];
    }else{
        if ([sdkType isEqual:@"1"]) {
            [self.txLivePlayer setRenderView:_videoPlayView];
            //anyway来表明是不是PC开播
            if ([minstr([playDic valueForKey:@"anyway"]) isEqual:@"1"]) {
                if ([minstr([playDic valueForKey:@"anyway"]) isEqual:@"1"]) {
                    [self changeViewFream:@"0"];
                }
                [self.txLivePlayer setRenderFillMode:V2TXLiveFillModeFit];
            }
            //isvideo 是不是视频
            if ([minstr([playDic valueForKey:@"isvideo"]) isEqual:@"1"]) {
                [self.txLivePlayer setRenderFillMode:V2TXLiveFillModeFill];
            }
            NSString *playUrl = [playDic valueForKey:@"pull"];
            V2TXLiveCode result = [self.txLivePlayer startLivePlay:playUrl];
            NSLog(@"wangminxin%ld",result);
            if( result != 0)
            {
                [MBProgressHUD showError:YZMsg(@"视频流播放失败")];
            }
            if( result == 0){
                NSLog(@"播放视频");
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                });
            }

        }else{
            NSString *url = [purl stringByAppendingFormat:@"?service=Linkmic.getSwRtcToken"];
            NSDictionary *dic = @{
                                  @"uid":[Config getOwnID],
                                  @"token":[Config getOwnToken],
                                  @"stream":minstr([playDic valueForKey:@"stream"])
            };
            [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
                if ([code isEqual:@"0"]) {
                    NSLog(@"getSwRtcToken-----:%@",data);
                    NSDictionary *infoDic = [[data valueForKey:@"info"] firstObject];
                    NSString *user_sw_token = minstr([infoDic valueForKey:@"user_sw_token"]);
                    [[YBAgoraManager shareInstance]joinChannelWithChannelId:minstr([playDic valueForKey:@"stream"]) andUserToken:user_sw_token WithModel:RtcMode_Living isBroadcaster:AgoraClientRoleAudience isGameLive:NO];
                    [[YBAgoraManager shareInstance]showWithCanvasView:_videoPlayView.frontView andUserId:minstr([playDic valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
                }else{
                    [MBProgressHUD showError:msg];
                }
            } Fail:^(id fail) {
                
            }];
        }
        [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    }
    closeBtn = [UIButton buttonWithType:0];
//    closeBtn.frame = CGRectMake(_videoPlayView.width-35, 10, 28, 28);
    [closeBtn setImage:[UIImage imageNamed:@"live_close"] forState:0];
    [closeBtn addTarget:self action:@selector(closeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [_videoPlayView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(_videoPlayView.mas_right).offset(-28);
        make.top.equalTo(_videoPlayView.mas_top).offset(10);
        make.width.height.mas_equalTo(28);
    }];
    [[NSUserDefaults standardUserDefaults]setBool:YES forKey:@"isShowChatLive"];
}
-(void)showAgoraLink:(NSNotification *)noti{
    NSDictionary *subdicsss = [noti userInfo];
    if ([minstr([subdicsss objectForKey:@"method"]) isEqual:@"ConnectVideo"]) {
        if ([minstr([subdicsss valueForKey:@"uid"]) isEqual:@"0"]) {
            _linkPlayView.hidden = YES;
            [[YBAgoraManager shareInstance]removePlayView:_linkPlayView WithUserId:minstr([subdicsss valueForKey:@"uid"])];
        }else{
            _linkPlayView.hidden = NO;
            [[YBAgoraManager shareInstance]showWithCanvasView:_linkPlayView andUserId:minstr([subdicsss valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
        }
    }else{
        if ([minstr([subdicsss valueForKey:@"pkuid"]) isEqual:@"0"]) {
            _videoPlayView.frame = CGRectMake(_window_width-vWidth-10,_window_height-50-vHeight, vWidth, vHeight);
            _videoPlayView.frontView.frame = CGRectMake(0, 0, _videoPlayView.width, _videoPlayView.height);
            _linkPlayView.hidden = YES;
            [[YBAgoraManager shareInstance]removePlayView:_linkPlayView WithUserId:minstr([subdicsss valueForKey:@"pkuid"])];
        }else{
            _videoPlayView.frame =  CGRectMake(_window_width-vHeight-10,_window_height-50-vHeight,vHeight, vWidth);
            _videoPlayView.frontView.frame = CGRectMake(0, 0, _videoPlayView.width/2, _videoPlayView.height);
            _linkPlayView.frame = CGRectMake(_videoPlayView.width/2, 0, _videoPlayView.width/2, _videoPlayView.height);
            _linkPlayView.hidden = NO;
            [[YBAgoraManager shareInstance]showWithCanvasView:_linkPlayView.frontView andUserId:minstr([subdicsss valueForKey:@"pkuid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
        }
    }
}
-(void)showAgoraLinkDic:(NSDictionary *)subdicsss{
    if ([minstr([subdicsss objectForKey:@"method"]) isEqual:@"ConnectVideo"]) {
        if ([minstr([subdicsss valueForKey:@"uid"]) isEqual:@"0"]) {
            _linkPlayView.hidden = YES;
            [[YBAgoraManager shareInstance]removePlayView:_linkPlayView WithUserId:minstr([subdicsss valueForKey:@"uid"])];
        }else{
            _linkPlayView.hidden = NO;
            [[YBAgoraManager shareInstance]showWithCanvasView:_linkPlayView andUserId:minstr([subdicsss valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
        }
    }else{
        if ([minstr([subdicsss valueForKey:@"pkuid"]) isEqual:@"0"]) {
            _videoPlayView.frame = CGRectMake(_window_width-vWidth-10,_window_height-50-vHeight, vWidth, vHeight);
            _videoPlayView.frontView.frame = CGRectMake(0, 0, _videoPlayView.width, _videoPlayView.height);
            _linkPlayView.hidden = YES;
            [[YBAgoraManager shareInstance]removePlayView:_linkPlayView WithUserId:minstr([subdicsss valueForKey:@"pkuid"])];
        }else{
            _videoPlayView.frame =  CGRectMake(_window_width-vHeight-10,_window_height-50-vHeight,vHeight, vWidth);
            _videoPlayView.frontView.frame = CGRectMake(0, 0, _videoPlayView.width/2, _videoPlayView.height);
            _linkPlayView.frame = CGRectMake(_videoPlayView.width/2, 0, _videoPlayView.width/2, _videoPlayView.height);
            _linkPlayView.hidden = NO;
            [[YBAgoraManager shareInstance]showWithCanvasView:_linkPlayView andUserId:minstr([subdicsss valueForKey:@"pkuid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
        }
    }
}

-(void)reEnter{
    if(self.fromShop){
        [[MXBADelegate sharedAppDelegate]popViewController:YES];
    }else{
        moviePlay *player = [[moviePlay alloc]init];
        player.playDoc = _playDic;
    //    player.type_val = type_val;
    //    player.livetype = livetype;
        player.sdkType = _sdkType;
        [[MXBADelegate sharedAppDelegate] pushViewController:player animated:YES];
    }

}
-(void)changeViewFream:(NSString *)direction{
    if([direction isEqual:@"0"]){
        _videoPlayView.frame =  CGRectMake(_window_width-vHeight-10,_window_height-50-vHeight,vHeight, vWidth);
    }else{
        _videoPlayView.frame = CGRectMake(_window_width-vWidth-10,_window_height-50-vHeight, vWidth, vHeight);
    }
    closeBtn.frame = CGRectMake(_videoPlayView.width-35, 10, 28, 28);

}
-(void)closeBtnClick{
    UIWindow *keyWin = [UIApplication sharedApplication].keyWindow;
    UIView *eleCerView = [keyWin viewWithTag:10005];
    if(eleCerView){
        if ([_sdkType isEqual:@"1"]) {
            if(self.txLivePlayer){
                [self.txLivePlayer stopPlay];
                self.txLivePlayer = nil;
            }
        }else{
            if (!_fromShop) {
                [[YBAgoraManager shareInstance]removePlayView:_videoPlayView WithUserId:minstr([_playDic valueForKey:@"uid"])];
                [[YBAgoraManager shareInstance]leaveChannel:nil];
            }else {
                [[YBAgoraManager shareInstance] muteRemoteAudio:YES];
            }
        }
        if(self.videoPlayer){
            [self.videoPlayer stop];
            self.videoPlayer = nil;
        }
        [eleCerView removeFromSuperview];
        [[NSUserDefaults standardUserDefaults]setBool:NO forKey:@"isShowChatLive"];

    }
}
-(V2TXLivePlayer *)txLivePlayer{
    if(!_txLivePlayer){
        _txLivePlayer = [[V2TXLivePlayer alloc] init];
        [_txLivePlayer setObserver:self];
        [_txLivePlayer enableObserveAudioFrame:YES];
        [_txLivePlayer setRenderFillMode:V2TXLiveFillModeFill];
        [_txLivePlayer enableReceiveSeiMessage:YES payloadType:242];
    }
    return _txLivePlayer;
}
#pragma mark -视频播放器
-(ZFPlayerController *)videoPlayer{
    if(!_videoPlayer){
        ZFIJKPlayerManager *playerManager = [[ZFIJKPlayerManager alloc] init];
        NSString *ijkRef = [NSString stringWithFormat:@"Referer:%@\r\n",h5url];
        [playerManager.options setFormatOptionValue:ijkRef forKey:@"headers"];

        _videoPlayer =[ZFPlayerController playerWithPlayerManager:playerManager containerView:_videoPlayView];
        _videoPlayer.shouldAutoPlay = YES;
        _videoPlayer.allowOrentitaionRotation = NO;
        _videoPlayer.WWANAutoPlay = YES;
        //不支持的方向
        _videoPlayer.disablePanMovingDirection = ZFPlayerDisablePanMovingDirectionVertical;
        //不支持的手势类型
        _videoPlayer.disableGestureTypes =  ZFPlayerDisableGestureTypesPinch;
        /// 1.0是消失100%时候
        _videoPlayer.playerDisapperaPercent = 1.0;
        //功能
        @weakify(self)
        _videoPlayer.playerPrepareToPlay = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, NSURL * _Nonnull assetURL) {
            NSLog(@"准备");
            @strongify(self)
            
        };
        _videoPlayer.playerReadyToPlay = ^(id<ZFPlayerMediaPlayback>  _Nonnull asset, NSURL * _Nonnull assetURL) {
            @strongify(self)
//            setFrontV.bigAvatarImageView.hidden = YES;
//            backScrollView.userInteractionEnabled = YES;
//            backScrollView.contentSize = CGSizeMake(_window_width*2,0);
//            buttomimageviews.hidden = YES;
//            self.fullControlView.playDoc = self.playDoc;

        };
        _videoPlayer.playerDidToEnd = ^(id  _Nonnull asset) {
            NSLog(@"结束");
            @strongify(self)
            [self.videoPlayer.currentPlayerManager replay];
        };
        _videoPlayer.orientationDidChanged = ^(ZFPlayerController * _Nonnull player, BOOL isFullScreen) {
            @strongify(self)
            if (isFullScreen) {
//                self.fullControlView.hidden = NO;
//                self.videoPlayer.controlView = self.fullControlView;
            }else{
//                self.fullControlView.hidden = YES;
            }

        };

    }
    return _videoPlayer;
}
-(void)playWithVideoPlayer{
    [self.videoPlayer setMuted:NO];
    NSString *playUrl = [_playDic valueForKey:@"pull"];
    self.videoPlayer.assetURL = [NSURL URLWithString:playUrl];
}
- (void)onError:(id<V2TXLivePlayer>)player code:(V2TXLiveCode)code message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo;
{
    if(code == V2TXLIVE_ERROR_DISCONNECTED){
        [self closeBtnClick];
    }
}
- (void)onVideoResolutionChanged:(id<V2TXLivePlayer>)player width:(NSInteger)width height:(NSInteger)height;
{
    NSLog(@"SMALL-------width:%d  ----height:%d",width, height);
    if(width > height){
        [self changeViewFream:@"0"];
    }else{
        [self changeViewFream:@"1"];

    }
}
- (void)onReceiveSeiMessage:(id<V2TXLivePlayer>)player payloadType:(int)payloadType data:(NSData *)data;
{
    NSString *result =[[ NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSDictionary *jsondic = [self dictionaryWithJsonString:result];
    if(jsondic){
        if([minstr([jsondic valueForKey:@"method"]) isEqual:@"kick"]){
            NSString *kickUser = minstr([jsondic valueForKey:@"touid"]);
            if([kickUser isEqual:[Config getOwnID]]){
                [self closeBtnClick];
            }
        }else if ([minstr([jsondic valueForKey:@"method"]) isEqual:@"endlive"]){
            [self closeBtnClick];
        }
    }
}
//json格式字符串转字典：
 
- (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString {
    if (jsonString == nil) {
        return nil;
    }
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                         
                                                        options:NSJSONReadingMutableContainers
                         
                                                          error:&err];
    if(err) {
        NSLog(@"json解析失败：%@",err);
        return nil;
    }
    return dic;
}
@end
