//
//  YBSmallLiveWindow.h
//  YBLive
//
//  Created by ybRRR on 2023/6/28.
//  Copyright © 2023 cat. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface YBSmallLiveWindow : NSObject

@property (nonatomic, assign)BOOL fromShop;
+(instancetype)shareInstance;
-(void)initWithLiveData:(NSDictionary *)playDic andSDKType:(NSString *)sdkType;
-(void)closeBtnClick;
-(void)changeViewFream:(NSString *)direction;
-(void)showAgoraLink:(NSDictionary *)msg;
-(void)showAgoraLinkDic:(NSDictionary *)subdicsss;
@end

