//
//  SmallTXView.h
//  YBLive
//
//  Created by ybRRR on 2023/6/28.
//  Copyright © 2023 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^smallTap)();

@interface SmallTXView : UIView <UIGestureRecognizerDelegate>
{
    //拖动按钮的起始坐标点
    CGPoint _touchPoint;
    //起始按钮的x,y值
    CGFloat _touchViewX;
    CGFloat _touchViewY;
    
    CGPoint _moveEndPoint;

}
@property (nonatomic, copy)smallTap tapEvent;
@property (nonatomic, strong)UIView *frontView;
@end

