//
//  SmallTXView.m
//  YBLive
//
//  Created by ybRRR on 2023/6/28.
//  Copyright © 2023 cat. All rights reserved.
//

#import "SmallTXView.h"

@implementation SmallTXView

-(instancetype)init
{
    self = [super init];
    if(self){
        _frontView = [[UIView alloc]init];
        [self addSubview:_frontView];
        [_frontView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.bottom.equalTo(self);
        }];
        UITapGestureRecognizer *_vodeoPlayTap  = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(reEnter)];
        _vodeoPlayTap.delegate = self;
        [self addGestureRecognizer:_vodeoPlayTap];
    }
    return self;
}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer{
    return YES;
}
-(void)reEnter{
    if(self.tapEvent){
        self.tapEvent();
    }
}
#pragma mark - 拖动
- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    UITouch *touch = [touches anyObject];
    _touchPoint = [touch locationInView:self];
    _touchViewX = self.frame.origin.x;
    _touchViewY = self.frame.origin.y;
}

- (void)touchesMoved:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    UITouch *touch = [touches anyObject];
    CGPoint currentPoint = [touch locationInView:self];
    //偏移量(当前坐标 - 起始坐标 = 偏移量)
    CGFloat offsetX = currentPoint.x - _touchPoint.x;
    CGFloat offsetY = currentPoint.y - _touchPoint.y;
    //只沿着Y轴移动，，
    CGFloat centerX = self.center.x + offsetX;  //self.center.x;   //
    CGFloat centerY = self.center.y + offsetY;

    self.center = CGPointMake(centerX, centerY);
    CGFloat popX = self.frame.origin.x;
    CGFloat popY = self.frame.origin.y;
    CGFloat popW = self.frame.size.width;
    CGFloat popH = self.frame.size.height;

    //x轴左右极限坐标
    if ((popX+popW) > _window_width){
        //按钮右侧越界
        centerX = _window_width - popW*0.5-5;
        self.center = CGPointMake(centerX, centerY);
    }else if (popX < 0){
        //按钮左侧越界
        centerX = popW * 0.5+5;
        self.center = CGPointMake(centerX, centerY);
    }
    //y轴上下极限坐标
    if (popY <= 80){
        //按钮顶部越界
        centerY = popH * 0.5+80;
        self.center = CGPointMake(centerX, centerY);
    }
    else if ((popY+popH+MAX(50, 80)) > _window_height){
        //按钮底部越界
        centerY = _window_height - popH*0.5 - MAX(80, 80);
        self.center = CGPointMake(centerX, centerY);
    }
    _frontView.center =self.center;
}
- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event{

    CGFloat popY = self.frame.origin.y;
    CGFloat popX = self.frame.origin.x;
    CGFloat minDistance = 2;
    //结束move的时候，计算移动的距离是>最低要求，如果没有，就调用按钮点击事件
    BOOL isOverX = fabs(popX - _touchViewX) > minDistance;
    BOOL isOverY = fabs(popY - _touchViewY) > minDistance;

    if (isOverX || isOverY) {
        //超过移动范围就不响应点击 - 只做移动操作
        NSLog(@"move - btn");
        [self touchesCancelled:touches withEvent:event];
    }else{
        [super touchesEnded:touches withEvent:event];
        NSLog(@"clikc - btn");
    }
}


@end
