//
//  YBAgoraManager.m
//  YBLive
//
//  Created by yunbao02 on 2024/1/30.
//  Copyright © 2024 cat. All rights reserved.
//

#import "YBAgoraManager.h"
#import "TRTCBroadcastExtensionLauncher.h"
#import <ReplayKit/ReplayKit.h>
#import <AGMCapturer/AGMCapturer.h>



@interface YBAgoraManager()<AgoraRtcEngineDelegate,AgoraVideoFrameDelegate>
{
    NSInteger streamID ;
    NSString *src_token;
    NSString *dest_token;
}
@property(nonatomic,strong)AgoraRtcEngineConfig *agoraConfig;
@property(nonatomic,strong)AgoraRtcEngineKit *agoraRtc;
@property(nonatomic,strong)NSMutableArray<AgoraRtcConnection *> *rtcConnArray;
@property(nonatomic,strong)NSMutableArray *canvasArray;
@property (nonatomic, strong)RPSystemBroadcastPickerView *systemBroadcastPicker;
@property (nonatomic, strong)AgoraScreenCaptureParameters2 *screenParams;
@property (nonatomic, strong)AgoraRtcChannelMediaOptions *rtcOps;
@property (nonatomic, strong)AgoraDataStreamConfig *dataConfig;
@property (nonatomic, strong)AgoraVideoEncoderConfiguration *videoConfig;
@property (nonatomic, strong)AgoraBeautyOptions *beautyOption;

@end

@implementation YBAgoraManager


static YBAgoraManager *_agoraInstance = nil;

+(instancetype)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _agoraInstance = [[super allocWithZone:NULL]init];
    });
    return _agoraInstance;
}
+ (instancetype)allocWithZone:(struct _NSZone *)zone{
    return [self shareInstance];
}
- (AgoraRtcEngineConfig *)agoraConfig{
    if (!_agoraConfig) {
        _agoraConfig = [[AgoraRtcEngineConfig alloc]init];
        _agoraConfig.appId = [common getSw_app_id];
        _agoraConfig.channelProfile = AgoraChannelProfileLiveBroadcasting;
        _agoraConfig.areaCode = AgoraAreaCodeTypeGlobal;
    }
    return _agoraConfig;
}
/// RTC
- (AgoraRtcEngineKit *)agoraRtc{
    if(!_agoraRtc){
        _agoraRtc = [AgoraRtcEngineKit sharedEngineWithConfig:self.agoraConfig delegate:self];
        [_agoraRtc setVideoFrameDelegate:self];
    }
    return _agoraRtc;
}
-(AgoraBeautyOptions *)beautyOption{
    if (!_beautyOption) {
        _beautyOption = [[AgoraBeautyOptions alloc]init];
        _beautyOption.lighteningContrastLevel = AgoraLighteningContrastNormal;
        _beautyOption.lighteningLevel = 0.5;
        _beautyOption.rednessLevel = 0.5;
        _beautyOption.smoothnessLevel = 0.5;
        _beautyOption.sharpnessLevel = 0.5;
    }
    return _beautyOption;
}
-(void)initArray{
    if (!_rtcConnArray) {
        _rtcConnArray = [NSMutableArray array];
        _canvasArray = [NSMutableArray array];
    }
}
#pragma mark -主播和用户 加入频道
-(void)joinChannelWithChannelId:(NSString *)channelId andUserToken:(NSString *)userToken WithModel:(RtcMode)model isBroadcaster:(AgoraClientRole)role isGameLive:(BOOL)isgamelive{
    [self initArray];
    _channelId = channelId;
    AgoraRtcConnection *rtcCon = [[AgoraRtcConnection alloc]init];
    rtcCon.channelId = channelId;
    rtcCon.localUid = [[Config getOwnID] integerValue];
    [_rtcConnArray addObject:rtcCon];
    
    _rtcOps = [[AgoraRtcChannelMediaOptions alloc]init];
    _rtcOps.publishMediaPlayerAudioTrack = YES;

    if (model == RtcMode_Living) {
        _rtcOps.autoSubscribeAudio = YES;
        _rtcOps.autoSubscribeVideo = YES;

    }else if(model == RtcMode_Chat){
        _rtcOps.autoSubscribeAudio = YES;
        _rtcOps.autoSubscribeVideo = NO;
    }
    if (role == AgoraClientRoleBroadcaster) {
        if(model == RtcMode_Living){
            _rtcOps.publishCameraTrack = YES;
        }
        _rtcOps.publishMicrophoneTrack = YES;
        [self.agoraRtc enableAudio];
        [self.agoraRtc enableVideo];
        if(isgamelive){
            [self.agoraRtc startScreenCapture:self.screenParams];
            [self prepareSystemBroadcaster];
            for (UIView *view in self.systemBroadcastPicker.subviews) {
                if ([view isKindOfClass:[UIButton class]]) {
                    [((UIButton *)view) sendActionsForControlEvents:(UIControlEventAllEvents)];
                    break;
                }
            }
        }
    }
    _rtcOps.clientRoleType = role;
    int resultCode = [self.agoraRtc joinChannelByToken:userToken channelId:channelId uid:[[Config getOwnID] integerValue] mediaOptions:_rtcOps joinSuccess:^(NSString * _Nonnull channel, NSUInteger uid, NSInteger elapsed) {
            NSLog(@"ybagoraManager---channel:%@   \n--uid:%ld  \n ---elspsed:%ld",channel, uid, elapsed);
            if (role == AgoraClientRoleAudience) {
    
            }else{
                if ([self.delegate respondsToSelector:@selector(joinChannelSuc)]) {
                    [self.delegate joinChannelSuc];
                }
            }
    }];
    NSLog(@"--0-0-0-0-:%d",resultCode);
}
- (void)prepareSystemBroadcaster {
    CGRect frame = CGRectMake(0, 0, 60, 60);
    self.systemBroadcastPicker = [[RPSystemBroadcastPickerView alloc]initWithFrame:frame];
    self.systemBroadcastPicker.showsMicrophoneButton = NO;
    self.systemBroadcastPicker.autoresizingMask = UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleRightMargin;
    NSString *bundleId = [NSBundle mainBundle].bundleIdentifier;
    self.systemBroadcastPicker.preferredExtension = [NSString stringWithFormat:@"%@.YBLiveScreen",bundleId];
}
- (AgoraScreenCaptureParameters2 *)screenParams {
    if (_screenParams == nil) {
        _screenParams = [[AgoraScreenCaptureParameters2 alloc] init];
        _screenParams.captureAudio = YES;
//        _screenParams.captureVideo = YES;
        AgoraScreenAudioParameters *audioParams = [[AgoraScreenAudioParameters alloc] init];
        audioParams.captureSignalVolume = 50;
        _screenParams.audioParams = audioParams;
        AgoraScreenVideoParameters *videoParams = [[AgoraScreenVideoParameters alloc] init];
        videoParams.dimensions = AgoraVideoDimension1280x720;// [self screenShareVideoDimension];
        videoParams.frameRate = AgoraVideoFrameRateFps15;
        videoParams.bitrate = AgoraVideoBitrateStandard;

        _screenParams.videoParams = videoParams;
    }
    return _screenParams;
}
- (CGSize)screenShareVideoDimension {
    CGSize screenSize = UIScreen.mainScreen.bounds.size;
    CGSize boundingSize = CGSizeMake(540, 960);
    CGFloat mW = boundingSize.width / screenSize.width;
    CGFloat mH = boundingSize.height / screenSize.height;
    if (mH < mW) {
        boundingSize.width = boundingSize.height / screenSize.height * screenSize.width;
    } else if (mW < mH) {
        boundingSize.height = boundingSize.width / screenSize.width * screenSize.height;
    }
    return boundingSize;
}

#pragma mark -主播和主播 加入频道
-(void)joinChannelExWithChannelId:(NSString *)channelId andUserToken:(NSString *)userToken WithModel:(RtcMode)model isBroadcaster:(AgoraClientRole)role{
    AgoraRtcConnection *rtcCon = [[AgoraRtcConnection alloc]init];
    rtcCon.channelId = channelId;
    rtcCon.localUid = [[Config getOwnID] integerValue];
    [_rtcConnArray addObject:rtcCon];

    AgoraRtcChannelMediaOptions *rtcOps = [[AgoraRtcChannelMediaOptions alloc]init];
    if (model == RtcMode_Living) {
        rtcOps.autoSubscribeAudio = YES;
        rtcOps.autoSubscribeVideo = YES;
        rtcOps.publishMediaPlayerAudioTrack = YES;

    }else if(model == RtcMode_Chat){
        rtcOps.autoSubscribeAudio = YES;
        rtcOps.autoSubscribeVideo = NO;
        rtcOps.publishMediaPlayerAudioTrack = YES;

    }
    if (role == AgoraClientRoleBroadcaster) {
        if(model == RtcMode_Living){
            rtcOps.publishCameraTrack = YES;
        }
        rtcOps.publishMicrophoneTrack = YES;
        [self.agoraRtc enableAudio];
        [self.agoraRtc enableVideo];
    }
    rtcOps.clientRoleType = role;
    int resultCode = [self.agoraRtc joinChannelExByToken:userToken connection:rtcCon delegate:self mediaOptions:rtcOps joinSuccess:^(NSString * _Nonnull channel, NSUInteger uid, NSInteger elapsed) {
        NSLog(@"ybagoraManager---channel:%@   \n--uid:%ld  \n ---elspsed:%ld",channel, uid, elapsed);
        if (role == AgoraClientRoleAudience) {
        }else{
            if ([self.delegate respondsToSelector:@selector(joinChannelSuc)]) {
                [self.delegate joinChannelSuc];
            }
        }
    }];
    NSLog(@"--0-0-0-0-:%d",resultCode);
}
-(void)startOrUpdateJoinChannelMediaRelayWithDic:(NSDictionary *)dicInfo andChannelName:(NSString *)channelId{
    AgoraChannelMediaRelayConfiguration *replayConfig = [[AgoraChannelMediaRelayConfiguration alloc]init];
    replayConfig.sourceInfo.token = minstr([dicInfo valueForKey:@"src_token"]);
    AgoraChannelMediaRelayInfo *relayInfo = [[AgoraChannelMediaRelayInfo alloc]init];
    relayInfo.token = minstr([dicInfo valueForKey:@"dest_token"]);;
    relayInfo.uid = 0;
    [replayConfig setDestinationInfo:relayInfo forChannelName:channelId];
    [self.agoraRtc startOrUpdateChannelMediaRelay:replayConfig];
    
    src_token = minstr([dicInfo valueForKey:@"src_token"]);
    dest_token = minstr([dicInfo valueForKey:@"dest_token"]);;
}

-(void)stopChannelMediaRelay{
    [self.agoraRtc stopChannelMediaRelay];
    NSLog(@"stopChannelMediaRelay======:%d",[self.agoraRtc stopChannelMediaRelay]);
}
- (void)rtcEngine:(AgoraRtcEngineKit * _Nonnull)engine
channelMediaRelayStateDidChange:(AgoraChannelMediaRelayState)state
            error:(AgoraChannelMediaRelayError)error;
{
    NSLog(@"channelMediaRelayStateDidChange======:%ld",state);

}
#pragma mark - 加入频道后更新频道媒体选项
-(void)updateChannel:(NSString *)channelId options:(AgoraRtcChannelMediaOptions *)options {
    AgoraRtcConnection *rtcCon = nil;
    for (AgoraRtcConnection *subCon in _rtcConnArray) {
        if([subCon.channelId isEqual:channelId]){
            rtcCon = subCon;
        }
    }
    [self.agoraRtc updateChannelWithMediaOptions:options];
}
#pragma mark - 加入频道后更新频道媒体选项
-(void)updateChannelEx:(NSString *)channelId options:(AgoraRtcChannelMediaOptions *)options {
    AgoraRtcConnection *rtcCon = nil;
    for (AgoraRtcConnection *subCon in _rtcConnArray) {
        if([subCon.channelId isEqual:channelId]){
            rtcCon = subCon;
        }
    }
    [self.agoraRtc updateChannelExWithMediaOptions:options connection:rtcCon];
//    [self.agoraRtc updateChannelWithMediaOptions:options];
}

#pragma mark - 切换连麦角色
-(void)switchRole {

}
#pragma mark -展示画面的view
-(void)showWithCanvasView:(UIView *)canvasView andUserId:(NSString *)userID  isBroadcaster:(AgoraClientRole)role RenderMode:(AgoraVideoRenderMode)renderMode{
    AgoraRtcVideoCanvas *canvas = [[AgoraRtcVideoCanvas alloc]init];
    canvas.view = canvasView;
    canvas.uid = [userID integerValue];
    canvas.renderMode = renderMode;// AgoraVideoRenderModeFit;
    canvas.setupMode = AgoraVideoViewSetupAdd;
    [_canvasArray addObject:canvas];
    if (role == AgoraClientRoleBroadcaster) {
        _videoConfig = [[AgoraVideoEncoderConfiguration alloc]init];
        _videoConfig.dimensions = AgoraVideoDimension1280x720;
        _videoConfig.mirrorMode = AgoraVideoMirrorModeEnabled;

        [self.agoraRtc setVideoEncoderConfiguration:_videoConfig];
        [self.agoraRtc setupLocalVideo:canvas];
        [self.agoraRtc startPreview];
        [self.agoraRtc setDefaultAudioRouteToSpeakerphone:YES];
        [self.agoraRtc enableAudio];
        [self.agoraRtc enableVideo];
    }else{
        AgoraRtcVideoCanvas *currentCanvas;
        AgoraRtcConnection *currentConn;
        for (AgoraRtcVideoCanvas *canvas in _canvasArray) {
            if (canvas.uid == [userID integerValue]) {
                currentCanvas = canvas;
            }
        }
        for (AgoraRtcConnection *conn in _rtcConnArray) {
            if (conn.localUid == [[Config getOwnID] integerValue]) {
                currentConn = conn;
            }
        }
        [self.agoraRtc setupRemoteVideo:currentCanvas];
    }
}
-(AgoraDataStreamConfig *)dataConfig{
    if (!_dataConfig) {
        _dataConfig = [[AgoraDataStreamConfig alloc]init];
        _dataConfig.ordered = YES;
        _dataConfig.syncWithAudio = YES;
    }
    return _dataConfig;
}
-(void)sendStreamMsg:(NSDictionary *)dataMsg andStreamId:(NSString *)streamId{
    streamID = 0;
    int resultCode =  [self.agoraRtc createDataStream:&(streamID) config:self.dataConfig];
    NSLog(@"agor---------:%d",resultCode);
    if (resultCode == 0) {
        NSData *msgData = [NSJSONSerialization dataWithJSONObject:dataMsg options:NSJSONWritingPrettyPrinted error:nil];
        [self.agoraRtc sendStreamMessage:streamID data:msgData];
    }
}
#pragma mar --连麦方法
#pragma mark - 离开Ex频道
-(void)leaveChannelEx {
    if (_AgoraRole == AgoraClientRoleBroadcaster) {
        [self.agoraRtc stopPreview];
    }
    for (AgoraRtcConnection *rtcCons in _rtcConnArray) {
        [self.agoraRtc leaveChannelEx:rtcCons leaveChannelBlock:^(AgoraChannelStats * _Nonnull stat) {
            NSLog(@"离开频道------：%@", stat);
        }];
    }
}
#pragma mark - 离开频道
-(void)leaveChannel:(NSString *)uid{
    if (uid) {
        if (_AgoraRole == AgoraClientRoleBroadcaster) {
            if([uid isEqual:[Config getOwnID]]){
                [self.agoraRtc stopPreview];
            }else{
                [_audienceView removeFromSuperview];
            }
            [self.agoraRtc leaveChannel:^(AgoraChannelStats * _Nonnull stat) {
                NSLog(@"离开频道------：%@", stat);
            }];

        }else{
            if([uid isEqual:[Config getOwnID]]){
                [_audienceView removeFromSuperview];
            }
        }
    }else{
        if (_AgoraRole == AgoraClientRoleBroadcaster) {
                [self.agoraRtc stopPreview];
        }
        [self.agoraRtc leaveChannel:^(AgoraChannelStats * _Nonnull stat) {
            NSLog(@"离开频道------：%@", stat);
        }];
        [_canvasArray removeAllObjects];
        if (_audienceView) {
            [_audienceView removeFromSuperview];
        }
        if(_broadcastView){
            [_broadcastView removeFromSuperview];
        }
    }
}
-(void)stopPushView{
    [self.agoraRtc stopPreview];
}
-(void)stopScreenCapture{
    // 停止屏幕共享
    [self.agoraRtc stopScreenCapture];
    // 停止在频道中发布屏幕采集的视频
    self.rtcOps.publishScreenCaptureVideo = false;
    // 在频道中发布摄像头采集的视频
    self.rtcOps.publishScreenCaptureAudio = false;
    self.rtcOps.publishCameraTrack = true;
    [self.agoraRtc updateChannelWithMediaOptions:self.rtcOps];
}
-(void)removePlayView:(UIView *)playView WithUserId:(NSString *)userID{
    for (int i = 0; i < _canvasArray.count; i ++) {
        AgoraRtcVideoCanvas *canvas = _canvasArray[i];
        if (canvas.uid == [userID integerValue]) {
            canvas.setupMode = AgoraVideoViewSetupRemove;
            [_canvasArray removeObjectAtIndex:i];
        }
    }
}
//断开连麦
-(void)disConnectLinkMic:(NSString *)uid{
    if (_AgoraRole == AgoraClientRoleBroadcaster) {
        if([uid isEqual:[Config getOwnID]]){
            [self.agoraRtc stopPreview];
        }else{
            [_audienceView removeFromSuperview];
        }
        AgoraRtcChannelMediaOptions *mediaOption = [[AgoraRtcChannelMediaOptions alloc]init];
        mediaOption.clientRoleType = AgoraClientRoleBroadcaster;
        [self updateChannel:_channelId options:mediaOption];
    }else{
        [_audienceView removeFromSuperview];
        if([uid isEqual: [Config getOwnID]]){
            [self.agoraRtc stopPreview];
        }
        AgoraRtcChannelMediaOptions *mediaOption = [[AgoraRtcChannelMediaOptions alloc]init];
        mediaOption.clientRoleType = AgoraClientRoleAudience;
        [self updateChannel:_channelId options:mediaOption];
    }
}
#pragma mark - 销毁
-(void)destroyRtc{
    if(!self.agoraRtc){
        return;
    }
    [self leaveChannelEx];
    [AgoraRtcEngineKit destroy];
}
#pragma mark -
-(void)resumePush{
//    [self.agoraRtc resumeAudio];
   
}
-(void)pausePush{
//    [self.agoraRtc disableAudio];
}
#pragma mark -静音
-(void)muteLocalAudioStream:(BOOL)isMute{
    [self.agoraRtc muteLocalAudioStream:isMute];
}
-(void)muteRemoteAudio:(BOOL)isMute {
    [self.agoraRtc muteAllRemoteAudioStreams:isMute];
}
#pragma mark -闪光灯
-(void)cameraTorch:(BOOL)isTorch{
    [self.agoraRtc setCameraTorchOn:isTorch];
}
#pragma mark -切换摄像头
-(void)changeCamera:(BOOL)isFront{
    [self.agoraRtc switchCamera];
}
#pragma mark -切换镜像
-(void)changeMirror:(BOOL)isMirror{
//    AgoraVideoEncoderConfiguration *videoConfig = [[AgoraVideoEncoderConfiguration alloc]init];
    if (isMirror) {
        _videoConfig.mirrorMode = AgoraVideoMirrorModeEnabled;
    }else{
        _videoConfig.mirrorMode =AgoraVideoMirrorModeDisabled;
    }
//    videoConfig.dimensions = AgoraVideoDimension1280x720;
    int resultMirr =  [self.agoraRtc setVideoEncoderConfiguration:_videoConfig];
    NSLog(@"0-0-0-0-0-----resultMirr:%d", resultMirr);
}
#pragma mark -播放音乐
-(void)playBGMWithPath:(NSString *)musicPath{
    int musicResult = [self.agoraRtc startAudioMixing:musicPath loopback:NO cycle:1 startPos:0];

    NSLog(@"0-0-0--0-0-0-0----:%d",musicResult);
}
#pragma mark -停止播放音乐
-(void)stopBGM{
    [self.agoraRtc stopAudioMixing];
}
#pragma mark - AgoraRtcEngineDelegate

- (void)rtcEngine:(AgoraRtcEngineKit *)engine didOccurWarning:(AgoraWarningCode)warningCode {
    NSLog(@"rk===>[agora-warning] %ld",(long)warningCode);
}
-(void)rtcEngine:(AgoraRtcEngineKit *)engine didOccurError:(AgoraErrorCode)errorCode {
    NSLog(@"rk===>[agora-error] %ld",(long)errorCode);
}
-(void)rtcEngine:(AgoraRtcEngineKit *)engine reportRtcStats:(AgoraChannelStats *)stats {
    NSLog(@"rk===>[agora-rtcStats] %lu",(unsigned long)stats.duration);
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine didJoinChannel:(NSString *)channel withUid:(NSUInteger)uid elapsed:(NSInteger)elapsed {
    NSLog(@"rk===>[agora-joinChannel] %lu",(unsigned long)uid);
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine didJoinedOfUid:(NSUInteger)uid elapsed:(NSInteger)elapsed{
    NSLog(@"rk===>[agora-user-join] %lu",(unsigned long)uid);
//    if(uid == [_broadcastIDStr integerValue]){
//        [self showWithCanvasView:_broadcastView andUserId:_broadcastIDStr isBroadcaster:AgoraClientRoleAudience];
//    }else{
//        //上麦用户
//        [self showWithCanvasView:_audienceView andUserId:[NSString stringWithFormat:@"%lu",uid] isBroadcaster:AgoraClientRoleAudience];
//    }
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine didOfflineOfUid:(NSUInteger)uid reason:(AgoraUserOfflineReason)reason {
    NSLog(@"rk===>[agora-user-offline] %lu",(unsigned long)uid);
    
}

- (void)rtcEngine:(AgoraRtcEngineKit * _Nonnull)engine firstLocalVideoFramePublishedWithElapsed:(NSInteger)elapsed{
    NSLog(@"rk===>[agora-firstLocalVideoFramePublishedWithElapsed] %lu",(unsigned long)elapsed);
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine firstRemoteVideoFrameOfUid:(NSUInteger)uid size:(CGSize)size elapsed:(NSInteger)elapsed{
    NSLog(@"rk===>[agora-first-video] %lu",(unsigned long)uid);
    if([self.delegate respondsToSelector:@selector(firstRemoteVideoSuccess)]){
        [self.delegate firstRemoteVideoSuccess];
    }
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine localVideoStateChangedOfState:(AgoraVideoLocalState)state error:(AgoraLocalVideoStreamError)error sourceType:(AgoraVideoSourceType)sourceType {
    if (state == AgoraVideoLocalStateCapturing && sourceType == AgoraVideoSourceTypeScreen) {
        _rtcOps.publishScreenCaptureAudio = YES;
        _rtcOps.publishScreenCaptureVideo = YES;
        _rtcOps.publishCameraTrack = NO;
        [self.agoraRtc updateChannelWithMediaOptions:_rtcOps];
    }
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine remoteVideoStateChangedOfUid:(NSUInteger)uid state:(AgoraVideoRemoteState)state reason:(AgoraVideoRemoteReason)reason elapsed:(NSInteger)elapsed{
    NSLog(@"rk===>[agora-state-change] %lu",(unsigned long)state);
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine localAudioStats:(AgoraRtcLocalAudioStats *)stats {
    NSLog(@"rk===>[agora-localAudioStats] %lu==>%lu",(unsigned long)stats.sentSampleRate,(unsigned long)stats.audioDeviceDelay);
}
-(void)rtcEngine:(AgoraRtcEngineKit *)engine localVideoStats:(AgoraRtcLocalVideoStats *)stats sourceType:(AgoraVideoSourceType)sourceType
{
    NSLog(@"rk===>[agora-localVideoStats] %lu==>%lu====>%ld",(unsigned long)stats.sentBitrate,(unsigned long)stats.sentFrameRate,(long)sourceType);
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine localVideoStats:(AgoraRtcLocalVideoStats *)stats {
    
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine remoteAudioStats:(AgoraRtcRemoteAudioStats *)stats {
    NSLog(@"rk===>[agora-remoteAudioStats] %lu==>%lu",(unsigned long)stats.quality,(unsigned long)stats.audioLossRate);
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine remoteVideoStats:(AgoraRtcRemoteVideoStats *)stats {
    NSLog(@"rk===>[agora-remoteVideoStats] %lu==>%lu",(unsigned long)stats.frameLossRate,(unsigned long)stats.packetLossRate);
}
- (BOOL)onCaptureVideoFrame:(AgoraOutputVideoFrame *)videoFrame sourceType:(AgoraVideoSourceType)sourceType
{
    
    if ([self.delegate respondsToSelector:@selector(beautyCaptureVideoFrame:sourceType:)]) {
        [self.delegate beautyCaptureVideoFrame:videoFrame sourceType:sourceType];
    }
    return YES;

}
- (void)rtcEngine:(AgoraRtcEngineKit * _Nonnull)engine
                receiveStreamMessageFromUid:(NSUInteger)uid
                streamId:(NSInteger)streamId
                data:(NSData * _Nonnull)data;
{
    NSString *receiveStr = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    NSData *datas = [receiveStr dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *jsondic = [NSJSONSerialization  JSONObjectWithData:datas options:NSJSONReadingMutableLeaves error:nil];
    NSLog(@"manager-----FromUid:%ld  ===:%ld  ====jsondic:%@",uid, streamId,jsondic);
    [[NSNotificationCenter defaultCenter]postNotificationName:@"showAgoraLink" object:nil userInfo:jsondic];
}
//音乐文件播放进度回调。
- (void)rtcEngine:(AgoraRtcEngineKit *_Nonnull)engine audioMixingPositionChanged:(NSInteger)position {
    NSLog(@"rk===>[agora-audioMixingPositionChanged] %lu",(unsigned long)position);
    if([self.delegate respondsToSelector:@selector(musicPositionChanged:)]) {
        [self.delegate musicPositionChanged:position];
    }
}
//音乐文件的播放状态已改变回调。
- (void)rtcEngine:(AgoraRtcEngineKit *_Nonnull)engine audioMixingStateChanged:(AgoraAudioMixingStateType)state
reasonCode:(AgoraAudioMixingReasonCode)reasonCode
{
    if (state == AgoraAudioMixingStateTypePlaying || state == AgoraAudioMixingStateTypeStopped) {
        if([self.delegate respondsToSelector:@selector(musicStateChanged:)]){
            [self.delegate musicStateChanged:state];
        }
        [self.agoraRtc adjustAudioMixingPublishVolume:80];
//      [self.agoraRtc adjustPlaybackSignalVolume:300];
    }
    NSLog(@"rk===>[agora-audioMixingStateChanged] %lu   \n reasonCode:%lu",(unsigned long)state,(unsigned long)reasonCode);
}
//美颜相关
- (void)setBeautyEffectOptions:(BOOL)enable options:(AgoraBeautyOptions* _Nullable)options;
{
    [self.agoraRtc setBeautyEffectOptions:enable options:self.beautyOption];
}
-(void)changeBeautyValue:(CGFloat)value{
    self.beautyOption.lighteningLevel = value;
    self.beautyOption.smoothnessLevel =value;
    self.beautyOption.sharpnessLevel = value;

    [self.agoraRtc setBeautyEffectOptions:YES options:self.beautyOption];

}
@end
