//
//  YBAgoraManager.h
//  YBLive
//
//  Created by yunbao02 on 2024/1/30.
//  Copyright © 2024 cat. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AgoraRtcKit/AgoraRtcKit.h>

typedef NS_ENUM(NSInteger,RtcMode) {
    RtcMode_Living,         // 直播
    RtcMode_Chat,           // 语音
};


@protocol YbAgoraDelegate <NSObject>
//加入频道
-(void)joinChannelSuc;
//获取到第一帧
-(void)firstRemoteVideoSuccess;
//音乐进度回调
-(void)musicPositionChanged:(NSInteger)position;
//音乐状态改变
-(void)musicStateChanged:(AgoraAudioMixingStateType)state;
//美颜回调
-(void)beautyCaptureVideoFrame:(AgoraOutputVideoFrame *)videoFrame sourceType:(AgoraVideoSourceType)sourceType;
@end

@interface YBAgoraManager : NSObject

@property (nonatomic, assign)id<YbAgoraDelegate>delegate;
@property (nonatomic, strong)NSString *broadcastIDStr;
@property (nonatomic, strong)UIView *broadcastView; //主播视图
@property (nonatomic, strong)UIView *audienceView;  //用户视图
@property (nonatomic, assign)AgoraClientRole AgoraRole;
@property (nonatomic, strong)NSString *channelId;
@property (nonatomic, assign)BOOL isLiveLinkMic;
+(instancetype)shareInstance;
//展示画面
//-(void)showWithCanvasView:(UIView *)canvasView andUserId:(NSString *)userID  isBroadcaster:(AgoraClientRole)role;
-(void)showWithCanvasView:(UIView *)canvasView andUserId:(NSString *)userID  isBroadcaster:(AgoraClientRole)role RenderMode:(AgoraVideoRenderMode)renderMode;
/// 加入频道
-(void)joinChannelWithChannelId:(NSString *)channelId andUserToken:(NSString *)userToken WithModel:(RtcMode)model isBroadcaster:(AgoraClientRole)role isGameLive:(BOOL)isgamelive;

-(void)joinChannelExWithChannelId:(NSString *)channelId andUserToken:(NSString *)userToken WithModel:(RtcMode)model isBroadcaster:(AgoraClientRole)role;
#pragma mark - 加入频道后更新频道媒体选项
-(void)updateChannel:(NSString *)channelId options:(AgoraRtcChannelMediaOptions *)options;
#pragma mark - 加入频道后更新频道媒体选项
-(void)updateChannelEx:(NSString *)channelId options:(AgoraRtcChannelMediaOptions *)options;
#pragma mark -断开连麦
-(void)disConnectLinkMic:(NSString *)uid;
//停止屏幕共享
-(void)stopScreenCapture;
/// 离开频道
-(void)leaveChannelEx;
#pragma mark - 离开频道
-(void)leaveChannel:(NSString *)uid;

-(void)stopPushView;
-(void)removePlayView:(UIView *)playView WithUserId:(NSString *)userID;
//闪光灯
-(void)cameraTorch:(BOOL)isTorch;
#pragma mark -切换摄像头
-(void)changeCamera:(BOOL)isFront;
#pragma mark -切换镜像
-(void)changeMirror:(BOOL)isMirror;
#pragma mark -播放音乐
-(void)playBGMWithPath:(NSString *)musicPath;
#pragma mark -停止播放音乐
-(void)stopBGM;

-(void)resumePush;
-(void)pausePush;
#pragma mark -静音
-(void)muteLocalAudioStream:(BOOL)isMute;
-(void)muteRemoteAudio:(BOOL)isMute;
-(void)startOrUpdateJoinChannelMediaRelayWithDic:(NSDictionary *)dicInfo andChannelName:(NSString *)channelId;
-(void)stopChannelMediaRelay;

-(void)sendStreamMsg:(NSDictionary *)dataMsg andStreamId:(NSString *)streamId;

- (void)setBeautyEffectOptions:(BOOL)enable options:(AgoraBeautyOptions* _Nullable)options;
-(void)changeBeautyValue:(CGFloat)value;
@end


