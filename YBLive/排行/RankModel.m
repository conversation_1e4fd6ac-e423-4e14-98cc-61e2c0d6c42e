//
//  RankModel.m
//  YBLive
//
//  Created by <PERSON><PERSON><PERSON> on 2018/2/2.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "RankModel.h"

@implementation RankModel

- (instancetype)initWithDic:(NSDictionary *)dic {
    self = [super init];
    if (self) {
        
        _totalCoinStr = YBValue(dic, @"totalcoin");
        _uidStr = YBValue(dic, @"uid");
        _unameStr = YBValue(dic, @"user_nickname");
        _iconStr = YBValue(dic, @"avatar_thumb");
        _level_anchor = YBValue(dic, @"level_anchor");
        _levelStr = YBValue(dic, @"level");
        _isAttentionStr = YBValue(dic, @"isAttention");
        _sex = YBValue(dic, @"sex");

    }
    return self;
}
+(instancetype)modelWithDic:(NSDictionary *)dic {
     return [[self alloc]initWithDic:dic];
}
@end
