//
//  WinningRecordVC.m
//  YBLive
//
//  Created by ybRRR on 2022/3/1.
//  Copyright © 2022 cat. All rights reserved.
//

#import "WinningRecordVC.h"
#import "turntableRecordCell.h"
@interface WinningRecordVC ()<UITableViewDelegate,UITableViewDataSource>
{
    UITableView *listTable;
    NSMutableArray *listArray;
    int page;
}
@end

@implementation WinningRecordVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"中奖记录");
    [self.rightBtn setTitle:YZMsg(@"清空") forState:0];
    [self.rightBtn setTitleColor:UIColor.blackColor forState:0];
    self.rightBtn.hidden = NO;
    listArray = [NSMutableArray array];
    page = 1;

    
    listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:0];
    listTable.separatorStyle = 0;
    listTable.delegate = self;
    listTable.dataSource = self;
    listTable.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:listTable];
    listTable.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
        page = 1;
        [self requestData];
    }];
    listTable.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
        page ++;
        [self requestData];

    }];
    [self requestData];

}
-(void)rightBtnClick
{
    UIAlertController *alertControl = [UIAlertController alertControllerWithTitle:nil message:YZMsg(@"确定清空所有中奖记录?") preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self clearHistoryList];
    }];
    [cancelAction setValue:UIColor.grayColor forKey:@"_titleTextColor"];
    [sureAction setValue:normalColors forKey:@"_titleTextColor"];
    [alertControl addAction:cancelAction];
    [alertControl addAction:sureAction];
    [self.navigationController presentViewController:alertControl animated:YES completion:nil];

}
-(void)clearHistoryList{
    YBWeakSelf;
    [YBToolClass postNetworkWithUrl:@"User.clearTurntableWinLists" andParameter:@{@"p":@(page)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            [weakSelf requestData];
        }
        [MBProgressHUD showError:msg];
    } fail:^{
    }];

}
- (void)requestData{
    [YBToolClass postNetworkWithUrl:@"User.getTurntableWinLists" andParameter:@{@"p":@(page)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [listTable.mj_header endRefreshing];
        [listTable.mj_footer endRefreshing];

        if (code == 0) {
            if (page == 1) {
                [listArray removeAllObjects];
            }
            [listArray addObjectsFromArray:info];
            [listTable reloadData];
        }
        if ([info count] == 0) {
            [listTable.mj_footer endRefreshingWithNoMoreData];
        }
        if(listArray.count < 1){
            [PublicView showTextNoData:listTable text1:@"" text2:YZMsg(@"暂无数据")];
        }else{
            [PublicView hiddenTextNoData:listTable];
        }

    } fail:^{
        [listTable.mj_header endRefreshing];
        [listTable.mj_footer endRefreshing];

    }];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return listArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    turntableRecordCell *cell = [tableView dequeueReusableCellWithIdentifier:@"turntableRecordCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"turntableRecordCell" owner:nil options:nil] lastObject];
    }
//    cell.nameL.text = [NSString stringWithFormat:@"%ld",indexPath.row + 1];
    cell.nameL.hidden = YES;
    NSDictionary *dic = listArray[indexPath.row];
    cell.giftNumL.text = [NSString stringWithFormat:@"x%@",minstr([dic valueForKey:@"nums"])];
    cell.timeL.text = minstr([dic valueForKey:@"addtime"]);
    [cell.giftImgV sd_setImageWithURL:[NSURL URLWithString:minstr([dic valueForKey:@"thumb"])]];
    cell.line.hidden = NO;
    cell.contentView.backgroundColor = UIColor.whiteColor;
    [cell resetLefMASConstraint];
    return cell;

}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 60;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

@end
