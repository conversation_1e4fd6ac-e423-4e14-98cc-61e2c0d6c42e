//
//  YBPersonTableViewCell.m
//  TCLVBIMDemo
//
//  Created by admin on 16/11/11.
//  Copyright © 2016年 tencent. All rights reserved.
//

#import "YBPersonTableViewCell.h"
#import "fansViewController.h"
//#import "UIView+Additions.h"
//底部View
#import "YBPersonTableViewModel.h"
#import "LiveNodeViewController.h"
@interface YBPersonTableViewCell ()
{
    //名字宽度
    UIView *lineV;
}
//底部view
@property (nonatomic, strong) UIView *bottomView;
@end
@implementation YBPersonTableViewCell
-(void)drawRect:(CGRect)rect{
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    CGContextSetLineWidth(ctx,10);
    CGContextSetStrokeColorWithColor(ctx,[UIColor colorWithRed:246/255.0 green:246/255.0 blue:246/255.0 alpha:1.0].CGColor);
    CGContextMoveToPoint(ctx,0,170);
    CGContextAddLineToPoint(ctx,(self.frame.size.width),170);
    CGContextStrokePath(ctx);
}
-(void)perform:(NSString *)text{
    if ([text rangeOfString:YZMsg(@"直播")].location != NSNotFound) {
    
        [self.personCellDelegate pushLiveNodeList];
        
    }else if ([text rangeOfString:YZMsg(@"粉丝")].location != NSNotFound){
        
        [self.personCellDelegate pushFansList];
        
    }else if ([text rangeOfString:YZMsg(@"关注")].location != NSNotFound){
        
        [self.personCellDelegate pushAttentionList];
        
    }
}
-(void)setModel:(YBPersonTableViewModel *)model{
    
    
    _model = model;
    _nameLabel.text = _model.user_nicename;

    NSString *laingname = minstr([Config getliang]);
    if ([laingname isEqual:@"0"]) {
        _IDL.text = [NSString stringWithFormat:@"ID:%@",_model.ID];
    }
    else{
        _IDL.text = [NSString stringWithFormat:@"%@:%@",YZMsg(@"靓"),laingname];
    }
    if ([_model.sex isEqualToString:@"1"]) {
        [_sexView setImage:[UIImage imageNamed:@"sex_man"]];
    }
    else{
        [_sexView setImage:[UIImage imageNamed:@"sex_woman"]];
    }
    [_fansBtn setTitle:[NSString stringWithFormat:@"%@ 粉丝",_model.fans] forState:0];
    [_followBtn setTitle:[NSString stringWithFormat:@"%@ 关注",_model.follows] forState:0];

    [_iconView sd_setImageWithURL:[NSURL URLWithString:_model.avatar] placeholderImage:[UIImage imageNamed:@"bg1"]];
    
}
+ (instancetype)cellWithTabelView:(UITableView *)tableView{
    static NSString *cellIdentifier = @"YBPersonTableViewCell";
    YBPersonTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIdentifier];
    if (cell == nil) {
        cell = [[YBPersonTableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellIdentifier];
    }
    return cell;
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self)
    {
        [self setupView];
        [self layoutUI];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}
//MARK:-设置控件
-(void)setupView
{
    UIImageView *backImgView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 205+statusbarHeight)];
    backImgView.image = [UIImage imageNamed:@"mine_headerBG"];
    backImgView.backgroundColor = normalColors;
    backImgView.userInteractionEnabled = YES;
    backImgView.contentMode = UIViewContentModeScaleToFill;
    [self.contentView addSubview:backImgView];
    

    //头像
    UIImageView *iconView = [[UIImageView alloc]init];
//    iconView.frame = CGRectMake(0,0,80,80);
    [iconView setClipsToBounds:YES];
    iconView.layer.masksToBounds = YES;
    iconView.layer.cornerRadius = 35;
    [iconView sizeToFit];
    self.iconView = iconView;
    [self.contentView addSubview:iconView];
    self.contentView.backgroundColor = RGB(238, 238, 238);
    //姓名
    UILabel *nameLabel = [[UILabel alloc]init];
    nameLabel.textColor = [UIColor whiteColor];
    nameLabel.textAlignment = NSTextAlignmentCenter;
    [nameLabel setFont:[UIFont boldSystemFontOfSize:18]];
    self.nameLabel = nameLabel;
    [self.contentView addSubview:nameLabel];
    //性别
    UIImageView *sexView = [[UIImageView alloc]init];
    [sexView setClipsToBounds:YES];
    [sexView setContentMode:UIViewContentModeScaleAspectFit];
    [self.contentView addSubview:sexView];
    self.sexView = sexView;
    //等级
    UIImageView *levelView = [[UIImageView alloc]init];
    [levelView setClipsToBounds:YES];
    [levelView setContentMode:UIViewContentModeScaleAspectFit];
    [self.contentView addSubview:levelView];
    self.levelView = levelView;
    
    UIImageView *levelViewhost = [[UIImageView alloc]init];
    [levelViewhost setClipsToBounds:YES];
    [levelViewhost setContentMode:UIViewContentModeScaleAspectFit];
    [self.contentView addSubview:levelViewhost];
    self.level_anchorView = levelViewhost;
    //编辑按钮
    UIButton *editBtn = [[UIButton alloc]init];
    [editBtn setImage:[UIImage imageNamed:@"mine_edit"] forState:UIControlStateNormal];
    [editBtn addTarget:self action:@selector(doEdit) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:editBtn];
    editBtn.imageEdgeInsets = UIEdgeInsetsMake(5, 5, 5, 5);
    self.editBtn = editBtn;

    //ID
    UILabel *ID = [[UILabel alloc]init];
    ID.backgroundColor = [UIColor clearColor];
    ID.textAlignment = NSTextAlignmentCenter;
    ID.textColor = [UIColor whiteColor];
    [ID setFont:[UIFont systemFontOfSize:13]];
    [self.contentView addSubview:ID];
    self.IDL = ID;
    
    UIButton *fansB = [UIButton buttonWithType:0];
    [fansB addTarget:self action:@selector(fansBtnClick) forControlEvents:UIControlEventTouchUpInside];
    fansB.titleLabel.font = [UIFont systemFontOfSize:13];
    [self.contentView addSubview:fansB];
    self.fansBtn = fansB;
    lineV = [[UIView alloc]init];
    lineV.backgroundColor = [UIColor whiteColor];
    [self.contentView addSubview:lineV];
    
    UIButton *followB = [UIButton buttonWithType:0];
    [followB addTarget:self action:@selector(followBtnClick) forControlEvents:UIControlEventTouchUpInside];
    followB.titleLabel.font = [UIFont systemFontOfSize:13];
    [self.contentView addSubview:followB];
    self.followBtn = followB;

    //底部view
    UIView *bottomView = [[UIView alloc]init];
    bottomView.backgroundColor = [UIColor whiteColor];
    bottomView.layer.cornerRadius = 5.0;
    bottomView.layer.masksToBounds = YES;
    [self.contentView addSubview:bottomView];
    self.bottomView = bottomView;
    NSArray *nameArr = @[@"消息",@"钱包",@"明细",@"商城"];
    NSArray *titleArr = @[@"消息",@"钱包",@"明细",@"道具"];

    if ([[PublicObj getAppBuild] isEqual: [common ios_shelves]]) {
        nameArr = @[@"消息",@"钱包",@"明细"];
        titleArr = @[@"消息",@"钱包",@"明细"];
    }
    CGFloat btnWidth = _window_width*0.92/nameArr.count;
    for (int i = 0; i < nameArr.count; i++) {
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(btnWidth*i, 0, btnWidth, 90);
        [btn setTitle:titleArr[i] forState:0];
        [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"icon%@",nameArr[i]]] forState:0];
        [btn addTarget:self action:@selector(bottomButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.titleLabel.font = [UIFont systemFontOfSize:12];
        [btn setTitleColor:RGB_COLOR(@"#646464", 1) forState:0];
        [bottomView addSubview:btn];
        [PublicObj setUpImgDownText:btn space:8];
    }
    LiveUser *user = [Config myProfile];
    [_iconView sd_setImageWithURL:[NSURL URLWithString:user.avatar]];
    _nameLabel.text = user.user_nickname;
    _IDL.text = [NSString stringWithFormat:@"ID:%@",user.ID];
    NSString *sexS = [NSString stringWithFormat:@"%@",user.sex];
    if ([sexS isEqualToString:@"1"]) {
        [_sexView setImage:[UIImage imageNamed:@"sex_man"]];
    }
    else{
        [_sexView setImage:[UIImage imageNamed:@"sex_woman"]];
    }
}
-(void)doEdit
{
    [self.personCellDelegate pushEditView];    
}
//MARK:-layoutSubviews
-(void)layoutUI
{
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make){
        make.width.height.mas_equalTo(70);
        make.left.mas_equalTo(self.contentView.mas_left).offset(10);
        make.top.mas_equalTo(self.contentView.mas_top).offset(49+statusbarHeight);
    }];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.iconView.mas_right).offset(15);
        make.centerY.equalTo(_iconView.mas_top).offset(3);
    }];
    
    [self.sexView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.top.mas_equalTo(self.nameLabel.mas_bottom).offset(7);
        make.height.mas_equalTo(15);
        make.width.mas_equalTo(18);

    }];
    
    [self.level_anchorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.sexView.mas_right).offset(5);
        make.width.mas_equalTo(30);
        make.height.mas_equalTo(15);
        make.centerY.equalTo(_sexView);
    }];
    [self.levelView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.level_anchorView.mas_right).offset(5);
        make.width.mas_equalTo(30);
        make.height.mas_equalTo(15);
        make.centerY.equalTo(_sexView);
    }];
    [self.editBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView.mas_right).offset(-5);
        make.top.equalTo(self.nameLabel.mas_top).offset(10);
        make.width.mas_equalTo(40);
        make.height.mas_equalTo(40);
    }];

    [self.IDL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.top.equalTo(self.sexView.mas_bottom).offset(8);
    }];
    [self.fansBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.top.equalTo(self.IDL.mas_bottom).offset(7);
        make.height.mas_equalTo(20);
    }];
    [lineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_fansBtn.mas_right).offset(15);
        make.centerY.equalTo(self.fansBtn);
        make.height.mas_equalTo(13);
        make.width.mas_equalTo(1);
    }];

    [self.followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(lineV.mas_right).offset(15);
        make.centerY.equalTo(self.fansBtn);
        make.height.mas_equalTo(20);
    }];

    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.width.equalTo(self.contentView).multipliedBy(0.92);
        make.bottom.equalTo(self.contentView.mas_bottom).offset(0);
        make.height.mas_equalTo(90);
    }];
}
- (void)fansBtnClick{
    [self.personCellDelegate pushFansList];
}
- (void)followBtnClick{
    [self.personCellDelegate pushAttentionList];
}
- (void)bottomButtonClick:(UIButton *)sender{
    [self.personCellDelegate pushViewControllerWithTitle:sender.titleLabel.text];
}
@end
