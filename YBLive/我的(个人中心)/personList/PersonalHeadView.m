//
//  PersonalHeadView.m
//  YBLive
//
//  Created by ybRRR on 2021/4/2.
//  Copyright © 2021 cat. All rights reserved.
//

#import "PersonalHeadView.h"
#import "fansViewController.h"
#import "attrViewController.h"
#import "MyCollectionVC.h"
#import "PersonHomeVC.h"

@implementation PersonalHeadView

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.layer.cornerRadius = 10;
        self.layer.masksToBounds = YES;        
        [self createInfo];
    }
    return self;
}
-(void)createInfo{
    NSArray *arr = @[YZMsg(@"粉丝"),YZMsg(@"关注"),YZMsg(@"收藏")];
    NSArray *numarr = @[@"10000",@"22222",@"99999"];
    for (int i = 0 ; i < arr.count; i ++) {
        UILabel *tl = [[UILabel alloc]init];
        tl.frame = CGRectMake(i * self.width/3, self.height-45, self.width/3, 20);
        tl.font = [UIFont systemFontOfSize:12];
        tl.textColor = [UIColor blackColor];
        tl.textAlignment = NSTextAlignmentCenter;
        tl.text = arr[i];
        [self addSubview:tl];
        
        UILabel *numLb = [[UILabel alloc]init];
        numLb.frame = CGRectMake(i * self.width/3, tl.top-18, self.width/3, 18);
        numLb.font = [UIFont boldSystemFontOfSize:14];
        numLb.textColor = [UIColor blackColor];
        numLb.textAlignment = NSTextAlignmentCenter;
        numLb.text = numarr[i];
        [self addSubview:numLb];
        
        if (i < 2) {
            UILabel *lineLb = [[UILabel alloc]init];
            lineLb.frame = CGRectMake(tl.right, numLb.top +5, 1, 30);
            lineLb.backgroundColor = CellRow_Cor;
            [self addSubview:lineLb];
        }
        if (i == 0) {
            fansNumLb = numLb;
        }else if(i == 1){
            attNumLb = numLb;
        }else{
            collectNumLb = numLb;
        }
        
        UIButton *btn = [UIButton buttonWithType:0];
        btn.tag = 10000+i;
        btn.frame = CGRectMake(i * self.width/3, numLb.top-5, self.width/3, 40);
        [btn addTarget:self action:@selector(clickInfo:) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:btn];

    }
}

-(void)doEdit{
    PersonHomeVC *vc = [[PersonHomeVC alloc] init];
    vc.userID = [Config getOwnID];
    [[MXBADelegate sharedAppDelegate] pushViewController:vc animated:YES];
}

-(void)clickInfo:(UIButton *)sender{
    switch (sender.tag) {
        case 10000:{
            NSLog(@"点了粉丝");
            fansViewController *fans = [[fansViewController alloc]init];
            fans.fensiUid = [Config getOwnID];
            [[MXBADelegate sharedAppDelegate] pushViewController:fans animated:YES];
        }
            break;
        case 10001:{
            NSLog(@"点了关注");
            attrViewController *attention = [[attrViewController alloc]init];
            attention.guanzhuUID = [Config getOwnID];
            [[MXBADelegate sharedAppDelegate] pushViewController:attention animated:YES];
        }
            break;
        case 10002:{
            NSLog(@"点了收藏");
            if ([[YBYoungManager shareInstance]isOpenYoung]) {
                [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
                return;
            }

            MyCollectionVC *collect = [[MyCollectionVC alloc]init];
            [[MXBADelegate sharedAppDelegate]pushViewController:collect animated:YES];
        }
            break;

        default:
            break;
    }
}
-(void)setDataDic:(NSDictionary *)dataDic
{
    fansNumLb.text = minstr([dataDic valueForKey:@"fans"]);
    attNumLb.text = minstr([dataDic valueForKey:@"follows"]);
    collectNumLb.text = minstr([dataDic valueForKey:@"goods_collect_nums"]);

}
@end
