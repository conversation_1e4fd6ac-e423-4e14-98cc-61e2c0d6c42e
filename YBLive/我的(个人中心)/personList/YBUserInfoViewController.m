#import "YBUserInfoViewController.h"
#import "YBUserInfoListTableViewCell.h"
#import "YBPersonTableViewCell.h"
#import "YBPersonTableViewModel.h"
#import "LiveNodeViewController.h"
#import "fansViewController.h"
#import "attrViewController.h"
#import "setView.h"
#import "myProfitVC.h"
#import "RechargeViewController.h"
#import "AppDelegate.h"
#import "PhoneLoginVC.h"
#import "mineVideoVC.h"
#import "MyViewHomeVC.h"
#import "RoomManagementVC.h"
#import "PersonHomeVC.h"
#import "MessageListVC.h"
#import "shopDetailVC.h"
#import "MineDynamicViewController.h"
#import "ShopHomeVC.h"
#import "PaidContentVC.h"
#import "DailyTasksVC.h"
#import "MyCollectionVC.h"
#import "PersonalHeadView.h"
#import "YBFunctionCell.h"
#import "WinningRecordVC.h"
#import "THeader.h"
#import "PersonHomeVC.h"
#import "HeaderBackImgView.h"
@interface YBUserInfoViewController ()<V2TIMConversationListener>
{
    NSArray *listArr;
    UIView *navi;
    UIImageView *headImg;
    UILabel *nameLb;
    UILabel *idLb;
    UILabel *coinLb;

    UIImageView *vipStateImg;
    UILabel *vipSubTitle;
    UIButton *vipBuyBtn;

}
@property (nonatomic, assign, getter=isOpenPay) BOOL openPay;
@property(nonatomic,strong)NSDictionary *infoArray;//个人信息
@property (nonatomic, strong) YBPersonTableViewModel *model;
@property(nonatomic,strong)HeaderBackImgView *headImgView;

//性别
@property (nonatomic, weak) UIImageView *sexView;
//等级
@property (nonatomic, weak) UIImageView *levelView;
//主播等级
@property (nonatomic, weak) UIImageView *level_anchorView;

@end
@implementation YBUserInfoViewController

-(void)getPersonInfo: (void(^)(NSDictionary *))success failure:(void(^)(void))failure {
    NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
    NSNumber *app_build = [infoDictionary objectForKey:@"CFBundleVersion"];//本地 build
    //NSString *appCurVersion = [infoDictionary objectForKey:@"CFBundleShortVersionString"];version
    //NSLog(@"当前应用软件版本:%@",appCurVersion);
    NSString *build = [NSString stringWithFormat:@"%@",app_build];
    //这个地方传版本号，做上架隐藏，只有版本号跟后台一致，才会隐藏部分上架限制功能，不会影响其他正常使用客户(后台位置：私密设置-基本设置 -IOS上架版本号)
    NSString *userBaseUrl = [NSString stringWithFormat:@"User.getBaseInfo&uid=%@&token=%@&version_ios=%@",[Config getOwnID],[Config getOwnToken],build];
    [YBToolClass postNetworkWithUrl:userBaseUrl andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            LiveUser *user = [Config myProfile];
            NSDictionary *infoDic = [info firstObject];
            self.infoArray = infoDic;
            user.user_nickname = minstr([infoDic valueForKey:@"user_nickname"]);
            user.sex = minstr([infoDic valueForKey:@"sex"]);
            user.level =minstr([infoDic valueForKey:@"level"]);
            user.avatar = minstr([infoDic valueForKey:@"avatar"]);
            user.city = minstr([infoDic valueForKey:@"city"]);
            user.level_anchor = minstr([infoDic valueForKey:@"level_anchor"]);
            [Config updateProfile:user];
            
            BOOL isSmall  = [[infoDic valueForKey:@"live_window"] boolValue];
            [common saveSmallLiveWindow:isSmall];
            //保存靓号和vip信息
            NSDictionary *liang = [infoDic valueForKey:@"liang"];
            NSString *liangnum = minstr([liang valueForKey:@"name"]);
            NSDictionary *vip = [infoDic valueForKey:@"vip"];
            NSString *type = minstr([vip valueForKey:@"type"]);
            NSDictionary *subdic = [NSDictionary dictionaryWithObjects:@[type,liangnum] forKeys:@[@"vip_type",@"liang"]];
            [Config saveVipandliang:subdic];
            _model = [YBPersonTableViewModel modelWithDic:infoDic];
            NSArray *list = [infoDic valueForKey:@"list"];
            listArr = list;
            [common savepersoncontroller:listArr];//保存在本地，防止没网的时候不显示
            
            [self setHeaderDataDic:infoDic];
            success ? success(infoDic) : nil;
        } else{
            failure ? failure() : nil;
            listArr = [NSArray arrayWithArray:[common getpersonc]];
            dispatch_async(dispatch_get_main_queue(), ^{
                
            });
        }

    } fail:^{
        failure ? failure() : nil;
        listArr = [NSArray arrayWithArray:[common getpersonc]];
        dispatch_async(dispatch_get_main_queue(), ^{
        });

    }];
}

-(void)getUnreadCount:(void(^)(NSInteger))complete{
    dispatch_queue_t queue = dispatch_queue_create("GetIMMessage", DISPATCH_QUEUE_SERIAL);
    dispatch_async(queue, ^{
        [[YBImManager shareInstance]getAllUnredNumExceptUser:nil complete:^(int allUnread) {
            dispatch_async(dispatch_get_main_queue(), ^{
                complete ? complete(allUnread) : nil;
            });
        }];
    });
}
- (void)onTotalUnreadMessageCountChanged:(UInt64)totalUnreadCount {
    [self getUnreadCount:^(NSInteger count) {
        self.unreadMessageChangedCallback ? self.unreadMessageChangedCallback(count) : nil;
    }];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.navigationBar.hidden = YES;
    self.navigationController.interactivePopGestureRecognizer.delegate = nil;
    listArr = [NSArray arrayWithArray:[common getpersonc]];
    //获取所有未读消息
    [[V2TIMManager sharedInstance] addConversationListener:self];
    [self getUnreadCount:^(NSInteger count) {
        
    }];

}

-(void)clickFunction:(NSDictionary *)dic
{
    int selectedid = [dic[@"id"] intValue];//选项ID
    NSString *url = [NSString stringWithFormat:@"%@",[dic valueForKey:@"href"]];
    NSString *name = minstr([dic valueForKey:@"name"]);

    if (url.length >9) {
        
        [self pushH5Webviewinfo:dic];
    }
    else{
        /*
         1我的收益  2 我的钻石  4 在线商城 5 装备中心 13 个性设置  19 我的视频
         其他页面 都是H5
         */
        switch (selectedid) {
            //原生页面无法动态添加
            case 1:
                [self Myearnings:name];//我的收益
                break;
            case 2:
                [self MyDiamonds:name];//我的钻石
                break;
            case 13:
                [self SetUp:name];//设置
                break;
            case 19:
                [self mineVideo:name];//我的视频
                break;
            case 20:
                [self MineRoom];//我的视频
                break;
            case 22:
                [self doMineShop:name];//我的店铺
                break;
            case 23:
                [self doMineDynamic:name];//我的动态
                break;
            case 24:
                [self doPayContent];//付费内容
                break;
            case 25:
                [self doDailyTasks];//每日任务
                break;
            case 26:
                [self doCollectGoods];//我的收藏
                break;
            default:
                break;
        }
    }

}

#pragma  mark ----点击头部
-(void)headerImgTap {
    YBWeakSelf;
    if (!_headImgView) {
        _headImgView = [[HeaderBackImgView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andHeadUrl:[Config getavatar] andUerid:[Config getavatar]];
        _headImgView.tapEvent = ^(NSString *types) {
//            if ([types isEqual:@"hide"]) {
//            }else if ([types isEqual:@"拍照"]){
//                [weakSelf clickTake];
//            }else if ([types isEqual:@"相册"]){
//                [weakSelf clickSel];
//            }
            [weakSelf.headImgView removeFromSuperview];
            weakSelf.headImgView = nil;
        };
    }
    [[UIApplication sharedApplication].keyWindow addSubview:_headImgView];

}
-(void)authBtnClick{
    YBWebViewController *VC = [[YBWebViewController alloc]init];
    VC.urls = [self addurl:[NSString stringWithFormat:@"%@/appapi/Auth/index",h5url]];
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];

}
-(void)setHeaderDataDic:(NSDictionary *)dataDic
{
    [headImg sd_setImageWithURL:[NSURL URLWithString:minstr([dataDic valueForKey:@"avatar"])]];
    coinLb.text = minstr([dataDic valueForKey:@"coin"]);
    NSString *sexS = minstr([dataDic valueForKey:@"sex"]);
    if ([sexS isEqualToString:@"1"]) {
        [_sexView setImage:[UIImage imageNamed:@"sex_man"]];
    }
    else{
        [_sexView setImage:[UIImage imageNamed:@"sex_woman"]];
    }
    NSDictionary *levelDic = [common getUserLevelMessage:minstr([dataDic valueForKey:@"level"])];
    [self.levelView sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic valueForKey:@"thumb"])]];
    NSDictionary *levelDic1 = [common getAnchorLevelMessage:minstr([dataDic valueForKey:@"level_anchor"])];
    [self.level_anchorView sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic1 valueForKey:@"thumb"])]];

    nameLb.text = minstr([dataDic valueForKey:@"user_nickname"]);
    
    NSString *liangname = minstr([[dataDic valueForKey:@"liang"] valueForKey:@"name"]);
    if ([liangname isEqual:@"0"]) {
        idLb.text = [NSString stringWithFormat:@"ID:%@",minstr([dataDic valueForKey:@"id"])];
    }else{
        idLb.text = [NSString stringWithFormat:@"%@:%@",YZMsg(@"靓"),liangname];
    }
    // 0 否  1 是
    NSDictionary *vipDic = [dataDic valueForKey:@"vip"];
    if ([minstr([vipDic valueForKey:@"type"]) isEqual:@"0"]) {
        vipStateImg.image = [UIImage imageNamed:getImagename(@"vip-开通会员")];
        vipSubTitle.text = YZMsg(@"会员专享礼包来了");
        [vipBuyBtn setTitle:YZMsg(@"立即开通") forState:0];
        [vipStateImg mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(62);
            make.height.mas_equalTo(15);
        }];

    }else{
        vipStateImg.image = [UIImage imageNamed:getImagename(@"vip-已开通会员")];
        vipSubTitle.text =[NSString stringWithFormat:@"%@:%@",YZMsg(@"到期时间"),minstr([vipDic valueForKey:@"endtime"])];
        [vipBuyBtn setTitle:YZMsg(@"续费会员") forState:0];
        [vipStateImg mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(77);
            make.height.mas_equalTo(15);

        }];

    }

}
-(void)doEdit{
//    otherUserMsgVC *vc = [[otherUserMsgVC alloc] init];
//    vc.userID = [Config getOwnID];
//    [[MXBADelegate sharedAppDelegate] pushViewController:vc animated:YES];
    PersonHomeVC *person = [[PersonHomeVC alloc]init];
    person.userID =  [Config getOwnID];
    [[MXBADelegate sharedAppDelegate]pushViewController:person animated:YES];

}

//我的收益
-(void)Myearnings{
    YBWebViewController *VC = [[YBWebViewController alloc]init];
    VC.titles = @"我的明细";
    VC.urls = [self addurl:[NSString stringWithFormat:@"%@/appapi/Detail/index?",h5url]];
    VC.itemID = @"1000";
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
}
//我的钻石
-(void)MyDiamonds{
    RechargeViewController *VC = [[RechargeViewController alloc]init];
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
}
-(void)setBtnClick{
    setView *VC = [[setView alloc]init];
    VC.titleStr = YZMsg(@"设置");
    VC.live_window =minstr([self.infoArray valueForKey:@"live_window"]);
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];

}
-(void)centerMsgBtnClick{
    MessageListVC *msgVC = [[MessageListVC alloc]init];
    [[MXBADelegate sharedAppDelegate]pushViewController:msgVC animated:YES];
}


-(void)vipBtnClick{
    [self pushViewControllerWithTitle:@"道具"];

}
- (void)bottomButtonClick:(UIButton *)sender{
    [self pushViewControllerWithTitle:sender.titleLabel.text];
}
-(void)pushH5Webviewinfo:(NSDictionary *)subdic{
    NSString *url = minstr([subdic valueForKey:@"href"]);
    if (url.length >9) {
        YBWebViewController *VC = [[YBWebViewController alloc]init];
        VC.titles = minstr([subdic valueForKey:@"name"]);
        VC.urls = [self addurl:url];
        VC.itemID = minstr([subdic valueForKey:@"id"]);
        [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
     }
}
//所有h5需要拼接uid和token
-(NSString *)addurl:(NSString *)url{
    NSString *language = [PublicObj getCurrentLanguage];
    return [url stringByAppendingFormat:@"&uid=%@&token=%@&language=%@",[Config getOwnID],[Config getOwnToken],language];
}
//我的收益
-(void)Myearnings:(NSString *)name{
    myProfitVC *VC = [[myProfitVC alloc]init];
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
}
//我的钻石
-(void)MyDiamonds:(NSString *)name{
    RechargeViewController *VC = [[RechargeViewController alloc]init];
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
}
//设置
-(void)SetUp:(NSString *)name{
    setView *VC = [[setView alloc]init];
    VC.titleStr = name;
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
}
//我的视频
-(void)mineVideo:(NSString *)name{
//    mineVideoVC *VC = [[mineVideoVC alloc]init];
//    VC.titleStr = name;
//    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
    
    MyViewHomeVC* VC = [[MyViewHomeVC alloc]init];
//    VC.titleStr = name;
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
}
//房间管理
- (void)MineRoom{
    RoomManagementVC *VC = [[RoomManagementVC alloc]init];
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (indexPath.section == 1 || [[YBYoungManager shareInstance]isOpenYoung]) { 

        NSArray *dataList =[listArr[indexPath.section] valueForKey:@"list"];
        NSDictionary *dic = dataList[indexPath.row];
        int selectedid = [dic[@"id"] intValue];//选项ID
        NSString *url = [NSString stringWithFormat:@"%@",[dic valueForKey:@"href"]];
        NSString *name = minstr([dic valueForKey:@"name"]);

        if (url.length >9) {
            
            [self pushH5Webviewinfo:dic];
        }
        else{
            /*
             1我的收益  2 我的钻石  4 在线商城 5 装备中心 13 个性设置  19 我的视频
             其他页面 都是H5
             */
            switch (selectedid) {
                //原生页面无法动态添加
                case 1:
                    [self Myearnings:name];//我的收益
                    break;
                case 2:
                    [self MyDiamonds:name];//我的钻石
                    break;
                case 13:
                    [self SetUp:name];//设置
                    break;
                case 19:
                    [self mineVideo:name];//我的视频
                    break;
                case 20:
                    [self MineRoom];//我的视频
                    break;
                case 22:
                    [self doMineShop:name];//我的店铺
                    break;
                case 23:
                    [self doMineDynamic:name];//我的动态
                    break;
                case 24:
                    [self doPayContent];//付费内容
                    break;
                case 25:
                    [self doDailyTasks];//每日任务
                    break;
                case 26:
                    [self doWinningRecord];//中奖记录
                    break;

                case 27:
                    [self doYoungVC];//青少年模式

                    break;
                default:
                    break;
            }

        }
    }
}//MARK:-懒加载

-(void)doYoungVC{
    [[YBYoungManager shareInstance]checkYoungStatus:YoungFrom_Center];
}
-(void)doWinningRecord{
    WinningRecordVC *recordVc = [[WinningRecordVC alloc]init];
    [[MXBADelegate sharedAppDelegate]pushViewController:recordVc animated:YES];
}

-(void)pushLiveNodeList{
    LiveNodeViewController *list = [[LiveNodeViewController alloc]init];
    [[MXBADelegate sharedAppDelegate] pushViewController:list animated:YES];
}
-(void)pushFansList{
    fansViewController *fans = [[fansViewController alloc]init];
    fans.fensiUid = [Config getOwnID];
    [[MXBADelegate sharedAppDelegate] pushViewController:fans animated:YES];
}
-(void)pushAttentionList{
    attrViewController *attention = [[attrViewController alloc]init];
    attention.guanzhuUID = [Config getOwnID];
    [[MXBADelegate sharedAppDelegate] pushViewController:attention animated:YES];
}
-(void)pushEditView{
    PersonHomeVC *vc = [[PersonHomeVC alloc] init];
    vc.userID = [Config getOwnID];
    [[MXBADelegate sharedAppDelegate] pushViewController:vc animated:YES];

}
- (void)doMineShop:(NSString *)name{
    ShopHomeVC *shop = [[ShopHomeVC alloc]init];
    shop.shop_name = name;
    shop.shop_switch = minstr([self.infoArray valueForKey:@"shop_switch"]);
    [[MXBADelegate sharedAppDelegate] pushViewController:shop animated:YES];

}
-(void)doMineDynamic:(NSString *)name{
    MineDynamicViewController *dynamic = [[MineDynamicViewController alloc]init];
    dynamic.titleStr = name;
    [[MXBADelegate sharedAppDelegate] pushViewController:dynamic animated:YES];

}
-(void)doPayContent{
    PaidContentVC *payVc = [[PaidContentVC alloc]init];
    [[MXBADelegate sharedAppDelegate]pushViewController:payVc animated:YES];
}

-(void)doDailyTasks{
    DailyTasksVC *task = [[DailyTasksVC alloc]init];
    [[MXBADelegate sharedAppDelegate]pushViewController:task animated:YES];
}
-(void)doCollectGoods{
    if ([[YBYoungManager shareInstance]isOpenYoung]) {
        [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
        return;
    }

    MyCollectionVC *collect = [[MyCollectionVC alloc]init];
    [[MXBADelegate sharedAppDelegate]pushViewController:collect animated:YES];
}
-(void)pushViewControllerWithTitle:(NSString *)title{
    if ([title isEqual:@"消息"]) {
        MessageListVC *msgVC = [[MessageListVC alloc]init];
        [[MXBADelegate sharedAppDelegate]pushViewController:msgVC animated:YES];

    }else if ([title isEqual:@"钱包"]) {
        [self MyDiamonds:[NSString stringWithFormat:@"我的%@",[common name_coin]]];
    }else if ([title isEqual:@"明细"]) {
        YBWebViewController *VC = [[YBWebViewController alloc]init];
        VC.titles = @"我的明细";
        VC.urls = [self addurl:[NSString stringWithFormat:@"%@/appapi/Detail/index?",h5url]];
        VC.itemID = @"1000";
        [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];

    }else if ([title isEqual:@"道具"]) {
        YBWebViewController *VC = [[YBWebViewController alloc]init];
        VC.titles = @"在线商城";
        VC.urls = [self addurl:[NSString stringWithFormat:@"%@/appapi/Mall/index?",h5url]];
        VC.itemID = @"1000";
        [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];

    }
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
//    if (scrollView.contentOffset.y > 64+statusbarHeight) {
//        navi.alpha = 1;
//    }else{
//        navi.alpha = scrollView.contentOffset.y/(64.00000+statusbarHeight);
//    }
}

//退出登录函数
-(void)quitLogin
{
    [[YBSmallLiveWindow shareInstance]closeBtnClick];

    NSString *aliasStr = [NSString stringWithFormat:@"youke"];
    [Config clearProfile];
    [[YBImManager shareInstance]imLogout];
    UIApplication *app =[UIApplication sharedApplication];
    AppDelegate *app2 = (AppDelegate *)app.delegate;
    PhoneLoginVC *login = [[PhoneLoginVC alloc]init];
    UINavigationController *nav = [[UINavigationController alloc]initWithRootViewController:login];
    app2.window.rootViewController = nav;
}
@end
