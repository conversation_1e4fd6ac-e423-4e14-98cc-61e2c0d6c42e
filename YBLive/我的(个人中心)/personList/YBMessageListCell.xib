<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="13529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13527"/>
        <capability name="Constraints to layout margins" minToolsVersion="6.0"/>
        <capability name="Constraints with non-1.0 multipliers" minToolsVersion="5.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="微软雅黑.ttf">
            <string>MicrosoftYaHei</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="63" id="qPD-I5-FQa" customClass="messageCellcell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="63"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="qPD-I5-FQa" id="j3P-XP-Lby">
                <rect key="frame" x="0.0" y="0.0" width="320" height="62.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TgS-Qd-CEN">
                        <rect key="frame" x="5" y="23" width="0.0" height="0.0"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Swx-NW-IR0">
                        <rect key="frame" x="10" y="11.5" width="40" height="40"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="40" id="c12-ie-7M3"/>
                            <constraint firstAttribute="width" constant="40" id="lNR-JN-OYW"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="20"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.MasksToBounds" value="YES"/>
                        </userDefinedRuntimeAttributes>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="name" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fdB-H9-TBa">
                        <rect key="frame" x="60" y="11.5" width="40" height="19.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="bAy-Tc-43b">
                        <rect key="frame" x="105" y="11.5" width="15" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="LQ3-2V-1f2"/>
                            <constraint firstAttribute="width" constant="15" id="VTf-Pj-54N"/>
                        </constraints>
                    </imageView>
                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Ei2-p1-OEp">
                        <rect key="frame" x="125" y="11.5" width="30" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="BzT-Tx-4sK"/>
                            <constraint firstAttribute="width" constant="30" id="mc9-qB-Xay"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="message" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JH7-gZ-N05">
                        <rect key="frame" x="60" y="31" width="160" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="time" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TQU-5u-9mM">
                        <rect key="frame" x="287" y="31" width="23" height="13.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <color key="textColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="tailTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0ge-Yu-MzV">
                        <rect key="frame" x="282" y="21.5" width="20" height="20"/>
                        <color key="backgroundColor" red="1" green="0.12406171000000001" blue="0.13364339820000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="FyG-4j-qBW"/>
                            <constraint firstAttribute="height" constant="20" id="Qsk-wa-1Ve"/>
                        </constraints>
                        <fontDescription key="fontDescription" name="MicrosoftYaHei" family="Microsoft YaHei" pointSize="13"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.maskToBounds" value="YES"/>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </button>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="siliao.png" translatesAutoresizingMaskIntoConstraints="NO" id="Oae-Jp-QaY">
                        <rect key="frame" x="213" y="16.5" width="40" height="30"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="40" id="kNp-UR-La5"/>
                            <constraint firstAttribute="height" constant="30" id="wuy-Pf-RkB"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="bAy-Tc-43b" firstAttribute="top" secondItem="fdB-H9-TBa" secondAttribute="top" id="6jo-qf-RH9"/>
                    <constraint firstItem="Ei2-p1-OEp" firstAttribute="leading" secondItem="bAy-Tc-43b" secondAttribute="trailing" constant="5" id="7lE-UP-E8g"/>
                    <constraint firstItem="Ei2-p1-OEp" firstAttribute="leading" secondItem="bAy-Tc-43b" secondAttribute="trailing" constant="5" id="DCt-O0-hcG"/>
                    <constraint firstItem="Ei2-p1-OEp" firstAttribute="top" secondItem="bAy-Tc-43b" secondAttribute="top" id="DOH-AC-1gW"/>
                    <constraint firstItem="Ei2-p1-OEp" firstAttribute="top" secondItem="bAy-Tc-43b" secondAttribute="top" id="ErP-Ds-kcN"/>
                    <constraint firstAttribute="trailingMargin" secondItem="Oae-Jp-QaY" secondAttribute="trailing" constant="51" id="KDJ-Og-ASp"/>
                    <constraint firstItem="fdB-H9-TBa" firstAttribute="top" secondItem="Swx-NW-IR0" secondAttribute="top" id="TDI-6M-VSk"/>
                    <constraint firstItem="0ge-Yu-MzV" firstAttribute="top" secondItem="fdB-H9-TBa" secondAttribute="top" id="VIF-wQ-K9X"/>
                    <constraint firstItem="JH7-gZ-N05" firstAttribute="top" secondItem="fdB-H9-TBa" secondAttribute="bottom" id="XQs-MQ-MLE"/>
                    <constraint firstItem="fdB-H9-TBa" firstAttribute="leading" secondItem="Swx-NW-IR0" secondAttribute="trailing" constant="10" id="bBO-7i-Hby"/>
                    <constraint firstItem="JH7-gZ-N05" firstAttribute="leading" secondItem="fdB-H9-TBa" secondAttribute="leading" id="c1m-4x-liu"/>
                    <constraint firstItem="0ge-Yu-MzV" firstAttribute="centerY" secondItem="j3P-XP-Lby" secondAttribute="centerY" id="cPe-hR-mn7"/>
                    <constraint firstItem="Swx-NW-IR0" firstAttribute="centerY" secondItem="j3P-XP-Lby" secondAttribute="centerY" id="dwj-l3-Wmp"/>
                    <constraint firstItem="TQU-5u-9mM" firstAttribute="top" secondItem="fdB-H9-TBa" secondAttribute="bottom" id="eQG-qV-Eu0"/>
                    <constraint firstItem="TgS-Qd-CEN" firstAttribute="top" secondItem="j3P-XP-Lby" secondAttribute="topMargin" constant="12" id="jNC-Cb-opC"/>
                    <constraint firstAttribute="trailingMargin" secondItem="0ge-Yu-MzV" secondAttribute="trailing" constant="2" id="lkU-tK-83s"/>
                    <constraint firstItem="bAy-Tc-43b" firstAttribute="top" secondItem="fdB-H9-TBa" secondAttribute="top" id="mxS-Ag-FyA"/>
                    <constraint firstItem="Oae-Jp-QaY" firstAttribute="centerY" secondItem="j3P-XP-Lby" secondAttribute="centerY" id="u8c-HF-3ER"/>
                    <constraint firstItem="bAy-Tc-43b" firstAttribute="leading" secondItem="fdB-H9-TBa" secondAttribute="trailing" constant="5" id="uV7-hj-3ij"/>
                    <constraint firstItem="bAy-Tc-43b" firstAttribute="leading" secondItem="fdB-H9-TBa" secondAttribute="trailing" constant="5" id="us2-MF-7dK"/>
                    <constraint firstItem="Swx-NW-IR0" firstAttribute="leading" secondItem="TgS-Qd-CEN" secondAttribute="trailing" constant="5" id="w7B-9e-E2a"/>
                    <constraint firstItem="Ei2-p1-OEp" firstAttribute="width" secondItem="bAy-Tc-43b" secondAttribute="width" multiplier="2" id="w7b-lf-qpk"/>
                    <constraint firstItem="Ei2-p1-OEp" firstAttribute="height" secondItem="bAy-Tc-43b" secondAttribute="height" id="xLb-9e-bcK"/>
                </constraints>
                <variation key="default">
                    <mask key="constraints">
                        <exclude reference="VIF-wQ-K9X"/>
                        <exclude reference="7lE-UP-E8g"/>
                        <exclude reference="ErP-Ds-kcN"/>
                        <exclude reference="w7b-lf-qpk"/>
                        <exclude reference="xLb-9e-bcK"/>
                        <exclude reference="6jo-qf-RH9"/>
                        <exclude reference="uV7-hj-3ij"/>
                    </mask>
                </variation>
            </tableViewCellContentView>
            <constraints>
                <constraint firstItem="JH7-gZ-N05" firstAttribute="width" secondItem="qPD-I5-FQa" secondAttribute="width" multiplier="0.5" id="0VK-jL-1dk"/>
                <constraint firstAttribute="trailing" secondItem="TQU-5u-9mM" secondAttribute="trailing" constant="10" id="Fw6-38-Eme"/>
                <constraint firstItem="Swx-NW-IR0" firstAttribute="leading" secondItem="qPD-I5-FQa" secondAttribute="leading" constant="10" id="Nsi-HO-usx"/>
            </constraints>
            <connections>
                <outlet property="anReadL" destination="0ge-Yu-MzV" id="fef-b4-xn8"/>
                <outlet property="iconB" destination="Swx-NW-IR0" id="TZI-fO-pNL"/>
                <outlet property="levelI" destination="Ei2-p1-OEp" id="6kl-is-mf3"/>
                <outlet property="messageL" destination="JH7-gZ-N05" id="wka-gb-z8v"/>
                <outlet property="nameL" destination="fdB-H9-TBa" id="aRS-Oi-POp"/>
                <outlet property="sexI" destination="bAy-Tc-43b" id="KxA-as-Vqs"/>
                <outlet property="timeL" destination="TQU-5u-9mM" id="H5W-3F-Pv8"/>
            </connections>
            <point key="canvasLocation" x="-52" y="129.5"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="siliao.png" width="75" height="58"/>
    </resources>
</document>
