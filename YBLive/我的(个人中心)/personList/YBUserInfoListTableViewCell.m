//
//  YBUserInfoListTableViewCell.m
//  TCLVBIMDemo
//
//  Created by admin on 16/11/11.
//  Copyright © 2016年 tencent. All rights reserved.
//
#import "YBUserInfoListTableViewCell.h"
@implementation YBUserInfoListTableViewCell
-(void)drawRect:(CGRect)rect{
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    CGContextSetLineWidth(ctx,1);
    CGContextSetStrokeColorWithColor(ctx,[UIColor groupTableViewBackgroundColor].CGColor);
    CGContextMoveToPoint(ctx,0,self.frame.size.height);
    CGContextAddLineToPoint(ctx,(self.frame.size.width),self.frame.size.height);
    CGContextStrokePath(ctx);
}
+ (instancetype)cellWithTabelView:(UITableView *)tableView{
    static NSString *cellIdentifier = @"YBUserInfoListTableViewCell";
    YBUserInfoListTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIdentifier];
    if (cell == nil)
    {
        YBUserInfoListTableViewCell *cell = [[YBUserInfoListTableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"YBUserInfoListTableViewCell"];
        cell.textLabel.textColor = UIColorFromRGB(0x4C4C4C);
    }
    return cell;
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier withIndex:(NSIndexPath *)indexpath
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    self.selectionStyle = UIAccessibilityTraitNone;
    self.contentView.backgroundColor = UIColor.clearColor;
    [self setUIWithIndex:indexpath];
    return self;
}
-(void)setUIWithIndex:(NSIndexPath *)indexpath
{
    
    _serviceLabel =[[UILabel alloc]init];
    _serviceLabel.textAlignment = NSTextAlignmentCenter;
    _serviceLabel.textColor = [UIColor blackColor];
    _serviceLabel.font = [UIFont boldSystemFontOfSize:15];
    
    _iconImage = [[UIImageView alloc]init];
    _iconImage.contentMode = UIViewContentModeScaleAspectFit;
    _nameL = [[UILabel alloc]init];
    _nameL.textAlignment = NSTextAlignmentCenter;
    _nameL.textColor = [UIColor blackColor];
    _nameL.font = [UIFont systemFontOfSize:14];
    UIImageView *rightImgV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"profit_right"]];
    [self.contentView addSubview:_serviceLabel];
    [self.contentView addSubview:_nameL];
    [self.contentView addSubview:_iconImage];
    [self.contentView addSubview:rightImgV];

    if (indexpath.row == 0) {
        _serviceLabel.hidden = NO;
        [_serviceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_top);
            make.left.equalTo(self.contentView.mas_left).offset(15);
            make.height.mas_equalTo(20);
        }];
    }else{
        _serviceLabel.hidden = YES;
        [_serviceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView);
            make.left.equalTo(self.contentView.mas_left).offset(15);
            make.height.mas_equalTo(1);
        }];
    }
    [_iconImage mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(20);
        make.top.equalTo(_serviceLabel.mas_bottom).offset(15);
        make.width.height.mas_equalTo(25);
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-15);
    }];

    [_nameL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_iconImage.mas_right).offset(10);
        make.centerY.equalTo(_iconImage.mas_centerY);
    }];

    [rightImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right).offset(-9);
        make.centerY.equalTo(_iconImage.mas_centerY);
        make.width.height.mas_equalTo(20);
    }];
}
@end
