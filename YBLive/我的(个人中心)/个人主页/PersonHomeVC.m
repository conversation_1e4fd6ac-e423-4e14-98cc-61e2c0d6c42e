//
//  PersonHomeVC.m
//  YBLive
//
//  Created by y<PERSON><PERSON><PERSON> on 2023/10/23.
//  Copyright © 2023 cat. All rights reserved.
//

#import "PersonHomeVC.h"
#import "jubaoVC.h"
#import "fenXiangView.h"
#import "HeaderBackImgView.h"
#import "TZImagePickerController.h"
#import "TYPagerView.h"
#import "TYTabPagerBar.h"
#import "PersonInfoView.h"
#import "userCenterDTview.h"
#import "PersonVideoView.h"
#import "PersonLiveView.h"
#import "CenterPayView.h"
#import "fansViewController.h"
#import "attrViewController.h"
#import "LivePlay.h"
#import "UserRoomViewController.h"
#import "myInfoEdit.h"
#import "shopDetailVC.h"
@interface PersonHomeVC ()<UINavigationControllerDelegate,UIImagePickerControllerDelegate,TZImagePickerControllerDelegate,UIGestureRecognizerDelegate,TYPagerViewDataSource, TYPagerViewDelegate,TYTabPagerBarDataSource,TYTabPagerBarDelegate,UIScrollViewDelegate>
{
    UILabel *titleLabel;
    BOOL _isCreatedUi;

    UIButton *returnBtn;
    UIButton *shareBtn;
    UIButton *reportBtn;
    
    fenXiangView *shareView;
    
    UIScrollView *backScroll;
    UIView *bottomView;
    NSDictionary *infoDic;
    UIImageView *iconImgView;
    UIImage *headBgimg;
    NSString *headBgStr;
    UIButton *editBtn;
    UIImageView *userHeadImg;
    UILabel *nameLabel;
    UIImageView *sexImgView;
    UIButton *livingBtn;
    
    UIView *messageView;
    CGFloat _beginScrollY;

    UIButton *jmsgBtn;
    UIButton *blackBtn;

    moviePlay *temp;
    UserRoomViewController *chatTemp;
    NSDictionary *selectedDic;

    NSString *type_val;//
    NSString *livetype;//
    NSString *_sdkType;//0-金山  1-腾讯
    UIAlertController *md5AlertController;

    CGFloat shopHeigt;
}
@property(nonatomic,strong)HeaderBackImgView *headImgView;
@property (nonatomic,strong) UIView *naviView;
@property (nonatomic, strong) NSArray *datas;
@property (nonatomic, weak) TYTabPagerBar *tabBar;
@property (nonatomic, weak) TYPagerView *pageView;
@property (nonatomic, strong)PersonInfoView *pInfoView;
@property (nonatomic, strong)PersonVideoView *pVideoView;
@property (nonatomic, strong)PersonLiveView *pLiveView;
@property (nonatomic,strong) userCenterDTview *dtView;
@property (nonatomic,strong) CenterPayView *payView;

@end

@implementation PersonHomeVC
-(void)doReturn{
    [[MXBADelegate sharedAppDelegate]popViewController:YES];
}
#pragma mark -请求数据
- (void)requestData{
    [YBToolClass postNetworkWithUrl:@"User.getUserHome" andParameter:@{@"touid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *subDic = [info firstObject];
            infoDic = subDic;
            
            [self createUI];
        }
    } fail:^{
        
    }];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.whiteColor;
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(refeshMyinfo) name:@"refeshMyinfo" object:nil];
    shopHeigt = 0;
    [self requestData];
}
#pragma mark ================ 导航栏 ===============
//创建l资料卡
//创建导航栏
-(UIView *)naviView{
    if (!_naviView) {
        _naviView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64+statusbarHeight)];
        _naviView.backgroundColor = [UIColor clearColor];

        titleLabel = [[UILabel alloc]initWithFrame:CGRectMake(50, 34+statusbarHeight, _window_width-100, 20)];
        titleLabel.textAlignment = NSTextAlignmentCenter;
        titleLabel.font = navtionTitleFont;
        titleLabel.textColor = [UIColor blackColor];
        [_naviView addSubview:titleLabel];
        
        returnBtn = [UIButton buttonWithType:0];
        returnBtn.frame = CGRectMake(0, 24+statusbarHeight, 40, 40);
        [returnBtn setImage:[UIImage imageNamed:@"person_back"] forState:0];
        [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
        returnBtn.imageEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
        [_naviView addSubview:returnBtn];

        reportBtn = [UIButton buttonWithType:0];
        reportBtn.frame = CGRectMake(shareBtn.left-40, 24+statusbarHeight, 40, 40);
        [reportBtn setImage:[UIImage imageNamed:@"person_report"] forState:0];
        reportBtn.imageEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
        [reportBtn addTarget:self action:@selector(reportBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [_naviView addSubview:reportBtn];
        
        if ([_userID isEqual:[Config getOwnID]]) {
            reportBtn.hidden = YES;
        }else{
            reportBtn.hidden = NO;
        }
    }
    return _naviView;
}
#pragma mark -举报
-(void)reportBtnClick{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    jubaoVC *vc = [[jubaoVC alloc]init];
    vc.dongtaiId = _userID;
    vc.isLive = YES;
    [self presentViewController:vc animated:YES completion:nil];

}
#pragma mark -分享
- (void)doShare{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (infoDic) {
        if (!shareView) {
            shareView = [[fenXiangView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
            [shareView GetDIc:infoDic];
            [[UIApplication sharedApplication].keyWindow addSubview:shareView];
        }else{
            [shareView show];
        }
    }
}

-(void)createUI{
    if (_isCreatedUi == YES) {
        return;
    }
    _isCreatedUi = YES;

    backScroll = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    backScroll.backgroundColor = [UIColor whiteColor];
    backScroll.bounces = NO;
    backScroll.delegate = self;
    [self.view addSubview:backScroll];
    if (@available(iOS 11.0, *)) {
        backScroll.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        // Fallback on earlier versions
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    [self.view addSubview:self.naviView];

    //添加头部图片
    iconImgView = [[UIImageView alloc]init];
    [iconImgView sd_setImageWithURL:[NSURL URLWithString:minstr([infoDic valueForKey:@"bg_img"])] placeholderImage:[UIImage imageNamed:@"bg1"]];
    iconImgView.contentMode = UIViewContentModeScaleAspectFill;
    iconImgView.clipsToBounds = YES;
    iconImgView.userInteractionEnabled = YES;
    [backScroll addSubview:iconImgView];
    [iconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.width.equalTo(backScroll);
        make.height.mas_equalTo(_window_width *0.85);
    }];
    UITapGestureRecognizer *headTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(headerImgTap)];
    headTap.delegate = self;
    [iconImgView addGestureRecognizer:headTap];

    [backScroll layoutIfNeeded];
    CGFloat shopHeight = [minstr([infoDic valueForKey:@"isshop"]) isEqual:@"1"] ? 60.0 : 0.0;

    bottomView = [[UIView alloc]initWithFrame:CGRectMake(0, iconImgView.bottom-20, _window_width, _window_height+shopHeight-64-statusbarHeight)];
    bottomView.backgroundColor = [UIColor whiteColor];
    [backScroll addSubview:bottomView];
    bottomView.layer.mask = [[YBToolClass sharedInstance] setViewLeftTop:15 andRightTop:15 andView:bottomView];

    userHeadImg = [[UIImageView alloc]init];
    [userHeadImg sd_setImageWithURL:[NSURL URLWithString:minstr([infoDic valueForKey:@"avatar"])] placeholderImage:[UIImage imageNamed:@"bg1"]];
    userHeadImg.contentMode = UIViewContentModeScaleToFill;
    userHeadImg.clipsToBounds = YES;
    userHeadImg.layer.cornerRadius = 64;
    userHeadImg.layer.masksToBounds = YES;
    userHeadImg.userInteractionEnabled = YES;
    [backScroll addSubview:userHeadImg];
    [userHeadImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(15);
        make.width.height.mas_equalTo(128);
        make.centerY.equalTo(bottomView.mas_top).mas_offset(20);
    }];
    UITapGestureRecognizer *iconTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(iconImgTap)];
    iconTap.delegate = self;
    [userHeadImg addGestureRecognizer:iconTap];

    [self setBottomUI];
    
    if (![_userID isEqual:[Config getOwnID]]) {
        [self lastBottom];
    }
}
-(void)lastBottom{
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-60-ShowDiff, _window_width, 60+ShowDiff)];
    view.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:view];
    
    NSArray *bottomTitleArr;
    NSArray *btnTypeArr;
    if ([[common letter_switch] isEqual:@"1"]) {
        bottomTitleArr = @[@"center-私信",@"center-拉黑"];
        btnTypeArr = @[@(BottomType_Chat), @(BottomType_Black)];
    }else{
        
        bottomTitleArr = @[@"center-拉黑"];
        btnTypeArr = @[@(BottomType_Black)];
    }
        for (int i = 0; i< bottomTitleArr.count; i++) {
            UIButton *btn= [UIButton buttonWithType:0];
            btn.frame = CGRectMake(view.width-12-(125*(i+1)), 0, 125, 57);
            [btn setImage:[UIImage imageNamed:getImagename(bottomTitleArr[i])] forState:0];
            [btn addTarget:self action:@selector(bottomBtnClick:) forControlEvents:UIControlEventTouchUpInside];
            [view addSubview:btn];
            _bottomBtnType =[btnTypeArr[i] intValue];
            if (_bottomBtnType == BottomType_Chat) {
                jmsgBtn = btn;
            }
            if (_bottomBtnType == BottomType_Black) {
                blackBtn = btn;
                [btn setImage:[UIImage imageNamed:getImagename(@"center-已拉黑")] forState:UIControlStateSelected];
                int isBlack = [minstr([infoDic valueForKey:@"isblack"]) intValue];
                blackBtn.selected = isBlack;
            }

        }
}
-(void)setBottomUI{
    
/**
 粉丝 * 关注 * 赞 *
 关注/编辑按钮
 */
    UIButton *fansBtn = [UIButton buttonWithType:0];
    NSString *fansStr =[NSString stringWithFormat:@"%@ \n %@",minstr([infoDic valueForKey:@"fans"]),YZMsg(@"粉丝")];
    fansBtn.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
    fansBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
    fansBtn.titleLabel.font = [UIFont systemFontOfSize:12];
    [fansBtn setTitleColor:UIColor.grayColor forState:0];
    NSMutableAttributedString *contentStr = [[NSMutableAttributedString alloc] initWithString:fansStr];
    NSRange redRange =[[contentStr string]rangeOfString:minstr([infoDic valueForKey:@"fans"])];
    [contentStr addAttributes:@{NSForegroundColorAttributeName:[UIColor blackColor],NSFontAttributeName:[UIFont boldSystemFontOfSize:14]} range:redRange];
    [fansBtn setAttributedTitle:contentStr forState:0];
    fansBtn.tag = 181221;
    [fansBtn addTarget:self action:@selector(fansOrFollowBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:fansBtn];
    [fansBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(userHeadImg.mas_trailing).mas_offset(40.0);
        make.top.equalTo(bottomView.mas_top);
        make.height.mas_equalTo(50);
    }];

    UIView *lineView = [[UIView alloc]init];
    lineView.backgroundColor = RGB(238, 238, 238);
    [bottomView addSubview:lineView];
    [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(fansBtn.mas_centerY);
        make.height.equalTo(fansBtn).multipliedBy(0.6);
        make.width.mas_equalTo(1);
        make.left.equalTo(fansBtn.mas_right).offset(15);
    }];

    UIButton *followBtn = [UIButton buttonWithType:0];
    NSString *followStr =[NSString stringWithFormat:@"%@ \n %@",minstr([infoDic valueForKey:@"follows"]),YZMsg(@"关注")];
    followBtn.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
    followBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
    followBtn.titleLabel.font = [UIFont systemFontOfSize:12];
    [followBtn setTitleColor:UIColor.grayColor forState:0];
    NSMutableAttributedString *followcontent = [[NSMutableAttributedString alloc] initWithString:followStr];
    NSRange flowRange =[[followcontent string]rangeOfString:minstr([infoDic valueForKey:@"follows"])];
    [followcontent addAttributes:@{NSForegroundColorAttributeName:[UIColor blackColor],NSFontAttributeName:[UIFont boldSystemFontOfSize:14]} range:flowRange];
    [followBtn setAttributedTitle:followcontent forState:0];
    followBtn.tag = 181222;
    [followBtn addTarget:self action:@selector(fansOrFollowBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:followBtn];
    [followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.height.width.equalTo(fansBtn);
        make.left.equalTo(lineView.mas_right).offset(15);
        make.trailing.equalTo(bottomView.mas_trailing).mas_offset(-15.0);
    }];
    UIView *lineView2 = [[UIView alloc]init];
    lineView2.backgroundColor = RGB(238, 238, 238);
    [bottomView addSubview:lineView2];
    [lineView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(followBtn.mas_centerY);
        make.height.equalTo(followBtn).multipliedBy(0.6);
        make.width.mas_equalTo(1);
        make.left.equalTo(followBtn.mas_right).offset(15);
    }];
    editBtn = [UIButton buttonWithType:0];
    [editBtn addTarget:self action:@selector(doEdit:) forControlEvents:UIControlEventTouchUpInside];
    [editBtn setBackgroundColor:RGB_COLOR(@"#010101", 1)];
    [editBtn setTitleColor:UIColor.whiteColor forState:0];
    editBtn.layer.cornerRadius = 12;
    editBtn.layer.masksToBounds = YES;
        editBtn.titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
    [bottomView addSubview:editBtn];
    [editBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(fansBtn.mas_bottom).offset(5);
        make.left.equalTo(fansBtn.mas_left);
        make.trailing.equalTo(bottomView.mas_trailing).mas_offset(-15.0);
        make.height.mas_equalTo(32.0);
    }];

    if ([_userID isEqual:[Config getOwnID]]) {
        [editBtn setTitle:YZMsg(@"编辑资料") forState:0];

    }else{
        if ([[infoDic valueForKey:@"isattention"] isEqual:@"0"]) {
            [editBtn setTitle:YZMsg(@"关注") forState:UIControlStateNormal];
            [editBtn setBackgroundColor:normalColors];
            [editBtn setTitleColor:UIColor.whiteColor forState:0];

        }
        else{
            [editBtn setTitle:YZMsg(@"已关注") forState:UIControlStateNormal];
            [editBtn setBackgroundColor:RGB(225, 225, 225)];
            [editBtn setTitleColor:RGB(125, 125, 125) forState:0];

        }

    }

    /**
     昵称 性别 等级 ID
     */

    nameLabel = [[UILabel alloc]init];
    nameLabel.textColor = [UIColor blackColor];
    nameLabel.font = [UIFont systemFontOfSize:17.0 weight:UIFontWeightMedium];
    nameLabel.text = minstr([infoDic valueForKey:@"user_nickname"]);
    [bottomView addSubview:nameLabel];
    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(editBtn.mas_bottom).offset(15);
        make.left.equalTo(userHeadImg.mas_left);//.offset(15);
        make.height.mas_equalTo(25);
    }];
    sexImgView = [[UIImageView alloc]init];
    if ([minstr([infoDic valueForKey:@"sex"]) isEqual:@"1"]) {
        sexImgView.image = [UIImage imageNamed:@"gender_color_male"];
    }else{
        sexImgView.image = [UIImage imageNamed:@"gender_color_famale"];
    }
    [bottomView addSubview:sexImgView];
    [sexImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLabel.mas_right).offset(5);
        make.centerY.equalTo(nameLabel.mas_centerY);
        make.width.mas_equalTo(32.0);
        make.height.mas_equalTo(24.0);
    }];
    UIImageView *hostImgView = [[UIImageView alloc]init];
    NSDictionary *levelDic1 = [common getAnchorLevelMessage:minstr([infoDic valueForKey:@"level_anchor"])];
    [hostImgView sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic1 valueForKey:@"thumb"])]];

    [bottomView addSubview:hostImgView];
    [hostImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.height.equalTo(sexImgView);
        make.left.equalTo(sexImgView.mas_right).offset(4);
        make.width.equalTo(hostImgView.mas_height).multipliedBy(2);
    }];

    UIImageView *levelImgView = [[UIImageView alloc]init];
    NSDictionary *levelDic = [common getUserLevelMessage:minstr([infoDic valueForKey:@"level"])];
    [levelImgView sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic valueForKey:@"thumb"])]];

    [bottomView addSubview:levelImgView];
    [levelImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.height.equalTo(sexImgView);
        make.left.equalTo(hostImgView.mas_right).offset(4);
        make.width.equalTo(hostImgView.mas_height).multipliedBy(2);
    }];
//
    UILabel *rateLabel = [[UILabel alloc]init];
    rateLabel.textColor = RGB_COLOR(@"#FF5979", 1);
    rateLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    NSString *laingname = minstr([[infoDic valueForKey:@"liang"] valueForKey:@"name"]);
    if ([laingname isEqual:@"0"]) {
        rateLabel.text = [NSString stringWithFormat:@"ID:%@",minstr([infoDic valueForKey:@"id"])];
    }
    else{
        rateLabel.text = [NSString stringWithFormat:@"%@:%@",YZMsg(@"靓"),laingname];
    }
    [bottomView addSubview:rateLabel];
    [rateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLabel);
        make.top.equalTo(nameLabel.mas_bottom).mas_offset(6.0);
        make.height.mas_equalTo(21.0);
    }];
    
    UIView *rateTrailingLine = [[UIView alloc]init];
    lineView.backgroundColor = RGB_COLOR(@"#CBCBCB", 1);
    [bottomView addSubview:rateTrailingLine];
    [rateTrailingLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(rateLabel.mas_trailing).mas_offset(4.0);
        make.centerY.equalTo(rateLabel);
        make.height.mas_equalTo(21.0);
        make.width.mas_equalTo(1);
    }];
    
    UIImageView *goldCoinImageView = [[UIImageView alloc] init];
    goldCoinImageView.image = [UIImage imageNamed:@"coin_goldcoin"];
    [bottomView addSubview:goldCoinImageView];
    [goldCoinImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(rateTrailingLine.mas_trailing).mas_offset(4.0);
        make.centerY.equalTo(rateLabel);
        make.width.height.mas_equalTo(16.0);
    }];
    
    UILabel *goldCoinValueLabel = [[UILabel alloc]init];
    goldCoinValueLabel.text = @"20";
    goldCoinValueLabel.textColor = RGB_COLOR(@"#7F7F7F", 1);
    goldCoinValueLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [bottomView addSubview:goldCoinValueLabel];
    [goldCoinValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(goldCoinImageView.mas_trailing).mas_offset(2.0);
        make.centerY.equalTo(rateLabel);
    }];
    
    UIImageView *goldBeanImageView = [[UIImageView alloc] init];
    goldBeanImageView.image = [UIImage imageNamed:@"coin_goldbean"];
    [bottomView addSubview:goldBeanImageView];
    [goldBeanImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(goldCoinValueLabel.mas_trailing).mas_offset(8.0);
        make.centerY.equalTo(rateLabel);
        make.width.height.mas_equalTo(16.0);
    }];
    
    UILabel *goldBeanValueLabel = [[UILabel alloc]init];
    goldBeanValueLabel.text = @"200";
    goldBeanValueLabel.textColor = RGB_COLOR(@"#7F7F7F", 1);
    goldBeanValueLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [bottomView addSubview:goldBeanValueLabel];
    [goldBeanValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(goldBeanImageView.mas_trailing).mas_offset(2.0);
        make.centerY.equalTo(rateLabel);
    }];
    
    livingBtn = [UIButton buttonWithType:0];
    [livingBtn addTarget:self action:@selector(domoviePlay) forControlEvents:UIControlEventTouchUpInside];
        NSURL *imgUrl = [[NSBundle mainBundle] URLForResource:getImagename(@"person_living") withExtension:@"gif"];
    [livingBtn sd_setImageWithURL:imgUrl forState:0];
    [bottomView addSubview:livingBtn];
    if ([minstr([infoDic valueForKey:@"islive"]) isEqual:@"1"]) {
        livingBtn.hidden = NO;
    }else{
        livingBtn.hidden = YES;
    }
    [livingBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(rateLabel.mas_bottom);
        make.right.equalTo(bottomView).offset(-15);
        make.width.mas_equalTo(90);
        make.height.mas_equalTo(26);
    }];

    /** 商店**/
    MASViewAttribute *shopTop = rateLabel.mas_bottom;
    if (![[YBYoungManager shareInstance]isOpenYoung]) {
        if([minstr([infoDic valueForKey:@"isshop"]) isEqual:@"1"]){
            shopHeigt = 60;

            UIButton *storeButton = [UIButton buttonWithType:0];
            [storeButton addTarget:self action:@selector(doUserShoreHome) forControlEvents:UIControlEventTouchUpInside];
            [bottomView addSubview:storeButton];
            [storeButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.right.equalTo(bottomView);
                make.top.equalTo(shopTop);
                make.height.mas_equalTo(60);
            }];
            UIImageView *storeImgV = [[UIImageView alloc]init];
            storeImgV.image = [UIImage imageNamed:@"shopicon_小店"];
            [storeButton addSubview:storeImgV];
            [storeImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(storeButton).offset(15);
                make.centerY.equalTo(storeButton);
                make.width.height.mas_equalTo(40);
            }];
            
            UILabel *storeNameL = [[UILabel alloc]init];
            storeNameL.font = [UIFont boldSystemFontOfSize:13];
            storeNameL.text = minstr([[infoDic valueForKey:@"shop"] valueForKey:@"name"]);
            [storeButton addSubview:storeNameL];
            [storeNameL mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(storeImgV.mas_right).offset(10);
                make.centerY.equalTo(storeImgV).multipliedBy(0.75);
            }];
            
            UILabel *titlelb = [[UILabel alloc]init];
            titlelb.backgroundColor = normalColors;
            titlelb.font = [UIFont systemFontOfSize:10];
            titlelb.textColor = [UIColor whiteColor];
            titlelb.textAlignment = NSTextAlignmentCenter;
            titlelb.layer.cornerRadius = 2;
            titlelb.layer.masksToBounds = YES;
            titlelb.text = YZMsg(@"进入小店");
            titlelb.adjustsFontSizeToFitWidth = YES;
            [storeButton addSubview:titlelb];
            [titlelb mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(storeNameL.mas_bottom).offset(5);
                make.left.equalTo(storeImgV.mas_right).offset(10);
                make.height.mas_equalTo(14);
                make.width.mas_equalTo(50);
            }];
            
            UILabel *storeNumL = [[UILabel alloc]init];
            storeNumL.font = [UIFont systemFontOfSize:10];
            storeNumL.textColor = RGB_COLOR(@"#969696", 1);
            storeNumL.text = [NSString stringWithFormat:@"%@%@%@",YZMsg(@"共"),minstr([[infoDic valueForKey:@"shop"] valueForKey:@"nums"]),YZMsg(@"件商品")];
            [storeButton addSubview:storeNumL];
            [storeNumL mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(titlelb.mas_right).offset(5);
                //            make.centerY.equalTo(storeImgV).multipliedBy(1.25);
                make.centerY.equalTo(titlelb.mas_centerY);
            }];
            
            UIImageView *storeRightImgV = [[UIImageView alloc]init];
            storeRightImgV.image = [UIImage imageNamed:@"profit_right"];
            [storeButton addSubview:storeRightImgV];
            [storeRightImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(storeButton).offset(-12);
                make.centerY.equalTo(storeButton);
                make.width.height.mas_equalTo(15);
            }];
            [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(0, 59, _window_width, 1) andColor:RGB_COLOR(@"#f0f0f0", 1) andView:storeButton];
            
            shopTop =storeButton.mas_bottom;
        }
    }
    if ([[YBYoungManager shareInstance]isOpenYoung]) {
        _datas = @[YZMsg(@"资料"),[NSString stringWithFormat:@"%@ %@",YZMsg(@"视频"),minstr([infoDic valueForKey:@"videonums"])],[NSString stringWithFormat:@"%@ %@",YZMsg(@"动态"),minstr([infoDic valueForKey:@"dynamicnums"])],[NSString stringWithFormat:@"%@ %@",YZMsg(@"直播"),minstr([infoDic valueForKey:@"livenums"])]];

    }else{
        _datas = @[
            YZMsg(@"资料"),
            [NSString stringWithFormat:@"%@ %@",YZMsg(@"视频"),minstr([infoDic valueForKey:@"videonums"])],
            [NSString stringWithFormat:@"%@ %@",YZMsg(@"动态"),minstr([infoDic valueForKey:@"dynamicnums"])],
            [NSString stringWithFormat:@"%@ %@",YZMsg(@"收藏"),minstr([infoDic valueForKey:@"TODO wuzihang"])]];
    }

    TYTabPagerBar *tabBar = [[TYTabPagerBar alloc]init];
    tabBar.layout.barStyle = TYPagerBarStyleProgressView;//TYPagerBarStyleProgressView;
    tabBar.dataSource = self;
    tabBar.delegate = self;
    tabBar.layout.cellWidth = (_window_width - 30)/_datas.count;
    tabBar.layout.progressHeight = 2;
    tabBar.layout.progressColor = UIColor.redColor;
    tabBar.layout.progressWidth = 15;
    tabBar.layout.normalTextColor = UIColor.grayColor;
    tabBar.layout.selectedTextColor = UIColor.blackColor;
    tabBar.layout.normalTextFont = [UIFont systemFontOfSize:15.0 weight:UIFontWeightRegular];
    tabBar.layout.selectedTextFont = [UIFont systemFontOfSize:15.0 weight:UIFontWeightMedium];
    [tabBar registerClass:[TYTabPagerBarCell class] forCellWithReuseIdentifier:[TYTabPagerBarCell cellIdentifier]];
    [bottomView addSubview:tabBar];
    _tabBar = tabBar;
    [tabBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(backScroll);
        make.top.equalTo(shopTop);
        make.width.mas_equalTo(_window_width);
        make.height.mas_equalTo(40);
    }];
    TYPagerView *pageView = [[TYPagerView alloc]init];
    //pageView.layout.progressAnimateEnabel = NO;
    //pageView.layout.prefetchItemCount = 1;
    pageView.layout.autoMemoryCache = NO;
    pageView.dataSource = self;
    pageView.delegate = self;
    // you can rigsiter cell like tableView
    [pageView.layout registerClass:[UIView class] forItemWithReuseIdentifier:@"cellId"];
    [bottomView addSubview:pageView];
    _pageView = pageView;
    [pageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(backScroll);
        make.top.equalTo(_tabBar.mas_bottom);
        make.width.mas_equalTo(_window_width);
//        make.height.mas_equalTo(40);
        make.bottom.equalTo(bottomView);
    }];

    [backScroll layoutIfNeeded];
    backScroll.contentSize = CGSizeMake(_window_width, _window_height+_window_width-28-64-statusbarHeight+200);
    [_tabBar reloadData];
    [_pageView updateData];
    
}
#pragma mark ----信息界面
-(PersonInfoView *)pInfoView{
    YBWeakSelf;
    if(!_pInfoView){
        _pInfoView = [[PersonInfoView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height-64-statusbarHeight-40) andInfoDic:infoDic andUserId:_userID];
        _pInfoView.imPressEvent = ^{
            [weakSelf requestNewImpress];
        };
    }
    return _pInfoView;
}
-(void)requestNewImpress{
    [YBToolClass postNetworkWithUrl:@"User.getUserHome" andParameter:@{@"touid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *subDic = [info firstObject];
            infoDic = subDic;
            [_pInfoView reloadImPress:infoDic];
        }
    } fail:^{
        
    }];

}
-(void)refeshMyinfo
{
    [YBToolClass postNetworkWithUrl:@"User.getUserHome" andParameter:@{@"touid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *subDic = [info firstObject];
            infoDic = subDic;
            [_pInfoView refreshUserInfo:infoDic];
            [userHeadImg sd_setImageWithURL:[NSURL URLWithString:minstr([infoDic valueForKey:@"avatar"])] placeholderImage:[UIImage imageNamed:@"bg1"]];
            nameLabel.text = minstr([infoDic valueForKey:@"user_nickname"]);
            if ([minstr([infoDic valueForKey:@"sex"]) isEqual:@"1"]) {
                sexImgView.image = [UIImage imageNamed:@"sex_man"];
            }else{
                sexImgView.image = [UIImage imageNamed:@"sex_woman"];
            }

        }
    } fail:^{
        
    }];

}
#pragma mark ----视频界面
-(PersonVideoView *)pVideoView{
    if(!_pVideoView){
        _pVideoView = [[PersonVideoView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height-64-statusbarHeight-40) andUserId:_userID];
    }
    return _pVideoView;
}
#pragma mark ----动态界面
- (userCenterDTview *)dtView{
    if (!_dtView) {
        CGFloat myselfHeight = [_userID isEqual: [Config getOwnID]] ? 0: 60;
        _dtView = [[userCenterDTview alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height-64-statusbarHeight-myselfHeight-_window_height *0.3+ShowDiff) andTouserID:_userID andRequestUrl:@"Dynamic.getHomeDynamic" andTopicId:@""];
    }
    return _dtView;
}
#pragma mark ------直播
-(PersonLiveView *)pLiveView{
    if(!_pLiveView){
        CGFloat myselfHeight = [_userID isEqual: [Config getOwnID]] ? 0: 60;

        _pLiveView = [[PersonLiveView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height-64-statusbarHeight-myselfHeight-_window_height *0.3+ShowDiff) andInfoDic:infoDic andUserId:_userID];
    }
    return _pLiveView;
}
#pragma mark  -----付费内容
-(CenterPayView *)payView
{
    if (!_payView) {
        CGFloat myselfHeight = [_userID isEqual: [Config getOwnID]] ? 0: 60;

        _payView = [[CenterPayView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height-64-statusbarHeight-myselfHeight-_window_height *0.3+ShowDiff) andData:[infoDic valueForKey:@"paidprogram_list"] andUserid:_userID];
    }
    return _payView;
}

#pragma  mark ----点击头部
-(void)headerImgTap {
    YBWeakSelf;
    if (!_headImgView) {
        _headImgView = [[HeaderBackImgView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andHeadUrl:minstr([infoDic valueForKey:@"bg_img"]) andUerid:_userID];
        _headImgView.tapEvent = ^(NSString *types) {
            if ([types isEqual:@"hide"]) {
            }else if ([types isEqual:@"拍照"]){
                [weakSelf clickTake];
            }else if ([types isEqual:@"相册"]){
                [weakSelf clickSel];
            }
            [weakSelf.headImgView removeFromSuperview];
            weakSelf.headImgView = nil;
        };
    }
    [[UIApplication sharedApplication].keyWindow addSubview:_headImgView];

}
#pragma mark ---点击头像
-(void)iconImgTap{
    YBWeakSelf;
    if (!_headImgView) {
        _headImgView = [[HeaderBackImgView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andHeadUrl:minstr([infoDic valueForKey:@"avatar"]) andUerid:_userID];
        [_headImgView hiddenSubBtn]; 
        _headImgView.tapEvent = ^(NSString *types) {
            [weakSelf.headImgView removeFromSuperview];
            weakSelf.headImgView = nil;
        };
    }
    [[UIApplication sharedApplication].keyWindow addSubview:_headImgView];

}
#pragma mark ----粉丝 关注
- (void)fansOrFollowBtnClick:(UIButton *)sender{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (sender.tag == 181221) {
        fansViewController *fans = [[fansViewController alloc]init];
        fans.fensiUid = _userID;
        [[MXBADelegate sharedAppDelegate]pushViewController:fans animated:YES];

    }else{
        attrViewController *att = [[attrViewController alloc]init];
        att.guanzhuUID = _userID;
        [[MXBADelegate sharedAppDelegate]pushViewController:att animated:YES];

    }
}
#pragma mark ----编辑资料
- (void)doEdit:(UIButton *)sender{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if ([sender.titleLabel.text isEqual:YZMsg(@"关注")] ||[sender.titleLabel.text isEqual:YZMsg(@"已关注")]) {
        [YBToolClass postNetworkWithUrl:@"User.setAttent" andParameter:@{@"touid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            if (code == 0) {
                NSString *isattent = [NSString stringWithFormat:@"%@",[[info firstObject] valueForKey:@"isattent"]];
                NSDictionary *subdic = [info firstObject];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"reloadLiveplayAttion" object:subdic];
                if (self.videoBlock) {
                    self.videoBlock(isattent);
                }
                if ([isattent isEqual:@"1"]) {
                    [editBtn setTitle:YZMsg(@"已关注") forState:0];
                    [editBtn setBackgroundColor:RGB(225, 225, 225)];
                    [editBtn setTitleColor:RGB(125, 125, 125) forState:0];
                    blackBtn.selected = NO;
                    NSLog(@"关注成功");
                    if (self.block) {
                        self.block();
                    }
                }
                else
                {
                    [editBtn setTitle:YZMsg(@"关注") forState:0];
                    [editBtn setBackgroundColor:normalColors];
                    [editBtn setTitleColor:UIColor.whiteColor forState:0];
                    NSLog(@"取消关注成功");
                }
            }
        } fail:^{
        }];
    }else{
        myInfoEdit *info = [[myInfoEdit alloc]init];
        [[MXBADelegate sharedAppDelegate] pushViewController:info animated:YES];

    }
}

#pragma  mark  --更新背景图--
-(void)clickTake{
    
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.allowsEditing = YES;
    imagePickerController.delegate = self;
    imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
    imagePickerController.allowsEditing = YES;
    imagePickerController.showsCameraControls = YES;
    imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    //imagePickerController.mediaTypes = @[(NSString *)kUTTypeImage];
    imagePickerController.modalPresentationStyle = 0;
    [[[MXBADelegate sharedAppDelegate]topViewController] presentViewController:imagePickerController animated:YES completion:nil];

}
-(void)clickSel {
    TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
    imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
    imagePC.modalPresentationStyle = 0;
    imagePC.showSelectBtn = YES;
    imagePC.allowCrop = NO;
    imagePC.allowPickingOriginalPhoto = NO;
    imagePC.oKButtonTitleColorNormal = normalColors;
    imagePC.allowTakePicture = YES;
    imagePC.allowTakeVideo = NO;
    imagePC.allowPickingVideo = NO;
    imagePC.allowPickingMultipleVideo = NO;
    [[[MXBADelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
}

- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    NSLog(@"------多选择图片--：%@",photos);
    UIImage *img = photos[0];
    headBgimg = img;
    [self updateBgImg];
}
-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    if (@available(iOS 11, *)) {
        UIScrollView.appearance.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"]) {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerEditedImage"];
        headBgimg = image;
        [self updateBgImg];
        [picker dismissViewControllerAnimated:YES completion:^{
            [UIApplication sharedApplication].statusBarHidden=NO;
        }];
        
    }
}
-(void)updateBgImg {
    [MBProgressHUD showMessage:@""];
    YBWeakSelf;
    [[YBStorageManage shareManage]getCOSInfo:^(int code) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (code == 0) {
                [weakSelf uploadHeadBgImg];
            }
        });
    }];
}
-(void)uploadHeadBgImg{
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);

    if (headBgimg) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_centerHeaderBg.png"];
            [[YBStorageManage shareManage]yb_storageImg:headBgimg andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                //图片成功
                headBgStr = key;
                dispatch_semaphore_signal(semaphore);
            }];

            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self setBgService];
        });
        NSLog(@"任务完成执行");
    });

}
-(void)setBgService{
    //YBWeakSelf;
    NSDictionary *parDic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"img":headBgStr};
    NSString *url = [purl stringByAppendingFormat:@"?service=User.updateBgImg"];

    
    [YBNetworking postWithUrl:url Dic:parDic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [MBProgressHUD hideHUD];

        if ([code isEqual:@"0"]) {
            NSDictionary*infos = [[data valueForKey:@"info"] firstObject];
            NSString *newUrl = minstr([infos valueForKey:@"bg_img"]);
            NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:infoDic];
            [m_dic setObject:newUrl forKey:@"bg_img"];
            infoDic = [NSDictionary dictionaryWithDictionary:m_dic];
            [iconImgView sd_setImageWithURL:[NSURL URLWithString:newUrl]];

        }
        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];

        }];
}

#pragma mark ============进入直播间=============
- (void)domoviePlay{
    BOOL isShowLive = [[NSUserDefaults standardUserDefaults]boolForKey:@"isShowChatLive"];
    if (isShowLive) {
        [[NSNotificationCenter defaultCenter]postNotificationName:@"HIDELIVEVIEW" object:nil];
    }

    for (UIViewController *tempVc in self.navigationController.viewControllers) {
        if ([tempVc isKindOfClass:[moviePlay class]]) {
            temp = (moviePlay *)tempVc;
        }else if ([tempVc isKindOfClass:[UserRoomViewController class]]){
            chatTemp = (UserRoomViewController *)tempVc;
        }
        
    }
    if (temp) {
        NSLog(@"%@",temp.playDoc) ;
        if ([minstr([temp.playDoc valueForKey:@"uid"]) isEqual:self.userID]) {
            [self.navigationController popToViewController:temp animated:YES];
        }else{
            [self goNewMoviePlay];
        }
    }else if (chatTemp){
        if ([minstr([chatTemp.playDoc valueForKey:@"uid"]) isEqual:self.userID]) {
            [self.navigationController popToViewController:chatTemp animated:YES];
        }else{
            [self goNewMoviePlay];
        }

    }
    else{
        [self goNewMoviePlay];
    }

}
- (void)goNewMoviePlay{
    [YBToolClass postNetworkWithUrl:@"Live.getLiveInfo" andParameter:@{@"liveuid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            selectedDic = [info firstObject];
            [self checklive:[selectedDic valueForKey:@"stream"] andliveuid:[selectedDic valueForKey:@"uid"]];
        }else{
            [MBProgressHUD showError:msg];
        }
        
    } fail:^{
        
    }];

}
-(void)checklive:(NSString *)stream andliveuid:(NSString *)liveuid{
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.checkLive"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.timeoutInterval = 5.0;
    request.HTTPMethod = @"post";
    NSString *param = [NSString stringWithFormat:@"uid=%@&token=%@&liveuid=%@&stream=%@",[Config getOwnID],[Config getOwnToken],liveuid,stream];
    request.HTTPBody = [param dataUsingEncoding:NSUTF8StringEncoding];
    NSURLResponse *response;
    NSError *error;
    NSData *backData = [NSURLConnection sendSynchronousRequest:request returningResponse:&response error:&error];
    if (error) {
        [MBProgressHUD showError:@"无网络"];
    }
    else{
        
        
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:backData options:NSJSONReadingMutableContainers error:nil];
        NSNumber *number = [dic valueForKey:@"ret"];
        
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [dic valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                NSString *type = [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                
                type_val =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type_val"]];
                livetype =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                _sdkType = minstr([info valueForKey:@"live_sdk"]);
                NSString *live_type =minstr([info valueForKey:@"live_type"]);
                if ([live_type isEqual:@"1"]) {
                    [[YBSmallLiveWindow shareInstance]closeBtnClick];
                    UserRoomViewController *chatroom = [[UserRoomViewController alloc]init];
                    chatroom.playDoc = selectedDic;
                    chatroom.sdkType = _sdkType;
                    [[MXBADelegate sharedAppDelegate] pushViewController:chatroom animated:YES];

                }else{
                    if ([type isEqual:@"0"]) {
                        [self pushMovieVC];
                    }
                    else if ([type isEqual:@"1"]){
                        NSString *_MD5 = [NSString stringWithFormat:@"%@",[info valueForKey:@"type_msg"]];
                        //密码
                        md5AlertController = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"本房间为密码房间，请输入密码") preferredStyle:UIAlertControllerStyleAlert];
                        //添加一个取消按钮
                        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            [self.navigationController popViewControllerAnimated:YES];
                            [self dismissViewControllerAnimated:NO completion:nil];
                        }];
                        [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                        [md5AlertController addAction:cancelAction];

                        //在AlertView中添加一个输入框
                        [md5AlertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
                            textField.secureTextEntry = YES;
                        }];
                        
                        //添加一个确定按钮 并获取AlertView中的第一个输入框 将其文本赋值给BUTTON的title
                        UIAlertAction *sureAction =[UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            UITextField *alertTextField = md5AlertController.textFields.firstObject;
                            //                        [self checkMD5WithText:envirnmentNameTextField.text andMD5:_MD5];
                            //输出 检查是否正确无误
                            NSLog(@"你输入的文本%@",alertTextField.text);
                            if ([_MD5 isEqualToString:[self stringToMD5:alertTextField.text]]) {
                                [self pushMovieVC];
                            }else{
                                alertTextField.text = @"";
                                [MBProgressHUD showError:YZMsg(@"密码错误")];
                                [self presentViewController:md5AlertController animated:true completion:nil];
                                return ;
                            }
                            
                        }];
                        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                        [md5AlertController addAction:sureAction];

                        
                        //present出AlertView
                        dispatch_async(dispatch_get_main_queue(), ^{
                            [self presentViewController:md5AlertController animated:true completion:nil];
                        });
                    }
                    else if ([type isEqual:@"2"] || [type isEqual:@"3"]){
                        if ([[YBYoungManager shareInstance]isOpenYoung]) {
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"青少年模式下不支持该功能") preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"知道了") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [self.navigationController popViewControllerAnimated:YES];
                                [self dismissViewControllerAnimated:NO completion:nil];

                            }];
                            [cancleAction setValue:[UIColor grayColor] forKey:@"_titleTextColor"];
                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"去关闭") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [[YBYoungManager shareInstance]checkYoungStatus:YoungFrom_Center];

                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                            [alertContro addAction:sureAction];
                            [self presentViewController:alertContro animated:YES completion:nil];
                        }else{
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:minstr([info valueForKey:@"type_msg"]) preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [self.navigationController popViewControllerAnimated:YES];
                                [self dismissViewControllerAnimated:NO completion:nil];
                                
                            }];
                            [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];

                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                if ([[Config getOwnID] intValue] <= 0) {
                                    [[YBToolClass sharedInstance]waringLogin];
                                    return;
                                }

                                [self doCoast];
                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];

                            [alertContro addAction:sureAction];
                            dispatch_async(dispatch_get_main_queue(), ^{
                                [self presentViewController:alertContro animated:YES completion:nil];
                            });
                            

                        }

                    }

                }
                
            }
            else{
                NSString *msg = [NSString stringWithFormat:@"%@",[data valueForKey:@"msg"]];
                [MBProgressHUD showError:msg];
            }
        }
        
    }
    
}
-(void)pushMovieVC{
    if (temp) {
        NSDictionary *dic = @{
                              @"dic":selectedDic,
                              @"type_val":type_val,
                              @"livetype":livetype,
                              @"sdkType":_sdkType
                              };
        [[NSNotificationCenter defaultCenter] postNotificationName:@"changePlayRoom" object:nil userInfo:dic];
        [self.navigationController popToViewController:temp animated:YES];

    }else{
        [[YBSmallLiveWindow shareInstance]closeBtnClick];
        
        moviePlay *player = [[moviePlay alloc]init];
        player.scrollarray = nil;
        player.scrollindex = 0;
        player.playDoc = selectedDic;
        player.type_val = type_val;
        player.livetype = livetype;
        player.sdkType = _sdkType;
        [[MXBADelegate sharedAppDelegate] pushViewController:player animated:YES];
    }
}
- (NSString *)stringToMD5:(NSString *)str
{
    
    //1.首先将字符串转换成UTF-8编码, 因为MD5加密是基于C语言的,所以要先把字符串转化成C语言的字符串
    const char *fooData = [str UTF8String];
    
    //2.然后创建一个字符串数组,接收MD5的值
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    
    //3.计算MD5的值, 这是官方封装好的加密方法:把我们输入的字符串转换成16进制的32位数,然后存储到result中
    CC_MD5(fooData, (CC_LONG)strlen(fooData), result);
    /**
     第一个参数:要加密的字符串
     第二个参数: 获取要加密字符串的长度
     第三个参数: 接收结果的数组
     */
    
    //4.创建一个字符串保存加密结果
    NSMutableString *saveResult = [NSMutableString string];
    
    //5.从result 数组中获取加密结果并放到 saveResult中
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [saveResult appendFormat:@"%02x", result[i]];
    }
    /*
     x表示十六进制，%02X  意思是不足两位将用0补齐，如果多余两位则不影响
     NSLog("%02X", 0x888);  //888
     NSLog("%02X", 0x4); //04
     */
    return saveResult;
}
//执行扣费
-(void)doCoast{
    [YBToolClass postNetworkWithUrl:@"Live.roomCharge" andParameter:@{@"liveuid":minstr([selectedDic valueForKey:@"uid"]),@"stream":minstr([selectedDic valueForKey:@"stream"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            NSDictionary *infos = [info firstObject];
            LiveUser *user = [Config myProfile];
            user.coin = [NSString stringWithFormat:@"%@",[infos valueForKey:@"coin"]];
            user.level = [NSString stringWithFormat:@"%@",[infos valueForKey:@"level"]];
            [Config updateProfile:user];

            [self pushMovieVC];
            //计时扣费
            
        }else{
            [MBProgressHUD showError:msg];
        }
        
    } fail:^{
    }];
    
}

#pragma mark ============店铺主页=============
- (void)doUserShoreHome{
    shopDetailVC *store = [[shopDetailVC alloc]init];
    store.toUserID = _userID;
    [[MXBADelegate sharedAppDelegate] pushViewController:store animated:YES];
}

- (void)bottomBtnClick:(UIButton *)sender{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }
    //私信
    if (sender == jmsgBtn) {
        if ([[common letter_switch] isEqual:@"0"]) {
            [MBProgressHUD showError:YZMsg(@"私信对话平台已关闭,暂时无法使用")];
            return;
        }

        TConversationCellData *data = [[TConversationCellData alloc] init];
        data.convId = self.userID;
        data.convType = TConv_Type_C2C;
        data.title =  minstr([infoDic valueForKey:@"user_nickname"]);
        data.userHeader = minstr([infoDic valueForKey:@"avatar"]);
        data.userName = minstr([infoDic valueForKey:@"user_nickname"]);
        data.isAtt = minstr([infoDic valueForKey:@"isattention"]);

        TChatC2CController *chat = [[TChatC2CController alloc] init];
        chat.conversation = data;
        [self.navigationController pushViewController:chat animated:YES];

    }
    //拉黑
    if (sender == blackBtn) {
        [YBToolClass postNetworkWithUrl:@"User.setBlack" andParameter:@{@"touid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            if (code == 0) {
                if (!blackBtn.selected) {
                    blackBtn.selected  = YES;
                    if (![_userID isEqual:[Config getOwnID]]) {
                        [editBtn setTitle:YZMsg(@"关注") forState:0];
                        [editBtn setBackgroundColor:normalColors];
                        [editBtn setTitleColor:UIColor.whiteColor forState:0];

                    }
                    [MBProgressHUD showError:YZMsg(@"已拉黑")];
                }
                else{
                    blackBtn.selected = NO;
                    [MBProgressHUD showError:YZMsg(@"已解除拉黑")];
                }

            }
        } fail:^{
            
        }];
    }
}

#pragma mark - TYTabPagerBarDataSource

- (NSInteger)numberOfItemsInPagerTabBar {
    return _datas.count;
}

- (UICollectionViewCell<TYTabPagerBarCellProtocol> *)pagerTabBar:(TYTabPagerBar *)pagerTabBar cellForItemAtIndex:(NSInteger)index {
    UICollectionViewCell<TYTabPagerBarCellProtocol> *cell = [pagerTabBar dequeueReusableCellWithReuseIdentifier:[TYTabPagerBarCell cellIdentifier] forIndex:index];
    cell.titleLabel.text = _datas[index];
    cell.titleLabel.adjustsFontSizeToFitWidth = YES;
    return cell;
}

#pragma mark - TYTabPagerBarDelegate

- (CGFloat)pagerTabBar:(TYTabPagerBar *)pagerTabBar widthForItemAtIndex:(NSInteger)index {
    NSString *title = _datas[index];
    return [pagerTabBar cellWidthForTitle:title];
}

- (void)pagerTabBar:(TYTabPagerBar *)pagerTabBar didSelectItemAtIndex:(NSInteger)index {
    [_pageView scrollToViewAtIndex:index animate:YES];
}

#pragma mark - TYPagerViewDataSource

- (NSInteger)numberOfViewsInPagerView {
    return _datas.count;
}

- (UIView *)pagerView:(TYPagerView *)pagerView viewForIndex:(NSInteger)index prefetching:(BOOL)prefetching {
    if (index == 0) {
        return self.pInfoView;
    }else if (index == 1) {
        return self.pVideoView;
    }else if (index == 2){
        return self.dtView;
    }else if (index == 3){
        return self.pLiveView;
    }else {
        return self.payView;
    }
}

#pragma mark - TYPagerViewDelegate

- (void)pagerView:(TYPagerView *)pagerView willAppearView:(UIView *)view forIndex:(NSInteger)index {
    NSLog(@"+++++++++willAppearViewIndex:%ld",index);
}
-(void)refreshByBlackEvent:(NSInteger)index {
//    if (index == 1) {
//        if (_dtRefresh) {
//            _dtRefresh = NO;
//            [dynamicView blackEventRes];
//        }
//    }else if (index == 2){
//        if (_xcRefresh) {
//            _xcRefresh = NO;
//            [albumView blackEventRes];
//        }
//    }else if(index == 3){
//        if (_jnRefresh) {
//            _jnRefresh = NO;
//            _skillPage = 1;
//            [self requestData];
//        }
//    }
}
- (void)pagerView:(TYPagerView *)pagerView willDisappearView:(UIView *)view forIndex:(NSInteger)index {
    //NSLog(@"---------willDisappearView:%ld",index);
}

- (void)pagerView:(TYPagerView *)pagerView transitionFromIndex:(NSInteger)fromIndex toIndex:(NSInteger)toIndex animated:(BOOL)animated {
    NSLog(@"fromIndex:%ld, toIndex:%ld",fromIndex,toIndex);
    [_tabBar scrollToItemFromIndex:fromIndex toIndex:toIndex animate:animated];
}

- (void)pagerView:(TYPagerView *)pagerView transitionFromIndex:(NSInteger)fromIndex toIndex:(NSInteger)toIndex progress:(CGFloat)progress {
    //NSLog(@"fromIndex:%ld, toIndex:%ld progress%.3f",fromIndex,toIndex,progress);
    [_tabBar scrollToItemFromIndex:fromIndex toIndex:toIndex progress:progress];
}

- (void)pagerViewWillBeginScrolling:(TYPagerView *)pageView animate:(BOOL)animate {
    //NSLog(@"pagerViewWillBeginScrolling");
}

- (void)pagerViewDidEndScrolling:(TYPagerView *)pageView animate:(BOOL)animate {
    //NSLog(@"pagerViewDidEndScrolling");
}


#pragma mark ============scroll代理=============
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    if (scrollView == backScroll) {
        CGFloat currentY = backScroll.contentOffset.y;
        int up = 0;
        if (currentY > _beginScrollY) {
            up = 1;
        }
        NSLog(@"---------------SCROLL---:%f",scrollView.contentOffset.y);
        if(IS_IPHONE_6){
            shopHeigt = 80;
        }
        if (scrollView.contentOffset.y >= _window_height *0.3+shopHeigt) {
            scrollView.contentOffset = CGPointMake(0, _window_height *0.3+shopHeigt);
        }
    }
}
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    if (scrollView == backScroll) {
        _beginScrollY = backScroll.contentOffset.y;
    }
}

@end
