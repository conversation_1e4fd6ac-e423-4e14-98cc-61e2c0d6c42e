//
//  PersonInfoView.m
//  YBLive
//
//  Created by y<PERSON><PERSON><PERSON> on 2023/10/24.
//  Copyright © 2023 cat. All rights reserved.
//

#import "PersonInfoView.h"
#import "UIImage+GradientColor.h"
#import "guardRankVC.h"
#import "impressVC.h"
#import "Live-Swift.h"
@interface PersonInfoView () <UICollectionViewDelegate, UICollectionViewDataSource>
{
    NSString *userID;
    UILabel *signatureLb;
    UILabel *otherInfoLb;

}

@property (nonatomic, strong) NSArray<FeatureTagTextImageItemData *> *features;
@end

@implementation PersonInfoView

-(instancetype)initWithFrame:(CGRect)frame andInfoDic:(NSDictionary *)dic andUserId:(NSString *)_userID
{
    self = [super initWithFrame:frame];
    if(self){
        self.features = @[
            
        ];
        userID = _userID;

        NSArray *array4 = @[YZMsg(@"个性签名"),YZMsg(@"答题擅长领域"),YZMsg(@"个人信息")];
        MASViewAttribute *bottomSpeace = self.mas_top;
        for (int i = 0; i < array4.count; i++) {
            UIView *lineV = [[UIView alloc]init];
            lineV.backgroundColor = normalColors;
            [self addSubview:lineV];
            [lineV mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(self.mas_leading).mas_offset(14.0);
                make.top.equalTo(bottomSpeace).offset(15);
                make.width.mas_equalTo(3);
                make.height.mas_equalTo(15);
            }];
            UILabel *label = [[UILabel alloc]init];
            label.text = array4[i];
            label.font = [UIFont systemFontOfSize:13];
            [self addSubview:label];
            [label mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(lineV);
                make.left.equalTo(lineV.mas_right).offset(8);
            }];
            if (i == 1) {
                UICollectionViewLeftAlignedLayout *layout = [[UICollectionViewLeftAlignedLayout alloc] init];
                layout.minimumLineSpacing = 4.0;
                layout.minimumInteritemSpacing = 4.0;
                UICollectionView *featureCollectionView = [[UICollectionView alloc]initWithFrame:CGRectZero collectionViewLayout:layout];
                featureCollectionView.userInteractionEnabled = NO;
                featureCollectionView.backgroundColor = [UIColor clearColor];
                featureCollectionView.delegate = self;
                featureCollectionView.dataSource = self;
                featureCollectionView.showsHorizontalScrollIndicator = NO;
                featureCollectionView.showsVerticalScrollIndicator = NO;
                featureCollectionView.pagingEnabled = NO;
                featureCollectionView.alwaysBounceHorizontal = YES;

                [featureCollectionView registerClass:[FeatureTagTextImageCell class] forCellWithReuseIdentifier:NSStringFromClass([FeatureTagTextImageCell class])];
                [self addSubview:featureCollectionView];
                [featureCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(lineV);
                    make.top.equalTo(lineV.mas_bottom).offset(5);
                    make.height.mas_equalTo(26);
                    make.width.equalTo(self).offset(-15);
                }];
                bottomSpeace = featureCollectionView.mas_bottom;
            }else{
                UILabel *labelllll = [[UILabel alloc]init];
                labelllll.textColor = RGB_COLOR(@"#7F7F7F", 1);
                labelllll.font = [UIFont systemFontOfSize:11];
                labelllll.numberOfLines = 0;
                [self addSubview:labelllll];
                [labelllll mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(lineV);
                    make.top.equalTo(lineV.mas_bottom).offset(10);
                    make.height.mas_greaterThanOrEqualTo(34);
                    make.width.equalTo(self);
                }];
                if (i == 0) {
                    labelllll.text = minstr([dic valueForKey:@"signature"]);
                    signatureLb = labelllll;
                }else{
                    labelllll.text = [NSString stringWithFormat:@"%@：%@\n\n%@：%@",YZMsg(@"生日"),minstr([dic valueForKey:@"birthday"]),YZMsg(@"所在地"),minstr([dic valueForKey:@"location"])];
                    otherInfoLb = labelllll;
                }
                bottomSpeace = labelllll.mas_bottom;
            }
        }

    }
    return self;
}

#pragma mark =====贡献榜
- (void)rankBtnClick:(UIButton *)sender{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (sender.tag == 1000) {
        //贡献榜
        NSString *language = [PublicObj getCurrentLanguage];
        YBWebViewController *rank = [[YBWebViewController alloc]init];
        rank.urls = [NSString stringWithFormat:@"%@/appapi/contribute/index?uid=%@&language=%@",h5url,userID,language];
        [[MXBADelegate sharedAppDelegate]pushViewController:rank animated:YES];

    }else{
        //守护榜
        guardRankVC *rank = [[guardRankVC alloc]init];
        rank.liveUID = userID;
        [[MXBADelegate sharedAppDelegate]pushViewController:rank animated:YES];

    }
}
#pragma mark =====添加印象
- (void)addImpBtnClick{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    YBWeakSelf;
    impressVC *vc = [[impressVC alloc]init];
    vc.isAdd = YES;
    vc.touid = userID;
    vc.refreshEvent = ^{
        [weakSelf refreshData];
    };
    [[MXBADelegate sharedAppDelegate]pushViewController:vc animated:YES];

}
-(void)refreshData{
    if(self.imPressEvent){
        self.imPressEvent();
    }
}
- (void)impressGoSelf{
    impressVC *vc = [[impressVC alloc]init];
    vc.isAdd = NO;
    vc.touid = @"0";
    [[MXBADelegate sharedAppDelegate] pushViewController:vc animated:YES];

}

#pragma mark ---刷新数据
-(void)refreshUserInfo:(NSDictionary *)dic{
    signatureLb.text = minstr([dic valueForKey:@"signature"]);
    otherInfoLb.text = [NSString stringWithFormat:@"%@：%@\n\n%@：%@",YZMsg(@"生日"),minstr([dic valueForKey:@"birthday"]),YZMsg(@"所在地"),minstr([dic valueForKey:@"location"])];
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.features.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView
                           cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    FeatureTagTextImageCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FeatureTagTextImageCell class])
                                                                               forIndexPath:indexPath];
    cell.feature = self.features[indexPath.row];
    return cell;
}

#pragma mark - UICollectionViewDelegateFlowLayout

- (CGSize)collectionView:(UICollectionView *)collectionView
                  layout:(UICollectionViewLayout *)collectionViewLayout
  sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSString *featureText = self.features[indexPath.row].feature;
    return [FeatureTagTextImageCell cellSizeWithData:featureText];
}
@end
