//
//  PersonLiveView.m
//  YBLive
//
//  Created by y<PERSON><PERSON><PERSON> on 2023/10/24.
//  Copyright © 2023 cat. All rights reserved.
//

#import "PersonLiveView.h"
#import "personLiveCell.h"
#import "hietoryPlay.h"
@interface PersonLiveView ()<UICollectionViewDelegate,UICollectionViewDataSource>
{
    UICollectionView *personCollection;
    NSMutableArray *liveArray;
    NSString *_userIDStr;
    int livePage;
    NSDictionary *infoDic;
}
@end

@implementation PersonLiveView

-(instancetype)initWithFrame:(CGRect)frame andInfoDic:(NSDictionary *)dic andUserId:(NSString *)_userID
{
    self = [super initWithFrame:frame];
    if(self){
        liveArray = [NSMutableArray array];
        _userIDStr = _userID;
        livePage = 1;
        infoDic = dic;
        
        UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
        flow.scrollDirection = UICollectionViewScrollDirectionVertical;
        flow.minimumLineSpacing = 0;
        flow.minimumInteritemSpacing = 0;
        personCollection = [[UICollectionView alloc]initWithFrame:CGRectMake(0,0, _window_width, self.height) collectionViewLayout:flow];
        personCollection.delegate   = self;
        personCollection.dataSource = self;
        personCollection.backgroundColor = [UIColor whiteColor];
        [self addSubview:personCollection];
//        [personCollection mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.right.equalTo(self);
//            make.top.equalTo(self).offset(10);
//            make.bottom.equalTo(self).offset([_userID isEqual:[Config getOwnID]] ? 0 : -(100+ShowDiff));
//        }];

        [personCollection registerNib:[UINib nibWithNibName:@"personLiveCell" bundle:nil] forCellWithReuseIdentifier:@"personLiveCELL"];

        if (@available(iOS 11.0, *)) {
            personCollection.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        } else {
            // Fallback on earlier versions
//            self.automaticallyAdjustsScrollViewInsets = NO;
        }

        personCollection.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
            livePage ++;
            [self pullInternet];
        }];
        [self pullInternet];

    }
    return self;
}

//请求直播记录分页
- (void)pullInternet{
    [YBToolClass postNetworkWithUrl:@"User.getLiverecord" andParameter:@{@"touid":_userIDStr,@"p":@(livePage)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [personCollection.mj_footer endRefreshing];
        if (code == 0) {
            [liveArray addObjectsFromArray:info];
            [personCollection reloadData];
            if ([info count] == 0) {
                [personCollection.mj_footer endRefreshingWithNoMoreData];
            }
        }
    } fail:^{
        [personCollection.mj_footer endRefreshing];
    }];
}
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return liveArray.count;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    personLiveCell *cell = (personLiveCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"personLiveCELL" forIndexPath:indexPath];
    cell.model = [[LiveNodeModel alloc]initWithDic:liveArray[indexPath.item]];
    return cell;
}
- (void) collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
        NSDictionary *subdics = liveArray[indexPath.item];
        [MBProgressHUD showMessage:@""];

        [YBToolClass postNetworkWithUrl:@"User.getAliCdnRecord" andParameter:@{@"id":minstr([subdics valueForKey:@"id"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            [MBProgressHUD hideHUD];

            if (code == 0) {
                NSDictionary *userDic = [NSDictionary dictionaryWithObjectsAndKeys:minstr([infoDic valueForKey:@"user_nickname"]),@"name",minstr([infoDic valueForKey:@"avatar"]),@"icon",minstr([infoDic valueForKey:@"id"]),@"id",minstr([infoDic valueForKey:@"level_anchor"]),@"level", nil];
                hietoryPlay *history = [[hietoryPlay alloc]init];
                history.url = [[info firstObject] valueForKey:@"url"];
                history.selectDic = userDic;
                history.shareDic = infoDic;
                [[MXBADelegate sharedAppDelegate]pushViewController:history animated:YES];

            }else{
                [MBProgressHUD showError:msg];
            }
        } fail:^{
            [MBProgressHUD hideHUD];
        }];
}
-(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    return CGSizeMake(_window_width, 50);
}


@end
