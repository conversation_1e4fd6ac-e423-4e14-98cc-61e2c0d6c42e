//
//  PersonInfoView.h
//  YBLive
//
//  Created by y<PERSON><PERSON><PERSON> on 2023/10/24.
//  Copyright © 2023 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^personInfoEvent)();

@interface PersonInfoView : UIView

@property (nonatomic, copy)personInfoEvent imPressEvent;

-(instancetype)initWithFrame:(CGRect)frame andInfoDic:(NSDictionary *)dic andUserId:(NSString *)_userID;
-(void)reloadImPress:(NSDictionary *)dic;
-(void)refreshUserInfo:(NSDictionary *)dic;
@end

