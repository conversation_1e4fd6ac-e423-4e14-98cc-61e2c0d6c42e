//
//  PersonHomeVC.h
//  YBLive
//
//  Created by y<PERSON><PERSON><PERSON> on 2023/10/23.
//  Copyright © 2023 cat. All rights reserved.
//

#import "YBBaseViewController.h"
typedef NS_ENUM(NSInteger,BottomType) {
    BottomType_Chat,     //私信
    BottomType_Black,   // 拉黑
};

typedef void (^videoFollowBlock)(NSString *attStr);
typedef void (^chatFollowBlock)();

@interface PersonHomeVC : UIViewController

@property(nonatomic,assign)BottomType bottomBtnType;
@property (nonatomic,copy) videoFollowBlock videoBlock;
@property (nonatomic,copy) chatFollowBlock block;

@property(nonatomic,strong)NSString *userID;

@end

