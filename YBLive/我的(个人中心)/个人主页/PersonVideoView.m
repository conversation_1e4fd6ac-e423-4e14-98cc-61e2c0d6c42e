//
//  PersonVideoView.m
//  YBLive
//
//  Created by y<PERSON><PERSON><PERSON> on 2023/10/24.
//  Copyright © 2023 cat. All rights reserved.
//

#import "PersonVideoView.h"
#import "mineVideoCell.h"
#import "YBLookVideoVC.h"
@interface PersonVideoView ()<UICollectionViewDelegate,UICollectionViewDataSource>
{
    UICollectionView *personCollection;
    int videoPage;
    NSString *_userIDStr;
    NSMutableArray *videoArray;

}
@end

@implementation PersonVideoView

-(instancetype)initWithFrame:(CGRect)frame  andUserId:(NSString *)_userID
{
    self = [super initWithFrame:frame];
    if(self){
        videoPage = 1;
        _userIDStr = _userID;
        videoArray = [NSMutableArray array];
        
        UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
        flow.scrollDirection = UICollectionViewScrollDirectionVertical;
        flow.minimumLineSpacing = 0;
        flow.minimumInteritemSpacing = 0;
        personCollection = [[UICollectionView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height-ShowDiff-60) collectionViewLayout:flow];
        personCollection.delegate   = self;
        personCollection.dataSource = self;
        personCollection.backgroundColor = [UIColor whiteColor];
        [self addSubview:personCollection];
        [personCollection mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self);
            make.top.equalTo(self).offset(10);
            make.bottom.equalTo(self).offset([_userID isEqual:[Config getOwnID]] ? 0 : -(60+ShowDiff));
        }];

//        [personCollection registerNib:[UINib nibWithNibName:@"personLiveCell" bundle:nil] forCellWithReuseIdentifier:@"personLiveCELL"];
        [personCollection registerNib:[UINib nibWithNibName:@"mineVideoCell" bundle:nil] forCellWithReuseIdentifier:@"mineVideoCELL"];
//        [personCollection registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"hotHeaderV"];

        if (@available(iOS 11.0, *)) {
            personCollection.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        } else {
            // Fallback on earlier versions
//            self.automaticallyAdjustsScrollViewInsets = NO;
        }

        personCollection.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
            videoPage++;
            [self pullVideoList];
        }];
        [self pullVideoList];

    }
    return self;
}
- (void)pullVideoList{
    [YBToolClass postNetworkWithUrl:@"video.gethomevideo" andParameter:@{@"touid":_userIDStr,@"p":@(videoPage)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [personCollection.mj_footer endRefreshing];
        if (code == 0) {
            [videoArray addObjectsFromArray:info];
            [personCollection reloadData];
            if ([info count] == 0) {
                [personCollection.mj_footer endRefreshingWithNoMoreData];
            }
        }
    } fail:^{
        [personCollection.mj_footer endRefreshing];
    }];

}
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return videoArray.count;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
        mineVideoCell *cell = (mineVideoCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"mineVideoCELL" forIndexPath:indexPath];
        cell.model = [[NearbyVideoModel alloc]initWithDic:videoArray[indexPath.row]];
        return cell;
}
- (void) collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
        YBLookVideoVC *ybLook = [[YBLookVideoVC alloc]init];
        if ([_userIDStr isEqual:[Config getOwnID]]) {
            ybLook.fromWhere = @"myVideoV";
        }else{
            ybLook.fromWhere = @"otherVideoV";
        }

        ybLook.firstPush = YES;
        ybLook.pushPlayIndex = indexPath.row;
        ybLook.sourceBaseUrl = [NSString stringWithFormat:@"%@/?service=video.gethomevideo&uid=%@&touid=%@",purl,[Config getOwnID],[Config getOwnID]];
        ybLook.videoList = videoArray;
        ybLook.pages =videoPage;
        ybLook.hidesBottomBarWhenPushed = YES;
        [[MXBADelegate sharedAppDelegate] pushViewController:ybLook animated:YES];
}
-(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    return CGSizeMake((_window_width-2)/3, (_window_width-2)/3/25*33);
}

@end
