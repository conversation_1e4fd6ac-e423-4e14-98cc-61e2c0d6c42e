<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="1wM-HJ-P1i" customClass="shareImgView">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="shareImg_bg.png" translatesAutoresizingMaskIntoConstraints="NO" id="rXQ-Ln-CNT">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="fxbg.png" translatesAutoresizingMaskIntoConstraints="NO" id="t1y-i2-osI">
                    <rect key="frame" x="32" y="308" width="311" height="338"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="338" id="Yel-sX-ril"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="云豹直播" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Yz-No-n7Z">
                    <rect key="frame" x="153" y="416" width="69.5" height="20.5"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xcs-cS-4Oj">
                    <rect key="frame" x="53" y="547" width="145" height="79"/>
                    <subviews>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="leG-3e-GZl">
                            <rect key="frame" x="0.0" y="9.5" width="60" height="60"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="60" id="d0o-7b-6gk"/>
                                <constraint firstAttribute="width" constant="60" id="oeg-jh-RRe"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="30"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="W5K-g6-3dm">
                            <rect key="frame" x="65" y="20" width="36" height="17"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5iz-dY-PVV">
                            <rect key="frame" x="65" y="47" width="33" height="16"/>
                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                            <color key="textColor" red="0.58823529411764708" green="0.58823529411764708" blue="0.58823529411764708" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="W5K-g6-3dm" firstAttribute="top" secondItem="Xcs-cS-4Oj" secondAttribute="top" constant="20" id="1AW-yU-NmO"/>
                        <constraint firstItem="5iz-dY-PVV" firstAttribute="top" secondItem="W5K-g6-3dm" secondAttribute="bottom" constant="10" id="EvY-OT-QKL"/>
                        <constraint firstItem="5iz-dY-PVV" firstAttribute="leading" secondItem="leG-3e-GZl" secondAttribute="trailing" constant="5" id="FqM-f4-wg5"/>
                        <constraint firstItem="leG-3e-GZl" firstAttribute="centerY" secondItem="Xcs-cS-4Oj" secondAttribute="centerY" id="HgI-WX-ZeE"/>
                        <constraint firstItem="leG-3e-GZl" firstAttribute="leading" secondItem="Xcs-cS-4Oj" secondAttribute="leading" id="V01-mj-SxL"/>
                        <constraint firstItem="W5K-g6-3dm" firstAttribute="leading" secondItem="leG-3e-GZl" secondAttribute="trailing" constant="5" id="hvC-xW-9R5"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="邀请码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TTp-JO-fy8">
                    <rect key="frame" x="166" y="475" width="43" height="17"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="bA9-vM-y6c">
                    <rect key="frame" x="246" y="549" width="75" height="75"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="75" id="3wC-ZM-jnR"/>
                        <constraint firstAttribute="height" constant="75" id="pc7-kI-ynp"/>
                    </constraints>
                </imageView>
                <imageView hidden="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="8un-iL-99D">
                    <rect key="frame" x="293" y="469" width="40" height="40"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="5"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </imageView>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="K9c-xT-vdq">
                    <rect key="frame" x="147.5" y="328" width="80" height="80"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="80" id="5oP-2X-rFL"/>
                        <constraint firstAttribute="height" constant="80" id="vLh-fy-bYu"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="25"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                            <real key="value" value="0.0"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bWq-of-JgR">
                    <rect key="frame" x="157" y="500" width="61.5" height="29"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="24"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="4Yz-No-n7Z" firstAttribute="top" secondItem="K9c-xT-vdq" secondAttribute="bottom" constant="8" id="2Sx-oH-4iA"/>
                <constraint firstItem="Xcs-cS-4Oj" firstAttribute="centerY" secondItem="bA9-vM-y6c" secondAttribute="centerY" id="3O7-uN-xHw"/>
                <constraint firstItem="K9c-xT-vdq" firstAttribute="centerX" secondItem="t1y-i2-osI" secondAttribute="centerX" id="7RW-Ak-F97"/>
                <constraint firstItem="bWq-of-JgR" firstAttribute="centerX" secondItem="TTp-JO-fy8" secondAttribute="centerX" id="EAm-Fx-jMH"/>
                <constraint firstItem="t1y-i2-osI" firstAttribute="leading" secondItem="1wM-HJ-P1i" secondAttribute="leading" constant="32" id="ELS-St-yP2"/>
                <constraint firstItem="4Yz-No-n7Z" firstAttribute="centerX" secondItem="K9c-xT-vdq" secondAttribute="centerX" id="EbJ-4j-ihb"/>
                <constraint firstItem="TTp-JO-fy8" firstAttribute="top" secondItem="4Yz-No-n7Z" secondAttribute="bottom" constant="38.5" id="FAO-ra-ACI"/>
                <constraint firstAttribute="trailing" secondItem="t1y-i2-osI" secondAttribute="trailing" constant="32" id="FWw-kI-ogN"/>
                <constraint firstItem="bWq-of-JgR" firstAttribute="top" secondItem="TTp-JO-fy8" secondAttribute="bottom" constant="8" id="LAl-Ch-z95"/>
                <constraint firstAttribute="bottom" secondItem="rXQ-Ln-CNT" secondAttribute="bottom" id="LPT-oD-PfS"/>
                <constraint firstItem="Xcs-cS-4Oj" firstAttribute="height" secondItem="bA9-vM-y6c" secondAttribute="height" multiplier="1.05333" id="MNi-nl-rLo"/>
                <constraint firstItem="bA9-vM-y6c" firstAttribute="trailing" secondItem="t1y-i2-osI" secondAttribute="trailing" constant="-22" id="ZVf-zJ-LVI"/>
                <constraint firstItem="Xcs-cS-4Oj" firstAttribute="leading" secondItem="t1y-i2-osI" secondAttribute="leading" constant="21" id="ZkG-GH-EJp"/>
                <constraint firstItem="rXQ-Ln-CNT" firstAttribute="leading" secondItem="1wM-HJ-P1i" secondAttribute="leading" id="ctp-3A-jiE"/>
                <constraint firstItem="TTp-JO-fy8" firstAttribute="centerX" secondItem="t1y-i2-osI" secondAttribute="centerX" id="dcf-e0-tfd"/>
                <constraint firstItem="K9c-xT-vdq" firstAttribute="top" secondItem="t1y-i2-osI" secondAttribute="top" constant="20" id="fUG-Xc-KQ0"/>
                <constraint firstItem="rXQ-Ln-CNT" firstAttribute="top" secondItem="1wM-HJ-P1i" secondAttribute="top" id="foN-Rb-9mC"/>
                <constraint firstAttribute="bottom" secondItem="t1y-i2-osI" secondAttribute="bottom" constant="21" id="gjZ-wl-OA4"/>
                <constraint firstItem="bA9-vM-y6c" firstAttribute="bottom" secondItem="t1y-i2-osI" secondAttribute="bottom" constant="-22" id="k6f-PV-3k1"/>
                <constraint firstAttribute="trailing" secondItem="rXQ-Ln-CNT" secondAttribute="trailing" id="kev-fL-Ajp"/>
                <constraint firstItem="bA9-vM-y6c" firstAttribute="leading" secondItem="Xcs-cS-4Oj" secondAttribute="trailing" constant="48" id="vBo-5h-kub"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="appNameL" destination="4Yz-No-n7Z" id="nRX-lO-wqz"/>
                <outlet property="bavImg" destination="rXQ-Ln-CNT" id="3Rd-0p-i4z"/>
                <outlet property="codeImgV" destination="bA9-vM-y6c" id="v82-pZ-uuy"/>
                <outlet property="iconImgV" destination="K9c-xT-vdq" id="vvb-LJ-qhC"/>
                <outlet property="invitationL" destination="bWq-of-JgR" id="W7I-fc-wM0"/>
                <outlet property="userIDL" destination="5iz-dY-PVV" id="pDK-XA-a6l"/>
                <outlet property="userIcon" destination="leG-3e-GZl" id="d5a-rJ-LPP"/>
                <outlet property="userIconSmall" destination="8un-iL-99D" id="WW9-8m-Kgx"/>
                <outlet property="userNameL" destination="W5K-g6-3dm" id="Mcy-Sq-dpn"/>
            </connections>
            <point key="canvasLocation" x="60" y="-26.53673163418291"/>
        </view>
    </objects>
    <resources>
        <image name="fxbg.png" width="625" height="677"/>
        <image name="shareImg_bg.png" width="750" height="1475"/>
    </resources>
</document>
