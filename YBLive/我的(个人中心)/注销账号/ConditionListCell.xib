<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16086"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="60" id="KGk-i7-Jjw" customClass="ConditionListCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="v6t-Ly-Ee5">
                        <rect key="frame" x="8" y="6" width="234" height="17"/>
                        <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Th8-hk-GgO">
                        <rect key="frame" x="8" y="33" width="234" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="未通过" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="N8j-qe-xKh">
                        <rect key="frame" x="267" y="6" width="43" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="oPX-NK-sYd">
                        <rect key="frame" x="246" y="6.5" width="16" height="16"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="16" id="EAD-1Q-QWm"/>
                            <constraint firstAttribute="height" constant="16" id="die-9t-rcW"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="Th8-hk-GgO" firstAttribute="top" secondItem="v6t-Ly-Ee5" secondAttribute="bottom" constant="10" id="EUi-nR-SHj"/>
                    <constraint firstItem="v6t-Ly-Ee5" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="Gl4-7v-AnL"/>
                    <constraint firstAttribute="trailing" secondItem="v6t-Ly-Ee5" secondAttribute="trailing" constant="78" id="Hov-fx-uib"/>
                    <constraint firstItem="oPX-NK-sYd" firstAttribute="centerY" secondItem="v6t-Ly-Ee5" secondAttribute="centerY" id="Ilt-zh-RFR"/>
                    <constraint firstItem="N8j-qe-xKh" firstAttribute="centerY" secondItem="v6t-Ly-Ee5" secondAttribute="centerY" id="KBc-Bf-puw"/>
                    <constraint firstItem="Th8-hk-GgO" firstAttribute="leading" secondItem="v6t-Ly-Ee5" secondAttribute="leading" id="Vit-rR-XeT"/>
                    <constraint firstItem="v6t-Ly-Ee5" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="6" id="bQb-H0-XmF"/>
                    <constraint firstAttribute="trailing" secondItem="N8j-qe-xKh" secondAttribute="trailing" constant="10" id="cc6-Ew-kE6"/>
                    <constraint firstAttribute="bottom" secondItem="Th8-hk-GgO" secondAttribute="bottom" constant="10" id="dyV-eR-3f8"/>
                    <constraint firstItem="Th8-hk-GgO" firstAttribute="trailing" secondItem="v6t-Ly-Ee5" secondAttribute="trailing" id="hnm-UD-b2T"/>
                    <constraint firstItem="N8j-qe-xKh" firstAttribute="leading" secondItem="oPX-NK-sYd" secondAttribute="trailing" constant="5" id="qCT-0z-U1G"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="contentLb" destination="Th8-hk-GgO" id="4LE-iI-FKc"/>
                <outlet property="statusImg" destination="oPX-NK-sYd" id="03Z-Ws-shK"/>
                <outlet property="statusLb" destination="N8j-qe-xKh" id="Ak7-3j-I1w"/>
                <outlet property="titleLb" destination="v6t-Ly-Ee5" id="LTG-xn-tgf"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="107.14285714285714"/>
        </tableViewCell>
    </objects>
</document>
