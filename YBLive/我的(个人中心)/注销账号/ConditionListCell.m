//
//  ConditionListCell.m
//  YBLive
//
//  Created by ybRRR on 2020/6/16.
//  Copyright © 2020 cat. All rights reserved.
//

#import "ConditionListCell.h"

@implementation ConditionListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(ConditionListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath {
    ConditionListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ConditionListCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"ConditionListCell" owner:nil options:nil]objectAtIndex:0];
    }
    return cell;
}
-(void)setInfoDic:(NSDictionary *)infoDic
{
    _titleLb.text = minstr([infoDic valueForKey:@"title"]);
    _contentLb.text =minstr([infoDic valueForKey:@"content"]);
    if ([minstr([infoDic valueForKey:@"is_ok"]) isEqual:@"1"]) {
        _statusImg.image = [UIImage imageNamed:@"condition_通过"];
        _statusLb.textColor = [UIColor grayColor];
        _statusLb.text = YZMsg(@"已通过");
    }else{
        _statusImg.image = [UIImage imageNamed:@"condition_未通过"];
        _statusLb.textColor = normalColors;
        _statusLb.text = YZMsg(@"未通过");

    }
}
@end
