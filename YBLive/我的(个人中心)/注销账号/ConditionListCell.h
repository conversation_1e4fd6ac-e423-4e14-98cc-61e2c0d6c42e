//
//  ConditionListCell.h
//  YBLive
//
//  Created by ybRRR on 2020/6/16.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface ConditionListCell : UITableViewCell
@property (strong, nonatomic) IBOutlet UILabel *titleLb;
@property (strong, nonatomic) IBOutlet UILabel *contentLb;
@property (strong, nonatomic) IBOutlet UIImageView *statusImg;
@property (strong, nonatomic) IBOutlet UILabel *statusLb;

@property (nonatomic,strong)NSDictionary *infoDic;

+(ConditionListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath*)indexPath;
@end

NS_ASSUME_NONNULL_END
