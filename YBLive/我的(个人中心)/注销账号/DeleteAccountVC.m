//
//  DeleteAccountVC.m
//  YBLive
//
//  Created by ybRRR on 2020/6/15.
//  Copyright © 2020 cat. All rights reserved.
//

#import "DeleteAccountVC.h"
#import <WebKit/WebKit.h>

@interface DeleteAccountVC ()<WKNavigationDelegate>

@property (nonatomic,strong) WKWebView *WKWebView;
@property (nonatomic,strong) CALayer *progresslayer;

@end

@implementation DeleteAccountVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"注销账号");
    
    self.WKWebView = [[WKWebView alloc] initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-50-ShowDiff)];
    self.WKWebView.navigationDelegate = self;
    [self.view addSubview:self.WKWebView];
    self.progresslayer = [[CALayer alloc]init];
    self.progresslayer.frame = CGRectMake(0, 0, _window_width*0.1, 2);
    self.progresslayer.backgroundColor = normalColors.CGColor;
    [self.WKWebView.layer addSublayer:self.progresslayer];
    
    [self.WKWebView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionNew context:nil];

    [self.WKWebView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:_urls]]];

    
    [self setBottomButton];
}
-(void)setBottomButton{

    UIButton *cancelBtn = [UIButton buttonWithType:0];
    cancelBtn.frame = CGRectMake(_window_width/2+20, _window_height-50-ShowDiff, (_window_width/2)*0.8, 40);
    [cancelBtn setTitle:YZMsg(@"取消") forState:0];
    [cancelBtn setTitleColor:[UIColor whiteColor] forState:0];
    cancelBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [cancelBtn setBackgroundColor:normalColors];
    cancelBtn.layer.cornerRadius = 20;
    cancelBtn.layer.masksToBounds = YES;
    [cancelBtn addTarget:self action:@selector(cancelBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:cancelBtn];
    
    UIButton *sureBtn = [UIButton buttonWithType:0];
    sureBtn.frame = CGRectMake(_window_width/2-20-(_window_width/2)*0.8, _window_height-50-ShowDiff, (_window_width/2)*0.8, 40);
    [sureBtn setTitle:YZMsg(@"确认注销") forState:0];
    [sureBtn setTitleColor:normalColors forState:0];
    sureBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [sureBtn setBackgroundColor:[UIColor whiteColor]];
    sureBtn.layer.borderColor = normalColors.CGColor;
    sureBtn.layer.borderWidth = 1;
    sureBtn.layer.cornerRadius = 20;
    sureBtn.layer.masksToBounds = YES;
    [sureBtn addTarget:self action:@selector(sureBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:sureBtn];

}
-(void)cancelBtnClick{
    [[MXBADelegate sharedAppDelegate]popViewController:YES];
}

-(void)sureBtnClick{
    YBWeakSelf;
    UIAlertController *alertControl = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"点击确认注销后,账号将立即注销") preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确认") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [weakSelf deleteAccount];
    }];
    [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    [sureAction setValue:normalColors forKey:@"_titleTextColor"];
    [alertControl addAction:cancelAction];
    [alertControl addAction:sureAction];
    [[[MXBADelegate sharedAppDelegate] topViewController]presentViewController:alertControl animated:YES completion:nil];
}

// 观察者
-(void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context{
    
    
    if ([keyPath isEqualToString:@"estimatedProgress"]) {
        
        self.progresslayer.opacity = 1;
        float floatNum = [[change objectForKey:@"new"] floatValue];
        self.progresslayer.frame = CGRectMake(0, 0, _window_width*floatNum, 2);
        if (floatNum == 1) {
            
            __weak __typeof(self)weakSelf = self;
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                
                weakSelf.progresslayer.opacity = 0;
                
            });
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                
                weakSelf.progresslayer.frame = CGRectMake(0, 0, 0, 3);
            });
        }
        
    }else{
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
    
}
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler{
    
    NSString *url = navigationAction.request.URL.absoluteString;
    if (navigationAction.targetFrame.isMainFrame) {
        NSLog(@"target is main ... %@",url);
        if (navigationAction.sourceFrame.mainFrame) {
            NSLog(@"source is main...%@",url);
            
        } else {
            NSLog(@"source is not main...%@",url);
        }
    } else {
        NSLog(@"target is not main ... %@",url);
    }
    decisionHandler(WKNavigationActionPolicyAllow);
    NSLog(@"在发送请求之前：%@",navigationAction.request.URL.absoluteString);
}

#pragma mark----删除账号-------
-(void)deleteAccount{
        NSString *url = [purl stringByAppendingFormat:@"?service=Login.cancelAccount"];
        
        NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken], @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]]};
        NSString *sign = [YBToolClass dynamicSortString:signdic];
        NSDictionary *dic = @{
                              @"uid":[Config getOwnID],
                              @"token":[Config getOwnToken],
                              @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                              @"sign":sign
                              };
        
        [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
            if ([code isEqual:@"0"]) {
                [MBProgressHUD showError:msg];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [[YBToolClass sharedInstance] quitLogin];
                });

            }else{
                [MBProgressHUD showError:msg];
            }
        } Fail:^(id fail) {
            
        }];

}

-(void)dealloc{
    NSLog(@"WKWebView dealloc------------");
    [self.WKWebView removeObserver:self forKeyPath:@"estimatedProgress"];

}

@end
