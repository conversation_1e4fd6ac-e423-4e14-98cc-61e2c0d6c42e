//
//  DeleteConditionVC.m
//  YBLive
//
//  Created by ybRRR on 2020/6/15.
//  Copyright © 2020 cat. All rights reserved.
//

#import "DeleteConditionVC.h"
#import "ConditionListCell.h"
#import "DeleteAccountVC.h"
@interface DeleteConditionVC ()<UITableViewDelegate, UITableViewDataSource>
{
    UIScrollView *_backScroll;
    UILabel *tapsLb;
    UIButton *nextBtn;
    
}
@property (nonatomic, strong)UITableView *listTable;
@property (nonatomic, strong)NSArray *listArr;
@end

@implementation DeleteConditionVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"注销条件");
    self.listArr = [NSArray array];
    [self requestData];
    
    tapsLb = [[UILabel alloc]init];
    tapsLb.frame = CGRectMake(18,  64+statusbarHeight+12, _window_width-24, 34);
    tapsLb.font = [UIFont boldSystemFontOfSize:14];
    tapsLb.textColor =[UIColor blackColor];
    tapsLb.text = YZMsg(@"满足以下条件，才能注销当前账户");
    tapsLb.numberOfLines = 0;
    [self.view addSubview:tapsLb];
    
    [self.view addSubview:self.listTable];
    [self setBottomButton];

}
-(UITableView *)listTable{
    if (!_listTable) {
        _listTable = [[UITableView alloc]initWithFrame:CGRectMake(12, tapsLb.bottom+10, _window_width-24, _window_height-64-statusbarHeight-65-ShowDiff-30) style:UITableViewStylePlain];
        _listTable.delegate = self;
        _listTable.dataSource = self;
        _listTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _listTable.rowHeight = UITableViewAutomaticDimension;
        _listTable.estimatedRowHeight = 200;
    }
    return _listTable;
}
-(void)requestData{
    NSString *url = [purl stringByAppendingFormat:@"?service=Login.getCancelCondition"];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken]
    };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            NSDictionary *infos = [[data valueForKey:@"info"] firstObject];
            if ([minstr([infos valueForKey:@"can_cancel"]) isEqual:@"0"]) {
                nextBtn.alpha = 0.6;
                nextBtn.userInteractionEnabled = NO;
            }
            self.listArr = [infos valueForKey:@"list"];
            [self.listTable reloadData];
        }
    } Fail:^(id fail) {
        
    }];
}
-(void)setBottomButton{

    nextBtn = [UIButton buttonWithType:0];
    nextBtn.frame = CGRectMake(30, _window_height-50-ShowDiff, _window_width-60, 40);
    [nextBtn setTitle:YZMsg(@"下一步") forState:0];
    [nextBtn setTitleColor:[UIColor whiteColor] forState:0];
    nextBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    [nextBtn setBackgroundColor:normalColors];
    nextBtn.layer.cornerRadius = 20;
    nextBtn.layer.masksToBounds = YES;
    [nextBtn addTarget:self action:@selector(nextBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:nextBtn];
}
//下一步
-(void)nextBtnClick{
    DeleteAccountVC *delete = [[DeleteAccountVC alloc]init];
    delete.urls = self.urls;
    [[MXBADelegate sharedAppDelegate]pushViewController:delete animated:YES];
}

#pragma makr --  UITableViewDelegate ---
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.listArr.count;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    ConditionListCell *cell = [ConditionListCell cellWithTab:tableView andIndexPath:indexPath];
    cell.infoDic = self.listArr[indexPath.row];
    return cell;
}
@end
