//
//  OtherRoomViewController.m
//  YBLive
//
//  Created by IOS1 on 2019/4/26.
//  Copyright © 2019 cat. All rights reserved.
//

#import "OtherRoomViewController.h"
#import "RoomUserTypeCell.h"
#import "RoomUserListViewController.h"
@interface OtherRoomViewController ()<UITableViewDelegate,UITableViewDataSource>{
    UITableView *leftTable;
    NSArray *listArray;
}


@end

@implementation OtherRoomViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = _titleStr;
    listArray = @[YZMsg(@"禁言用户"),YZMsg(@"拉黑用户")];
    leftTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-ShowDiff) style:0];
    leftTable.delegate = self;
    leftTable.dataSource = self;
    leftTable.separatorStyle = 0;
    leftTable.backgroundColor = RGB_COLOR(@"#fafafa", 1);
    [self.view addSubview:leftTable];

}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return listArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    RoomUserTypeCell *cell = [tableView dequeueReusableCellWithIdentifier:@"RoomUserTypeCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"RoomUserTypeCell" owner:nil options:nil] lastObject];
    }
    cell.titleL.text = listArray[indexPath.row];
    return cell;
    
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    RoomUserListViewController *vc = [[RoomUserListViewController alloc]init];
    vc.type = indexPath.row+1;
    vc.titleStr = listArray[indexPath.row];
    vc.liveuid = _liveuid;
    [[MXBADelegate sharedAppDelegate]pushViewController:vc animated:YES];

}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50.0;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
