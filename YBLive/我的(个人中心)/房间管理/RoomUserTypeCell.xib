<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14460.31" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14460.20"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="RoomUserTypeCELL" id="KGk-i7-Jjw" customClass="RoomUserTypeCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fTe-1q-whu">
                        <rect key="frame" x="15" y="14.5" width="31" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="profit_right.png" translatesAutoresizingMaskIntoConstraints="NO" id="ixn-5w-60k">
                        <rect key="frame" x="297" y="14.5" width="15" height="15"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="15" id="bsJ-ob-p8v"/>
                            <constraint firstAttribute="height" constant="15" id="qGg-kf-cpL"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Wyj-pg-eg1">
                        <rect key="frame" x="15" y="42.5" width="290" height="1"/>
                        <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="LEJ-1o-AAx"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="Wyj-pg-eg1" firstAttribute="leading" secondItem="fTe-1q-whu" secondAttribute="leading" id="9D7-ud-Xfj"/>
                    <constraint firstAttribute="trailing" secondItem="ixn-5w-60k" secondAttribute="trailing" constant="8" id="Jsb-Dg-OnQ"/>
                    <constraint firstItem="fTe-1q-whu" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="QLK-21-RGM"/>
                    <constraint firstItem="fTe-1q-whu" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="Qoo-m3-kgZ"/>
                    <constraint firstItem="ixn-5w-60k" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="RcI-ck-Szr"/>
                    <constraint firstAttribute="trailing" secondItem="Wyj-pg-eg1" secondAttribute="trailing" constant="15" id="tw4-rc-dd8"/>
                    <constraint firstAttribute="bottom" secondItem="Wyj-pg-eg1" secondAttribute="bottom" id="yzf-sU-ge4"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="titleL" destination="fTe-1q-whu" id="cIB-Dj-Ck6"/>
            </connections>
            <point key="canvasLocation" x="129.59999999999999" y="136.73163418290855"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="profit_right.png" width="72" height="72"/>
    </resources>
</document>
