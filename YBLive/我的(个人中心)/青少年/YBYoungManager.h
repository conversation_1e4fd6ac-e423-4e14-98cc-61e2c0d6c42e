//
//  YBYoungManager.h
//  iphoneLive
//
//  Created by YB007 on 2022/6/2.
//  Copyright © 2022 cat. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger,YoungFrom) {
    YoungFrom_Home,     // 首页
    YoungFrom_Center,   // 个人主页
    YoungFrom_SaveData,
};

@interface YBYoungManager : NSObject
@property(nonatomic,assign)BOOL isOpenYoung;
+(instancetype)shareInstance;

-(void)checkYoungStatus:(YoungFrom)youngFrom;



#pragma mark - 小窗
-(void)showYoungSmallPop;
-(void)destroySamllPop;
-(void)smallEnterYoungModel;

-(void)showTipsWindowWithMsg:(NSString *)tipStr;
-(void)changeYoungState:(BOOL)stateBool;
@end


