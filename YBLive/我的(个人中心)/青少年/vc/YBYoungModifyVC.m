//
//  YBYoungModifyVC.m
//  iphoneLive
//
//  Created by YB007 on 2022/6/2.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBYoungModifyVC.h"

#import "RKCodeInputView.h"

@interface YBYoungModifyVC ()

@property(nonatomic,strong)RKCodeInputView *civOld;     //旧密码
@property(nonatomic,strong)RKCodeInputView *civNew;     //新密码
@property(nonatomic,strong)RKCodeInputView *civSure;    //确认密码

@property(nonatomic,strong)UIButton *modifyBtn;

@end

@implementation YBYoungModifyVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.titleL.text = YZMsg(@"修改密码");
    self.titleL.textColor = RGB_COLOR(@"#323232", 1);
//    [self.leftBtn setImage:[UIImage imageNamed:@"pubBlack_back"] forState:0];
//    self.subNavi.backgroundColor = UIColor.whiteColor;
    self.view.backgroundColor = UIColor.whiteColor;
    
    YBWeakSelf;
    NSArray *titleA = @[@"请输入当前密码",@"请输入新的密码",@"请确定新的密码"];
    MASViewAttribute *mas_top = self.naviView.mas_bottom;
    for (int i =0 ; i<titleA.count; i++) {
        UIView *itemV = [[UIView alloc]init];
        itemV.backgroundColor = UIColor.clearColor;
        [self.view addSubview:itemV];
        [itemV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
            make.left.equalTo(self.view.mas_left).offset(15);
            make.top.equalTo(mas_top).offset(20);
        }];
        mas_top = itemV.mas_bottom;
        
        UILabel *titL = [[UILabel alloc]init];
        titL.text = YZMsg(titleA[i]);
        titL.font = SYS_Font(15);
        titL.textColor = RGB_COLOR(@"#323232", 1);
        [itemV addSubview:titL];
        [titL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(itemV);
            make.height.mas_equalTo(20);
        }];
        
        RKCodeInputView *codeIV = [[RKCodeInputView alloc]init];
        codeIV.contentView.backgroundColor = UIColor.whiteColor;
        codeIV.secureTextEntry = YES;
        [itemV addSubview:codeIV];
        [codeIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(itemV.mas_width).multipliedBy(0.8);
            make.left.equalTo(itemV);
            make.height.mas_equalTo(40);
            make.top.equalTo(titL.mas_bottom).offset(10);
            make.bottom.equalTo(itemV);
        }];
        
        [itemV layoutIfNeeded];
        [codeIV updateSubViews];
        if (i == 0) {
            _civOld = codeIV;
        }else if(i == 1){
            _civNew = codeIV;
        }else{
            _civSure = codeIV;
        }
        codeIV.changeEvent = ^{
            [weakSelf changeEvent];
        };
    }
    
    _modifyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _modifyBtn.titleLabel.font = SYS_Font(15);
    _modifyBtn.layer.cornerRadius = 5;
    _modifyBtn.layer.masksToBounds = YES;
    [_modifyBtn addTarget:self action:@selector(clickModifyBtn) forControlEvents:UIControlEventTouchUpInside];
    [_modifyBtn setTitleColor:[UIColor grayColor] forState:0];
    [_modifyBtn setTitle:YZMsg(@"确定修改") forState:0];
    [_modifyBtn setBackgroundColor:RGB_COLOR(@"#d9d9d9", 1)];
    _modifyBtn.enabled = NO;
    [self.view addSubview:_modifyBtn];
    [_modifyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.view.mas_width).offset(-30);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo(46);
        make.bottom.equalTo(self.view.mas_bottom).offset(-60-ShowDiff);
    }];
    
}

-(void)changeEvent {
    if (_civOld.codeText.length>=4 &&
        _civNew.codeText.length>=4 &&
        _civSure.codeText.length>=4) {
        
        _modifyBtn.enabled = YES;
        _modifyBtn.selected = YES;
        [_modifyBtn setBackgroundColor:normalColors];
        [_modifyBtn setTitleColor:[UIColor whiteColor] forState:0];

    }else{
        _modifyBtn.enabled = NO;
        _modifyBtn.selected = NO;
        [_modifyBtn setBackgroundColor:RGB_COLOR(@"#d9d9d9", 1)];
        [_modifyBtn setTitleColor:[UIColor grayColor] forState:0];

    }
}

-(void)clickModifyBtn {
    if (![_civNew.codeText isEqual:_civSure.codeText]) {
        [MBProgressHUD showError:YZMsg(@"输入密码不一致，请重新输入")];
        return;
    }
    YBWeakSelf;
    NSLog(@"old:%@,new:%@,sure:%@",_civOld.codeText,_civNew.codeText,_civSure.codeText);
    NSString *url = [purl stringByAppendingFormat:@"?service=User.updateTeenagerPassword"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"oldpassword":_civOld.codeText,
                          @"password":_civNew.codeText
    };

    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            [MBProgressHUD showError:msg];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf.navigationController popViewControllerAnimated:YES];
            });
        }
        [MBProgressHUD showError:msg];
    } Fail:^(id fail) {
        
    }];

}

@end
