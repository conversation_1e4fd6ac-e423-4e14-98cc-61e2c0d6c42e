//
//  RKCodeView.h
//  YBSoul
//
//  Created by YB007 on 2021/3/9.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger,CodeUIStyle) {
    CodeUIStyle_Block,
    CodeUIStyle_Line,
};

@interface RKCodeView : UIView

///文字
@property (nonatomic, strong) NSString *text;

///显示光标 默认关闭
@property (nonatomic) BOOL showCursor;

@property(nonatomic,assign)CodeUIStyle uiStyle;

@property(nonatomic,strong)UIFont *textFont;
@property(nonatomic,strong)UIColor *textCor;
/// 密文
@property(nonatomic,assign)BOOL secureTextEntry;
-(void)updateCodeView;
@end

NS_ASSUME_NONNULL_END
