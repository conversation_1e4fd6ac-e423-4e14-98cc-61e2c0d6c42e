//
//  RKCodeInputView.h
//  YBSoul
//
//  Created by YB007 on 2021/3/9.
//

#import <UIKit/UIKit.h>

#import "RKCodeView.h"

NS_ASSUME_NONNULL_BEGIN

typedef void (^inputFinishBlock)(void);

@interface RKCodeInputView : UIView

///验证码文字
@property (strong, nonatomic) NSString *codeText;

///设置验证码位数 默认 4 位
@property (nonatomic) NSInteger codeCount;

///验证码数字之间的间距 默认 35
@property (nonatomic) CGFloat codeSpace;

@property(nonatomic,assign)CodeUIStyle uiStyle;

@property(nonatomic,copy)inputFinishBlock finishEvent;

@property(nonatomic,copy)inputFinishBlock changeEvent;
///放置小格子
@property (strong, nonatomic) UIView *contentView;

/// 键盘类型
@property(nonatomic,assign)UIKeyboardType keyboardType;
@property(nonatomic,strong)UIFont *textFont;
@property(nonatomic,strong)UIColor *textCor;

@property(nonatomic,assign)BOOL becomeFirstRes;

/// 密文
@property(nonatomic,assign)BOOL secureTextEntry;

- (void)updateSubViews;
/// 清空
-(void)clearText;

@end

NS_ASSUME_NONNULL_END
