//
//  YBYoungSetVC.m
//  iphoneLive
//
//  Created by YB007 on 2022/6/2.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBYoungSetVC.h"
#import "RKCodeInputView.h"
#import "AppDelegate.h"
#import "ZYTabBarController.h"

@interface YBYoungSetVC ()
{
    BOOL isFirstSet;
    NSString *setType;
}
@property(nonatomic,strong)RKCodeInputView *codeInputView;  /// 输入框


@end

@implementation YBYoungSetVC
-(void)doReturn
{
    if (isFirstSet && _isHaveSet) {
        UIApplication *app =[UIApplication sharedApplication];
        AppDelegate *app2 = (AppDelegate*)app.delegate;
        ZYTabBarController *root = [[ZYTabBarController alloc]init];
        UINavigationController *nav = [[UINavigationController alloc]initWithRootViewController:root];
        app2.window.rootViewController = nav;

        [[NSNotificationCenter defaultCenter]postNotificationName:@"openYoung_notification" object:nil];

    }else{
        [self.navigationController popViewControllerAnimated:YES];

    }
}
- (void)viewDidLoad {
    [super viewDidLoad];
    setType = @"1";

    self.titleL.textColor = RGB_COLOR(@"#323232", 1);
//    [self.leftBtn setImage:[UIImage imageNamed:@"pubBlack_back"] forState:0];
//    self.subNavi.backgroundColor = UIColor.whiteColor;
    self.view.backgroundColor = UIColor.whiteColor;
    
    UILabel *topTitleL = [[UILabel alloc]init];
    topTitleL.font = [UIFont boldSystemFontOfSize:18];
    topTitleL.textColor = RGB_COLOR(@"#323232", 1);
    topTitleL.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:topTitleL];
    [topTitleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.centerX.equalTo(self.view);
        make.height.mas_equalTo(30);
        make.top.equalTo(self.naviView.mas_bottom).offset(20);
    }];
    
    self.codeInputView = [[RKCodeInputView alloc]init];
    _codeInputView.contentView.backgroundColor = UIColor.whiteColor;
    _codeInputView.secureTextEntry = YES;
    [self.view addSubview:_codeInputView];
    [_codeInputView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.view.mas_width).multipliedBy(0.6);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo(40);
        make.top.equalTo(topTitleL.mas_bottom).offset(15);
    }];
    YBWeakSelf;
    _codeInputView.finishEvent = ^{
        NSLog(@"input:%@",weakSelf.codeInputView.codeText);
        if (weakSelf.isfromClose) {
            //关闭青少年模式
            [weakSelf closeYoung];
        }else{
            //开启青少年模式
            //开启青少年模式输入密码
            [weakSelf setTeenagerPassword];

        }
    };
    // 更新布局
    [self.view layoutIfNeeded];
    [_codeInputView updateSubViews];
    
    if (!_isHaveSet) {
        self.titleL.text = YZMsg(@"设置密码");
        topTitleL.text = YZMsg(@"请设置新密码");
        isFirstSet = YES;
    }else{
        self.titleL.text = YZMsg(@"输入密码");
        topTitleL.text = YZMsg(@"请输入密码");
    }
    
}
-(void)closeYoung{
    
    NSString *url = [purl stringByAppendingFormat:@"?service=User.closeTeenager"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"password":_codeInputView.codeText
    };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [MBProgressHUD showError:msg];

        if ([code isEqual:@"0"]) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [[YBYoungManager shareInstance]changeYoungState:NO];
                if (self.yongSetBlock) {
                    self.yongSetBlock(NO, YES);
                }

//                [self.navigationController popToRootViewControllerAnimated:YES];
                UIApplication *app =[UIApplication sharedApplication];
                AppDelegate *app2 = (AppDelegate*)app.delegate;
                ZYTabBarController *root = [[ZYTabBarController alloc]init];
                UINavigationController *nav = [[UINavigationController alloc]initWithRootViewController:root];
                app2.window.rootViewController = nav;
                [[NSNotificationCenter defaultCenter]postNotificationName:@"closeYoung_notification" object:nil];

            });
        }
    } Fail:^(id fail) {
        
    }];

}
-(void)showFirtTips{
    YBWeakSelf;
    UIAlertController *showAlert = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"密码设置成功，请记住您设置的密码") preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *resetAction = [UIAlertAction actionWithTitle:YZMsg(@"重新设置") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        setType = @"0";
        [_codeInputView clearText];
    }];
    UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        UIApplication *app =[UIApplication sharedApplication];
        AppDelegate *app2 = (AppDelegate*)app.delegate;
        ZYTabBarController *root = [[ZYTabBarController alloc]init];
        UINavigationController *nav = [[UINavigationController alloc]initWithRootViewController:root];
        app2.window.rootViewController = nav;

        [[NSNotificationCenter defaultCenter]postNotificationName:@"openYoung_notification" object:nil];

    }];
    [resetAction setValue:RGB(125,125,125) forKey:@"_titleTextColor"];
    [sureAction setValue:normalColors forKey:@"_titleTextColor"];

    [showAlert addAction:resetAction];
    [showAlert addAction:sureAction];
    [[[MXBADelegate sharedAppDelegate] topViewController]presentViewController:showAlert animated:YES completion:nil];
}
-(void)setTeenagerPassword{
    YBWeakSelf;
    NSString *url = [purl stringByAppendingFormat:@"?service=User.setTeenagerPassword"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"password":_codeInputView.codeText,
                          @"type":setType
    };

    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            [[YBYoungManager shareInstance]changeYoungState:YES];
            if (self.yongSetBlock) {
                self.yongSetBlock(YES, YES);
            }

            if (!weakSelf.isHaveSet) {
                //开启青少年模式第一次设置密码
                [weakSelf showFirtTips];
                weakSelf.isHaveSet = YES;
            }else{
//                [self.navigationController popToRootViewControllerAnimated:YES];
                UIApplication *app =[UIApplication sharedApplication];
                AppDelegate *app2 = (AppDelegate*)app.delegate;
                ZYTabBarController *root = [[ZYTabBarController alloc]init];
                UINavigationController *nav = [[UINavigationController alloc]initWithRootViewController:root];
                app2.window.rootViewController = nav;

                [[NSNotificationCenter defaultCenter]postNotificationName:@"openYoung_notification" object:nil];
            }
        }
    } Fail:^(id fail) {
        
    }];
}
@end
