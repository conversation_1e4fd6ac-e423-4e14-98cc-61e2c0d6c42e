//
//  YBYoungManager.m
//  iphoneLive
//
//  Created by YB007 on 2022/6/2.
//  Copyright © 2022 cat. All rights reserved.
//

#import "YBYoungManager.h"
#import "YBYoungSmall.h"
#import "YBYoungModeVC.h"

@interface YBYoungManager()

@property(nonatomic,strong)YBYoungSmall *smallPop;

@property(nonatomic,assign)BOOL ispwd;              // 是否设置密码
@property(nonatomic,assign)BOOL isstate;            // 是否开启青少年模式
@property(nonatomic,assign)NSString *is_tip;        //是否提示用户弹窗显示青少年模式下不能继续使用app   0 否  1 是
@property(nonatomic,assign)NSString *tips;          //弹窗显示青少年模式下不能继续使用app的提示语
@property(nonatomic,strong)NSString *promptStr;     // 小窗提示内容
@property(nonatomic,strong)NSArray *youngList;      // 青少年模式说明

@end

@implementation YBYoungManager

static YBYoungManager *_youngManager = nil;

+(instancetype)shareInstance{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _youngManager = [[super allocWithZone:NULL] init];
    });
    return _youngManager;
}
+ (instancetype)allocWithZone:(struct _NSZone *)zone{
    return [self shareInstance];
}

- (BOOL)isOpenYoung{
    return self.isstate;
}
-(void)changeYoungState:(BOOL)stateBool
{
    self.isstate = stateBool;
}
/// 检测
-(void)checkYoungStatus:(YoungFrom)youngFrom;{
    
    /// 请求接口【是否开启、是否设置，】
    NSString *url = [purl stringByAppendingFormat:@"?service=User.checkTeenager"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
    };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            NSDictionary *infoDic = [[data valueForKey:@"info"] firstObject];
            // 是否设置密码
            self.ispwd = [minstr([infoDic valueForKey:@"is_setpassword"])intValue];
            // 是否开启青少年模式
            self.isstate = [minstr([infoDic valueForKey:@"status"])intValue];
//            // 是否提示用户弹窗显示青少年模式下不能继续使用app
            self.is_tip = minstr([infoDic valueForKey:@"is_tip"]);
            self.tips =minstr([infoDic valueForKey:@"tips"]);
            // 小窗提示语
            self.promptStr =[common getTeenager_des];// YZMsg(@"为呵护未成年人健康成长，特别推出 青少年模式，该模式下部分功能无法 正常使用。请监护人主动选择，并设 置监护密码。");//minstr([infoDic valueForKey:@"prompt"]);
            // 青少年模式说明数组
            self.youngList =@[@{@"thumb":@"young-lock",@"name":YZMsg(@"开启青少年模式，需先设置独立密码")},
                              @{@"thumb":@"young-recharge",@"name":YZMsg(@"无法进行充值、打赏等操作")},
                              @{@"thumb":@"young-time",@"name":YZMsg(@"自动开启时间锁，每天使用时长不超过 40分钟，每日晚22时至次日6时期间 无法使用")},
            ];
            if ([self.is_tip isEqual:@"1"]) {
                [self showTipsWindowWithMsg:self.tips];
                return;
            }
            
            if (youngFrom == YoungFrom_Home) {
                if (!self.isstate) {
                    [self showYoungSmallPop];
                }
            }else if(youngFrom == YoungFrom_Center){
                [self enterYoungMode];
            }else{
                return;
            }
            if (self.isstate == 1) {
                [[NSNotificationCenter defaultCenter]postNotificationName:@"openYoung_notification" object:nil];
            }

        }else{
            [MBProgressHUD showError:msg];
        }
        } Fail:^(id fail) {
            
        }];
}
#pragma mark - 开启青少年模式
-(void)enterYoungMode {
    YBYoungModeVC *modeVC = [[YBYoungModeVC alloc]init];
    modeVC.ispwd = self.ispwd;
    modeVC.isstate = self.isstate;
    modeVC.youngList = self.youngList;
    [[MXBADelegate sharedAppDelegate] pushViewController:modeVC animated:YES];
}

#pragma mark - 小窗开始
-(void)showYoungSmallPop {
    [self destroySamllPop];
    _smallPop = [YBYoungSmall showYoungPop:self.promptStr];
}
-(void)destroySamllPop {
    if (_smallPop) {
        [_smallPop removeFromSuperview];
        _smallPop = nil;
    }
}
-(void)smallEnterYoungModel;{
    [self destroySamllPop];
    [self enterYoungMode];
}
#pragma mark - 小窗结束
-(void)showTipsWindowWithMsg:(NSString *)tipStr{
    UIAlertController *showAlert = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:tipStr preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"知道了") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        exit(0);
    }];
    UIAlertAction *closeAction = [UIAlertAction actionWithTitle:YZMsg(@"去关闭") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
        [self enterYoungMode];
        [[NSNotificationCenter defaultCenter]postNotificationName:@"closeYoung_notification" object:nil];

    }];
    [cancelAction setValue:UIColor.grayColor forKey:@"_titleTextColor"];
    [closeAction setValue:normalColors forKey:@"_titleTextColor"];
    [showAlert addAction:cancelAction];
    [showAlert addAction:closeAction];
    [[[MXBADelegate sharedAppDelegate] topViewController] presentViewController:showAlert animated:YES completion:nil];
}

@end
