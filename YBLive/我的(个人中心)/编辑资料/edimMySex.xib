<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="13529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13527"/>
        <capability name="Constraints to layout margins" minToolsVersion="6.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="微软雅黑.ttf">
            <string>MicrosoftYaHei</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="gZf-KJ-cBC" customClass="sexCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="gZf-KJ-cBC" id="aCE-h9-pVB">
                <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="性别" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qKl-9h-UbF">
                        <rect key="frame" x="15" y="11" width="32" height="22"/>
                        <fontDescription key="fontDescription" name="MicrosoftYaHei" family="Microsoft YaHei" pointSize="16"/>
                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="global_male" translatesAutoresizingMaskIntoConstraints="NO" id="pZ5-Ff-dSs">
                        <rect key="frame" x="274" y="12" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="DKQ-cS-yGs"/>
                            <constraint firstAttribute="height" constant="20" id="dTl-oM-SQU"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="pZ5-Ff-dSs" firstAttribute="centerY" secondItem="aCE-h9-pVB" secondAttribute="centerY" id="1I9-hU-hoq"/>
                    <constraint firstItem="qKl-9h-UbF" firstAttribute="centerY" secondItem="aCE-h9-pVB" secondAttribute="centerY" id="Qij-tS-thu"/>
                    <constraint firstAttribute="trailingMargin" secondItem="pZ5-Ff-dSs" secondAttribute="trailing" constant="10" id="ViZ-kq-byU"/>
                </constraints>
            </tableViewCellContentView>
            <constraints>
                <constraint firstItem="qKl-9h-UbF" firstAttribute="leading" secondItem="gZf-KJ-cBC" secondAttribute="leading" constant="15" id="nSj-iI-NwW"/>
            </constraints>
            <connections>
                <outlet property="imageV" destination="pZ5-Ff-dSs" id="0Cc-jf-zI0"/>
                <outlet property="sexLabel" destination="qKl-9h-UbF" id="xfI-Y1-dXG"/>
            </connections>
            <point key="canvasLocation" x="150" y="235"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="global_male" width="23" height="23"/>
    </resources>
</document>
