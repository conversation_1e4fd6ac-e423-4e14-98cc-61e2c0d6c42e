#import "iconC.h"
#import "Utils.h"
#import "UIImageView+WebCache.h"
#import "SDWebImage/UIButton+WebCache.h"
#import "UIButton+WebCache.h"
#import <SDWebImage/SDImageCache.h>

@interface iconC ()<UIImagePickerControllerDelegate,UINavigationControllerDelegate>
{
    NSString  *filePath;
    NSURL *imageURL;
    int setvisicon;
    NSString *headerUrl;
}
@property (weak, nonatomic) IBOutlet UIButton *iconBTN;
- (IBAction)paizhao:(id)sender;
- (IBAction)xiangce:(id)sender;
- (IBAction)fanhui:(id)sender;
- (IBAction)iconBTNNNN:(id)sender;
@property (weak, nonatomic) IBOutlet UIButton *paizhaob;
@property (weak, nonatomic) IBOutlet UIButton *xiangceb;
@end
@implementation iconC
- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    LiveUser *user = [[LiveUser alloc] init];
    user =  [Config myProfile];
    NSURL *url = [NSURL URLWithString:user.avatar];
    [self.iconBTN sd_setBackgroundImageWithURL:url forState:UIControlStateNormal];
    self.iconBTN.enabled = NO;
    
    self.iconBTN.layer.masksToBounds = YES;
    self.iconBTN.layer.cornerRadius = _window_width*0.8/2;
    self.paizhaob.layer.masksToBounds = YES;
    self.paizhaob.layer.cornerRadius = 15;
    self.xiangceb.layer.masksToBounds = YES;
    self.xiangceb.layer.cornerRadius = 15;    
}
- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    setvisicon = 1;
}
- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    setvisicon = 0;
}
- (IBAction)paizhao:(id)sender {
    
    
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.allowsEditing = YES;
    imagePickerController.delegate = self;
    imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
    imagePickerController.allowsEditing = YES;
    imagePickerController.showsCameraControls = YES;
    imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
   // imagePickerController.mediaTypes = @[(NSString *)kUTTypeImage];
    [self presentViewController:imagePickerController animated:YES completion:nil];
}
- (IBAction)xiangce:(id)sender {
    
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.allowsEditing = YES;
    imagePickerController.delegate = self;
    imagePickerController.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    imagePickerController.allowsEditing = YES;
   // imagePickerController.mediaTypes = @[(NSString *)kUTTypeImage];
    [self presentViewController:imagePickerController animated:YES completion:nil];
}
-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    
    
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"])
    {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerEditedImage"];
        NSData *data;
        if (UIImagePNGRepresentation(image) == nil)
        {
            data = UIImageJPEGRepresentation(image, 1.0);
        }
        else
        {
            data = UIImagePNGRepresentation(image);
        }
        
        //图片保存的路径
        NSString * DocumentsPath = [NSHomeDirectory() stringByAppendingPathComponent:@"Documents"];
        
        NSFileManager *fileManager = [NSFileManager defaultManager];
        
        //把刚刚图片转换的data对象拷贝至沙盒中 并保存为image.png
        [fileManager createDirectoryAtPath:DocumentsPath withIntermediateDirectories:YES attributes:nil error:nil];
        [fileManager createFileAtPath:[DocumentsPath stringByAppendingString:@"/image.png"] contents:data attributes:nil];
        
        //得到选择后沙盒中图片的完整路径
        filePath = [[NSString alloc]initWithFormat:@"%@%@",DocumentsPath, @"/image.png"];
        
        imageURL = [NSURL URLWithString:filePath];
        
       /*
        MBProgressHUD *HUD = [Utils createHUD];
        HUD.labelText = @"正在上传头像";
        */
        UIImage *headimage = [UIImage imageWithContentsOfFile:filePath];
        YBWeakSelf;
        [[YBStorageManage shareManage]getCOSInfo:^(int code) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (code == 0) {
                    [weakSelf startUploadCer:headimage];
                }
            });
        }];

//        AFHTTPSessionManager *session = [AFHTTPSessionManager manager];
//        NSString *url = [purl stringByAppendingFormat:@"?service=User.updateAvatar&uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
//
//        [session POST:url parameters:nil headers:nil constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
//            if (image) {
//                [formData appendPartWithFileData:[Utils compressImage:image] name:@"file" fileName:@"duibinaf.png" mimeType:@"image/jpeg"];
//            }
//
//        } progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
//                              NSLog(@"-------%@",responseObject);
//                              NSArray *data = [responseObject valueForKey:@"data"];
//                              NSString *ret = [NSString stringWithFormat:@"%@",[responseObject valueForKey:@"ret"]];
//                              if ([ret isEqual:@"200"]) {
//
//
//                              NSNumber *number = [data valueForKey:@"code"] ;
//                              if([number isEqualToNumber:[NSNumber numberWithInt:0]])
//                              {
//
//            //                      [[SDImageCache sharedImageCache] clearDisk];
//                                  [[SDImageCache sharedImageCache] clearMemory];//可有可无
//
//                                  NSString *info = [[data valueForKey:@"info"] firstObject];
//                                  NSString *avatar = [info valueForKey:@"avatar"];
//                                  NSString *avatar_thumb = [info valueForKey:@"avatar_thumb"];
//                                  [self.iconBTN sd_setBackgroundImageWithURL:[NSURL URLWithString:avatar] forState:UIControlStateNormal];
//                                  LiveUser *user = [[LiveUser alloc]init];
//                                  user.avatar = avatar;
//                                  user.avatar_thumb = avatar_thumb;
//
//                                  [Config updateProfile:user];
//
//
//                              }
//                                   }
//                              else{
//                                  [MBProgressHUD showError:[responseObject valueForKey:@"msg"]];
//                              }
//
//        } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
//            //NSLog(@"-------%@",error);
//            [MBProgressHUD showError:YZMsg(@"上传失败")];
//
//        }];
                
         [picker dismissViewControllerAnimated:YES completion:nil];
    }
}
-(void)startUploadCer:(UIImage *)header{
    [MBProgressHUD showMessage:@""];
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    YBWeakSelf;
    //正面照
    if (header) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_cerFace.png"];
            [[YBStorageManage shareManage]yb_storageImg:header andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                headerUrl = minstr(key);
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf goResServer];
        });
        NSLog(@"任务完成执行");
    });

}
-(void)goResServer{
    NSString *url = [purl stringByAppendingFormat:@"?service=User.updateAvatar"];

    NSDictionary *postDic = @{
        @"uid":[Config getOwnID],
        @"token":[Config getOwnToken],
        @"avatar":headerUrl,
    };
    
    [YBNetworking postWithUrl:url Dic:postDic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
              NSString *info = [[data valueForKey:@"info"] firstObject];
              NSString *avatar = [info valueForKey:@"avatar"];
              NSString *avatar_thumb = [info valueForKey:@"avatar_thumb"];
              [self.iconBTN sd_setBackgroundImageWithURL:[NSURL URLWithString:avatar] forState:UIControlStateNormal];
              LiveUser *user = [[LiveUser alloc]init];
              user.avatar = avatar;
              user.avatar_thumb = avatar_thumb;

              [Config updateProfile:user];

        }

    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];

    }];

}
- (IBAction)fanhui:(id)sender {
    
    [self dismissViewControllerAnimated:YES completion:nil];
    [self.navigationController popViewControllerAnimated:YES];
    
}
- (IBAction)iconBTNNNN:(id)sender {
    
    [self dismissViewControllerAnimated:YES completion:nil];
    [self.navigationController popViewControllerAnimated:YES];

}
- (void)navigationController:(UINavigationController *)navigationController didShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if ([UIDevice currentDevice].systemVersion.floatValue < 11) {
        return;
    }
    if ([viewController isKindOfClass:NSClassFromString(@"PUPhotoPickerHostViewController")]) {
        [viewController.view.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.frame.size.width < 42) {
                [viewController.view sendSubviewToBack:obj];
                *stop = YES;
            }
        }];
    }
}

@end
