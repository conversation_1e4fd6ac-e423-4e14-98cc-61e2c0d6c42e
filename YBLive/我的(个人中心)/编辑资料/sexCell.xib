<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="11762" systemVersion="16A323" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="11757"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="微软雅黑.ttf">
            <string>MicrosoftYaHei</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="KGk-i7-Jjw" customClass="sexCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="性别" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="U6l-vd-gWy">
                        <rect key="frame" x="40" y="13" width="30" height="18"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="30" id="cdN-ms-HSn"/>
                            <constraint firstAttribute="height" constant="18" id="iHY-pe-ZP8"/>
                        </constraints>
                        <fontDescription key="fontDescription" name="MicrosoftYaHei" family="Microsoft YaHei" pointSize="14"/>
                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Xjt-EV-zfp">
                        <rect key="frame" x="282" y="13" width="18" height="18"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="18" id="Bid-rq-WM6"/>
                            <constraint firstAttribute="height" constant="18" id="own-Df-dHs"/>
                        </constraints>
                    </imageView>
                </subviews>
            </tableViewCellContentView>
            <constraints>
                <constraint firstItem="Xjt-EV-zfp" firstAttribute="centerY" secondItem="KGk-i7-Jjw" secondAttribute="centerY" id="aNt-ng-5uT"/>
                <constraint firstItem="U6l-vd-gWy" firstAttribute="centerY" secondItem="KGk-i7-Jjw" secondAttribute="centerY" id="h9G-GC-b0Z"/>
                <constraint firstItem="U6l-vd-gWy" firstAttribute="leading" secondItem="KGk-i7-Jjw" secondAttribute="leading" constant="40" id="oPd-fp-gAz"/>
                <constraint firstAttribute="trailing" secondItem="Xjt-EV-zfp" secondAttribute="trailing" constant="20" id="zv3-NF-zzg"/>
            </constraints>
            <point key="canvasLocation" x="197" y="221"/>
        </tableViewCell>
        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" id="k4c-Bk-rqH">
            <rect key="frame" x="0.0" y="0.0" width="139" height="90"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <point key="canvasLocation" x="324.5" y="256"/>
        </imageView>
    </objects>
</document>
