<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14313.18" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14283.14"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="sexChange">
            <connections>
                <outlet property="manBTN" destination="1Hw-Pc-HFm" id="6P8-t4-pBV"/>
                <outlet property="manlabel" destination="hYe-7d-VZR" id="2vG-JZ-ye6"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
                <outlet property="womanBTN" destination="jb6-Ms-4kO" id="rNy-by-WXU"/>
                <outlet property="womanlabel" destination="Apv-Vm-I4z" id="Lnh-4B-GXl"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="osx-Y6-759">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="64"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="性别" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Kt-jV-sU8">
                            <rect key="frame" x="0.0" y="28" width="375" height="20.5"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hXN-13-fn7">
                            <rect key="frame" x="19" y="28" width="15" height="21"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="15" id="eaK-Xo-dEA"/>
                                <constraint firstAttribute="height" constant="21" id="f0S-2y-sSw"/>
                            </constraints>
                            <state key="normal" image="icon_arrow_leftsssa.png"/>
                            <connections>
                                <action selector="cancle:" destination="-1" eventType="touchUpInside" id="ifK-wW-cRi"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3bs-sv-pNd">
                            <rect key="frame" x="-8.5" y="3.5" width="70" height="70"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="70" id="Eve-Ll-NyI"/>
                                <constraint firstAttribute="height" constant="70" id="x8i-87-Gxm"/>
                            </constraints>
                            <connections>
                                <action selector="cancle:" destination="-1" eventType="touchUpInside" id="7nu-f3-5iA"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" red="0.96470588235294119" green="0.96470588235294119" blue="0.96470588235294119" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="3bs-sv-pNd" firstAttribute="centerX" secondItem="hXN-13-fn7" secondAttribute="centerX" id="Byz-x8-Ueg"/>
                        <constraint firstItem="hXN-13-fn7" firstAttribute="centerY" secondItem="osx-Y6-759" secondAttribute="centerY" multiplier="1.2" id="Iv3-Zm-My7"/>
                        <constraint firstItem="6Kt-jV-sU8" firstAttribute="width" secondItem="osx-Y6-759" secondAttribute="width" id="J8N-Gp-cZv"/>
                        <constraint firstAttribute="height" constant="64" id="UHs-Fd-bmV"/>
                        <constraint firstItem="3bs-sv-pNd" firstAttribute="centerY" secondItem="hXN-13-fn7" secondAttribute="centerY" id="aG8-uU-bEq"/>
                        <constraint firstItem="6Kt-jV-sU8" firstAttribute="leading" secondItem="osx-Y6-759" secondAttribute="leading" id="pjl-Hq-aA6"/>
                        <constraint firstItem="hXN-13-fn7" firstAttribute="leading" secondItem="osx-Y6-759" secondAttribute="leading" constant="19" id="qYj-Yh-vg4"/>
                        <constraint firstItem="6Kt-jV-sU8" firstAttribute="centerY" secondItem="osx-Y6-759" secondAttribute="centerY" multiplier="1.2" id="tdn-GC-Zbb"/>
                    </constraints>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1Hw-Pc-HFm">
                    <rect key="frame" x="157.5" y="203.5" width="60" height="60"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="60" id="c5a-QC-9V7"/>
                        <constraint firstAttribute="width" constant="60" id="qLl-CH-ovA"/>
                    </constraints>
                    <state key="normal" image="choice_sex_nanren"/>
                    <connections>
                        <action selector="doman:" destination="-1" eventType="touchUpInside" id="yyv-op-i78"/>
                    </connections>
                </button>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hYe-7d-VZR">
                    <rect key="frame" x="137.5" y="283.5" width="100" height="26"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="100" id="SmP-kL-hdw"/>
                        <constraint firstAttribute="height" constant="26" id="sJk-sd-CLu"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="20"/>
                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="jb6-Ms-4kO">
                    <rect key="frame" x="157.5" y="337" width="60" height="60"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="60" id="0Pe-AH-Iug"/>
                        <constraint firstAttribute="height" constant="60" id="8z8-Tq-lkv"/>
                    </constraints>
                    <state key="normal" image="choice_sex_un_femal"/>
                    <connections>
                        <action selector="dowoman:" destination="-1" eventType="touchUpInside" id="b4H-Q5-BhY"/>
                    </connections>
                </button>
                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Apv-Vm-I4z">
                    <rect key="frame" x="137.5" y="417" width="100" height="28"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="100" id="2ME-qL-ruF"/>
                        <constraint firstAttribute="height" constant="28" id="ere-FS-TJY"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="20"/>
                    <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
            <constraints>
                <constraint firstItem="jb6-Ms-4kO" firstAttribute="centerX" secondItem="osx-Y6-759" secondAttribute="centerX" id="37R-tS-mMU"/>
                <constraint firstItem="osx-Y6-759" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="8n2-Go-4u5"/>
                <constraint firstItem="jb6-Ms-4kO" firstAttribute="centerY" secondItem="osx-Y6-759" secondAttribute="centerY" multiplier="1.2" id="Du7-US-kmf"/>
                <constraint firstItem="hYe-7d-VZR" firstAttribute="top" secondItem="1Hw-Pc-HFm" secondAttribute="bottom" constant="20" id="JUA-l8-jpj"/>
                <constraint firstItem="Apv-Vm-I4z" firstAttribute="top" secondItem="jb6-Ms-4kO" secondAttribute="bottom" constant="20" id="TU5-Y2-qxD"/>
                <constraint firstItem="osx-Y6-759" firstAttribute="width" secondItem="i5M-Pr-FkT" secondAttribute="width" id="Tii-2b-P6S"/>
                <constraint firstItem="osx-Y6-759" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="Uza-tx-6ee"/>
                <constraint firstItem="jb6-Ms-4kO" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="Xkf-kB-DCs"/>
                <constraint firstItem="1Hw-Pc-HFm" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="ZuN-mJ-I2q"/>
                <constraint firstItem="hXN-13-fn7" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="eA7-74-iWR"/>
                <constraint firstItem="hYe-7d-VZR" firstAttribute="centerX" secondItem="1Hw-Pc-HFm" secondAttribute="centerX" id="eFz-GW-v3T"/>
                <constraint firstItem="osx-Y6-759" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="lFn-82-bIi"/>
                <constraint firstItem="Apv-Vm-I4z" firstAttribute="centerX" secondItem="jb6-Ms-4kO" secondAttribute="centerX" id="tCq-Bv-GwI"/>
                <constraint firstItem="1Hw-Pc-HFm" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" multiplier="0.7" id="wNx-LG-lse"/>
                <constraint firstItem="jb6-Ms-4kO" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" multiplier="1.1" id="xPm-jK-Bc6"/>
            </constraints>
            <variation key="default">
                <mask key="constraints">
                    <exclude reference="8n2-Go-4u5"/>
                    <exclude reference="eA7-74-iWR"/>
                    <exclude reference="37R-tS-mMU"/>
                    <exclude reference="Du7-US-kmf"/>
                </mask>
            </variation>
            <point key="canvasLocation" x="69.5" y="152.5"/>
        </view>
    </objects>
    <resources>
        <image name="choice_sex_nanren" width="126" height="126"/>
        <image name="choice_sex_un_femal" width="126" height="126"/>
        <image name="icon_arrow_leftsssa.png" width="24" height="32"/>
    </resources>
</document>
