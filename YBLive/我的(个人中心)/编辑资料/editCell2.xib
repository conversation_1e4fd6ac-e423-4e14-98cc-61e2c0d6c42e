<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" tag="1000" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="num" id="nvL-St-06f" customClass="InfoEdit2TableViewCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="nvL-St-06f" id="we4-0g-BSA">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0Nz-XT-eYh">
                        <rect key="frame" x="12" y="13" width="37.5" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" red="0.58823529411764708" green="0.59215686274509804" blue="0.59607843137254901" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HOT-vO-0cD">
                        <rect key="frame" x="240" y="0.0" width="40" height="44"/>
                        <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="profit_right.png" translatesAutoresizingMaskIntoConstraints="NO" id="ItT-qK-N12">
                        <rect key="frame" x="291" y="12" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="Qgj-We-DaE"/>
                            <constraint firstAttribute="width" constant="20" id="lO3-Wb-ruZ"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YeL-3G-RNh">
                        <rect key="frame" x="0.0" y="43" width="320" height="1"/>
                        <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="FpF-Vm-p83"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="YeL-3G-RNh" secondAttribute="trailing" id="2gb-cD-Hvo"/>
                    <constraint firstItem="0Nz-XT-eYh" firstAttribute="centerY" secondItem="we4-0g-BSA" secondAttribute="centerY" id="8SM-2q-z2l"/>
                    <constraint firstItem="HOT-vO-0cD" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="0Nz-XT-eYh" secondAttribute="trailing" constant="5" id="Ar2-8r-3BS"/>
                    <constraint firstItem="YeL-3G-RNh" firstAttribute="leading" secondItem="we4-0g-BSA" secondAttribute="leading" id="IPj-S9-hun"/>
                    <constraint firstItem="HOT-vO-0cD" firstAttribute="height" secondItem="we4-0g-BSA" secondAttribute="height" id="PRk-8U-9tz"/>
                    <constraint firstItem="ItT-qK-N12" firstAttribute="centerY" secondItem="we4-0g-BSA" secondAttribute="centerY" id="XEj-TE-1jo"/>
                    <constraint firstAttribute="trailing" secondItem="ItT-qK-N12" secondAttribute="trailing" constant="9" id="dB3-rQ-dEo"/>
                    <constraint firstItem="HOT-vO-0cD" firstAttribute="centerY" secondItem="0Nz-XT-eYh" secondAttribute="centerY" id="e2T-Gt-zCO"/>
                    <constraint firstItem="0Nz-XT-eYh" firstAttribute="leading" secondItem="we4-0g-BSA" secondAttribute="leading" constant="12" id="fL9-rA-qdv"/>
                    <constraint firstAttribute="bottom" secondItem="YeL-3G-RNh" secondAttribute="bottom" id="hgB-r6-FuW"/>
                </constraints>
            </tableViewCellContentView>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="HOT-vO-0cD" secondAttribute="trailing" constant="40" id="cAv-Af-Br3"/>
            </constraints>
            <connections>
                <outlet property="labContrName" destination="0Nz-XT-eYh" id="1Qe-FY-VuX"/>
                <outlet property="labDetail" destination="HOT-vO-0cD" id="DO2-OQ-4dV"/>
                <outlet property="rightImg" destination="ItT-qK-N12" id="ze8-DD-Ru9"/>
            </connections>
            <point key="canvasLocation" x="33.600000000000001" y="4.497751124437781"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="profit_right.png" width="72" height="72"/>
    </resources>
</document>
