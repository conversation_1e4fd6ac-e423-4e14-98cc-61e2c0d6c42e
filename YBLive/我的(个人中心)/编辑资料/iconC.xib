<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="13529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13527"/>
        <capability name="Aspect ratio constraints" minToolsVersion="5.1"/>
        <capability name="Constraints with non-1.0 multipliers" minToolsVersion="5.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="iconC">
            <connections>
                <outlet property="iconBTN" destination="hf5-0k-PrQ" id="zVz-S8-c40"/>
                <outlet property="paizhaob" destination="asQ-o8-ddc" id="vmX-my-BAG"/>
                <outlet property="view" destination="1rb-ww-HXG" id="bnI-K1-5tb"/>
                <outlet property="xiangceb" destination="R2l-xk-CMy" id="cHE-ho-vbR"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="1rb-ww-HXG" userLabel="iconC">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hf5-0k-PrQ">
                    <rect key="frame" x="37.5" y="40" width="300" height="300"/>
                    <constraints>
                        <constraint firstAttribute="width" secondItem="hf5-0k-PrQ" secondAttribute="height" multiplier="1:1" id="1Dw-kn-fFL"/>
                    </constraints>
                    <connections>
                        <action selector="iconBTNNNN:" destination="-1" eventType="touchUpInside" id="xvV-xb-wrS"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="asQ-o8-ddc">
                    <rect key="frame" x="38" y="360" width="300" height="30"/>
                    <color key="backgroundColor" red="0.37647058820000001" green="0.80392156859999997" blue="0.75294117650000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="30" id="0OT-2c-Vr0"/>
                    </constraints>
                    <state key="normal" title="拍照">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="paizhao:" destination="-1" eventType="touchUpInside" id="IY7-q7-U1c"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lfe-eH-0Lz">
                    <rect key="frame" x="173" y="583" width="30" height="30"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="30" id="7GH-Dk-L5v"/>
                        <constraint firstAttribute="height" constant="30" id="gbE-Qu-WSD"/>
                    </constraints>
                    <state key="normal" backgroundImage="close_.png"/>
                    <connections>
                        <action selector="fanhui:" destination="-1" eventType="touchUpInside" id="Onm-37-rwL"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="R2l-xk-CMy">
                    <rect key="frame" x="38" y="410" width="300" height="30"/>
                    <color key="backgroundColor" red="0.37647058820000001" green="0.80392156859999997" blue="0.75294117650000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="30" id="V8Y-vI-fgE"/>
                    </constraints>
                    <state key="normal" title="从手机相册选择">
                        <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="xiangce:" destination="-1" eventType="touchUpInside" id="7Gc-US-FTk"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="asQ-o8-ddc" firstAttribute="top" secondItem="hf5-0k-PrQ" secondAttribute="bottom" constant="20" id="5Pb-Qt-QlI"/>
                <constraint firstItem="lfe-eH-0Lz" firstAttribute="centerX" secondItem="1rb-ww-HXG" secondAttribute="centerX" id="81p-tp-D2t"/>
                <constraint firstItem="hf5-0k-PrQ" firstAttribute="width" secondItem="1rb-ww-HXG" secondAttribute="width" multiplier="0.8" id="Bmi-Hu-Gjx"/>
                <constraint firstItem="R2l-xk-CMy" firstAttribute="width" secondItem="asQ-o8-ddc" secondAttribute="width" id="FDI-x6-V3I"/>
                <constraint firstItem="R2l-xk-CMy" firstAttribute="top" secondItem="asQ-o8-ddc" secondAttribute="bottom" constant="20" id="HSX-xA-Ifm"/>
                <constraint firstItem="asQ-o8-ddc" firstAttribute="width" secondItem="1rb-ww-HXG" secondAttribute="width" multiplier="0.8" id="Jpg-x9-Mxa"/>
                <constraint firstItem="asQ-o8-ddc" firstAttribute="centerX" secondItem="1rb-ww-HXG" secondAttribute="centerX" id="MQC-D3-TiN"/>
                <constraint firstItem="hf5-0k-PrQ" firstAttribute="centerX" secondItem="1rb-ww-HXG" secondAttribute="centerX" id="YpW-02-yew"/>
                <constraint firstItem="asQ-o8-ddc" firstAttribute="leading" secondItem="R2l-xk-CMy" secondAttribute="leading" id="cTb-6I-GP2"/>
                <constraint firstItem="hf5-0k-PrQ" firstAttribute="top" secondItem="1rb-ww-HXG" secondAttribute="top" constant="40" id="eCR-Vt-9qd"/>
                <constraint firstAttribute="bottom" secondItem="lfe-eH-0Lz" secondAttribute="bottom" constant="54" id="y3d-sE-gjr"/>
            </constraints>
            <point key="canvasLocation" x="368.5" y="6.5"/>
        </view>
    </objects>
    <resources>
        <image name="close_.png" width="45" height="45"/>
    </resources>
</document>
