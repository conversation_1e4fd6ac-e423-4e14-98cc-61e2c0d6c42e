//
//  myInfoEdit.m
//  YBLive
//
//  Created by cat on 16/3/13.
//  Copyright © 2016年 cat. All rights reserved.
//

#import "myInfoEdit.h"
#import "InfoEdit1TableViewCell.h"
#import "InfoEdit2TableViewCell.h"
#import "EditNiceName.h"
#import "EditSignature.h"
#import "iconC.h"
#import "sexCell.h"
#import "sexChange.h"
#import "UIImageView+WebCache.h"
#import "impressVC.h"
#import <SDWebImage/SDImageCache.h>

@import CoreLocation;

@interface myInfoEdit ()<UITableViewDataSource,UITableViewDelegate,UIImagePickerControllerDelegate,UINavigationControllerDelegate,UIPickerViewDelegate, UIPickerViewDataSource,CLLocationManagerDelegate>
{
    int setvisinfo;
    UIDatePicker *datapicker;
    NSString *datestring;//保存时间
    UIAlertController *alert;
    UIActivityIndicatorView *testActivityIndicator;//菊花
    NSArray *itemArray;
    
    UIView *cityPickBack;
    UIPickerView *cityPicker;
    //省市区-数组
    NSArray *province;
    NSArray *city;
    NSArray *district;
    
    //省市区-字符串
    NSString *provinceStr;
    NSString *cityStr;
    NSString *districtStr;
    
    NSDictionary *areaDic;
    NSString *selectedProvince;
    UILabel *location_tf;
    
    CLLocationManager   *_lbsManager;

    NSString *normalProvince;
    NSString *normalCity;
    NSString *normalDistrict;
    
    NSString *headerUrl;

}

@end

@implementation myInfoEdit


- (void)viewDidLoad {
    [super viewDidLoad];
    self.tableView.separatorStyle = UITableViewCellAccessoryNone;
    self.navigationController.interactivePopGestureRecognizer.delegate = (id) self;
    [self navtion];
    normalProvince = @"";
    normalCity = @"";
    normalDistrict = @"";
    itemArray = @[YZMsg(@"昵称"),YZMsg(@"性别"),YZMsg(@"生日"),YZMsg(@"所在地"),YZMsg(@"签名")];

    self.view.backgroundColor = [UIColor whiteColor];
    datapicker = [[UIDatePicker alloc]initWithFrame:CGRectMake(0,0, _window_width-16, _window_height*0.3)];
    
    NSDate *currentDate = [NSDate date];
    [datapicker setMaximumDate:currentDate];
    
    NSDateFormatter  * formatter = [[ NSDateFormatter   alloc ] init ];
    
    [formatter  setDateFormat : @"yyyy-MM-dd" ];
    
    NSString  * mindateStr =  @"1950-01-01" ;
    
    NSDate  * mindate = [formatter  dateFromString :mindateStr];
    
    datapicker . minimumDate = mindate;
    
    datapicker.maximumDate=[NSDate date];

    [datapicker addTarget:self action:@selector(oneDatePickerValueChanged:) forControlEvents:UIControlEventValueChanged ];
    if (@available(iOS 13.4, *)) {
        datapicker.preferredDatePickerStyle = UIDatePickerStyleWheels;
    }
    datapicker.datePickerMode = UIDatePickerModeDate;
    //设置为中文
    NSLocale *locale = [[NSLocale alloc]initWithLocaleIdentifier:@"zh_CN"];
    if (![lagType containsString:@"zh"]) {
              locale = [[NSLocale alloc]initWithLocaleIdentifier:@"en"];
          }
    datapicker.locale =locale;
    NSString *alertTitle;
    if (_window_height <= 667.0) {
        alertTitle = @"\n\n\n\n\n\n\n\n\n";
    }else{
        alertTitle = @"\n\n\n\n\n\n\n\n\n\n\n\n";
    }
    
    alert = [UIAlertController alertControllerWithTitle:@"\n\n\n\n\n\n\n\n\n\n\n\n" message:nil preferredStyle:UIAlertControllerStyleActionSheet];
    
    [alert.view addSubview:datapicker];
    
    UIAlertAction *ok = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        [self getbirthday];
    }];
    
    UIAlertAction *cancel = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction *action) {
    }];
    [ok setValue:normalColors forKey:@"_titleTextColor"];
    [cancel setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    [alert addAction:ok];
    [alert addAction:cancel];

    NSBundle *bundle = [NSBundle mainBundle];
    NSString *plistPath = [bundle pathForResource:@"area" ofType:@"plist"];
    areaDic = [[NSDictionary alloc] initWithContentsOfFile:plistPath];
    
    NSArray *components = [areaDic allKeys];
    NSArray *sortedArray = [components sortedArrayUsingComparator: ^(id obj1, id obj2) {
        
        if ([obj1 integerValue] > [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedDescending;
        }
        
        if ([obj1 integerValue] < [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedAscending;
        }
        return (NSComparisonResult)NSOrderedSame;
    }];
    
    NSMutableArray *provinceTmp = [[NSMutableArray alloc] init];
    for (int i=0; i<[sortedArray count]; i++) {
        NSString *index = [sortedArray objectAtIndex:i];
        NSArray *tmp = [[areaDic objectForKey: index] allKeys];
        [provinceTmp addObject: [tmp objectAtIndex:0]];
    }
    //---> //rk_3-7 修复首次加载问题
    province = [[NSArray alloc] initWithArray: provinceTmp];
    NSString *index = [sortedArray objectAtIndex:0];
    //NSString *selected = [province objectAtIndex:0];
    selectedProvince = [province objectAtIndex:0];
    NSDictionary *proviceDic = [NSDictionary dictionaryWithDictionary: [[areaDic objectForKey:index]objectForKey:selectedProvince]];
    
    NSArray *cityArray = [proviceDic allKeys];
    NSDictionary *cityDic = [NSDictionary dictionaryWithDictionary: [proviceDic objectForKey: [cityArray objectAtIndex:0]]];
    //city = [[NSArray alloc] initWithArray: [cityDic allKeys]];
    
    NSArray *citySortedArray = [cityArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
        if ([obj1 integerValue] > [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedDescending;//递减
        }
        if ([obj1 integerValue] < [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedAscending;//上升
        }
        return (NSComparisonResult)NSOrderedSame;
    }];
    NSMutableArray *m_array = [[NSMutableArray alloc] init];
    for (int i=0; i<[citySortedArray count]; i++) {
        NSString *index = [citySortedArray objectAtIndex:i];
        NSArray *temp = [[proviceDic objectForKey: index] allKeys];
        [m_array addObject: [temp objectAtIndex:0]];
    }
    city = [NSArray arrayWithArray:m_array];
    //<-----------
    
    NSString *selectedCity = [city objectAtIndex: 0];
    district = [[NSArray alloc] initWithArray: [cityDic objectForKey: selectedCity]];
    self.tableView.frame = CGRectMake(0, 64+statusbarHeight, _window_width, _window_height - 64- statusbarHeight);
    
//    [self location];
    [[RKLBSManager shareManager]startLocation];
    [[RKLBSManager shareManager]locationUpdae:^(NSString *Province, NSString *City, NSString *District) {
            normalProvince =Province;
            normalCity = City;
            normalDistrict = District;
        }];

    
}
//生日
-(void)getbirthday{
    
    testActivityIndicator = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
    testActivityIndicator.center = self.view.center;
    [self.view addSubview:testActivityIndicator];
    testActivityIndicator.color = [UIColor blackColor];
    [testActivityIndicator startAnimating]; // 开始旋转
    
    
    if (datestring == nil) {
        
        NSDate *currentDate = [NSDate date];//获取当前时间，日期
        NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
        [dateFormatter setDateFormat:@"YYYY-MM-dd"];
        datestring = [dateFormatter stringFromDate:currentDate];
        
    }
    NSDictionary *dic = [NSDictionary dictionaryWithObjects:@[datestring] forKeys:@[@"birthday"]];
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:NSJSONWritingPrettyPrinted error:nil];
    NSString *jsonStr = [[NSString alloc]initWithData:jsonData encoding:NSUTF8StringEncoding];
    
    NSString *url = [NSString stringWithFormat:@"User.updateFields&uid=%@&token=%@&fields=%@",[Config getOwnID],[Config getOwnToken],jsonStr];
    [YBToolClass postNetworkWithUrl:url andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            [[NSNotificationCenter defaultCenter]postNotificationName:@"refeshMyinfo" object:nil];
 
            LiveUser *user = [[LiveUser alloc] init];
            user.birthday = datestring;
            [Config updateProfile:user];
            [self.tableView reloadData];
            [MBProgressHUD showError:YZMsg(@"修改成功")];
            
        }else if(code == 10011){
            [MBProgressHUD showError:msg];
        }
        [testActivityIndicator stopAnimating]; // 结束旋转
        [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏

    } fail:^{
        [testActivityIndicator stopAnimating]; // 结束旋转
        [testActivityIndicator setHidesWhenStopped:YES]; //当旋转结束时隐藏

    }];

    
}
- (void)oneDatePickerValueChanged:(UIDatePicker *) sender {

    
    NSDate *select = [sender date]; // 获取被选中的时间
    NSDateFormatter *selectDateFormatter = [[NSDateFormatter alloc] init];
    [selectDateFormatter setDateFormat:@"YYYY-MM-dd"];
    datestring = [selectDateFormatter stringFromDate:select]; // 把date类型转为设置好格式的string类型
}
-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:YES];
    setvisinfo = 1;
    [self.tableView reloadData];
//    [self.tableView reloadData];
}
- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    setvisinfo = 0;
}
-(void)navtion{
    
    UIView *navtion = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64 + statusbarHeight)];
    navtion.backgroundColor = navigationBGColor;
    UILabel *label = [[UILabel alloc]init];
    label.text = YZMsg(@"编辑资料");
    [label setFont:navtionTitleFont];
    
    label.textColor = navtionTitleColor;
    label.frame = CGRectMake(0, statusbarHeight,_window_width,84);
    // label.center = navtion.center;
    label.textAlignment = NSTextAlignmentCenter;
    [navtion addSubview:label];
    
    UIButton *returnBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    returnBtn.frame = CGRectMake(8,24 + statusbarHeight,40,40);
    returnBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 0, 12.5, 25);
    [returnBtn setImage:[UIImage imageNamed:@"icon_arrow_leftsssa.png"] forState:UIControlStateNormal];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:returnBtn];
    UIButton *btnttttt = [UIButton buttonWithType:UIButtonTypeCustom];
    btnttttt.backgroundColor = [UIColor clearColor];
    [btnttttt addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    btnttttt.frame = CGRectMake(0,0,100,64);
    [navtion addSubview:btnttttt];
    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(0, navtion.height-1, _window_width, 1) andColor:RGB(244, 245, 246) andView:navtion];

    [self.view addSubview:navtion];
    
    
}
-(void)money{
    
    
}
-(void)doReturn{
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    //判断返回cell
    LiveUser *users = [Config myProfile];
    if (indexPath.section == 0) {
        InfoEdit1TableViewCell *cell = [InfoEdit1TableViewCell cellWithTableView:tableView];
        cell.imgRight.layer.masksToBounds = YES;
        cell.imgRight.layer.cornerRadius = 20;
        return cell;
    }
    else
    {
        InfoEdit2TableViewCell *cell = [InfoEdit2TableViewCell cellWithTableView:tableView];
        cell.labContrName.text = itemArray[indexPath.row];

        if (indexPath.row == 0) {
            cell.labDetail.text = [Config getOwnNicename];
        }
        else if(indexPath.row == 1)
        {
            if ([users.sex isEqual:@"1"]) {
                cell.labDetail.text = YZMsg(@"男");
            }else{
                cell.labDetail.text = YZMsg(@"女");
            }

        }
        else if (indexPath.row == 2){
            cell.labDetail.text = users.birthday;
        }
        else if (indexPath.row == 3){
            cell.labDetail.text = [Config location] ? [Config location] : [cityDefault getaddr];
            location_tf = cell.labDetail;

        }
        else if (indexPath.row == 4){
            cell.labDetail.text = [Config getOwnSignature];

        }
        else{
            cell.labDetail.text = @"";
        }
        return cell;

    }
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    
    if (indexPath.section == 0) {
        return 60;
    }
    else
        
        return 50;
    
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    //判断当前分区返回分区行数
    if (section == 0 ) {
        return 1;
    }
    else
    {
        return itemArray.count;
    }
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    //返回分区数
    return 2;
}
- ( CGFloat )tableView:( UITableView *)tableView heightForHeaderInSection:( NSInteger )section
{
    return 0.01;
    
}
-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger) section
{
    return 2;
}
-(UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section
{
    UIView *footerView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 2)];
    footerView.backgroundColor = RGB(245, 245, 245);
    return footerView;
}
//点击事件
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    //判断返回cell
    
    if (indexPath.section == 0) {
        UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];
        UIAlertAction *picAction = [UIAlertAction actionWithTitle:YZMsg(@"相册") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self selectThumbWithType:UIImagePickerControllerSourceTypePhotoLibrary];
        }];
        [picAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
        [alertContro addAction:picAction];
        UIAlertAction *photoAction = [UIAlertAction actionWithTitle:YZMsg(@"拍照") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self selectThumbWithType:UIImagePickerControllerSourceTypeCamera];
        }];
        [photoAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
        [alertContro addAction:photoAction];
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        }];
        [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];

        [alertContro addAction:cancleAction];
        
        [self presentViewController:alertContro animated:YES completion:nil];
    }
    else {
        if(indexPath.row == 0)
        {
            EditNiceName *EditNameView = [[EditNiceName alloc] init];
            [[MXBADelegate sharedAppDelegate] pushViewController:EditNameView animated:YES];
        }
        else if(indexPath.row == 1){
            sexChange *sex = [[sexChange alloc]init];
            [[MXBADelegate sharedAppDelegate] pushViewController:sex animated:YES];
        }
        else if (indexPath.row == 2){
            
            [self presentViewController:alert animated:YES completion:nil];
            
        }else if (indexPath.row == 3){
            [self clickChangeLocation];

        }else{
            EditSignature *EditSignatureView = [[EditSignature alloc] init];
            [[MXBADelegate sharedAppDelegate] pushViewController:EditSignatureView animated:YES];
        }
    }
}
- (void)selectThumbWithType:(UIImagePickerControllerSourceType)type{
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.delegate = self;
    imagePickerController.sourceType = type;
    imagePickerController.allowsEditing = YES;
    if (type == UIImagePickerControllerSourceTypeCamera) {
        imagePickerController.showsCameraControls = YES;
        imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    }
    [self presentViewController:imagePickerController animated:YES completion:nil];
}
-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    
    [MBProgressHUD showMessage:YZMsg(@"正在上传")];
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"])
    {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerEditedImage"];
        NSData *data;
//        if (UIImagePNGRepresentation(image) == nil)
//        {
            data = UIImageJPEGRepresentation(image, 0.5);
//        }
//        else
//        {
//            data = UIImagePNGRepresentation(image);
//        }
        YBWeakSelf;
        [[YBStorageManage shareManage]getCOSInfo:^(int code) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (code == 0) {
                    [weakSelf startUploadCer:image];
                }
            });
        }];

        
//        AFHTTPSessionManager *session = [AFHTTPSessionManager manager];
//        NSString *url = [purl stringByAppendingFormat:@"?service=User.updateAvatar&uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
        
        
//        [session POST:url parameters:nil headers:nil constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
//            if (image) {
//                [formData appendPartWithFileData:data name:@"file" fileName:@"duibinaf.png" mimeType:@"image/jpeg"];
//            }
//
//        } progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
//            [MBProgressHUD hideHUD];
//            NSLog(@"-------%@",responseObject);
//            NSArray *data = [responseObject valueForKey:@"data"];
//            NSString *ret = [NSString stringWithFormat:@"%@",[responseObject valueForKey:@"ret"]];
//            if ([ret isEqual:@"200"]) {
//
//
//                NSNumber *number = [data valueForKey:@"code"] ;
//                if([number isEqualToNumber:[NSNumber numberWithInt:0]])
//                {
//
//                    [[SDImageCache sharedImageCache] clearMemory];//可有可无
//
//                    NSString *info = [[data valueForKey:@"info"] firstObject];
//                    NSString *avatar = [info valueForKey:@"avatar"];
//                    NSString *avatar_thumb = [info valueForKey:@"avatar_thumb"];
//                    LiveUser *user = [[LiveUser alloc]init];
//                    user.avatar = avatar;
//                    user.avatar_thumb = avatar_thumb;
//
//                    [Config updateProfile:user];
//                    [self.tableView reloadData];
//                    [MBProgressHUD showError:minstr([data valueForKey:@"msg"])];
//                }
//            }
//            else{
//                [MBProgressHUD showError:[responseObject valueForKey:@"msg"]];
//            }
//
//        } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
//            [MBProgressHUD hideHUD];
//            [MBProgressHUD showError:YZMsg(@"上传失败")];
//
//        }];
        
        [picker dismissViewControllerAnimated:YES completion:nil];
    }
}
-(void)startUploadCer:(UIImage *)header{
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    YBWeakSelf;
    //正面照
    if (header) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_cerFace.png"];
            [[YBStorageManage shareManage]yb_storageImg:header andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                headerUrl = minstr(key);
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf goResServer];
        });
        NSLog(@"任务完成执行");
    });

}
-(void)goResServer{
    NSString *url = [purl stringByAppendingFormat:@"?service=User.updateAvatar"];

    NSDictionary *postDic = @{
        @"uid":[Config getOwnID],
        @"token":[Config getOwnToken],
        @"avatar":headerUrl,
    };
    
    [YBNetworking postWithUrl:url Dic:postDic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            [[NSNotificationCenter defaultCenter]postNotificationName:@"refeshMyinfo" object:nil];

            NSString *info = [[data valueForKey:@"info"] firstObject];
            NSString *avatar = [info valueForKey:@"avatar"];
            NSString *avatar_thumb = [info valueForKey:@"avatar_thumb"];
            LiveUser *user = [[LiveUser alloc]init];
            user.avatar = avatar;
            user.avatar_thumb = avatar_thumb;

            [Config updateProfile:user];
            [self.tableView reloadData];
            [MBProgressHUD showError:minstr([data valueForKey:@"msg"])];

        }

    } Fail:^(id fail) {
        [MBProgressHUD hideHUD];

    }];

}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker{
    [picker dismissViewControllerAnimated:YES completion:nil];
}
- (void)navigationController:(UINavigationController *)navigationController didShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if ([UIDevice currentDevice].systemVersion.floatValue < 11) {
        return;
    }
    if ([viewController isKindOfClass:NSClassFromString(@"PUPhotoPickerHostViewController")]) {
        [viewController.view.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.frame.size.width < 42) {
                [viewController.view sendSubviewToBack:obj];
                *stop = YES;
            }
        }];
    }
}
//城市选择
-(void)clickChangeLocation {
    
    if (!cityPickBack) {
        cityPickBack = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        cityPickBack.backgroundColor = RGB_COLOR(@"#000000", 0.3);
        [self.view addSubview:cityPickBack];
        
        UIView *titleView = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-240, _window_width, 40)];
        titleView.backgroundColor = RGB_COLOR(@"#ececec", 1);
        [cityPickBack addSubview:titleView];
        
        UIButton *cancleBtn = [UIButton buttonWithType:0];
        cancleBtn.frame = CGRectMake(20, 0, 80, 40);
        cancleBtn.tag = 100;
        [cancleBtn setTitle:YZMsg(@"取消") forState:0];
        [cancleBtn setTitleColor:[UIColor grayColor] forState:0];
        [cancleBtn addTarget:self action:@selector(cityCancleOrSure:) forControlEvents:UIControlEventTouchUpInside];
        [titleView addSubview:cancleBtn];
        UIButton *sureBtn = [UIButton buttonWithType:0];
        sureBtn.frame = CGRectMake(_window_width-100, 0, 80, 40);
        sureBtn.tag = 101;
        [sureBtn setTitle:YZMsg(@"确定") forState:0];
        [sureBtn setTitleColor:normalColors forState:0];
        [sureBtn addTarget:self action:@selector(cityCancleOrSure:) forControlEvents:UIControlEventTouchUpInside];
        [titleView addSubview:sureBtn];
        
        cityPicker = [[UIPickerView alloc]initWithFrame:CGRectMake(0, _window_height-200, _window_width, 200)];
        cityPicker.backgroundColor = [UIColor whiteColor];
        cityPicker.delegate = self;
        cityPicker.dataSource = self;
        cityPicker.showsSelectionIndicator = YES;
//        [cityPicker selectRow: 0 inComponent: 0 animated: YES];
        [cityPickBack addSubview:cityPicker];
        
        
        [self setLocationAddress];
    }else{
        cityPickBack.hidden = NO;
    }
    
}
- (void)cityCancleOrSure:(UIButton *)button{
    if (button.tag == 100) {
        //return;
    }else{
        NSInteger provinceIndex = [cityPicker selectedRowInComponent: 0];
        NSInteger cityIndex = [cityPicker selectedRowInComponent: 1];
        NSInteger districtIndex = [cityPicker selectedRowInComponent: 2];
        
        provinceStr = [province objectAtIndex: provinceIndex];
        cityStr = [city objectAtIndex: cityIndex];
        districtStr = [district objectAtIndex:districtIndex];
        NSString *dizhi = [NSString stringWithFormat:@"%@%@%@",provinceStr,cityStr,districtStr];
        location_tf.text = dizhi;
        [self updateLocation:dizhi];
    }
    cityPickBack.hidden = YES;
    
}
#pragma mark- Picker Data Source Methods
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    if (pickerView == cityPicker) {
        return 3;
    }
    return 0;
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    if (pickerView == cityPicker) {
        if (component == 0) {
            return [province count];
        }
        else if (component == 1) {
            return [city count];
        }
        else {
            return [district count];
        }
    }else{
        return 100;
    }
}


#pragma mark- Picker Delegate Methods

- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    if (pickerView == cityPicker) {
        if (component == 0) {
            return [province objectAtIndex: row];
        }
        else if (component == 1) {
            return [city objectAtIndex: row];
        }
        else {
            return [district objectAtIndex: row];
        }
    }else{
        return nil;
    }
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component{
    if (pickerView == cityPicker) {
        if (component == 0) {
            selectedProvince = [province objectAtIndex: row];
            NSDictionary *tmp = [NSDictionary dictionaryWithDictionary: [areaDic objectForKey: [NSString stringWithFormat:@"%ld", row]]];
            NSDictionary *dic = [NSDictionary dictionaryWithDictionary: [tmp objectForKey: selectedProvince]];
            NSArray *cityArray = [dic allKeys];
            NSArray *sortedArray = [cityArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
                
                if ([obj1 integerValue] > [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedDescending;//递减
                }
                if ([obj1 integerValue] < [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedAscending;//上升
                }
                return (NSComparisonResult)NSOrderedSame;
            }];
            
            NSMutableArray *array = [[NSMutableArray alloc] init];
            for (int i=0; i<[sortedArray count]; i++) {
                NSString *index = [sortedArray objectAtIndex:i];
                NSArray *temp = [[dic objectForKey: index] allKeys];
                [array addObject: [temp objectAtIndex:0]];
            }
            
            city = [[NSArray alloc] initWithArray: array];
            
            NSDictionary *cityDic = [dic objectForKey: [sortedArray objectAtIndex: 0]];
            district = [[NSArray alloc] initWithArray: [cityDic objectForKey: [city objectAtIndex: 0]]];
            [cityPicker selectRow: 0 inComponent: 1 animated: YES];
            [cityPicker selectRow: 0 inComponent: 2 animated: YES];
            [cityPicker reloadComponent: 1];
            [cityPicker reloadComponent: 2];
            
        } else if (component == 1) {
            NSString *provinceIndex = [NSString stringWithFormat: @"%ld", [province indexOfObject: selectedProvince]];
            NSDictionary *tmp = [NSDictionary dictionaryWithDictionary: [areaDic objectForKey: provinceIndex]];
            NSDictionary *dic = [NSDictionary dictionaryWithDictionary: [tmp objectForKey: selectedProvince]];
            NSArray *dicKeyArray = [dic allKeys];
            NSArray *sortedArray = [dicKeyArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
                
                if ([obj1 integerValue] > [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedDescending;
                }
                
                if ([obj1 integerValue] < [obj2 integerValue]) {
                    return (NSComparisonResult)NSOrderedAscending;
                }
                return (NSComparisonResult)NSOrderedSame;
            }];
            
            NSDictionary *cityDic = [NSDictionary dictionaryWithDictionary: [dic objectForKey: [sortedArray objectAtIndex: row]]];
            NSArray *cityKeyArray = [cityDic allKeys];
            
            district = [[NSArray alloc] initWithArray: [cityDic objectForKey: [cityKeyArray objectAtIndex:0]]];
            [cityPicker selectRow: 0 inComponent: 2 animated: YES];
            [cityPicker reloadComponent: 2];
        }
    }else{
        
    }
    
}


- (CGFloat)pickerView:(UIPickerView *)pickerView widthForComponent:(NSInteger)component {
    if (component == 0) {
        return 80;
    }
    else if (component == 1) {
        return 100;
    }
    else {
        return 115;
    }
}

- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(UIView *)view {
    UILabel *myView = nil;
    if (pickerView == cityPicker) {
        if (component == 0) {
            myView = [[UILabel alloc] initWithFrame:CGRectMake(0.0, 0.0, _window_width/3, 30)];
            myView.textAlignment = NSTextAlignmentCenter;
            myView.text = [province objectAtIndex:row];
            myView.font = [UIFont systemFontOfSize:14];
            myView.backgroundColor = [UIColor clearColor];
        }
        else if (component == 1) {
            myView = [[UILabel alloc] initWithFrame:CGRectMake(0.0, 0.0, _window_width/3, 30)];
            myView.textAlignment = NSTextAlignmentCenter;
            myView.text = [city objectAtIndex:row];
            myView.font = [UIFont systemFontOfSize:14];
            myView.backgroundColor = [UIColor clearColor];
        }
        else {
            myView = [[UILabel alloc] initWithFrame:CGRectMake(0.0, 0.0, _window_width/3, 30)];
            myView.textAlignment = NSTextAlignmentCenter;
            myView.text = [district objectAtIndex:row];
            myView.font = [UIFont systemFontOfSize:14];
            myView.backgroundColor = [UIColor clearColor];
        }
    }
    return myView;
}
- (void)updateLocation:(NSString *)dizhi{
    [MBProgressHUD showMessage:@""];
    NSDictionary *dic = [NSDictionary dictionaryWithObjects:@[dizhi] forKeys:@[@"location"]];
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:NSJSONWritingPrettyPrinted error:nil];
    NSString *jsonStr = [[NSString alloc]initWithData:jsonData encoding:NSUTF8StringEncoding];
    
    NSString *url = [NSString stringWithFormat:@"User.updateFields&uid=%@&token=%@&fields=%@",[Config getOwnID],[Config getOwnToken],jsonStr];
    [YBToolClass postNetworkWithUrl:url andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            [[NSNotificationCenter defaultCenter]postNotificationName:@"refeshMyinfo" object:nil];
            LiveUser *user = [[LiveUser alloc] init];
            
            
            user.location = dizhi;
            
            [Config updateProfile:user];
            
            
        }
        [MBProgressHUD showError:msg];
        
    } fail:^{
        [MBProgressHUD hideHUD];
    }];

}



//-(void)location{
//    if (!_lbsManager) {
//        _lbsManager = [[CLLocationManager alloc] init];
//        [_lbsManager setDesiredAccuracy:kCLLocationAccuracyBest];
//        _lbsManager.delegate = self;
//        // 兼容iOS8定位
//        // 兼容iOS8定位
//        CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
//
//        if (kCLAuthorizationStatusDenied == status || kCLAuthorizationStatusRestricted == status) {
//            NSLog(@"请打开您的位置服务!");
//            NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
//
//               UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:nil message:[NSString stringWithFormat:@"打开“定位服务”来允许“%@”确定您的位置",[infoDictionary objectForKey:@"CFBundleDisplayName"]] preferredStyle:UIAlertControllerStyleAlert];
//               UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:@"设置" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//                   [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
//               }];
//               [alertContro addAction:cancleAction];
//            [cancleAction setValue:normalColors forKey:@"_titleTextColor"];
//               UIAlertAction *sureAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//                   
//               }];
//               [alertContro addAction:sureAction];
//            [sureAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
//
//            [[[MXBADelegate sharedAppDelegate]topViewController]presentViewController:alertContro animated:YES completion:nil];
//
//        }else{
//            SEL requestSelector = NSSelectorFromString(@"requestWhenInUseAuthorization");
//            if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusNotDetermined && [_lbsManager respondsToSelector:requestSelector]) {
//                [_lbsManager requestWhenInUseAuthorization];  //调用了这句,就会弹出允许框了.
//            } else {
//                [_lbsManager startUpdatingLocation];
//            }
//        }
//    }
//}
//- (void)stopLbs {
//    [_lbsManager stopUpdatingHeading];
//    _lbsManager.delegate = nil;
//    _lbsManager = nil;
//}
//- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
//    if (status == kCLAuthorizationStatusRestricted || status == kCLAuthorizationStatusDenied) {
//        [self stopLbs];
//    } else {
//        [_lbsManager startUpdatingLocation];
//    }
//}
//- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error
//{
//    [self stopLbs];
//}
//- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray *)locations
//{
//    CLLocation *newLocatioin = locations[0];
//    liveCity *cityU = [cityDefault myProfile];
//    cityU.lat = [NSString stringWithFormat:@"%f",newLocatioin.coordinate.latitude];
//    cityU.lng = [NSString stringWithFormat:@"%f",newLocatioin.coordinate.longitude];
//    CLGeocoder *geocoder = [[CLGeocoder alloc] init];
//    [geocoder reverseGeocodeLocation:newLocatioin completionHandler:^(NSArray *placemarks, NSError *error) {
//        if (!error)
//        {
//            CLPlacemark *placeMark = placemarks[0];
//            NSString *addr = [NSString stringWithFormat:@"%@%@%@",placeMark.administrativeArea,placeMark.locality,placeMark.subLocality];
//            NSLog(@"hhhhhhhh----:%@", addr);
//            normalProvince =placeMark.administrativeArea;
//            normalCity = placeMark.locality;
//            normalDistrict = placeMark.subLocality;
//        }
//    }];
//     [self stopLbs];
//}

-(void)setLocationAddress{
    int provinceIndex = 0;
    int cityIndex = 0;

    NSArray *components = [areaDic allKeys];
    NSArray *sortedArray = [components sortedArrayUsingComparator: ^(id obj1, id obj2) {
        
        if ([obj1 integerValue] > [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedDescending;
        }
        
        if ([obj1 integerValue] < [obj2 integerValue]) {
            return (NSComparisonResult)NSOrderedAscending;
        }
        return (NSComparisonResult)NSOrderedSame;
    }];
    
    NSMutableArray *provinceTmp = [[NSMutableArray alloc] init];
    for (int i=0; i<[sortedArray count]; i++) {
        NSString *index = [sortedArray objectAtIndex:i];
        NSArray *tmp = [[areaDic objectForKey: index] allKeys];
        [provinceTmp addObject: [tmp objectAtIndex:0]];
    }

    if (normalProvince.length > 0) {
        selectedProvince = normalProvince;

        for (int i = 0; i < province.count; i ++) {
            if ([normalProvince isEqual:province[i]]) {
                provinceIndex = i;
                NSString *index = [sortedArray objectAtIndex:i];

                NSDictionary *proviceDic = [NSDictionary dictionaryWithDictionary: [[areaDic objectForKey:index]objectForKey:normalProvince]];
                NSArray *cityArray = [proviceDic allKeys];
//                NSDictionary *cityDic = [NSDictionary dictionaryWithDictionary: [proviceDic objectForKey: [cityArray objectAtIndex:i]]];

                NSArray *citySortedArray = [cityArray sortedArrayUsingComparator: ^(id obj1, id obj2) {
                    if ([obj1 integerValue] > [obj2 integerValue]) {
                        return (NSComparisonResult)NSOrderedDescending;//递减
                    }
                    if ([obj1 integerValue] < [obj2 integerValue]) {
                        return (NSComparisonResult)NSOrderedAscending;//上升
                    }
                    return (NSComparisonResult)NSOrderedSame;
                }];
                NSMutableArray *m_array = [[NSMutableArray alloc] init];
                for (int i=0; i<[citySortedArray count]; i++) {
                    NSString *index = [citySortedArray objectAtIndex:i];
                    NSArray *temp = [[proviceDic objectForKey: index] allKeys];
                    [m_array addObject: [temp objectAtIndex:0]];
                }
               NSArray *cityArr = [NSArray arrayWithArray:m_array];
                city =[NSArray arrayWithArray:m_array];
                for (int j = 0; j < cityArr.count; j ++) {
                    if ([normalCity isEqual:cityArr[j]]) {
                        cityIndex = j;
                        NSString *keys = [NSString stringWithFormat:@"%d",cityIndex];
                        NSDictionary *dicssss = [NSDictionary dictionaryWithDictionary: [proviceDic objectForKey: keys]];

                        NSString *selectedCity = [cityArr objectAtIndex: j];
                        district = [[NSArray alloc] initWithArray: [dicssss objectForKey: selectedCity]];

                        NSArray * districtArr = [[NSArray alloc] initWithArray: [dicssss objectForKey: selectedCity]];
                        for (int k = 0; k <districtArr.count; k ++) {
                            if ([normalDistrict isEqual:districtArr[k]]) {
                                [cityPicker selectRow: provinceIndex inComponent: 0 animated: YES];
                                [cityPicker reloadComponent: 1];

                                [cityPicker selectRow: cityIndex inComponent: 1 animated: YES];
                                [cityPicker reloadComponent: 2];

                                [cityPicker selectRow: k inComponent: 2 animated: YES];
                            }
                        }
                    }
                }
            }
        }
    }
    NSLog(@"province===:%@  \n city:%@   \ndistrict:%@",province,city,district);
}
@end
