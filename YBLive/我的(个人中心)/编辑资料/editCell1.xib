<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14868" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14824"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="微软雅黑.ttf">
            <string>MicrosoftYaHei</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" tag="1000" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="vh8-2h-1Cb" customClass="InfoEdit1TableViewCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="vh8-2h-1Cb" id="nub-4h-MwN">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="头像" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jny-Eo-6Zf">
                        <rect key="frame" x="10" y="12" width="30" height="20"/>
                        <fontDescription key="fontDescription" name="MicrosoftYaHei" family="Microsoft YaHei" pointSize="15"/>
                        <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mine_noHeader.png" translatesAutoresizingMaskIntoConstraints="NO" id="HM2-H1-VwR">
                        <rect key="frame" x="240" y="2" width="40" height="40"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="40" id="NZb-qr-jUb"/>
                            <constraint firstAttribute="width" constant="40" id="opi-8J-Rnu"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="profit_right.png" translatesAutoresizingMaskIntoConstraints="NO" id="7yc-LA-DZU">
                        <rect key="frame" x="291" y="12" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="FHZ-24-Ix1"/>
                            <constraint firstAttribute="height" constant="20" id="lrD-ir-aGt"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailingMargin" secondItem="HM2-H1-VwR" secondAttribute="trailing" id="0Gu-5m-ytB"/>
                    <constraint firstItem="jny-Eo-6Zf" firstAttribute="centerY" secondItem="nub-4h-MwN" secondAttribute="centerY" id="3OD-gd-bRY"/>
                    <constraint firstItem="HM2-H1-VwR" firstAttribute="top" secondItem="nub-4h-MwN" secondAttribute="topMargin" id="8YZ-CN-iDG"/>
                    <constraint firstItem="HM2-H1-VwR" firstAttribute="top" secondItem="nub-4h-MwN" secondAttribute="top" constant="2" id="8ec-qS-dQI"/>
                    <constraint firstAttribute="trailing" secondItem="HM2-H1-VwR" secondAttribute="trailing" constant="40" id="ROe-tG-ZLV"/>
                    <constraint firstAttribute="bottomMargin" secondItem="HM2-H1-VwR" secondAttribute="bottom" id="e6k-BC-CS6"/>
                    <constraint firstItem="7yc-LA-DZU" firstAttribute="centerY" secondItem="nub-4h-MwN" secondAttribute="centerY" id="inw-Lb-agj"/>
                    <constraint firstItem="HM2-H1-VwR" firstAttribute="centerY" secondItem="nub-4h-MwN" secondAttribute="centerY" id="lN3-O4-Xy4"/>
                    <constraint firstAttribute="bottom" secondItem="HM2-H1-VwR" secondAttribute="bottom" constant="2" id="p0O-UV-vkr"/>
                    <constraint firstAttribute="trailing" secondItem="7yc-LA-DZU" secondAttribute="trailing" constant="9" id="ql7-wz-EEs"/>
                    <constraint firstItem="jny-Eo-6Zf" firstAttribute="leading" secondItem="nub-4h-MwN" secondAttribute="leading" constant="10" id="tKz-vI-M6m"/>
                </constraints>
                <variation key="default">
                    <mask key="constraints">
                        <exclude reference="0Gu-5m-ytB"/>
                        <exclude reference="8YZ-CN-iDG"/>
                        <exclude reference="8ec-qS-dQI"/>
                        <exclude reference="e6k-BC-CS6"/>
                        <exclude reference="p0O-UV-vkr"/>
                    </mask>
                </variation>
            </tableViewCellContentView>
            <connections>
                <outlet property="imgRight" destination="HM2-H1-VwR" id="JB6-xP-rFK"/>
                <outlet property="labLeftName" destination="jny-Eo-6Zf" id="7Sv-WB-YVe"/>
            </connections>
            <point key="canvasLocation" x="60.799999999999997" y="2.6986506746626691"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="mine_noHeader.png" width="124" height="124"/>
        <image name="profit_right.png" width="72" height="72"/>
    </resources>
</document>
