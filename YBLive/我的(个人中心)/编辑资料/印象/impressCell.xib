<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14460.31" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14460.20"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="impressCell">
            <rect key="frame" x="0.0" y="0.0" width="136" height="36"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="136" height="36"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="GNX-9h-uYB">
                        <rect key="frame" x="0.0" y="0.0" width="136" height="36"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="15"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="21Q-6B-SAG">
                        <rect key="frame" x="0.0" y="0.0" width="136" height="36"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                        </userDefinedRuntimeAttributes>
                    </label>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="21Q-6B-SAG" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="fn8-QF-A0I"/>
                <constraint firstItem="GNX-9h-uYB" firstAttribute="height" secondItem="21Q-6B-SAG" secondAttribute="height" id="fom-gx-fV2"/>
                <constraint firstItem="21Q-6B-SAG" firstAttribute="width" secondItem="gTV-IL-0wX" secondAttribute="width" id="hQU-3F-xf5"/>
                <constraint firstItem="GNX-9h-uYB" firstAttribute="centerY" secondItem="21Q-6B-SAG" secondAttribute="centerY" id="hdH-aX-By6"/>
                <constraint firstItem="GNX-9h-uYB" firstAttribute="centerX" secondItem="21Q-6B-SAG" secondAttribute="centerX" id="vQN-Cb-xtd"/>
                <constraint firstItem="21Q-6B-SAG" firstAttribute="centerY" secondItem="gTV-IL-0wX" secondAttribute="centerY" id="x46-Wr-MLO"/>
                <constraint firstItem="21Q-6B-SAG" firstAttribute="height" secondItem="gTV-IL-0wX" secondAttribute="height" id="xvI-9W-SWB"/>
                <constraint firstItem="GNX-9h-uYB" firstAttribute="width" secondItem="21Q-6B-SAG" secondAttribute="width" id="zKV-pA-fPS"/>
            </constraints>
            <size key="customSize" width="136" height="36"/>
            <connections>
                <outlet property="backImgView" destination="GNX-9h-uYB" id="W8R-79-c66"/>
                <outlet property="contentL" destination="21Q-6B-SAG" id="LfM-ST-Mce"/>
            </connections>
            <point key="canvasLocation" x="198.40000000000001" y="122.33883058470765"/>
        </collectionViewCell>
    </objects>
</document>
