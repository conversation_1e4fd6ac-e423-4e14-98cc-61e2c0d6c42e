//
//  mineVideoCell.m
//  YBLive
//
//  Created by <PERSON> on 2018/12/14.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "mineVideoCell.h"

@implementation mineVideoCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.stateLb.adjustsFontSizeToFitWidth = YES;
}
- (void)setModel:(NearbyVideoModel *)model{
    _model = model;
    [_thumbImgView sd_setImageWithURL:[NSURL URLWithString:_model.videoImage]];
    _numsL.text = _model.views;
    if ([model.status isEqual:@"0"]) {
        self.stateLb.hidden = NO;
        self.stateLb.text = YZMsg(@"审核中");
    }else if ([model.status isEqual:@"2"]){
        self.stateLb.hidden = NO;
        self.stateLb.text = YZMsg(@"已拒绝");
    }else{
        self.stateLb.hidden = YES;

    }
}
@end
