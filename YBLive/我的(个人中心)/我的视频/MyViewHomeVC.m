//
//  MyViewHomeVC.m
//  YBLive
//
//  Created by ybRRR on 2023/6/27.
//  Copyright © 2023 cat. All rights reserved.
//

#import "MyViewHomeVC.h"
#import "SJPageViewController.h"
#import "mineVideoVC.h"
#import "UISegmentedControl+YBSegment.h"
#import "MyLikeVideoVC.h"

@interface MyViewHomeVC ()<SJPageViewControllerDelegate, SJPageViewControllerDataSource>
{
    UISegmentedControl * segment1;
    UILabel *lineLb;
}
@property (nonatomic, strong) SJPageViewController *pageViewController;
@property (nonatomic, strong) NSArray *datas;

@end

@implementation MyViewHomeVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"视频");
    _datas = @[YZMsg(@"我的视频"),YZMsg(@"我的喜欢")];

    _pageViewController = [SJPageViewController pageViewControllerWithOptions:@{SJPageViewControllerOptionInterPageSpacingKey:@(2)}];
    _pageViewController.dataSource = self;
    _pageViewController.delegate = self;
    _pageViewController.view.frame = CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight);
    [self addChildViewController:_pageViewController];
    [self.view addSubview:_pageViewController.view];
    
}
- (SJPageViewControllerHeaderMode)modeForHeaderWithPageViewController:(SJPageViewController *)pageViewController {
    return SJPageViewControllerHeaderModePinnedToTop;
}
- (CGFloat)heightForHeaderPinToVisibleBoundsWithPageViewController:(SJPageViewController *)pageViewController {
    return 0.0001;
}

//返回控制器的数量
- (NSUInteger)numberOfViewControllersInPageViewController:(SJPageViewController *)pageViewController {
    return 2;
}
// 返回`index`对应的控制器
- (UIViewController *)pageViewController:(SJPageViewController *)pageViewController viewControllerAtIndex:(NSInteger)index {
    if (index == 0) {
        mineVideoVC *myView= [[mineVideoVC alloc]init];
        return myView;
    }
    else {
        MyLikeVideoVC *likeView= [[MyLikeVideoVC alloc]init];
        return likeView;
    }
    
}
- (void)pageViewController:(SJPageViewController *)pageViewController focusedIndexDidChange:(NSUInteger)index;
{

    [segment1 setSelectedSegmentIndex:index];
    [self setLineFrameWithIndex:index];
}
- (nullable __kindof UIView *)viewForHeaderInPageViewController:(SJPageViewController *)pageViewController {
    UIView *headerView = [UIView.alloc initWithFrame:CGRectMake(0, 0, _window_width, 50)];
    headerView.backgroundColor = UIColor.whiteColor;
        
    segment1 = [[UISegmentedControl alloc]initWithItems:_datas];
    segment1.frame = CGRectMake(0, 5, _window_width, 40);
    if (@available(iOS 13.0, *)) {
        [segment1 ensureiOS12Style];
    } else {
        segment1.tintColor = [UIColor clearColor];
    }
    NSDictionary *nomalC = [NSDictionary dictionaryWithObjectsAndKeys:[UIFont boldSystemFontOfSize:14],NSFontAttributeName,[UIColor grayColor], NSForegroundColorAttributeName, nil];
    [segment1 setTitleTextAttributes:nomalC forState:UIControlStateNormal];
    
    NSDictionary *selC = [NSDictionary dictionaryWithObjectsAndKeys:[UIFont boldSystemFontOfSize:16],NSFontAttributeName,[UIColor blackColor], NSForegroundColorAttributeName, nil];
    [segment1 setTitleTextAttributes:selC forState:UIControlStateSelected];
    segment1.selectedSegmentIndex = 0;
    [segment1 addTarget:self action:@selector(segmentChange:) forControlEvents:UIControlEventValueChanged];
    [headerView addSubview:segment1];
    
    lineLb = [[UILabel alloc]initWithFrame:CGRectMake(segment1.left +(segment1.width/2/2-20), segment1.bottom, 40, 3)];
    lineLb.backgroundColor = normalColors;
    lineLb.hidden = NO;
    lineLb.layer.cornerRadius = 1.5;
    lineLb.layer.masksToBounds  =YES;
    [headerView addSubview:lineLb];
    [self setLineFrameWithIndex:segment1.selectedSegmentIndex];

    return headerView;
}
-(void)setLineFrameWithIndex:(NSInteger)index{
    CGFloat lineWidth = segment1.width/2;
    CGFloat leftx = 0;
    if (index == 0) {
        leftx= segment1.left +lineWidth/2-20;
    }else{
        leftx = segment1.right-lineWidth/2-20;
    }
    [UIView animateWithDuration:0.3 animations:^{
        lineLb.left = leftx;
    }];
}
- (void)segmentChange:(UISegmentedControl *)seg{
    NSLog(@"点击了-----index:%ld",seg.selectedSegmentIndex) ;

    [_pageViewController setViewControllerAtIndex:seg.selectedSegmentIndex];
    [self setLineFrameWithIndex:seg.selectedSegmentIndex];
}

@end
