//
//  MyLikeVideoVC.m
//  YBLive
//
//  Created by ybRRR on 2023/6/27.
//  Copyright © 2023 cat. All rights reserved.
//

#import "MyLikeVideoVC.h"
#import "YBLookVideoVC.h"
#import "mineVideoCell.h"

@interface MyLikeVideoVC ()<UICollectionViewDelegate,UICollectionViewDataSource>
{
    int page;
    NSMutableArray *infoArray;
}
@property (nonatomic,strong)UICollectionView *collectionView;

@end

@implementation MyLikeVideoVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = [UIColor whiteColor];
    infoArray = [NSMutableArray array];
    page = 1;
    UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
    flow.scrollDirection = UICollectionViewScrollDirectionVertical;
    flow.itemSize = CGSizeMake((_window_width-2)/3, (_window_width-2)/3/25*33);
    flow.minimumLineSpacing = 1;
    flow.minimumInteritemSpacing = 1;
    self.collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height-(64+statusbarHeight)) collectionViewLayout:flow];
    [self.collectionView registerNib:[UINib nibWithNibName:@"mineVideoCell" bundle:nil] forCellWithReuseIdentifier:@"mineVideoCELL"];
    self.collectionView.delegate =self;
    self.collectionView.dataSource = self;
    self.collectionView.backgroundColor = [UIColor whiteColor];
    self.collectionView.mj_footer  = [MJRefreshBackFooter footerWithRefreshingBlock:^{
        page ++;
        [self requestData];
    }];
    
    self.collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        page = 1;
        [self requestData];
    }];
    
    [self.view addSubview:self.collectionView];
    [self requestData];

}

- (void)requestData{

    [YBToolClass postNetworkWithUrl:@"Video.getLikeVideos" andParameter:@{@"p":@(page)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
        if (code == 0) {
            if (page == 1) {
                [infoArray removeAllObjects];
            }
            [infoArray addObjectsFromArray:info];
            if (infoArray.count == 0) {
                [PublicView showTextNoData:_collectionView text1:YZMsg(@"你赞过的作品都会放到这里") text2:@""];
            }else{
                [PublicView hiddenTextNoData:_collectionView];
            }
            [_collectionView reloadData];
        }
    } fail:^{
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
    }];
}
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return infoArray.count;
}

-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    YBLookVideoVC *ybLook = [[YBLookVideoVC alloc]init];
    ybLook.fromWhere = @"myVideoV";
    ybLook.firstPush = YES;
    ybLook.pushPlayIndex = indexPath.row;
    ybLook.sourceBaseUrl = [NSString stringWithFormat:@"%@/?service=Video.getLikeVideos&uid=%@&token=%@&p=%@",purl,[Config getOwnID],[Config getOwnToken],@(page)];
    ybLook.videoList = infoArray;
    ybLook.pages =page;
    ybLook.hidesBottomBarWhenPushed = YES;
    [[MXBADelegate sharedAppDelegate] pushViewController:ybLook animated:YES];
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    mineVideoCell *cell = (mineVideoCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"mineVideoCELL" forIndexPath:indexPath];
    NSDictionary *subdic = infoArray[indexPath.row];
    cell.model = [[NearbyVideoModel alloc] initWithDic:subdic];
    return cell;
}


@end
