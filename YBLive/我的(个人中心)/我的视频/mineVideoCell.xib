<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16086"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="mineVideoCELL" id="gTV-IL-0wX" customClass="mineVideoCell">
            <rect key="frame" x="0.0" y="0.0" width="117" height="152"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="117" height="152"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="nxA-Al-ltJ">
                        <rect key="frame" x="0.0" y="0.0" width="117" height="152"/>
                        <color key="backgroundColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </imageView>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="我的视频封面底部阴影.png" translatesAutoresizingMaskIntoConstraints="NO" id="Uhr-Bf-GRo">
                        <rect key="frame" x="0.0" y="101.5" width="117" height="50.5"/>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="123" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="02V-jt-L1j">
                        <rect key="frame" x="90.5" y="130.5" width="20.5" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="我的视频观看人数.png" translatesAutoresizingMaskIntoConstraints="NO" id="WdL-P8-taC">
                        <rect key="frame" x="74.5" y="131.5" width="13" height="13"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="13" id="5Po-gQ-SXq"/>
                            <constraint firstAttribute="height" constant="13" id="SP8-tc-Ced"/>
                        </constraints>
                    </imageView>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="审核中" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sDp-fw-80n">
                        <rect key="frame" x="71" y="7" width="40" height="16"/>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.36507708937448219" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="40" id="7eb-LU-cXT"/>
                            <constraint firstAttribute="height" constant="16" id="TrB-Ed-b7s"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="8"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </label>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="Uhr-Bf-GRo" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="6Qm-qg-XJA"/>
                <constraint firstItem="WdL-P8-taC" firstAttribute="centerY" secondItem="02V-jt-L1j" secondAttribute="centerY" id="6Vt-DV-f3Q"/>
                <constraint firstAttribute="bottom" secondItem="Uhr-Bf-GRo" secondAttribute="bottom" id="EtY-rL-aRX"/>
                <constraint firstItem="Uhr-Bf-GRo" firstAttribute="height" secondItem="gTV-IL-0wX" secondAttribute="height" multiplier="1/3" id="GKq-g1-vcB"/>
                <constraint firstAttribute="bottom" secondItem="02V-jt-L1j" secondAttribute="bottom" constant="7" id="IcU-3k-FUw"/>
                <constraint firstItem="nxA-Al-ltJ" firstAttribute="centerY" secondItem="gTV-IL-0wX" secondAttribute="centerY" id="KWo-1Q-hlD"/>
                <constraint firstItem="nxA-Al-ltJ" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="M33-T4-EpM"/>
                <constraint firstAttribute="trailing" secondItem="sDp-fw-80n" secondAttribute="trailing" constant="6" id="Rvc-dK-RuE"/>
                <constraint firstItem="sDp-fw-80n" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="7" id="XYB-gf-2mL"/>
                <constraint firstItem="nxA-Al-ltJ" firstAttribute="height" secondItem="gTV-IL-0wX" secondAttribute="height" id="a1q-3m-Jhr"/>
                <constraint firstItem="nxA-Al-ltJ" firstAttribute="width" secondItem="gTV-IL-0wX" secondAttribute="width" id="cEA-hr-cMC"/>
                <constraint firstAttribute="trailing" secondItem="Uhr-Bf-GRo" secondAttribute="trailing" id="gg7-Vt-8FV"/>
                <constraint firstItem="02V-jt-L1j" firstAttribute="leading" secondItem="WdL-P8-taC" secondAttribute="trailing" constant="3" id="i98-Mf-6r8"/>
                <constraint firstAttribute="trailing" secondItem="02V-jt-L1j" secondAttribute="trailing" constant="6" id="x6E-Bz-NCx"/>
            </constraints>
            <size key="customSize" width="117" height="152"/>
            <connections>
                <outlet property="numsL" destination="02V-jt-L1j" id="P7q-Rb-Jue"/>
                <outlet property="stateLb" destination="sDp-fw-80n" id="WKM-b7-P42"/>
                <outlet property="thumbImgView" destination="nxA-Al-ltJ" id="6ye-az-vh6"/>
            </connections>
            <point key="canvasLocation" x="93.599999999999994" y="55.772113943028494"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="我的视频封面底部阴影.png" width="248" height="120"/>
        <image name="我的视频观看人数.png" width="30" height="36"/>
    </resources>
</document>
