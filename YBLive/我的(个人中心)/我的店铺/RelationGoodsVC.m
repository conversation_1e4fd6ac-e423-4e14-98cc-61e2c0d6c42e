//
//  RelationGoodsVC.m
//  YBLive
//
//  Created by IOS1 on 2019/8/30.
//  Copyright © 2019 cat. All rights reserved.
//

#import "RelationGoodsVC.h"
#import "goodsShowCell.h"

@interface RelationGoodsVC ()<UITableViewDelegate,UITableViewDataSource,UITextFieldDelegate>{
    int page;
    NSMutableArray *goodsList;
    RelationGoodsModel *selectModel;
    
    NSString *pullUrl;
}
@property (nonatomic,strong)UITextField *searchT;
@property (nonatomic,strong) UITableView *goodsTableV;

@end

@implementation RelationGoodsVC
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    if (_isVideo) {
        self.titleL.text = YZMsg(@"关联商品");
    }else{
        self.titleL.text = YZMsg(@"添加商品");
    }
}
- (void)viewDidLoad {
    [super viewDidLoad];
    page = 1;
    goodsList = [NSMutableArray array];
    if (_isOtherSale) {
        pullUrl = @"Shop.searchOnsalePlatformGoods";
    }else{
        pullUrl = @"Shop.searchShopGoods";
    }
    [self creatSearch];
    [self.view addSubview:self.goodsTableV];
    [self searchAnchorWithText:@""];
}
- (void)creatSearch{
    _searchT = [[UITextField alloc]initWithFrame:CGRectMake(13, statusbarHeight + 64 + 8, _window_width-26, 30)];
    _searchT.backgroundColor = RGB_COLOR(@"#fafafa", 1);
    _searchT.font = SYS_Font(15);
    _searchT.placeholder = YZMsg(@"请输入商品名称");
    _searchT.layer.cornerRadius = 15;
    _searchT.layer.masksToBounds = YES;
    _searchT.delegate = self;
    _searchT.leftViewMode = UITextFieldViewModeAlways;
    _searchT.keyboardType = UIKeyboardTypeWebSearch;
    UIImageView *leftImgView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, 30, 30)];
    leftImgView.image = [UIImage imageNamed:@"left_search"];
    _searchT.leftView = leftImgView;
    [self.view addSubview:_searchT];
}
- (UITableView *)goodsTableV{
    if (!_goodsTableV) {
        _goodsTableV = [[UITableView alloc]initWithFrame:CGRectMake(0, _searchT.bottom+8, _window_width, _window_height-ShowDiff-(_searchT.bottom+8)) style:0];
        _goodsTableV.delegate = self;
        _goodsTableV.dataSource = self;
        _goodsTableV.separatorStyle = 0;
        _goodsTableV.backgroundColor = [UIColor whiteColor];
        _goodsTableV.mj_header = [MJRefreshHeader headerWithRefreshingBlock:^{
            page = 1;
            [self searchAnchorWithText:_searchT.text];
        }];
        _goodsTableV.mj_footer = [MJRefreshFooter footerWithRefreshingBlock:^{
            page ++;
            [self searchAnchorWithText:_searchT.text];
        }];

    }
    return _goodsTableV;
}
#pragma mark ================ searchBar代理 ===============
- (BOOL)textFieldShouldReturn:(UITextField *)textField{
    [_searchT resignFirstResponder];
    page = 1;
    [self searchAnchorWithText:_searchT.text];
    return YES;
}
- (void)searchAnchorWithText:(NSString *)searchStr{
    [YBToolClass postNetworkWithUrl:[NSString stringWithFormat:@"%@&keywords=%@&p=%@",pullUrl,searchStr,@(page)] andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [_goodsTableV.mj_header endRefreshing];
        [_goodsTableV.mj_footer endRefreshing];
        if (code == 0) {
            if (page == 1) {
                [goodsList removeAllObjects];
            }
            for (NSDictionary *dic in info) {
                RelationGoodsModel *model = [[RelationGoodsModel alloc]initWithDic:dic];
                model.goosdType = 3;
                [goodsList addObject:model];
            }
            [_goodsTableV reloadData];
        }

    } fail:^{
        [_goodsTableV.mj_header endRefreshing];
        [_goodsTableV.mj_footer endRefreshing];

    }];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return goodsList.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    goodsShowCell *cell = [tableView dequeueReusableCellWithIdentifier:@"goodsShowCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"goodsShowCell" owner:nil options:nil] lastObject];
    }
    RelationGoodsModel *model = goodsList[indexPath.row];
    if (_isVideo) {
        cell.setBtn.hidden = YES;
        cell.stateImgV.hidden = NO;
        if ([model.goodsid isEqual:_selectID]) {
            cell.stateImgV.image = [UIImage imageNamed:@"shop_sel"];
            if (!selectModel) {
                selectModel = model;
            }
        }else{
            cell.stateImgV.image = [UIImage imageNamed:@"shop_nor"];
        }
    }else{
        cell.setBtn.hidden = NO;
        cell.stateImgV.hidden = YES;
        if (_isOtherSale) {
            cell.priceOldL.hidden = NO;
            cell.priceOldL.text =[NSString stringWithFormat:@"%@ %@%@",YZMsg(@"佣"),YZMsg(@"¥"),model.commission];
            cell.priceOldL.textColor = normalColors;
            model.isOtherSale = @"1";
        }
    }
    cell.model = model;
    cell.haveList = self.haveGoods;
    cell.numberLb.hidden = YES;
    return cell;

}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 101;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (_isVideo) {
        RelationGoodsModel *model = goodsList[indexPath.row];
        if (model.goodsid == _selectID) {
            _selectID = @"0";
            selectModel = nil;
        }else{
            selectModel = model;
            _selectID = selectModel.goodsid;
        }
        [tableView reloadData];
    }
}
- (void)doReturn{
    if (_isVideo) {
        
        if (selectModel) {
            self.block(selectModel.name, selectModel.goodsid);
        }else{
            self.block(@"关联商品", @"0");
        }
    }
    [super doReturn];
}
//ray---v210423
-(void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [_searchT resignFirstResponder];
}
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [_searchT resignFirstResponder];
}
@end
