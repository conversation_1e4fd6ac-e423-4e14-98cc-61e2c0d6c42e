//
//  goodsShowCell.h
//  YBLive
//
//  Created by IOS1 on 2019/8/30.
//  Copyright © 2019 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "RelationGoodsModel.h"

NS_ASSUME_NONNULL_BEGIN
@protocol goodsShowCellDelegate <NSObject>
-(void)clickToShopLook;
-(void)removeThisGoods:(RelationGoodsModel *)model;
-(void)reloadShowGoods:(NSDictionary *)infos andmodel:(RelationGoodsModel *)model;
@end
@interface goodsShowCell : UITableViewCell
@property (weak, nonatomic) IBOutlet UIImageView *thumbImgV;
@property (weak, nonatomic) IBOutlet UILabel *nameL;
@property (weak, nonatomic) IBOutlet UILabel *priceL;
@property (weak, nonatomic) IBOutlet UILabel *priceOldL;
@property (weak, nonatomic) IBOutlet UIButton *setBtn;
@property (weak, nonatomic) IBOutlet UIImageView *stateImgV;
@property (weak, nonatomic) IBOutlet UILabel *countLb;
@property (strong, nonatomic) IBOutlet UIView *lineView;
@property (strong, nonatomic) IBOutlet UIView *liveGoodsView;
@property (strong, nonatomic) IBOutlet UIButton *showBtn;
@property (strong, nonatomic) IBOutlet UIButton *removeBtn;
@property (strong, nonatomic) NSString *liveUid;
@property (nonatomic,strong) RelationGoodsModel *model;
@property (nonatomic,weak) id<goodsShowCellDelegate> delegate;
@property (nonatomic,strong)NSMutableArray *haveList;
@property (weak, nonatomic) IBOutlet UILabel *numberLb;

@end

NS_ASSUME_NONNULL_END
