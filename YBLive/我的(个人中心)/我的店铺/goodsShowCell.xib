<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="goodsShowCELL" rowHeight="104" id="KGk-i7-Jjw" customClass="goodsShowCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="104"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="104"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="5WE-rn-5hM">
                        <rect key="frame" x="10" y="14.5" width="75" height="75"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="75" id="QuN-0c-Vhn"/>
                            <constraint firstAttribute="width" constant="75" id="qW3-X2-0Xd"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="URU-qe-lqN">
                        <rect key="frame" x="104" y="19.5" width="136" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="¥ 80" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CIG-m7-dbM">
                        <rect key="frame" x="104" y="68.5" width="30" height="16"/>
                        <fontDescription key="fontDescription" type="boldSystem" pointSize="13"/>
                        <color key="textColor" red="0.98431372549999996" green="0.28235294119999998" blue="0.54901960780000003" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="¥99" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Qhc-pN-fHL">
                        <rect key="frame" x="149" y="68.5" width="25" height="16"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.78823529411764703" green="0.78823529411764703" blue="0.78823529411764703" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mzF-Gr-8H7">
                        <rect key="frame" x="149" y="76" width="25" height="1"/>
                        <color key="backgroundColor" red="0.78823529410000004" green="0.78823529410000004" blue="0.78823529410000004" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="qhD-6f-wwp"/>
                        </constraints>
                    </view>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EFM-4v-GKS">
                        <rect key="frame" x="250" y="40" width="60" height="24"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="60" id="daz-y8-L0v"/>
                            <constraint firstAttribute="height" constant="24" id="u80-TR-I84"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="boldSystem" pointSize="11"/>
                        <state key="normal" title="去看看">
                            <color key="titleColor" red="0.98431372549999996" green="0.28235294119999998" blue="0.2274509804" alpha="1" colorSpace="calibratedRGB"/>
                        </state>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="12"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="1"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" red="0.98431372549999996" green="0.28235294119999998" blue="0.2274509804" alpha="1" colorSpace="calibratedRGB"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                        <connections>
                            <action selector="setBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="bwH-bZ-2nI"/>
                        </connections>
                    </button>
                    <imageView hidden="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="MQZ-mE-ThN">
                        <rect key="frame" x="289" y="44" width="16" height="16"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="16" id="YAP-XT-C3e"/>
                            <constraint firstAttribute="width" constant="16" id="eeZ-cX-hNO"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="C9c-l6-ayG">
                        <rect key="frame" x="10" y="103" width="300" height="1"/>
                        <color key="backgroundColor" red="0.94117647058823528" green="0.94117647058823528" blue="0.94117647058823528" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="I1k-zU-e6c"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="s0t-1E-bbN">
                        <rect key="frame" x="104" y="46.5" width="33" height="16"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.98431372549019602" green="0.28235294117647058" blue="0.5490196078431373" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YfH-aE-h6T">
                        <rect key="frame" x="227" y="38" width="83" height="28"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fDN-uh-B7q">
                                <rect key="frame" x="41" y="10" width="1" height="8"/>
                                <color key="backgroundColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="1" id="Hhd-vP-Qxa"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TUx-YN-PXr">
                                <rect key="frame" x="0.0" y="0.0" width="39" height="28"/>
                                <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                <state key="normal" title="展示">
                                    <color key="titleColor" red="0.98431372549999996" green="0.28235294119999998" blue="0.54901960780000003" alpha="1" colorSpace="calibratedRGB"/>
                                </state>
                                <connections>
                                    <action selector="showBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="Qlb-B7-Nqc"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7rL-Sa-rcj">
                                <rect key="frame" x="44" y="0.0" width="39" height="28"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <state key="normal" title="移除">
                                    <color key="titleColor" red="0.98431372549999996" green="0.28235294119999998" blue="0.54901960780000003" alpha="1" colorSpace="calibratedRGB"/>
                                </state>
                                <connections>
                                    <action selector="removeBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="YhH-zu-OwN"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="TUx-YN-PXr" firstAttribute="leading" secondItem="YfH-aE-h6T" secondAttribute="leading" id="5pd-yc-shH"/>
                            <constraint firstAttribute="bottom" secondItem="7rL-Sa-rcj" secondAttribute="bottom" id="8dq-15-f8R"/>
                            <constraint firstAttribute="height" constant="28" id="9m5-A2-gi7"/>
                            <constraint firstItem="fDN-uh-B7q" firstAttribute="top" secondItem="YfH-aE-h6T" secondAttribute="top" constant="10" id="B25-D1-wPW"/>
                            <constraint firstItem="TUx-YN-PXr" firstAttribute="top" secondItem="YfH-aE-h6T" secondAttribute="top" id="DMi-XL-DLZ"/>
                            <constraint firstItem="7rL-Sa-rcj" firstAttribute="top" secondItem="YfH-aE-h6T" secondAttribute="top" id="H6k-jt-XJk"/>
                            <constraint firstItem="fDN-uh-B7q" firstAttribute="leading" secondItem="TUx-YN-PXr" secondAttribute="trailing" constant="2" id="QNp-uy-xnz"/>
                            <constraint firstItem="fDN-uh-B7q" firstAttribute="centerX" secondItem="YfH-aE-h6T" secondAttribute="centerX" id="UCA-Tp-VSi"/>
                            <constraint firstAttribute="bottom" secondItem="fDN-uh-B7q" secondAttribute="bottom" constant="10" id="UfO-VU-FD3"/>
                            <constraint firstAttribute="trailing" secondItem="7rL-Sa-rcj" secondAttribute="trailing" id="eya-lW-KOv"/>
                            <constraint firstAttribute="bottom" secondItem="TUx-YN-PXr" secondAttribute="bottom" id="hGW-98-ldA"/>
                            <constraint firstAttribute="width" constant="83" id="rqf-Ea-1r4"/>
                            <constraint firstItem="7rL-Sa-rcj" firstAttribute="leading" secondItem="fDN-uh-B7q" secondAttribute="trailing" constant="2" id="spv-ek-91H"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="14"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" red="0.98431372549999996" green="0.28235294119999998" blue="0.54901960780000003" alpha="1" colorSpace="calibratedRGB"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="1"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="32S-9N-E9k">
                        <rect key="frame" x="10" y="14.5" width="30" height="17"/>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.39966256277901785" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="17" id="7bV-cL-dcD"/>
                            <constraint firstAttribute="width" constant="30" id="Gag-fn-98d"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="mzF-Gr-8H7" firstAttribute="trailing" secondItem="Qhc-pN-fHL" secondAttribute="trailing" id="5Wq-ze-FKa"/>
                    <constraint firstAttribute="bottom" secondItem="C9c-l6-ayG" secondAttribute="bottom" id="8wN-Xo-WaT"/>
                    <constraint firstItem="s0t-1E-bbN" firstAttribute="top" secondItem="URU-qe-lqN" secondAttribute="bottom" constant="10" id="AjP-N9-IcV"/>
                    <constraint firstItem="C9c-l6-ayG" firstAttribute="leading" secondItem="5WE-rn-5hM" secondAttribute="leading" id="Bg1-sC-xz2"/>
                    <constraint firstItem="Qhc-pN-fHL" firstAttribute="leading" secondItem="CIG-m7-dbM" secondAttribute="trailing" constant="15" id="Eg2-H1-htc"/>
                    <constraint firstItem="CIG-m7-dbM" firstAttribute="leading" secondItem="URU-qe-lqN" secondAttribute="leading" id="Jsq-Kw-bY7"/>
                    <constraint firstAttribute="trailing" secondItem="YfH-aE-h6T" secondAttribute="trailing" constant="10" id="MBT-rB-fkx"/>
                    <constraint firstItem="s0t-1E-bbN" firstAttribute="leading" secondItem="URU-qe-lqN" secondAttribute="leading" id="Ny6-OI-uML"/>
                    <constraint firstItem="5WE-rn-5hM" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="O0B-Zy-WBq"/>
                    <constraint firstItem="32S-9N-E9k" firstAttribute="top" secondItem="5WE-rn-5hM" secondAttribute="top" id="Oc7-cB-iJy"/>
                    <constraint firstItem="5WE-rn-5hM" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="OyM-uh-kd7"/>
                    <constraint firstItem="EFM-4v-GKS" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="VQA-Ea-NlE"/>
                    <constraint firstAttribute="trailing" secondItem="MQZ-mE-ThN" secondAttribute="trailing" constant="15" id="W3v-gg-8Z4"/>
                    <constraint firstItem="URU-qe-lqN" firstAttribute="leading" secondItem="5WE-rn-5hM" secondAttribute="trailing" constant="19" id="Yr4-oK-wa6"/>
                    <constraint firstAttribute="trailing" secondItem="EFM-4v-GKS" secondAttribute="trailing" constant="10" id="ZPR-py-inO"/>
                    <constraint firstItem="URU-qe-lqN" firstAttribute="top" secondItem="5WE-rn-5hM" secondAttribute="top" constant="5" id="gP4-2Z-L45"/>
                    <constraint firstItem="mzF-Gr-8H7" firstAttribute="centerY" secondItem="Qhc-pN-fHL" secondAttribute="centerY" id="gbX-LN-tQO"/>
                    <constraint firstItem="Qhc-pN-fHL" firstAttribute="bottom" secondItem="CIG-m7-dbM" secondAttribute="bottom" id="hPU-iJ-8sy"/>
                    <constraint firstItem="YfH-aE-h6T" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="hRr-GT-FQD"/>
                    <constraint firstAttribute="trailing" secondItem="URU-qe-lqN" secondAttribute="trailing" constant="80" id="kH3-4k-94A"/>
                    <constraint firstItem="mzF-Gr-8H7" firstAttribute="leading" secondItem="Qhc-pN-fHL" secondAttribute="leading" id="kII-nH-XWa"/>
                    <constraint firstItem="MQZ-mE-ThN" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="pwv-xs-7KB"/>
                    <constraint firstItem="32S-9N-E9k" firstAttribute="leading" secondItem="5WE-rn-5hM" secondAttribute="leading" id="sn2-M1-QS3"/>
                    <constraint firstItem="C9c-l6-ayG" firstAttribute="trailing" secondItem="EFM-4v-GKS" secondAttribute="trailing" id="wMa-4G-2Ak"/>
                    <constraint firstItem="CIG-m7-dbM" firstAttribute="bottom" secondItem="5WE-rn-5hM" secondAttribute="bottom" constant="-5" id="yiI-Ru-qbH"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="countLb" destination="s0t-1E-bbN" id="xru-g1-R7z"/>
                <outlet property="lineView" destination="mzF-Gr-8H7" id="JRV-Vh-wgi"/>
                <outlet property="liveGoodsView" destination="YfH-aE-h6T" id="nhC-im-V7f"/>
                <outlet property="nameL" destination="URU-qe-lqN" id="k4j-rV-zKC"/>
                <outlet property="numberLb" destination="32S-9N-E9k" id="YDQ-Y7-0cK"/>
                <outlet property="priceL" destination="CIG-m7-dbM" id="5Ce-04-tr1"/>
                <outlet property="priceOldL" destination="Qhc-pN-fHL" id="vj6-I0-CnI"/>
                <outlet property="removeBtn" destination="7rL-Sa-rcj" id="AXT-ku-Qqz"/>
                <outlet property="setBtn" destination="EFM-4v-GKS" id="Tcw-a0-ssF"/>
                <outlet property="showBtn" destination="TUx-YN-PXr" id="vF9-Qk-PYb"/>
                <outlet property="stateImgV" destination="MQZ-mE-ThN" id="rAQ-qi-fkW"/>
                <outlet property="thumbImgV" destination="5WE-rn-5hM" id="uOu-oL-vKU"/>
            </connections>
            <point key="canvasLocation" x="-94.400000000000006" y="90.854572713643179"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
