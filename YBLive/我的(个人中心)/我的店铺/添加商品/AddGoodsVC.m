//
//  videoAddGoodsVC.m
//  iphoneLive
//
//  Created by IOS1 on 2019/7/3.
//  Copyright © 2019 cat. All rights reserved.
//

#import "AddGoodsVC.h"
#import <Qiniu/QiniuSDK.h>
#import "CommodityDetailModel.h"

@interface AddGoodsVC ()<UIImagePickerControllerDelegate,UINavigationControllerDelegate,UITextViewDelegate>{
    UITextView *linkT;
    UITextView *nameT;
    UITextView *yuanjiaT;
    UITextView *xianjiaT;
    UITextView *contentT;
    UIButton *shopImgBtn;
    UIImage *selectImg;
    UIButton *addBtn;
    NSMutableArray *btnArray;
    UIView *moveLineView;
    UIScrollView *backScroll;
    
    UIScrollView *wxScroll;
    UIButton *wxAddBtn;
    UITextView *idT;
    UITextView *nameT2;
    UITextView *yuanjiaT2;
    UITextView *xianjiaT2;
    UITextView *contentT2;
    UIImageView *goodsThumbImgV;
    NSString *addType;
    NSDictionary *wxGoodsDic;
    
    NSString *thumbImgStr;
}
@property (nonatomic, strong)CommodityDetailModel *commodityModel;

@end

@implementation AddGoodsVC

#pragma mark----获取商品详情------
-(void)getGoodInfo{
    NSString *url = [purl stringByAppendingFormat:@"?service=Shop.getGoodsInfo"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"goodsid":self.goodsID,
                          };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            NSLog(@"commodity-----data:%@",data);
            self.commodityModel = [CommodityDetailModel modelWithDic:[[data valueForKey:@"info"]firstObject]];
            [self reloadUIData];
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];

}
-(void)reloadUIData{
    linkT.text = self.commodityModel.href;
    nameT.text = self.commodityModel.name;
    xianjiaT.text = [NSString stringWithFormat:@"¥%@",self.commodityModel.present_price];
    yuanjiaT.text = [NSString stringWithFormat:@"¥%@",self.commodityModel.original_price];
    contentT.text = self.commodityModel.goods_desc;
    thumbImgStr = self.commodityModel.thumbs_format[0];
    [shopImgBtn sd_setImageWithURL:[NSURL URLWithString:thumbImgStr] forState:0];
    selectImg = shopImgBtn.imageView.image;
    addBtn.alpha = 1;
    addBtn.userInteractionEnabled = YES;

}

- (void)ChangeBtnBackground{
    if (linkT.text.length > 0 && nameT.text.length > 0 && xianjiaT.text.length > 0  && contentT.text.length > 0 && selectImg ) {
        addBtn.alpha = 1;
        addBtn.userInteractionEnabled = YES;
    }else{
        addBtn.alpha = 0.5;
        addBtn.userInteractionEnabled = NO;
    }
}
- (void)topBtnClick:(UIButton *)sender{
    if (sender.selected) {
        return;
    }
    [UIView animateWithDuration:0.3 animations:^{
        moveLineView.centerX = sender.centerX;
    }];
    for (UIButton *btn in btnArray) {
        if (sender == btn) {
            btn.selected = YES;
        }else{
            btn.selected = NO;
        }
    }
    if (sender.tag == 1000) {
        addType = @"0";
        backScroll.hidden = NO;
        wxScroll.hidden = YES;
    }else{
        addType = @"1";
        backScroll.hidden = YES;
        wxScroll.hidden = NO;
    }
}
- (void)creatNaviIteam{
    btnArray = [NSMutableArray array];
    NSArray *array = @[@"淘宝商品",@"商城小程序"];
    if (![[common applets_switch] isEqual:@"1"]) {
        array = @[@"淘宝商品"];
    }
    for (int i = 0; i < array.count; i ++) {
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(_window_width/2-100 + i *100, 24+statusbarHeight, 100, 40);
        if (array.count == 1) {
            btn.frame = CGRectMake(_window_width/2-50, 24+statusbarHeight, 100, 40);
        }
        [btn setTitle:array[i] forState:0];
        [btn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
        [btn setTitleColor:RGB_COLOR(@"#323232", 1) forState:UIControlStateSelected];
        btn.titleLabel.font = SYS_Font(16);
        [btn addTarget:self action:@selector(topBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        btn.tag = 1000 + i;
        [self.naviView addSubview:btn];
        [btnArray addObject:btn];
        if (i == 0) {
            moveLineView = [[UIView alloc]initWithFrame:CGRectMake(btn.centerX-7.5, btn.bottom-5, 15, 3)];
            moveLineView.backgroundColor = normalColors;
            moveLineView.layer.cornerRadius =1;
            moveLineView.layer.masksToBounds = YES;
            [self.naviView addSubview:moveLineView];
        }
    }
}
- (void)viewDidLoad {
    [super viewDidLoad];
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(selfTapClick)];
    [self.view addGestureRecognizer:tap];
    addType = @"0";
    thumbImgStr = @"";
    self.titleL.text = YZMsg(@"站外商品");
    self.titleL.font = [UIFont systemFontOfSize:15];
//    [self creatNaviIteam];
    [self creatUI];
//    if ([[common applets_switch] isEqual:@"1"]) {
//        [self creatWXScroll];
//    }
    if ([_fromWhere isEqual:@"seller"]) {
        [self getGoodInfo];
    }
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ChangeBtnBackground) name:UITextViewTextDidChangeNotification object:nil];
    //增加监听，当键盘出现或改变时收出消息
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];

}
- (void)creatUI{
    backScroll = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight)];
    [self.view addSubview:backScroll];
    NSArray *array = @[YZMsg(@"*商品链接"),YZMsg(@"*商品名称(最多可输入20个字)"),YZMsg(@"*原价"),YZMsg(@"*现价"),YZMsg(@"*商品简介(最多可输入50个字)"),YZMsg(@"*商品图片")];
    CGFloat yyyy = 0.0;
    for (int i = 0; i < array.count; i ++) {
        UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, yyyy, _window_width, 30)];
        [backScroll addSubview:view];
        UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(20, 0, view.width-20, view.height)];
        label.textColor = RGB_COLOR(@"#969696", 1);
        label.font = [UIFont systemFontOfSize:12];
        [view addSubview:label];
        label.attributedText = [self setAttStr:array[i]];
        UIView *textView = [[UIView alloc]initWithFrame:CGRectMake(0, view.bottom, _window_width, 40)];
        textView.backgroundColor = RGB_COLOR(@"#FAFAFA", 1);
        if (i == 4) {
            textView.height = 70;
        }
        if (i == 5) {
            textView.height = 120;
        }
        [backScroll addSubview:textView];
        
        if (i != 5) {
            UITextView *textT = [[UITextView alloc]initWithFrame:CGRectMake(20, 0, textView.width-40, 40)];
            textT.font = [UIFont systemFontOfSize:13];
            textT.backgroundColor = [UIColor clearColor];
            textT.delegate = self;
            textT.returnKeyType = UIReturnKeyDone;
            [textView addSubview:textT];
            switch (i) {
                case 0:
                    
                    linkT = textT;
                    if (_goodsInfo) {
                        linkT.text = minstr([_goodsInfo valueForKey:@"href"]);
                    }
                    break;
                case 1:
                    nameT = textT;
                    if (_goodsInfo) {
                        nameT.text = minstr([_goodsInfo valueForKey:@"name"]);
                    }

                    break;
                case 2:
                    yuanjiaT = textT;
                    if ([_goodsInfo valueForKey:@"old_price"]) {
                        yuanjiaT.text = minstr([_goodsInfo valueForKey:@"old_price"]);
                    }

                    break;
                case 3:
                    xianjiaT = textT;
                    if (_goodsInfo) {
                        xianjiaT.text = minstr([_goodsInfo valueForKey:@"price"]);
                    }

                    break;
                case 4:
                    textT.returnKeyType = UIReturnKeyDefault;

                    contentT = textT;
                    if (_goodsInfo) {
                        contentT.text = minstr([_goodsInfo valueForKey:@"des"]);
                    }

                    break;

                default:
                    break;
            }
        }else{
            textView.backgroundColor = [UIColor clearColor];
            shopImgBtn = [UIButton buttonWithType:0];
            shopImgBtn.frame = CGRectMake(15, 0, 90, 120);
            [shopImgBtn setImage:[UIImage imageNamed:@"video_shop_add"] forState:0];
//            [shopImgBtn setBackgroundColor:[[UIColor whiteColor] colorWithAlphaComponent:0.03]];
            [shopImgBtn addTarget:self action:@selector(shopImgBtnClick:) forControlEvents:UIControlEventTouchUpInside];
            shopImgBtn.imageView.contentMode = UIViewContentModeScaleAspectFill;
            [textView addSubview:shopImgBtn];
            if (_goodsInfo) {
                selectImg = [_goodsInfo valueForKey:@"thumb"];
                [shopImgBtn setImage:selectImg forState:0];
            }

        }
        
        yyyy = textView.bottom;
        
    }
    addBtn = [UIButton buttonWithType:0];
    addBtn.frame = CGRectMake(15, yyyy + 20, _window_width-30, 40);
//    [addBtn setBackgroundColor:RGB_COLOR(@"#EA377F", 1)];
    [addBtn setBackgroundImage:[UIImage imageNamed:@"startLive_back"]];
    addBtn.alpha = 0.5;
    addBtn.userInteractionEnabled = NO;
    [addBtn addTarget:self action:@selector(addBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [addBtn setTitle:YZMsg(@"确认添加商品") forState:0];
    addBtn.layer.cornerRadius = 20;
    addBtn.layer.masksToBounds = YES;
    addBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [backScroll addSubview:addBtn];
    backScroll.contentSize = CGSizeMake(0, addBtn.bottom);
}
- (void)creatWXScroll{
    
    wxScroll = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight)];
    wxScroll.hidden = YES;
    [self.view addSubview:wxScroll];
    NSArray *array = @[YZMsg(@"商品ID（输入小程序中商品对应ID，将自动获取商品信息）"),YZMsg(@"商品名称"),YZMsg(@"原价"),YZMsg(@"现价"),YZMsg(@"商品简介"),YZMsg(@"商品图片")];
    CGFloat yyyy = 0.0;
    for (int i = 0; i < array.count; i ++) {
        UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, yyyy, _window_width, 30)];
        [wxScroll addSubview:view];
        UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(20, 0, view.width-20, view.height)];
        label.textColor = RGB_COLOR(@"#969696", 1);
        label.font = [UIFont systemFontOfSize:12];
        [view addSubview:label];
        UIView *textView = [[UIView alloc]initWithFrame:CGRectMake(0, view.bottom, _window_width, 40)];
        if (i == 4) {
            textView.height = 80;
        }
        if (i == 5) {
            textView.height = 90;
        }
        [wxScroll addSubview:textView];
        if (i == 0) {
            label.attributedText = [self setWXAttStr:array[i]];
            textView.userInteractionEnabled = YES;
            textView.backgroundColor = [UIColor whiteColor];
        }else{
            label.text = array[i];
            textView.userInteractionEnabled = NO;
            textView.backgroundColor = RGB_COLOR(@"#FAFAFA", 1);

        }

        if (i != 5) {
            UITextView *textT = [[UITextView alloc]initWithFrame:CGRectMake(20, 0, textView.width-40, 40)];
            textT.font = [UIFont systemFontOfSize:13];
            textT.backgroundColor = [UIColor clearColor];
            textT.delegate = self;
            [textView addSubview:textT];
            if (i == 0) {
                textT.width = textView.width-150;
                textT.layer.borderWidth = 1;
                textT.layer.borderColor = RGB_COLOR(@"#eeeeee", 1).CGColor;
                UIButton *getGoodsMsgBtn = [UIButton buttonWithType:0];
                getGoodsMsgBtn.frame = CGRectMake(textT.right + 10, textT.top+5, 100, 30);
                [getGoodsMsgBtn setBackgroundImage:[UIImage imageNamed:@"startLive_back"]];
                [getGoodsMsgBtn addTarget:self action:@selector(getGoodsMsgBtnClick) forControlEvents:UIControlEventTouchUpInside];
                [getGoodsMsgBtn setTitle:@"获取商品信息" forState:0];
                getGoodsMsgBtn.layer.cornerRadius = 15;
                getGoodsMsgBtn.layer.masksToBounds = YES;
                getGoodsMsgBtn.titleLabel.font = [UIFont systemFontOfSize:12];
                [textView addSubview:getGoodsMsgBtn];

            }
            switch (i) {
                case 0:

                    idT = textT;
                    if (_goodsInfo) {
                        linkT.text = minstr([_goodsInfo valueForKey:@"href"]);
                    }
                    break;
                case 1:
                    nameT2 = textT;
                    if (_goodsInfo) {
                        nameT.text = minstr([_goodsInfo valueForKey:@"name"]);
                    }
                    
                    break;
                case 2:
                    yuanjiaT2 = textT;
                    if ([_goodsInfo valueForKey:@"old_price"]) {
                        yuanjiaT.text = minstr([_goodsInfo valueForKey:@"old_price"]);
                    }
                    
                    break;
                case 3:
                    xianjiaT2 = textT;
                    if (_goodsInfo) {
                        xianjiaT.text = minstr([_goodsInfo valueForKey:@"price"]);
                    }
                    
                    break;
                case 4:
                    contentT2 = textT;
                    if (_goodsInfo) {
                        contentT.text = minstr([_goodsInfo valueForKey:@"des"]);
                    }
                    
                    break;
                    
                default:
                    break;
            }
        }else{
            textView.backgroundColor = [UIColor clearColor];
            
            goodsThumbImgV = [[UIImageView alloc]initWithFrame:CGRectMake(15, 0, 90, 90)];
            goodsThumbImgV.backgroundColor = RGB_COLOR(@"#fafafa", 1);
            goodsThumbImgV.contentMode = UIViewContentModeScaleAspectFill;
            goodsThumbImgV.clipsToBounds = YES;
            [textView addSubview:goodsThumbImgV];
        }
        
        yyyy = textView.bottom;
        
    }
    wxAddBtn = [UIButton buttonWithType:0];
    wxAddBtn.frame = CGRectMake(15, yyyy + 20, _window_width-30, 40);
    //    [addBtn setBackgroundColor:RGB_COLOR(@"#EA377F", 1)];
    [wxAddBtn setBackgroundImage:[UIImage imageNamed:@"startLive_back"]];
    wxAddBtn.alpha = 0.5;
    wxAddBtn.userInteractionEnabled = NO;
    [wxAddBtn addTarget:self action:@selector(wxAddBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [wxAddBtn setTitle:YZMsg(@"确认添加商品") forState:0];
    wxAddBtn.layer.cornerRadius = 20;
    wxAddBtn.layer.masksToBounds = YES;
    wxAddBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [wxScroll addSubview:wxAddBtn];
    wxScroll.contentSize = CGSizeMake(0, wxAddBtn.bottom);

    
    
}
- (NSMutableAttributedString *)setAttStr:(NSString *)str{
    NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc]initWithString:str];
    if ([str hasPrefix:@"*"]) {
        [attStr addAttribute:NSForegroundColorAttributeName
                            value:[UIColor redColor]
                            range:NSMakeRange(0, 1)];
    }
    return attStr;
}
- (NSMutableAttributedString *)setWXAttStr:(NSString *)str{
    NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc]initWithString:str];
    NSRange range = [str rangeOfString:@"（"];
    [attStr addAttribute:NSForegroundColorAttributeName
                   value:RGB_COLOR(@"#323232", 1)
                   range:NSMakeRange(0, range.location)];
    [attStr addAttribute:NSFontAttributeName
                   value:SYS_Font(10)
                   range:NSMakeRange(range.location, str.length - range.location)];

    return attStr;
}

- (void)shopImgBtnClick:(UIButton *)sender{
    UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"相册") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self selectThumbWithType:UIImagePickerControllerSourceTypePhotoLibrary];
    }];
    [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    [alertContro addAction:cancleAction];
    UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"拍照") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self selectThumbWithType:UIImagePickerControllerSourceTypeCamera];
    }];
    [sureAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    [alertContro addAction:sureAction];
    
    UIAlertAction *ccccAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
    }];
    [ccccAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    [alertContro addAction:ccccAction];

    [self presentViewController:alertContro animated:YES completion:nil];

}
- (void)selectThumbWithType:(UIImagePickerControllerSourceType)type{
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.delegate = self;
    imagePickerController.sourceType = type;
    imagePickerController.allowsEditing = NO;
    if (type == UIImagePickerControllerSourceTypeCamera) {
        imagePickerController.showsCameraControls = YES;
        imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    }
    [self presentViewController:imagePickerController animated:YES completion:nil];
}
-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"])
    {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerOriginalImage"];
        UIImageOrientation imageOrientation = image.imageOrientation;
        if(imageOrientation!=UIImageOrientationUp)
        {
            // 原始图片可以根据照相时的角度来显示，但UIImage无法判定，于是出现获取的图片会向左转９０度的现象。
            // 以下为调整图片角度的部分
            UIGraphicsBeginImageContext(image.size);
            [image drawInRect:CGRectMake(0, 0, image.size.width, image.size.height)];
            image = UIGraphicsGetImageFromCurrentImageContext();
            UIGraphicsEndImageContext();
            // 调整图片角度完毕
        }

        selectImg = image;
        [shopImgBtn setImage:image forState:UIControlStateNormal];
        [self ChangeBtnBackground];
    }
    [picker dismissViewControllerAnimated:YES completion:nil];
}
-(void)imagePickerControllerDidCancel:(UIImagePickerController *)picker{
    [picker dismissViewControllerAnimated:YES completion:nil];
}
- (void)navigationController:(UINavigationController *)navigationController didShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if ([UIDevice currentDevice].systemVersion.floatValue < 11) {
        return;
    }
    if ([viewController isKindOfClass:NSClassFromString(@"PUPhotoPickerHostViewController")]) {
        [viewController.view.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.frame.size.width < 42) {
                [viewController.view sendSubviewToBack:obj];
                *stop = YES;
            }
        }];
    }
}

- (void)addBtnClick:(UIButton *)sender{
        if ([PublicObj checkNull:linkT.text]) {
            [MBProgressHUD showError:YZMsg(@"请输入商品链接")];
            return;
        }
        
        if ([PublicObj checkNull:nameT.text]) {
            [MBProgressHUD showError:YZMsg(@"请输入商品名称")];
            return;
        }
        if ([PublicObj checkNull:xianjiaT.text]) {
            [MBProgressHUD showError:YZMsg(@"请输入商品价格")];
            return;
        }
        if ([PublicObj checkNull:contentT.text]) {
            [MBProgressHUD showError:YZMsg(@"请输入商品简介")];
            return;
        }
        if (contentT.text.length > 50) {
            [MBProgressHUD showError:YZMsg(@"商品简介最多50字")];
            return;
        }
        
        if (!selectImg) {
            [MBProgressHUD showError:YZMsg(@"请上传商品封面")];
            return;
        }
        [self getQiniuToken];
        
}
#pragma mark - UITextViewDelegate
- (void)textViewDidChange:(UITextView*)textView {
    NSString *nameString;
    NSString *desString;
    if (textView == nameT) {
        nameString = nameT.text;
    }else if(textView == contentT){
        desString = contentT.text;
    }
    NSString *lang = [[[UITextInputMode activeInputModes]firstObject] primaryLanguage]; // 键盘输入模式
    if ([lang isEqualToString:@"zh-Hans"]) { // 简体中文输入，包括简体拼音，健体五笔，简体手写
        UITextRange *selectedRange = [textView markedTextRange];//获取高亮部分
        UITextPosition *position = [textView positionFromPosition:selectedRange.start offset:0];
        //没有高亮选择的字，则对已输入的文字进行字数统计和限制
        if (!position) {
            if (nameString.length > 15) {
                nameT.text = [nameString substringToIndex:15];
            }
            if (desString.length > 50) {
                contentT.text = [desString substringToIndex:50];
            }
        }else{
            //有高亮选择的字符串，则暂不对文字进行统计和限制
        }
    }else{
        // 中文输入法以外的直接对其统计限制即可，不考虑其他语种情况
        if (nameString.length > 15) {
            nameT.text = [nameString substringToIndex:15];
        }
        if (desString.length > 50) {
            contentT.text = [desString substringToIndex:50];
        }
    }
}

- (void)getQiniuToken{
    [MBProgressHUD showMessage:@""];
    
    YBWeakSelf;
    [[YBStorageManage shareManage]getCOSInfo:^(int code) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (code == 0) {
                    [weakSelf startUpload];
            }
        });
    }];


    
//    NSString *url = [purl stringByAppendingFormat:@"?service=Video.getQiniuToken"];
//    [YBNetworking postWithUrl:url Dic:nil Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
//        if ([code isEqual:@"0"]) {
//            NSDictionary *info =  [[data valueForKey:@"info"] firstObject];
//            NSString *qntoken = [NSString stringWithFormat:@"%@",[info valueForKey:@"token"]];
//            [self uploadqn:qntoken];
//        }else{
//            [MBProgressHUD hideHUD];
//            [MBProgressHUD showError:[data valueForKey:@"msg"]];
//        }
//    } Fail:^(id fail) {
//        [MBProgressHUD hideHUD];
//        [MBProgressHUD showError:@"网络连接断开，上传失败"];
//    }];
//
}
-(void)startUpload{
    YBWeakSelf;
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);

    //获取视频和图片
    NSData *imageData = UIImagePNGRepresentation(selectImg);
    UIImage *herfImg = [UIImage imageWithData:imageData];

    //传图片
    dispatch_group_async(group, queue, ^{
        NSString *imageName = [PublicObj getNameBaseCurrentTime:@"goods.png"];
        [[YBStorageManage shareManage]yb_storageImg:herfImg andName:imageName progress:^(CGFloat percent) {
            
        }complete:^(int code, NSString *key) {
            thumbImgStr = key;
            dispatch_semaphore_signal(semaphore);
        }];
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    });
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf uploadimagesuccess:thumbImgStr];
        });
        NSLog(@"任务完成执行");
    });

}
- (void)uploadqn:(NSString *)qntoken{
    QNConfiguration *config = [QNConfiguration build:^(QNConfigurationBuilder *builder) {
        builder.zone = [QNFixedZone zone0];
    }];
    QNUploadOption *option = [[QNUploadOption alloc]initWithMime:nil progressHandler:^(NSString *key, float percent) {
        
    } params:nil checkCrc:NO cancellationSignal:nil];
    QNUploadManager *upManager = [[QNUploadManager alloc] initWithConfiguration:config];
    //获取视频和图片
    NSData *imageData = UIImagePNGRepresentation(selectImg);
    NSString *imageName = [PublicObj getNameBaseCurrentTime:@"goods.png"];
    //传图片
    YBWeakSelf;
    [upManager putData:imageData key:[NSString stringWithFormat:@"image_%@",imageName] token:qntoken complete:^(QNResponseInfo *info, NSString *key, NSDictionary *resp) {
        
        if (info.ok) {
            thumbImgStr = key;
            //图片成功
            [weakSelf uploadimagesuccess:thumbImgStr];
            //传视频
        }
        else {
            [MBProgressHUD hideHUD];
            //图片失败
            NSLog(@"%@",info.error);
            [MBProgressHUD showError:YZMsg(@"上传失败")];
        }
    } option:option];


}
- (void)uploadimagesuccess:(NSString *)thumbStr{
    
    if ([xianjiaT.text containsString:@"¥"]) {
        xianjiaT.text  = [xianjiaT.text stringByReplacingOccurrencesOfString:@"¥" withString:@""];
    }
    if ([yuanjiaT.text containsString:@"¥"]) {
        yuanjiaT.text  = [yuanjiaT.text stringByReplacingOccurrencesOfString:@"¥" withString:@""];
    }
    if ([_fromWhere isEqual:@"seller"]) {
        NSDictionary *dic = @{
                              @"goodsid":self.goodsID,
                              @"href":linkT.text,
                              @"name":nameT.text,
                              @"present_price":xianjiaT.text,
                              @"original_price":yuanjiaT.text,
                              @"goods_desc":contentT.text,
                              @"thumb":thumbImgStr,
                              };
        [YBToolClass postNetworkWithUrl:@"Seller.upOutsideGoods" andParameter:dic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            [MBProgressHUD hideHUD];

            [MBProgressHUD showError:msg];
            if (code == 0) {
                [self.navigationController popViewControllerAnimated:YES];
            }
            
        } fail:^{
            [MBProgressHUD hideHUD];
            [MBProgressHUD showError:YZMsg(@"网络连接断开，上传失败")];

        }];

    }else{
        NSDictionary *dic = @{
                              @"href":linkT.text,
                              @"name":nameT.text,
                              @"present_price":xianjiaT.text,
                              @"original_price":yuanjiaT.text,
                              @"goods_desc":contentT.text,
                              @"thumb":thumbImgStr,
                              };
        [YBToolClass postNetworkWithUrl:@"Seller.setOutsideGoods" andParameter:dic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            [MBProgressHUD hideHUD];

            [MBProgressHUD showError:msg];
            if (code == 0) {
                [self.navigationController popViewControllerAnimated:YES];
            }
            
        } fail:^{
            [MBProgressHUD hideHUD];
            [MBProgressHUD showError:YZMsg(@"网络连接断开，上传失败")];

        }];

    }

}
- (void)selfTapClick{
    [self.view endEditing:YES];
}

- (void)getGoodsMsgBtnClick{
    if ([PublicObj checkNull:idT.text]) {
        [MBProgressHUD showError:YZMsg(@"请输入商品ID")];
        return;
    }
    
    [MBProgressHUD showMessage:@""];
    [YBToolClass postNetworkWithUrl:@"Shop.GetApplets" andParameter:@{@"id":idT.text} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD hideHUD];

        if (code == 0) {
            wxGoodsDic = info;
            nameT2.text = minstr([wxGoodsDic valueForKey:@"name"]);
            yuanjiaT2.text = minstr([wxGoodsDic valueForKey:@"old_price"]);
            xianjiaT2.text = minstr([wxGoodsDic valueForKey:@"price"]);
            contentT2.text = minstr([wxGoodsDic valueForKey:@"des"]);
            [goodsThumbImgV sd_setImageWithURL:[NSURL URLWithString:minstr([wxGoodsDic valueForKey:@"thumb"])]];
            wxAddBtn.alpha = 1;
            wxAddBtn.userInteractionEnabled = YES;

        }else{
            [MBProgressHUD showError:msg];
        }
    } fail:^{
        [MBProgressHUD hideHUD];

    }];
}
- (void)wxAddBtnClick:(UIButton *)sender{
    NSMutableDictionary *dic = @{@"type":@"1"}.mutableCopy;
    [dic addEntriesFromDictionary:wxGoodsDic];
    [YBToolClass postNetworkWithUrl:@"Shop.SetGoods" andParameter:dic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD hideHUD];

        [MBProgressHUD showError:msg];
        if (code == 0) {
            [self.navigationController popViewControllerAnimated:YES];
        }
        
    } fail:^{
        [MBProgressHUD hideHUD];
        [MBProgressHUD showError:YZMsg(@"网络连接断开，上传失败")];

    }];

}

#pragma mark -- 获取键盘高度
- (void)keyboardWillShow:(NSNotification *)aNotification
{
    if (contentT.isFirstResponder) {
            NSDictionary *userInfo = [aNotification userInfo];
        NSValue *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
        CGRect keyboardRect = [aValue CGRectValue];
        CGFloat height = keyboardRect.origin.y;
        CGFloat heightw = keyboardRect.size.height;
        int newHeight = _window_height - height -44;
        [UIView animateWithDuration:0.3 animations:^{
            [backScroll setContentOffset:CGPointMake(0, heightw-44)];
        }];
    }
    //获取键盘的高度
}
- (void)keyboardWillHide:(NSNotification *)aNotification
{

    
    [UIView animateWithDuration:0.1 animations:^{
        [UIView animateWithDuration:0.3 animations:^{
            [backScroll setContentOffset:CGPointMake(0, 0)];
        }];
    }];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if (textView != contentT) {
        if ([text isEqualToString:@"\n"]) {
            [self.view endEditing:YES];
            return NO;//这里返回NO，就代表return键值失效，即页面上按下return，不会出现换行，如果为yes，则输入页面会换行
        }

    }
    if (textView == xianjiaT || textView == yuanjiaT || textView == xianjiaT2 || textView == yuanjiaT2) {
        if (text.length == 0) {
            return YES;
        }
        NSString *checkStr = [textView.text stringByReplacingCharactersInRange:range withString:text];
        
        NSString *regex = @"^\\-?([1-9]\\d*|0)(\\.\\d{0,2})?$";
        return [self isValid:checkStr withRegex:regex];
    }
    return YES;
}
- (BOOL) isValid:(NSString*)checkStr withRegex:(NSString*)regex {
    NSPredicate *predicte = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [predicte evaluateWithObject:checkStr];
}

@end
