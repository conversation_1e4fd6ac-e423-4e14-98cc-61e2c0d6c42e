<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell autoresizesSubviews="NO" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="shopCELL" id="gTV-IL-0wX" customClass="shopCell">
            <rect key="frame" x="0.0" y="0.0" width="188" height="268"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="188" height="268"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="R4Z-wS-dFq">
                        <rect key="frame" x="0.0" y="0.0" width="188" height="200"/>
                    </imageView>
                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ivX-1r-MVK">
                        <rect key="frame" x="5" y="5" width="46.5" height="20"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P4P-Yq-3gy">
                                <rect key="frame" x="10" y="4" width="26.5" height="12"/>
                                <fontDescription key="fontDescription" type="system" pointSize="10"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="P4P-Yq-3gy" secondAttribute="trailing" constant="10" id="9gd-wV-qum"/>
                            <constraint firstItem="P4P-Yq-3gy" firstAttribute="leading" secondItem="ivX-1r-MVK" secondAttribute="leading" constant="10" id="QyF-wC-xcc"/>
                            <constraint firstAttribute="height" constant="20" id="XGW-fl-fXs"/>
                            <constraint firstItem="P4P-Yq-3gy" firstAttribute="centerY" secondItem="ivX-1r-MVK" secondAttribute="centerY" id="a7E-R6-aeM"/>
                            <constraint firstItem="P4P-Yq-3gy" firstAttribute="centerX" secondItem="ivX-1r-MVK" secondAttribute="centerX" id="kBZ-Mz-kkP"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4Pg-HG-eGq">
                        <rect key="frame" x="0.0" y="200" width="188" height="68"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FH3-Fx-w7c">
                                <rect key="frame" x="7" y="5" width="175" height="16"/>
                                <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="GmW-DO-X1G">
                                <rect key="frame" x="132" y="46" width="48" height="1"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <color key="backgroundColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mSN-Jq-GQk">
                                <rect key="frame" x="7" y="35" width="40" height="23"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                <color key="textColor" red="1" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="已售0件" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7xn-W9-s7L">
                                <rect key="frame" x="135.5" y="39.5" width="44.5" height="14.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <color key="textColor" systemColor="systemGrayColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="7xn-W9-s7L" secondAttribute="trailing" constant="8" id="002-6h-82Q"/>
                            <constraint firstAttribute="bottom" secondItem="mSN-Jq-GQk" secondAttribute="bottom" constant="10" id="65x-l5-PMf"/>
                            <constraint firstItem="GmW-DO-X1G" firstAttribute="centerY" secondItem="7xn-W9-s7L" secondAttribute="centerY" id="71V-re-ofg"/>
                            <constraint firstItem="FH3-Fx-w7c" firstAttribute="top" secondItem="4Pg-HG-eGq" secondAttribute="top" constant="5" id="Ba9-FF-Ax4"/>
                            <constraint firstItem="7xn-W9-s7L" firstAttribute="centerY" secondItem="mSN-Jq-GQk" secondAttribute="centerY" id="DXd-uG-Nmu"/>
                            <constraint firstItem="FH3-Fx-w7c" firstAttribute="leading" secondItem="4Pg-HG-eGq" secondAttribute="leading" constant="7" id="ETJ-C1-AuZ"/>
                            <constraint firstItem="mSN-Jq-GQk" firstAttribute="leading" secondItem="FH3-Fx-w7c" secondAttribute="leading" id="Hz2-Ei-aPU"/>
                            <constraint firstAttribute="trailing" secondItem="FH3-Fx-w7c" secondAttribute="trailing" constant="6" id="hau-uP-xmG"/>
                            <constraint firstAttribute="height" constant="68" id="kUf-Gi-uKC"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="R4Z-wS-dFq" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="3PQ-Ds-O4i"/>
                <constraint firstAttribute="trailing" secondItem="4Pg-HG-eGq" secondAttribute="trailing" id="8Kg-No-iCX"/>
                <constraint firstItem="4Pg-HG-eGq" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="DfI-yf-8sZ"/>
                <constraint firstItem="R4Z-wS-dFq" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="PQC-KX-21x"/>
                <constraint firstItem="ivX-1r-MVK" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="5" id="V9k-ps-jTt"/>
                <constraint firstAttribute="trailing" secondItem="R4Z-wS-dFq" secondAttribute="trailing" id="ZwG-4m-Y7S"/>
                <constraint firstAttribute="bottom" secondItem="4Pg-HG-eGq" secondAttribute="bottom" id="aUf-Hc-ID9"/>
                <constraint firstItem="ivX-1r-MVK" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="5" id="sNF-Wt-VVz"/>
                <constraint firstItem="4Pg-HG-eGq" firstAttribute="top" secondItem="R4Z-wS-dFq" secondAttribute="bottom" id="yXP-jR-RzB"/>
            </constraints>
            <size key="customSize" width="188" height="268"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="5"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="lineLb" destination="GmW-DO-X1G" id="w4v-p6-Fh7"/>
                <outlet property="priceL" destination="mSN-Jq-GQk" id="unf-R1-HQU"/>
                <outlet property="sellCountLb" destination="7xn-W9-s7L" id="YvY-Kk-J5r"/>
                <outlet property="statusLabel" destination="P4P-Yq-3gy" id="zYw-Q4-K0o"/>
                <outlet property="statusView" destination="ivX-1r-MVK" id="s4d-Pw-S54"/>
                <outlet property="thumbImgV" destination="R4Z-wS-dFq" id="eAZ-SK-4se"/>
                <outlet property="titleL" destination="FH3-Fx-w7c" id="T2p-iW-YTe"/>
            </connections>
            <point key="canvasLocation" x="84.799999999999997" y="111.54422788605699"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemGrayColor">
            <color red="0.55686274509803924" green="0.55686274509803924" blue="0.57647058823529407" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
