<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell autoresizesSubviews="NO" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="PlatformCELL" id="p92-fw-jUc" userLabel="PlatformCeLL" customClass="PlatformCell">
            <rect key="frame" x="0.0" y="0.0" width="193" height="294"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="193" height="294"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="h0I-1k-4qP">
                        <rect key="frame" x="0.0" y="0.0" width="193" height="193"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="h0I-1k-4qP" secondAttribute="height" multiplier="1:1" id="5Qr-9i-X3v"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Brg-io-h2B">
                        <rect key="frame" x="7" y="200" width="179" height="16"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PIG-os-WeO">
                        <rect key="frame" x="7" y="248" width="35.5" height="16"/>
                        <fontDescription key="fontDescription" type="boldSystem" pointSize="13"/>
                        <color key="textColor" red="0.98431372549999996" green="0.28235294119999998" blue="0.54901960780000003" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hfm-Q5-vRg">
                        <rect key="frame" x="5" y="5" width="46.5" height="20"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XzG-ra-VCE">
                                <rect key="frame" x="10" y="4" width="26.5" height="12"/>
                                <fontDescription key="fontDescription" type="system" pointSize="10"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="XzG-ra-VCE" firstAttribute="leading" secondItem="hfm-Q5-vRg" secondAttribute="leading" constant="10" id="03s-Z8-hiE"/>
                            <constraint firstItem="XzG-ra-VCE" firstAttribute="centerY" secondItem="hfm-Q5-vRg" secondAttribute="centerY" id="6yj-WS-c3l"/>
                            <constraint firstAttribute="height" constant="20" id="CNR-sJ-64c"/>
                            <constraint firstItem="XzG-ra-VCE" firstAttribute="centerX" secondItem="hfm-Q5-vRg" secondAttribute="centerX" id="QAO-pz-HkS"/>
                            <constraint firstAttribute="trailing" secondItem="XzG-ra-VCE" secondAttribute="trailing" constant="10" id="vPb-Hw-hHS"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="已售0件" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="m2b-0B-sNT">
                        <rect key="frame" x="130" y="248" width="48" height="16"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" systemColor="systemGrayColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FYU-Rz-WmO">
                        <rect key="frame" x="130" y="255.5" width="48" height="1"/>
                        <color key="backgroundColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="5dh-Nh-hIl"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="佣 ¥100" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Pmg-7j-dlV">
                        <rect key="frame" x="7" y="269" width="60" height="20"/>
                        <color key="backgroundColor" red="1" green="0.90154599471830987" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="1WU-62-ceJ"/>
                            <constraint firstAttribute="width" constant="60" id="VSa-9L-xXA"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" red="0.98431372549999996" green="0.18039215689999999" blue="0.54901960780000003" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </label>
                </subviews>
            </view>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="Brg-io-h2B" firstAttribute="leading" secondItem="h0I-1k-4qP" secondAttribute="leading" constant="7" id="10C-8G-pq3"/>
                <constraint firstAttribute="trailing" secondItem="m2b-0B-sNT" secondAttribute="trailing" constant="15" id="4Nf-R3-gyk"/>
                <constraint firstItem="Brg-io-h2B" firstAttribute="width" secondItem="h0I-1k-4qP" secondAttribute="width" constant="-14" id="5o8-Cx-IUk"/>
                <constraint firstItem="hfm-Q5-vRg" firstAttribute="leading" secondItem="p92-fw-jUc" secondAttribute="leading" constant="5" id="71q-IG-l8f"/>
                <constraint firstAttribute="bottom" secondItem="PIG-os-WeO" secondAttribute="bottom" constant="30" id="845-Kb-raW"/>
                <constraint firstItem="Pmg-7j-dlV" firstAttribute="leading" secondItem="PIG-os-WeO" secondAttribute="leading" id="DES-2A-iYO"/>
                <constraint firstAttribute="bottom" secondItem="Pmg-7j-dlV" secondAttribute="bottom" constant="5" id="VMV-hM-be4"/>
                <constraint firstItem="h0I-1k-4qP" firstAttribute="top" secondItem="p92-fw-jUc" secondAttribute="top" id="ZCc-cU-Aha"/>
                <constraint firstItem="FYU-Rz-WmO" firstAttribute="trailing" secondItem="m2b-0B-sNT" secondAttribute="trailing" id="d2H-NS-i2K"/>
                <constraint firstItem="m2b-0B-sNT" firstAttribute="centerY" secondItem="PIG-os-WeO" secondAttribute="centerY" id="h0Z-W4-d7w"/>
                <constraint firstItem="h0I-1k-4qP" firstAttribute="leading" secondItem="p92-fw-jUc" secondAttribute="leading" id="jHf-aQ-fxC"/>
                <constraint firstItem="Brg-io-h2B" firstAttribute="top" secondItem="h0I-1k-4qP" secondAttribute="bottom" constant="7" id="oRm-wL-FEp"/>
                <constraint firstItem="PIG-os-WeO" firstAttribute="leading" secondItem="Brg-io-h2B" secondAttribute="leading" id="qLh-U2-s1E"/>
                <constraint firstItem="hfm-Q5-vRg" firstAttribute="top" secondItem="p92-fw-jUc" secondAttribute="top" constant="5" id="tkr-rV-WgS"/>
                <constraint firstAttribute="trailing" secondItem="h0I-1k-4qP" secondAttribute="trailing" id="xpZ-r2-1i9"/>
                <constraint firstItem="FYU-Rz-WmO" firstAttribute="centerY" secondItem="m2b-0B-sNT" secondAttribute="centerY" id="xqL-RM-SWH"/>
                <constraint firstItem="FYU-Rz-WmO" firstAttribute="leading" secondItem="m2b-0B-sNT" secondAttribute="leading" id="z9B-1M-S4K"/>
            </constraints>
            <size key="customSize" width="193" height="294"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="5"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="commissionLb" destination="Pmg-7j-dlV" id="hjr-Yy-q1e"/>
                <outlet property="lineLb" destination="FYU-Rz-WmO" id="Zz3-S7-7hE"/>
                <outlet property="priceL" destination="PIG-os-WeO" id="0Nn-Nh-90s"/>
                <outlet property="sellCountLb" destination="m2b-0B-sNT" id="GX8-Ey-oPN"/>
                <outlet property="statusLabel" destination="XzG-ra-VCE" id="1JT-9q-hWB"/>
                <outlet property="statusView" destination="hfm-Q5-vRg" id="gWS-u4-c1z"/>
                <outlet property="thumbImgV" destination="h0I-1k-4qP" id="vUn-6y-FQQ"/>
                <outlet property="titleL" destination="Brg-io-h2B" id="rsa-5i-1DF"/>
            </connections>
            <point key="canvasLocation" x="-3.6231884057971016" y="107.14285714285714"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemGrayColor">
            <color red="0.55686274509803924" green="0.55686274509803924" blue="0.57647058823529407" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
