//
//  goodsShowCell.m
//  YBLive
//
//  Created by IOS1 on 2019/8/30.
//  Copyright © 2019 cat. All rights reserved.
//

#import "goodsShowCell.h"
#import "GoodsDetailViewController.h"
#import "CommodityDetailVC.h"
#import "OutsideGoodsDetailVC.h"
@implementation goodsShowCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [_showBtn setTitle:YZMsg(@"展示") forState:0];
    [_removeBtn setTitle:YZMsg(@"移除") forState:0];
    _showBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
    _removeBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
- (void)setModel:(RelationGoodsModel *)model{
    //goosdType 1-主播直播间商品 2-用户直播间商品 3-添加在售商品
    _model = model;
    [_thumbImgV sd_setImageWithURL:[NSURL URLWithString:_model.thumb]];
    _nameL.text = _model.name;
    _priceL.text = [NSString stringWithFormat:@"¥%@",_model.price];
    if ([model.type isEqual:@"1"]) {
        _priceOldL.hidden = NO;
        _lineView.hidden = NO;
        _priceOldL.text = [NSString stringWithFormat:@"¥%@",_model.original_price];
    }
    if ([_model.issale isEqual:@"1"]) {
        [_setBtn setTitle:YZMsg(@"已添加") forState:0];
        [_setBtn setTitleColor:RGB_COLOR(@"#C8C8C8", 1) forState:0];
        _setBtn.layer.borderColor = RGB_COLOR(@"#C8C8C8", 1).CGColor;
    }else{
        [_setBtn setTitle:YZMsg(@"添加") forState:0];
        [_setBtn setTitleColor:normalColors forState:0];
        _setBtn.layer.borderColor = normalColors.CGColor;
    }
    
    // 销量
    if (![PublicObj checkNull:model.sale_nums_format]) {
        _countLb.hidden = NO;
        _countLb.text = model.sale_nums_format;
    }else {
        _countLb.hidden = YES;
    }
}
- (IBAction)setBtnClick:(id)sender {
    //添加平台商品
    if ([_model.isOtherSale isEqual:@"1"]) {
        //增删在售商品
        NSString *saleStr;
        if ([_model.issale isEqual:@"1"]) {
            saleStr = @"0";
        }else{
            saleStr = @"1";
        }
        NSLog(@"sdsjdsjkkk----:%@", self.haveList);
        for (RelationGoodsModel *model in self.haveList) {
            if ([model.goodsid isEqual:_model.goodsid]) {
                return;
            }
        }

        [YBToolClass postNetworkWithUrl:@"Shop.setPlatformGoodsSale" andParameter:@{@"goodsid":_model.goodsid,@"issale":saleStr} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            [MBProgressHUD showError:msg];
            if (code == 0) {
                _model.issale = saleStr;
                if ([_model.issale isEqual:@"1"]) {
                    [_setBtn setTitle:YZMsg(@"已添加") forState:0];
                    [_setBtn setTitleColor:RGB_COLOR(@"#C8C8C8", 1) forState:0];
                    _setBtn.layer.borderColor = RGB_COLOR(@"#C8C8C8", 1).CGColor;
                }else{
                    [_setBtn setTitle:YZMsg(@"添加") forState:0];
                    [_setBtn setTitleColor:normalColors forState:0];
                    _setBtn.layer.borderColor = normalColors.CGColor;
                }
            }
        } fail:^{
            
        }];

    }else{
        //1-主播直播间商品 2-用户直播间商品 3-添加在售商品
        if (_model.goosdType == 3 || _model.goosdType == 1) {
            //增删在售商品
            NSString *saleStr;
            if (_model.goosdType == 1) {
                saleStr = @"0";
            }else{
                if ([_model.issale isEqual:@"1"]) {
                    saleStr = @"0";
                }else{
                    saleStr = @"1";
                }
            }
            NSLog(@"sdsjdsjkkk----:%@", self.haveList);
            for (RelationGoodsModel *model in self.haveList) {
                if ([model.goodsid isEqual:_model.goodsid]) {
                    return;
                }
            }
            [YBToolClass postNetworkWithUrl:@"Shop.SetSale" andParameter:@{@"goodsid":_model.goodsid,@"issale":saleStr} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
                [MBProgressHUD showError:msg];
                if (code == 0) {
                    _model.issale = saleStr;
                    if (_model.goosdType == 1) {
                        if (self.delegate) {
                            [self.delegate removeThisGoods:_model];
                        }
                    }else{
                        if ([_model.issale isEqual:@"1"]) {
                            [_setBtn setTitle:YZMsg(@"已添加") forState:0];
                            [_setBtn setTitleColor:RGB_COLOR(@"#C8C8C8", 1) forState:0];
                            _setBtn.layer.borderColor = RGB_COLOR(@"#C8C8C8", 1).CGColor;
                        }else{
                            [_setBtn setTitle:YZMsg(@"添加") forState:0];
                            [_setBtn setTitleColor:normalColors forState:0];
                            _setBtn.layer.borderColor = normalColors.CGColor;
                        }
                    }
                }
            } fail:^{
                
            }];
        }else{
            if (_model.goosdType == 2){
                if([self.delegate respondsToSelector:@selector(clickToShopLook)]){
                    [self.delegate clickToShopLook];
                }
                [PublicObj checkGoodsExistenceWithID:_model.goodsid Existence:^(NSString *code, NSString *msg) {
                    if ([code isEqual:@"0"]) {
                        if ([_model.type isEqual:@"1"]) {
                            OutsideGoodsDetailVC *detail = [[OutsideGoodsDetailVC alloc]init];
                            detail.goodsID = _model.goodsid;
                            [[MXBADelegate sharedAppDelegate] pushViewController:detail animated:YES];

                        }else{
                            CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
                            detail.goodsID = _model.goodsid;
                            detail.liveUid = self.liveUid;
                            [[MXBADelegate sharedAppDelegate] pushViewController:detail animated:YES];

                        }

                    }else{
                        [MBProgressHUD showError:msg];

                    }
                }];

            }
        }

    }
    
}
- (IBAction)showBtnClick:(UIButton *)sender {
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.setLiveGoodsIsShow"];
    
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"goodsid":_model.goodsid
                          };

    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSDictionary *infos = [[data valueForKey:@"info"] firstObject];
        NSLog(@"infos------set goods：%@",infos);
        [MBProgressHUD showError:YZMsg(@"设置成功")];
        [self.delegate reloadShowGoods:infos andmodel:_model];
    } Fail:^(id fail) {
        
    }];
}
- (IBAction)removeBtnClick:(UIButton *)sender {
    NSString *saleStr;
    if (_model.goosdType == 1) {
        saleStr = @"0";
    }
    NSString *urlStr = @"";
    if ([_model.type isEqual:@"2"]) {
        urlStr = @"Shop.setPlatformGoodsSale";
    }else{

        urlStr = @"Shop.SetSale";
    }
    [YBToolClass postNetworkWithUrl:urlStr andParameter:@{@"goodsid":_model.goodsid,@"issale":saleStr} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD showError:msg];
        if (code == 0) {
            _model.issale = saleStr;
            if (_model.goosdType == 1) {
                if (self.delegate) {
                    [self.delegate removeThisGoods:_model];
                }
            }
        }
    } fail:^{
        
    }];

}

@end
