

#import "setView.h"
#import "InfoEdit2TableViewCell.h"
#import "ZFModalTransitionAnimator.h"
#import "userItemCell5.h"
#import "getpasswangViewController.h"
#import "PhoneLoginVC.h"
#import "AppDelegate.h"
#import <SDWebImage/SDImageCache.h>
#import "AppDelegate.h"
#import "DeleteConditionVC.h"
#import "ZYTabBarController.h"
#import "YBAlertActionSheet.h"
#import <TPNS-iOS/XGPush.h>

@interface setView ()<UITableViewDataSource,UITableViewDelegate>
{
    int setvissssaaasas;
    int isNewBuid;//判断是不是最新版本
    NSArray *infoArray;
    float MBCache;
    UISwitch *nightSwitch;
    UISwitch *smallwindowSwitch;
    UISwitch *voiceSwitch;

}
@property (nonatomic, strong) ZFModalTransitionAnimator *animator;
@property (nonatomic, strong) YBAlertActionSheet *actionSheet;
@end
@implementation setView
- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationController.navigationBar.backgroundColor = RGB(246, 246, 246);
    self.navigationController.interactivePopGestureRecognizer.delegate = (id) self;
    self.view.backgroundColor = [UIColor groupTableViewBackgroundColor];
    self.tableView.tableFooterView = [[UIView alloc]init];
    self.tableView.frame = CGRectMake(0, 64+statusbarHeight, _window_width, _window_height - 64 - statusbarHeight);
    
//    NSUInteger bytesCache = [[SDImageCache sharedImageCache] getSize];
    NSUInteger bytesCache = [[SDImageCache sharedImageCache]totalDiskSize];
    //换算成 MB (注意iOS中的字节之间的换算是1000不是1024)
    MBCache = bytesCache/1000/1000;
    
    [self requestData];
    
    
}
- (void)requestData{
    [YBToolClass postNetworkWithUrl:@"User.getPerSetting" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            infoArray = info;
            NSMutableArray *arr = [NSMutableArray arrayWithArray:info];
            NSDictionary *subDic = @{@"name":YZMsg(@"夜间模式")};
            NSDictionary *smallDic = @{@"name":YZMsg(@"小窗模式")};
            NSDictionary *voiceDic = @{@"name":YZMsg(@"声音音效")};
            NSDictionary *langeDic = @{@"name":YZMsg(@"语言切换")};

            [arr insertObject:subDic atIndex:0];
            [arr insertObject:smallDic atIndex:1];
            [arr insertObject:voiceDic atIndex:2];
            [arr insertObject:langeDic atIndex:3];

            infoArray = arr;
            
            [self.tableView reloadData];
        }
    } fail:^{
        
    }];
}
- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    setvissssaaasas = 0;
}
-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:YES];
    setvissssaaasas = 1;
    self.navigationController.navigationBarHidden = YES;

    [self navtion];
//    [self.tableView reloadData];
}
-(void)navtion{
    UIView *navtion = [[UIView alloc]initWithFrame:CGRectMake(0,0, _window_width, 64 + statusbarHeight)];
    navtion.backgroundColor = [UIColor whiteColor];
    UILabel *labels = [[UILabel alloc]init];
    labels.text = _titleStr;
    [labels setFont:navtionTitleFont];
    labels.textColor = navtionTitleColor;
    labels.frame = CGRectMake(0,statusbarHeight,_window_width,84);
    labels.textAlignment = NSTextAlignmentCenter;
    [navtion addSubview:labels];
    UIButton *returnBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    UIButton *bigBTN = [[UIButton alloc]initWithFrame:CGRectMake(0, statusbarHeight, _window_width/2, 64)];
    [bigBTN addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:bigBTN];
    returnBtn.frame = CGRectMake(8,24 + statusbarHeight,40,40);
    returnBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 0, 12.5, 25);
    [returnBtn setImage:[UIImage imageNamed:@"icon_arrow_leftsssa.png"] forState:UIControlStateNormal];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:returnBtn];
    UIButton *btnttttt = [UIButton buttonWithType:UIButtonTypeCustom];
    btnttttt.backgroundColor = [UIColor clearColor];
    [btnttttt addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    btnttttt.frame = CGRectMake(0,0,100,64);
    [navtion addSubview:btnttttt];
    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(0, navtion.height-1, _window_width, 1) andColor:RGB(244, 245, 246) andView:navtion];

    [self.view addSubview:navtion];
}
-(void)doReturn{    
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}
-(void)nightValueChange{
//    nightSwitch.on = !nightSwitch.on;

    AppDelegate * appDelegate = (AppDelegate*)[UIApplication sharedApplication].delegate;
    if (nightSwitch.on) {
        [[NSUserDefaults standardUserDefaults]setBool:YES forKey:@"isNight"];
        
//        appDelegate.brightnessLayer.hidden = NO;
        [[NSNotificationCenter defaultCenter]postNotificationName:@"showBrihightLayer" object:nil];
    }else{
        [[NSUserDefaults standardUserDefaults]setBool:NO forKey:@"isNight"];
//        appDelegate.brightnessLayer.hidden = YES;
        [[NSNotificationCenter defaultCenter]postNotificationName:@"hideBrihightLayer" object:nil];

    }
//    [appDelegate.window.layer insertSublayer:appDelegate.brightnessLayer atIndex:0];
//    [appDelegate.window.layer addSublayer:appDelegate.brightnessLayer];
    
    NSLog(@"=====:%@====%d",appDelegate.brightnessLayer,appDelegate.brightnessLayer.hidden);
    
    
    
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    UIFont *font = [UIFont fontWithName:@"Heiti SC" size:15];
    if (indexPath.section == 0) {
        NSDictionary *subDic = infoArray[indexPath.row];

        NSString *itemID = minstr([subDic valueForKey:@"id"]);
        if (indexPath.row == 0) {
            UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell"];
            UILabel *titleLb = [[UILabel alloc]initWithFrame:CGRectMake(12, 11, 100, 20)];
            titleLb.textColor = [UIColor blackColor];
            titleLb.font = font;
            titleLb.text =minstr([subDic valueForKey:@"name"]);
            [cell.contentView addSubview:titleLb];
            
            nightSwitch = [[UISwitch alloc]initWithFrame:CGRectMake(_window_width-80, 11, 40, 20)];
            BOOL isNight  =[[NSUserDefaults standardUserDefaults]boolForKey:@"isNight"];
            if (isNight) {
                nightSwitch.on = YES;

            }else{
                nightSwitch.on = NO;

            }
            [nightSwitch addTarget:self action:@selector(nightValueChange) forControlEvents:UIControlEventValueChanged];
            [cell.contentView addSubview:nightSwitch];
            return cell;
        }else if (indexPath.row == 1){
            UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell"];
            UILabel *titleLb = [[UILabel alloc]initWithFrame:CGRectMake(12, 11, 140, 20)];
            titleLb.textColor = [UIColor blackColor];
            titleLb.font = font;
            titleLb.text =minstr([subDic valueForKey:@"name"]);
            [cell.contentView addSubview:titleLb];
            
            smallwindowSwitch = [[UISwitch alloc]initWithFrame:CGRectMake(_window_width-80, 11, 40, 20)];
            BOOL isSmall  = [common getSmallLiveWindow];
            if (isSmall) {
                smallwindowSwitch.on = YES;

            }else{
                smallwindowSwitch.on = NO;

            }
            [smallwindowSwitch addTarget:self action:@selector(samallValueChange) forControlEvents:UIControlEventValueChanged];
            [cell.contentView addSubview:smallwindowSwitch];
            return cell;

        }else if (indexPath.row == 2){
            
            UITableViewCell *cell = [[UITableViewCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell"];
            UILabel *titleLb = [[UILabel alloc]initWithFrame:CGRectMake(12, 11, 100, 20)];
            titleLb.textColor = [UIColor blackColor];
            titleLb.font = font;
            titleLb.text =minstr([subDic valueForKey:@"name"]);
            [cell.contentView addSubview:titleLb];
            
            voiceSwitch = [[UISwitch alloc]initWithFrame:CGRectMake(_window_width-80, 11, 40, 20)];
            BOOL isvoice  =[[NSUserDefaults standardUserDefaults]boolForKey:@"voiceSwitch"];
            if (isvoice) {
                voiceSwitch.on = YES;

            }else{
                voiceSwitch.on = NO;

            }
            [voiceSwitch addTarget:self action:@selector(voiceValueChange) forControlEvents:UIControlEventValueChanged];
            [cell.contentView addSubview:voiceSwitch];
            return cell;

        }else if (indexPath.row == 3){
            InfoEdit2TableViewCell *cell = [InfoEdit2TableViewCell cellWithTableView:tableView];
            cell.labContrName.text = minstr([subDic valueForKey:@"name"]);
            cell.labContrName.font = font;
            cell.labContrName.textColor = [UIColor blackColor];
            NSString *tstring;
            if ([lagType isEqual:@"zh-Hans"]) {
                tstring = YZMsg(@"简体中文");
            }else{
                tstring = YZMsg(@"English");
            }
            cell.labDetail.text =tstring;
            cell.labDetail.textColor = [UIColor blackColor];

            return cell;

        }else{
            if ([itemID intValue] == 16) {
                InfoEdit2TableViewCell *cell = [InfoEdit2TableViewCell cellWithTableView:tableView];
                cell.labContrName.text = minstr([subDic valueForKey:@"name"]);
                cell.labContrName.font = font;
                cell.labContrName.textColor = [UIColor blackColor];
                NSString *build = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleVersion"];//本地的版本号
                cell.labDetail.text = build;
                cell.labDetail.textColor = normalColors;
                cell.rightImg.hidden = YES;
                [cell.labDetail mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.right.equalTo(cell.mas_right).offset(-12);
                }];

                NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
                //ios_shelves 为上架版本号，与本地一致则为上架版本,需要隐藏一些东西
                NSNumber *app_build = [infoDictionary objectForKey:@"CFBundleVersion"];//本地 build
                NSString *buildsss = [NSString stringWithFormat:@"%@",app_build];
                if (![[common ios_shelves] isEqual:buildsss]) {
                    cell.hidden = NO;
                    
                }else
                {
                    cell.hidden = YES;
                }
                return cell;

            }else{
                NSDictionary *subDic = infoArray[indexPath.row];
                InfoEdit2TableViewCell *cell = [InfoEdit2TableViewCell cellWithTableView:tableView];
                cell.labContrName.text = minstr([subDic valueForKey:@"name"]);
                cell.labContrName.font = font;
                cell.labContrName.textColor = [UIColor blackColor];

                if ([itemID intValue] == 18) {
                    cell.labDetail.text = [NSString stringWithFormat:@"%.2fMB",MBCache];
                    [cell.labDetail mas_updateConstraints:^(MASConstraintMaker *make) {
                        make.right.equalTo(cell.mas_right).offset(-12);
                    }];
                    cell.rightImg.hidden = YES;
                }else{
                    cell.labDetail.text = @"";
                }
                cell.labDetail.textColor = normalColors;

                return cell;

            }
         }
        }
    else
    {
        userItemCell5 *cell = [[NSBundle mainBundle]loadNibNamed:@"userItemCell5" owner:self options:nil].lastObject;
        
            return cell;
    
    }
}
-(UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    
    return nil;
    
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    //判断当前分区返回分区行数
    if (section == 0 ) {
        return infoArray.count;
    }
    else
    {
        return 1;
    }
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    //返回分区数
    return 2;
}
- ( CGFloat )tableView:( UITableView *)tableView heightForHeaderInSection:( NSInteger )section
{
    return 6;
    
}
-(CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger) section
{
    return 1;
}
//点击事件
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (indexPath.section == 0) {
        if (indexPath.row == 0) {
            return;
        }
        if(indexPath.row == 3){
            [self selectViewShow];
            return;
        }
        
        NSDictionary *subDic = infoArray[indexPath.row];
        int idd = [minstr([subDic valueForKey:@"id"]) intValue];
        switch (idd) {
            case 0:
            {
                NSString *language = [PublicObj getCurrentLanguage];
                YBWebViewController *web = [[YBWebViewController alloc]init];
                web.urls = [NSString stringWithFormat:@"%@&uid=%@&token=%@&language=%@",minstr([subDic valueForKey:@"href"]),[Config getOwnID],[Config getOwnToken],language];
                [[MXBADelegate sharedAppDelegate]pushViewController:web animated:YES];

            }
                break;
            case 15:
            {
                //修改密码
                getpasswangViewController *tuisong = [[getpasswangViewController alloc]init];
                [[MXBADelegate sharedAppDelegate]pushViewController:tuisong animated:YES];

            }
                break;
            case 16:
            {
                //版本更新
                [self getbanben];
            }
                break;
            case 17:
            {
                NSString* phoneVersion = [[UIDevice currentDevice] systemVersion];
                NSLog(@"手机系统版本: %@", phoneVersion);
                //手机型号
                NSString* phoneModel = [[UIDevice currentDevice] model];
                NSLog(@"手机型号：%@",phoneModel);
                NSString *language = [PublicObj getCurrentLanguage];

                YBWebViewController *web = [[YBWebViewController alloc]init];
                web.urls = [NSString stringWithFormat:@"%@&uid=%@&token=%@&version=%@&model=%@&language=%@",minstr([subDic valueForKey:@"href"]),[Config getOwnID],[Config getOwnToken],phoneVersion,phoneModel,language];
                [[MXBADelegate sharedAppDelegate]pushViewController:web animated:YES];

            }
                break;
            case 18:
            {
                //清除缓存
                [self clearCrash];
            }
                break;

            case 19:
            {
                //注销账号
                DeleteConditionVC *delete = [[DeleteConditionVC alloc]init];
                delete.urls =minstr([subDic valueForKey:@"href"]);
                [[MXBADelegate sharedAppDelegate]pushViewController:delete animated:YES];
            }
                break;

        }    }
    else if (indexPath.section == 1){
        UIAlertController *alertControl = [UIAlertController alertControllerWithTitle:nil message:YZMsg(@"确定退出登录吗？") preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            
        }];
        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"退出登录") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [self quitLogin];

        }];
        [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
        [alertControl addAction:cancleAction];
        [alertControl addAction:sureAction];
        [[[MXBADelegate sharedAppDelegate]topViewController] presentViewController:alertControl animated:YES completion:nil];
    }
    
}
#pragma mark -----小窗模式------
-(void)samallValueChange{
    NSString *url = [purl stringByAppendingFormat:@"?service=User.setLiveWindow"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          };

    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if([code isEqual:@"0"]){
            NSDictionary *infos = [[data valueForKey:@"info"] firstObject];
            NSString *status = minstr([infos valueForKey:@"status"]);
            [common saveSmallLiveWindow:[status boolValue]];
        }
        [MBProgressHUD showError:msg];
    } Fail:^(id fail) {
        
    }];
}
-(void)voiceValueChange{
    [common saveSwitch:voiceSwitch.on];

}

-(void)getbanben{
    
    NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
    NSNumber *app_build = [infoDictionary objectForKey:@"CFBundleVersion"];//本地
    NSNumber *build = (NSNumber *)[common ipa_ver];//远程
    NSComparisonResult r = [app_build compare:build];
if (r == NSOrderedAscending || r == NSOrderedDescending) {//可改为if(r == -1L)
            [[UIApplication sharedApplication]openURL:[NSURL URLWithString:[common app_ios]]];
            [MBProgressHUD hideHUD];
    }else if(r == NSOrderedSame) {//可改为if(r == 0L)
//        UIAlertView *alert = [[UIAlertView alloc]initWithTitle:YZMsg(@"当前已是最新版本") message:nil delegate:self cancelButtonTitle:YZMsg(@"确定") otherButtonTitles:nil, nil];
//                    [alert show];
        [MBProgressHUD showError:YZMsg(@"已经是最新版本")];
    }
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (void)clearTmpPics
{
//    [[SDImageCache sharedImageCache] clearDisk];
 
}
//MARK:-设置tableivew分割线
-(void)setTableViewSeparator
{
    if ([self.tableView respondsToSelector:@selector(setSeparatorInset:)]) {
        [self.tableView setSeparatorInset:UIEdgeInsetsZero];
    }
    if ([self.tableView respondsToSelector:@selector(setLayoutMargins:)])  {
        [self.tableView setLayoutMargins:UIEdgeInsetsZero];
    }
}
-(void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPa
{
    if ([cell respondsToSelector:@selector(setLayoutMargins:)]) {
        [cell setLayoutMargins:UIEdgeInsetsZero];
    }
    if ([cell respondsToSelector:@selector(setSeparatorInset:)]){
        [cell setSeparatorInset:UIEdgeInsetsZero];
    }
}
//退出登录函数
-(void)quitLogin
{
    [YBToolClass postNetworkWithUrl:@"Login.logout" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            [[YBSmallLiveWindow shareInstance]closeBtnClick];

            [[YBImManager shareInstance]imLogout];
            [[NSNotificationCenter defaultCenter]postNotificationName:@"closeYoung_notification" object:nil];

            [Config clearProfile];
            LiveUser *userInfo = [[LiveUser alloc] initWithDic:[PublicObj visitorDic]];
            [Config saveProfile:userInfo];

            UIApplication *app =[UIApplication sharedApplication];
            AppDelegate *app2 = (AppDelegate*)app.delegate;
            ZYTabBarController *root = [[ZYTabBarController alloc]init];
            UINavigationController *nav = [[UINavigationController alloc]initWithRootViewController:root];
            app2.window.rootViewController = nav;

        }else{
            [MBProgressHUD showError:msg];
        }
    } fail:^{
        
    }];
}
- (void)clearCrash{
    [[SDImageCache sharedImageCache] clearDiskOnCompletion:nil];
    [_tableView reloadData];
    MBCache = 0;
    [MBProgressHUD showError:YZMsg(@"缓存已清除")];
}

#pragma mark --语言切换
-(void)selectViewShow{
    YBWeakSelf;
    NSArray *arr = @[@"简体中文",@"English"];
    _actionSheet = [[YBAlertActionSheet alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) cancelTitle:YZMsg(@"取消") cancelColor:RGBA(41,142,245,1)  andRowHeight:50 andOtherTitle:arr];
    
    _actionSheet.btnEvent = ^(NSString *btnTitle) {
        NSString *titleStr = btnTitle;
        if ([titleStr isEqual:YZMsg(@"取消")]) {
            [weakSelf.actionSheet removeFromSuperview];
            weakSelf.actionSheet = nil;
        }else if ([titleStr isEqual:@"English"]) {
            [weakSelf setLanguage:@"en"];

            [weakSelf.actionSheet removeFromSuperview];
            weakSelf.actionSheet = nil;
            [weakSelf.actionSheet setSelectIndex:10000];
            [[RookieTools shareInstance] resetLanguage:@"en" withFrom:@""];
            [weakSelf buildUpdate:@"en"];
            
            [[XGPushTokenManager defaultTokenManager]clearAndAppendTags:@[@"en",[Config getOwnID]]];

        }else if ([titleStr isEqual:@"简体中文"]) {
            [weakSelf setLanguage:@"zh-cn"];

            [weakSelf.actionSheet removeFromSuperview];
            weakSelf.actionSheet = nil;
            [weakSelf.actionSheet setSelectIndex:10001];
            [[RookieTools shareInstance] resetLanguage:@"zh-Hans" withFrom:@""];
            [weakSelf buildUpdate:@"zh-cn"];
            [[XGPushTokenManager defaultTokenManager]clearAndAppendTags:@[@"zh-cn",[Config getOwnID]]];

        }
    };
    [self.view addSubview:_actionSheet];
    if ([lagType isEqual:@"zh-Hans"]) {
        [_actionSheet setSelectIndex:10001];
    }else if ([lagType isEqual:EN]){
        [_actionSheet setSelectIndex:10000];
    }
}
-(void)setLanguage:(NSString *)lang{
    
    NSString *url = [purl stringByAppendingFormat:@"?service=User.setLanguage"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"lang_type":lang
                          };

    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
            
        } Fail:^(id fail) {
            
        }];
}
-(void)buildUpdate:(NSString *)language{
    NSMutableDictionary *pDic = [[NSMutableDictionary alloc]initWithObjectsAndKeys:language,@"language", nil];

    AFHTTPSessionManager *session = [AFHTTPSessionManager manager];
    [session POST:[NSString stringWithFormat:@"%@?service=Home.getConfig",purl] parameters:pDic headers:nil progress:^(NSProgress * _Nonnull uploadProgress) {
        
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSNumber *number = [responseObject valueForKey:@"ret"] ;
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [responseObject valueForKey:@"data"];
            int code = [minstr([data valueForKey:@"code"]) intValue];
            NSArray *info = [data valueForKey:@"info"];
            if (code == 0) {
                NSDictionary *subdic = [info firstObject];
                if (![subdic isEqual:[NSNull null]]) {
                    liveCommon *commons = [[liveCommon alloc]initWithDic:subdic];
                    [common saveProfile:commons];
                    //接口请求完成发送通知
                    [[NSNotificationCenter defaultCenter] postNotificationName:@"home.getconfig" object:nil];
                }
            }
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        
    }];
    
}

@end
