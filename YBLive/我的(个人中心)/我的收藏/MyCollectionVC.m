//
//  MyCollectionVC.m
//  YBLive
//
//  Created by ybRRR on 2020/10/26.
//  Copyright © 2020 cat. All rights reserved.
//

#import "MyCollectionVC.h"
#import "MyCollectionCell.h"
#import "OutsideGoodsDetailVC.h"
#import "CommodityDetailVC.h"
#import "CommodityModel.h"
@interface MyCollectionVC ()<UITableViewDelegate, UITableViewDataSource>
{
    int pageIndex;
    BOOL isEdit;
}
@property (nonatomic, strong)UITableView *collectionTable;
@property (nonatomic, strong)NSMutableArray *dataArray;
@property (nonatomic, strong)NSMutableArray *sel_arr;
@end

@implementation MyCollectionVC


-(void)requestData{
    NSString *url = [purl stringByAppendingFormat:@"?service=Shop.getGoodsCollect"];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"p":@(pageIndex)
                          };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [_collectionTable.mj_header endRefreshing];
        [_collectionTable.mj_footer endRefreshing];
        if ([code isEqual:@"0"]) {
            NSArray *infoArr = [data valueForKey:@"info"];
            if (pageIndex == 1) {
                [self.dataArray removeAllObjects];
                if (infoArr.count < 1) {
                    [PublicView showImgNoData:self.collectionTable name:@"" text:YZMsg(@"暂无收藏")];
                    [self.collectionTable reloadData];
                    return ;
                }else{
                    [PublicView hiddenImgNoData:self.collectionTable];
                }

            }
            for (int i = 0; i < infoArr.count; i ++) {
                CommodityModel *models = [[CommodityModel alloc]initWithDic:infoArr[i]];
                models.selectType = @"0";
//                [self.dataArray addObjectsFromArray:infoArr];
                [self.dataArray addObject:models];

            }

            [self.collectionTable reloadData];

        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        [_collectionTable.mj_header endRefreshing];
        [_collectionTable.mj_footer endRefreshing];

    }];


}
-(void)initData{
    isEdit = NO;
    pageIndex= 1;
    self.dataArray = [NSMutableArray array];
    self.sel_arr = [NSMutableArray array];
}
-(void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:YES];
    [self requestData];

}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"我的收藏");
    [self.rightBtn setTitle:YZMsg(@"管理") forState:0];
    self.rightBtn.hidden = NO;
    [self.rightBtn setTitleColor:[UIColor blackColor] forState:0];
    [self initData];
    [self.view addSubview:self.collectionTable];
}
-(void)rightBtnClick
{
    isEdit = !isEdit;
    if (isEdit) {
        [self.rightBtn setTitle:YZMsg(@"删除") forState:UIControlStateNormal];
        [_collectionTable reloadData];

    }else{
        NSString *idsStr =@"";
        
        for (CommodityModel *models in self.dataArray) {
            if ([models.selectType isEqual:@"1"]) {
                idsStr = [idsStr stringByAppendingString:[NSString stringWithFormat:@"%@,",models.idStr]];
            }
        }
        idsStr = [idsStr substringToIndex:idsStr.length-1];
        NSLog(@"mycollectionVC------ids:%@",idsStr);
        if (idsStr.length < 1) {
            isEdit= YES;
            [MBProgressHUD showError:YZMsg(@"请选择要删除的商品")];
            return;
        }
        [self showDeleteAlert:idsStr];
    }
}
-(void)showDeleteAlert:(NSString *)idStr{
    YBWeakSelf;
    UIAlertController *alertContrl = [UIAlertController alertControllerWithTitle:nil message:YZMsg(@"确定删除所选内容?") preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [weakSelf delGoodsCollect:idStr];
    }];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    [sureAction setValue:normalColors forKey:@"_titleTextColor"];
    [cancelAction setValue:UIColor.grayColor forKey:@"_titleTextColor"];
    
    [alertContrl addAction:cancelAction];
    [alertContrl addAction:sureAction];

    [self.navigationController presentViewController:alertContrl animated:YES completion:nil];

}
-(void)delGoodsCollect:(NSString *)idsStr{
   
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Shop.delGoodsCollect"];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"ids":idsStr
                          };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
            if ([code isEqual:@"0"]) {
                [self.rightBtn setTitle:YZMsg(@"管理") forState:UIControlStateNormal];
                [self requestData];
            }
        [MBProgressHUD showError:msg];
        } Fail:^(id fail) {
            
        }];

}
-(UITableView *)collectionTable{
    if (!_collectionTable) {
        _collectionTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:UITableViewStylePlain];
        _collectionTable.delegate = self;
        _collectionTable.dataSource = self;
        _collectionTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _collectionTable.allowsMultipleSelectionDuringEditing = YES;

        _collectionTable.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            pageIndex= 1;
            [self requestData];

        }];
        _collectionTable.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
            pageIndex +=1;
            [self requestData];
        }];

    }
    return _collectionTable;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.dataArray.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 85;
}
- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath {
    return YES;
}
// 定义编辑样式
- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewCellEditingStyleDelete;
}
- (nullable NSArray<UITableViewRowAction *> *)tableView:(UITableView *)tableView editActionsForRowAtIndexPath:(NSIndexPath *)indexPath {
    YBWeakSelf;
    UITableViewRowAction *rowAction = [UITableViewRowAction rowActionWithStyle:UITableViewRowActionStyleNormal title:YZMsg(@"删除") handler:^(UITableViewRowAction * _Nonnull action, NSIndexPath * _Nonnull indexPath) {
        CommodityModel *models = self.dataArray[indexPath.row];
        [weakSelf showDeleteAlert:models.idStr];
    }];
    rowAction.backgroundColor =normalColors;
    NSArray *arr = @[rowAction];
    return arr;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    MyCollectionCell *cell = [MyCollectionCell cellWithTab:tableView andIndexPath:indexPath];
    cell.models = self.dataArray[indexPath.row];
    if (isEdit) {
        cell.selImg.hidden = NO;
        [cell.selImg mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(16);
        }];
    }else{
        cell.selImg.hidden = YES;
        [cell.selImg mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(1);
        }];

    }
    return cell;

}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    CommodityModel *models = self.dataArray[indexPath.row];
    if (isEdit) {
        _sel_arr = self.dataArray;
        for (int i = 0; i < _sel_arr.count; i ++) {
            CommodityModel *changeModel =_sel_arr[i];
            if ([models.idStr isEqual:changeModel.idStr]) {
                if ([changeModel.selectType isEqual:@"0"]) {
                    changeModel.selectType = @"1";
                }else{
                    changeModel.selectType = @"0";
                }
            }
        }
        _dataArray = _sel_arr;
        [_collectionTable reloadData];
    }else{
        [tableView deselectRowAtIndexPath:indexPath animated:YES];

        [PublicObj checkGoodsExistenceWithID:models.idStr Existence:^(NSString *code, NSString *msg) {
            if ([code isEqual:@"0"]) {
                if ([models.type isEqual:@"1"]) {
                    OutsideGoodsDetailVC *detail = [[OutsideGoodsDetailVC alloc]init];
                    detail.goodsID = models.idStr;
                    [[MXBADelegate sharedAppDelegate] pushViewController:detail animated:YES];

                }else{
                    CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
                    detail.goodsID = models.idStr;
                    [[MXBADelegate sharedAppDelegate] pushViewController:detail animated:YES];
                }

            }else{
                [MBProgressHUD showError:msg];
            }
        }];

    }

}

@end
