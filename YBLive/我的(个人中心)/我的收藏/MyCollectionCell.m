//
//  MyCollectionCell.m
//  YBLive
//
//  Created by ybRRR on 2020/10/26.
//  Copyright © 2020 cat. All rights reserved.
//

#import "MyCollectionCell.h"

@implementation MyCollectionCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(MyCollectionCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath {
    MyCollectionCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MyCollectionCell"];
     if (!cell) {
             cell = [[[NSBundle mainBundle]loadNibNamed:@"MyCollectionCell" owner:nil options:nil]objectAtIndex:0];
     }
     return cell;

}
-(void)setModels:(CommodityModel *)models
{
    _models = models;
    self.titleLb.text = models.name;
    [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:models.thumb]];
    self.priceLb.text =[NSString stringWithFormat:@"¥%@",models.price];
    if ([models.type isEqual:@"1"]) {
        self.oldPriceLb.hidden = NO;
        self.lbLine.hidden = NO;
        self.oldPriceLb.text = [NSString stringWithFormat:@"¥%@",models.original_price];
    }
    if ([models.selectType isEqual:@"0"]) {
        self.selImg.image = [UIImage imageNamed:@"commodity_记录未选"];
    }else{
        self.selImg.image = [UIImage imageNamed:@"commodity_记录选中"];
    }

}
-(void)setDataDic:(NSDictionary *)dataDic
{
    _dataDic = dataDic;
    self.titleLb.text = minstr([dataDic valueForKey:@"name"]);
    [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr([dataDic valueForKey:@"thumb"])]];
    self.priceLb.text =[NSString stringWithFormat:@"¥%@",minstr([dataDic valueForKey:@"price"])];
    if ([minstr([dataDic valueForKey:@"type"]) isEqual:@"1"]) {
        self.oldPriceLb.hidden = NO;
        self.lbLine.hidden = NO;
        self.oldPriceLb.text = [NSString stringWithFormat:@"¥%@",minstr([dataDic valueForKey:@"original_price"])];
    }
}

@end
