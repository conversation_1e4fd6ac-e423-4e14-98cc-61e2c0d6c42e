//
//  MyCollectionCell.h
//  YBLive
//
//  Created by ybRRR on 2020/10/26.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "CommodityModel.h"

@interface MyCollectionCell : UITableViewCell
@property (nonatomic, strong)NSDictionary *dataDic;
@property (weak, nonatomic) IBOutlet UIImageView *thumbImg;
@property (weak, nonatomic) IBOutlet UILabel *titleLb;
@property (weak, nonatomic) IBOutlet UILabel *priceLb;
@property (weak, nonatomic) IBOutlet UILabel *oldPriceLb;
@property (weak, nonatomic) IBOutlet UILabel *lbLine;
@property (weak, nonatomic) IBOutlet UIImageView *selImg;


@property (nonatomic, strong)CommodityModel *models;

+(MyCollectionCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;
@end

