<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="80" id="KGk-i7-Jjw" customClass="MyCollectionCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="85"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="85"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="WRg-Jc-QdX">
                        <rect key="frame" x="36" y="5.5" width="74" height="74"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="74" id="67g-FZ-SM5"/>
                            <constraint firstAttribute="height" constant="74" id="Mtj-MZ-tUj"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标题" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aGs-TO-7R1">
                        <rect key="frame" x="115" y="10.5" width="193" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZhY-Ao-NBa">
                        <rect key="frame" x="115" y="57.5" width="35.5" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" red="0.98431372549999996" green="0.28235294119999998" blue="0.54901960780000003" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yws-b3-sRV">
                        <rect key="frame" x="158.5" y="57.5" width="35.5" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XRN-b4-hV3">
                        <rect key="frame" x="158.5" y="65.5" width="35.5" height="1"/>
                        <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="aO3-S5-lbe"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8un-US-aoc">
                        <rect key="frame" x="10" y="83" width="298" height="1"/>
                        <color key="backgroundColor" systemColor="groupTableViewBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="1jw-AR-neE"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="commodity_记录未选.png" translatesAutoresizingMaskIntoConstraints="NO" id="QcX-a8-aj0">
                        <rect key="frame" x="10" y="34.5" width="16" height="16"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="16" id="A1a-zU-HEp"/>
                            <constraint firstAttribute="height" constant="16" id="WSI-PJ-RcL"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="aGs-TO-7R1" secondAttribute="trailing" constant="12" id="6hk-0Y-Rg2"/>
                    <constraint firstItem="QcX-a8-aj0" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="EA0-ua-fCa"/>
                    <constraint firstItem="XRN-b4-hV3" firstAttribute="trailing" secondItem="yws-b3-sRV" secondAttribute="trailing" id="GzK-BA-Ecn"/>
                    <constraint firstItem="XRN-b4-hV3" firstAttribute="centerX" secondItem="yws-b3-sRV" secondAttribute="centerX" id="Hcl-ZX-Flo"/>
                    <constraint firstItem="WRg-Jc-QdX" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="JJ5-N3-T7Y"/>
                    <constraint firstAttribute="trailing" secondItem="8un-US-aoc" secondAttribute="trailing" constant="12" id="NA0-Lw-jfN"/>
                    <constraint firstItem="QcX-a8-aj0" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="QjA-BE-W6n"/>
                    <constraint firstItem="yws-b3-sRV" firstAttribute="centerY" secondItem="ZhY-Ao-NBa" secondAttribute="centerY" id="RLu-fh-2iP"/>
                    <constraint firstAttribute="bottom" secondItem="8un-US-aoc" secondAttribute="bottom" constant="1" id="RMR-ze-83M"/>
                    <constraint firstItem="8un-US-aoc" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="SeG-eH-L2Q"/>
                    <constraint firstItem="aGs-TO-7R1" firstAttribute="leading" secondItem="WRg-Jc-QdX" secondAttribute="trailing" constant="5" id="Sq7-Uq-5Y2"/>
                    <constraint firstItem="yws-b3-sRV" firstAttribute="leading" secondItem="ZhY-Ao-NBa" secondAttribute="trailing" constant="8" id="cqw-Nf-oLi"/>
                    <constraint firstItem="XRN-b4-hV3" firstAttribute="leading" secondItem="yws-b3-sRV" secondAttribute="leading" id="jJB-VW-0Eh"/>
                    <constraint firstItem="XRN-b4-hV3" firstAttribute="centerY" secondItem="yws-b3-sRV" secondAttribute="centerY" id="jkH-20-Kav"/>
                    <constraint firstItem="WRg-Jc-QdX" firstAttribute="leading" secondItem="QcX-a8-aj0" secondAttribute="trailing" constant="10" id="meU-Lr-bZA"/>
                    <constraint firstItem="ZhY-Ao-NBa" firstAttribute="leading" secondItem="aGs-TO-7R1" secondAttribute="leading" id="n6J-JO-osf"/>
                    <constraint firstItem="ZhY-Ao-NBa" firstAttribute="bottom" secondItem="WRg-Jc-QdX" secondAttribute="bottom" constant="-5" id="oMd-so-cNU"/>
                    <constraint firstItem="aGs-TO-7R1" firstAttribute="top" secondItem="WRg-Jc-QdX" secondAttribute="top" constant="5" id="obk-ha-8pw"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="lbLine" destination="XRN-b4-hV3" id="ajB-1S-jo8"/>
                <outlet property="oldPriceLb" destination="yws-b3-sRV" id="Mgg-1Z-G3C"/>
                <outlet property="priceLb" destination="ZhY-Ao-NBa" id="5F6-i5-Wqz"/>
                <outlet property="selImg" destination="QcX-a8-aj0" id="iBg-Xr-EFG"/>
                <outlet property="thumbImg" destination="WRg-Jc-QdX" id="G0d-ij-dSR"/>
                <outlet property="titleLb" destination="aGs-TO-7R1" id="oDx-bi-tSz"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="115.51339285714285"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="commodity_记录未选.png" width="22" height="22"/>
        <systemColor name="groupTableViewBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
