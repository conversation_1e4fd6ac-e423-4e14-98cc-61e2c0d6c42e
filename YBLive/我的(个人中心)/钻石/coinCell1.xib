<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14460.31" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14460.20"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="103" id="Gcx-Th-MzK" customClass="coinCell1">
            <rect key="frame" x="0.0" y="0.0" width="320" height="103"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="Gcx-Th-MzK" id="bWN-dS-7ff">
                <rect key="frame" x="0.0" y="0.0" width="320" height="102.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JQV-qs-sV7">
                        <rect key="frame" x="16" y="0.0" width="288" height="102.5"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="账户余额" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DWc-zl-nyV">
                        <rect key="frame" x="127.5" y="21" width="65.5" height="19.5"/>
                        <fontDescription key="fontDescription" type="system" weight="thin" pointSize="16"/>
                        <color key="textColor" red="0.38431372549019605" green="0.38823529411764707" blue="0.39215686274509803" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rua-2F-rZL">
                        <rect key="frame" x="148.5" y="49" width="23" height="45.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="38"/>
                        <color key="textColor" red="1" green="0.78823529411764703" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <color key="backgroundColor" red="0.96862745098039216" green="0.96862745098039216" blue="0.96862745098039216" alpha="1" colorSpace="calibratedRGB"/>
                <constraints>
                    <constraint firstItem="JQV-qs-sV7" firstAttribute="height" secondItem="bWN-dS-7ff" secondAttribute="height" id="0Wv-LG-1fC"/>
                    <constraint firstItem="Rua-2F-rZL" firstAttribute="centerX" secondItem="bWN-dS-7ff" secondAttribute="centerX" id="805-jn-MaK"/>
                    <constraint firstItem="Rua-2F-rZL" firstAttribute="centerY" secondItem="bWN-dS-7ff" secondAttribute="centerY" multiplier="1.4" id="M2e-xn-PFu"/>
                    <constraint firstItem="JQV-qs-sV7" firstAttribute="centerY" secondItem="bWN-dS-7ff" secondAttribute="centerY" id="Og4-sI-9Zs"/>
                    <constraint firstItem="JQV-qs-sV7" firstAttribute="width" secondItem="bWN-dS-7ff" secondAttribute="width" multiplier="0.9" id="bdH-Lk-QnX"/>
                    <constraint firstItem="DWc-zl-nyV" firstAttribute="centerX" secondItem="bWN-dS-7ff" secondAttribute="centerX" id="hCC-Bb-Z5o"/>
                    <constraint firstItem="JQV-qs-sV7" firstAttribute="centerX" secondItem="bWN-dS-7ff" secondAttribute="centerX" id="mCz-MI-DA7"/>
                    <constraint firstItem="DWc-zl-nyV" firstAttribute="centerY" secondItem="bWN-dS-7ff" secondAttribute="centerY" multiplier="0.6" id="qpT-bm-fWm"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="labCoin" destination="Rua-2F-rZL" id="f5G-As-0AK"/>
                <outlet property="yueL" destination="DWc-zl-nyV" id="JKT-7m-orK"/>
            </connections>
            <point key="canvasLocation" x="235.19999999999999" y="225.33733133433284"/>
        </tableViewCell>
    </objects>
</document>
