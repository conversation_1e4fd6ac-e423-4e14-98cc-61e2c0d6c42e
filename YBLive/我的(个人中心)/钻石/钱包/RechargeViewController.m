//
//  RechargeViewController.m
//  live1v1
//
//  Created by IOS1 on 2019/4/4.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import "RechargeViewController.h"
#import "applePay.h"
#import <WXApi.h>
#import "Order.h"
#import <AlipaySDK/AlipaySDK.h>
#import "DataSigner.h"
#import "DataVerifier.h"
#import "BraintreePayPal.h"
#import "FirstChargeView.h"
@interface RechargeViewController ()<applePayDelegate,WXApiDelegate,BTViewControllerPresentingDelegate>{
    UILabel *coinL;
    UILabel *jifenL;

    UIImageView *headerImgV;
    NSDictionary *subDic;
    NSArray *allArray;
    UIScrollView *backScroll;
    NSMutableArray *payTypeArray;
    NSMutableArray *coinArray;
    applePay *applePays;//苹果支付
    UIActivityIndicatorView *testActivityIndicator;//菊花
    NSString *payTypeID;
    BOOL isCreatUI;
    NSDictionary *payTypeSelDic;
    
    UILabel *tipsTitleLabel;
    UILabel *tipsContentLabel;
    NSString *shortDesc;
    NSString *_braintreeStr;
    UIButton *paybtn;
}
@property(nonatomic,strong)NSDictionary *seleDic;//选中的钻石字典
//支付宝
@property(nonatomic,copy)NSString *aliapp_key_ios;
@property(nonatomic,copy)NSString *aliapp_partner;
@property(nonatomic,copy)NSString *aliapp_seller_id;
//微信
@property(nonatomic,copy)NSString *wx_appid;
@property (strong, nonatomic) BTPayPalDriver *payPalDriver;

@property (nonatomic, strong)FirstChargeView *chargeView;
@end

@implementation RechargeViewController
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:YES];
    [self requestData];

}

- (void)viewDidLoad {
    [super viewDidLoad];
//    self.titleL.text = @"钱包";
    self.naviView.hidden = YES;
    self.view.backgroundColor = RGB(238, 238, 238);
    payTypeArray = [NSMutableArray array];
    coinArray = [NSMutableArray array];
    applePays = [[applePay alloc]init];
    applePays.delegate = self;
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(requestData) name:@"RELOADBLANCE" object:nil];
    

    UIView *backHeader = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 339)];
    CAGradientLayer *gradientLayer =  [CAGradientLayer layer];
    gradientLayer.frame = backHeader.bounds;
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1, 0);
    gradientLayer.locations = @[@(0),@(1.0)];//渐变点
    [gradientLayer setColors:@[(id)[RGB(0,129,255) CGColor],(id)[RGB(107,195,255) CGColor]]];//渐变数组
    [backHeader.layer addSublayer:gradientLayer];
    [self.view addSubview:backHeader];
    
    UIView *navtion = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64 + statusbarHeight)];
    navtion.backgroundColor =[UIColor clearColor];
    UILabel *tlabel = [[UILabel alloc]init];
    tlabel.text = YZMsg(@"钱包");
    [tlabel setFont:navtionTitleFont];
    tlabel.textColor = [UIColor whiteColor];
    tlabel.frame = CGRectMake(0, statusbarHeight,_window_width,84);
    tlabel.textAlignment = NSTextAlignmentCenter;
    [navtion addSubview:tlabel];
    UIButton *returnBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    UIButton *bigBTN = [[UIButton alloc]initWithFrame:CGRectMake(0, statusbarHeight, _window_width/2, 64)];
    [bigBTN addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:bigBTN];
    returnBtn.frame = CGRectMake(8,24 + statusbarHeight,40,40);
    returnBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 0, 12.5, 25);
    [returnBtn setImage:[UIImage imageNamed:@"personBack"] forState:UIControlStateNormal];
    [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:returnBtn];
    UIButton *btnttttt = [UIButton buttonWithType:UIButtonTypeCustom];
    btnttttt.backgroundColor = [UIColor clearColor];
    [btnttttt addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
    btnttttt.frame = CGRectMake(0,0,100,64);
    [navtion addSubview:btnttttt];
    [backHeader addSubview:navtion];

    UIButton *historyBtn = [UIButton buttonWithType:0];
    [historyBtn setImage:[UIImage imageNamed:@"recharge_明细"] forState:0];
    historyBtn.frame = CGRectMake(_window_width-60, statusbarHeight+10, 40, 40);
    historyBtn.centerY = tlabel.centerY;
    [historyBtn addTarget:self action:@selector(historyBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [navtion addSubview:historyBtn];

    
    headerImgV = [[UIImageView alloc]initWithFrame:CGRectMake(16, navtion.bottom, _window_width-32, _window_width*0.35)];
    headerImgV.userInteractionEnabled = YES;
    headerImgV.contentMode = UIViewContentModeScaleToFill;
    headerImgV.clipsToBounds = YES;
    headerImgV.backgroundColor = UIColor.whiteColor;
    headerImgV.layer.cornerRadius = 10;
    headerImgV.layer.masksToBounds = YES;
//    headerImgV.image = [UIImage imageNamed:@"recharge_背景"];
    [backHeader addSubview:headerImgV];
    UILabel *labelll = [[UILabel alloc]init];
    labelll.textColor = [UIColor grayColor];
    labelll.font = SYS_Font(12);
    labelll.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"我的 "),[common name_coin]];
    [headerImgV addSubview:labelll];
    [labelll mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(headerImgV).multipliedBy(0.5);
        make.centerY.equalTo(headerImgV).multipliedBy(0.65);
    }];
    coinL = [[UILabel alloc]init];
    coinL.textColor = [UIColor blackColor];
    coinL.font = [UIFont boldSystemFontOfSize:28];
    coinL.text = @"0";
    [headerImgV addSubview:coinL];
    [coinL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(labelll);
        make.centerY.equalTo(headerImgV).multipliedBy(1.11);
    }];
    UILabel *labelll2 = [[UILabel alloc]init];
    labelll2.textColor = [UIColor grayColor];
    labelll2.font = SYS_Font(12);
    labelll2.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"我的 "),[common name_score]];
    [headerImgV addSubview:labelll2];
    [labelll2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(headerImgV).multipliedBy(1.5);
        make.centerY.equalTo(headerImgV).multipliedBy(0.65);
    }];
    jifenL = [[UILabel alloc]init];
    jifenL.textColor = [UIColor blackColor];
    jifenL.font = [UIFont boldSystemFontOfSize:28];
    jifenL.text = @"0";
    [headerImgV addSubview:jifenL];
    [jifenL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(labelll2);
        make.centerY.equalTo(headerImgV).multipliedBy(1.11);
    }];
    UIView *lineV = [[UIView alloc]init];
    lineV.backgroundColor = [[UIColor grayColor] colorWithAlphaComponent:0.3];
    [headerImgV addSubview:lineV];
    [lineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(headerImgV);
        make.width.mas_equalTo(1);
        make.height.equalTo(headerImgV).multipliedBy(0.3);
    }];

    if ([[PublicObj getAppBuild] isEqual:[common ios_shelves]]) {
        labelll2.hidden = jifenL.hidden = lineV.hidden = YES;
        [labelll mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(headerImgV).multipliedBy(1);
            make.centerY.equalTo(headerImgV).multipliedBy(0.65);
        }];
        [coinL mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(labelll);
            make.centerY.equalTo(headerImgV).multipliedBy(1.11);
        }];
    }
    
    backScroll = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 64 + statusbarHeight+headerImgV.height+10, _window_width, _window_height-64-statusbarHeight-headerImgV.height-10)];
    backScroll.backgroundColor =UIColor.clearColor;//RGB(238, 238, 238);
    [self.view addSubview:backScroll];
    backScroll.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        [self requestData];
    }];
    if (@available(iOS 11.0, *)) {
        backScroll.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        // Fallback on earlier versions
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
}
-(void)historyBtnClick{
    NSString *language = [PublicObj getCurrentLanguage];

    NSString *url =[NSString stringWithFormat:@"%@/appapi/charge/index?&language=%@",h5url,language];
    YBWebViewController *VC = [[YBWebViewController alloc]init];
    VC.urls = [self addurl:url];
    [[MXBADelegate sharedAppDelegate] pushViewController:VC animated:YES];
}
//所有h5需要拼接uid和token
-(NSString *)addurl:(NSString *)url{
    return [url stringByAppendingFormat:@"&uid=%@&token=%@",[Config getOwnID],[Config getOwnToken]];
}

- (void)requestData{
    NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
    NSNumber *app_build = [infoDictionary objectForKey:@"CFBundleVersion"];//本地 build
    //NSString *appCurVersion = [infoDictionary objectForKey:@"CFBundleShortVersionString"];version
    //NSLog(@"当前应用软件版本:%@",appCurVersion);
    NSString *build = [NSString stringWithFormat:@"%@",app_build];

    [YBToolClass postNetworkWithUrl:@"User.getBalance" andParameter:@{@"type":@"1",@"version_ios":build} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [backScroll.mj_header endRefreshing];
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            coinL.text = minstr([infoDic valueForKey:@"coin"]);
            jifenL.text = minstr([infoDic valueForKey:@"score"]);
            LiveUser *user = [Config myProfile];
            user.coin = minstr([infoDic valueForKey:@"coin"]);
            [Config saveProfile:user];
//            if ([[PublicObj getAppBuild] isEqual:[common ios_shelves]]) {
//                tipsTitleLabel.text = @"";
//                tipsContentLabel.text = @"";
//            }else{
//                tipsTitleLabel.text = minstr([infoDic valueForKey:@"tip_t"]);
//                tipsContentLabel.text = minstr([infoDic valueForKey:@"tip_d"]);
//            }
//            tipsTitleLabel.text = minstr([infoDic valueForKey:@"tip_t"]);
//            tipsContentLabel.text = minstr([infoDic valueForKey:@"tip_d"]);
            if (self.block) {
                self.block(minstr([infoDic valueForKey:@"coin"]));
            }
            if (allArray.count == 0) {
                _aliapp_key_ios = [infoDic valueForKey:@"aliapp_key_ios"];
                _aliapp_partner = [infoDic valueForKey:@"aliapp_partner"];
                _aliapp_seller_id = [infoDic valueForKey:@"aliapp_seller_id"];
                //微信的信息
                _wx_appid = [infoDic valueForKey:@"wx_appid"];
                [WXApi registerApp:_wx_appid universalLink:WechatUniversalLink];
                
                //            NSMutableArray *a1 = [NSMutableArray array];
                //            [a1 addObjectsFromArray:[infoDic valueForKey:@"paylist"]];
                //            [a1 addObjectsFromArray:[infoDic valueForKey:@"paylist"]];
                //
                //            NSMutableArray *a2 = [NSMutableArray array];
                //            [a2 addObjectsFromArray:[infoDic valueForKey:@"rules"]];
                //            [a2 addObjectsFromArray:[infoDic valueForKey:@"rules"]];
                //            allArray = @[a1,a2];
                
                NSArray *ssssss = [infoDic valueForKey:@"paylist"];
                if (ssssss.count > 0) {
                    allArray = @[[infoDic valueForKey:@"rules"],[infoDic valueForKey:@"paylist"]];
                    if (!isCreatUI) {
                        [self creatUI];
                    }
                }
            }
            
        }
    } fail:^{
        [backScroll.mj_header endRefreshing];
    }];
}


-(void)creatUI{
    [backScroll layoutIfNeeded];
    if (allArray.count > 0) {
        NSArray *array = allArray[0];
        if (array.count == 0) {
            return;
        }
    }
    isCreatUI = YES;
    
    UIView *backView = [[UIView alloc]init];
    backView.frame = CGRectMake(15, 20, _window_width-30, 100);
    backView.backgroundColor = UIColor.whiteColor;
    backView.layer.cornerRadius = 10;
    backView.layer.masksToBounds = YES;
    [backScroll addSubview:backView];

    CGFloat btnWidth;
    CGFloat btnHeight;
    CGFloat btnSH = 0.0;

    btnWidth = (backView.width-60)/3;
    btnHeight = 50;
    btnSH = 60;

    CGFloat speace = (_window_width-60-btnWidth*3)/2;
    CGFloat y = 20;

    for (int i = 0; i < allArray.count; i++) {
        UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(15, y, 140, 20)];
        label.font = SYS_Font(12);
        label.textColor =[UIColor blackColor];// RGB_COLOR(@"#646464", 1);
        NSArray *array = allArray[i];

        [backView addSubview:label];
        if (i == 0)
        {
            label.text = YZMsg(@"请选择充值金额");
            
            UILabel *youngLb = [[UILabel alloc]init];
            youngLb.text = YZMsg(@"未成年人禁止充值消费");
            youngLb.textColor = normalColors;
            youngLb.font =[UIFont systemFontOfSize:12];
            youngLb.numberOfLines = 0;
            [backView addSubview:youngLb];
            [youngLb mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(label.mas_centerY);
                make.right.equalTo(backView.mas_right).offset(-10);
                make.left.equalTo(label.mas_right).offset(20);
            }];
            
            UIImageView *tanImg =[[UIImageView alloc]init];
            tanImg.image = [UIImage imageNamed:@"young-叹号"];
            [backView addSubview:tanImg];
            [tanImg mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(youngLb.mas_centerY);
                make.width.height.mas_equalTo(13);
                make.right.equalTo(youngLb.mas_left);
            }];
            
            CGFloat lastBottom;
            lastBottom =label.bottom;
            if (![[PublicObj getAppBuild] isEqual:[common ios_shelves]]) {
                UIButton *firstChargeBtn = [UIButton buttonWithType:0];
                firstChargeBtn.frame = CGRectMake(15, lastBottom+10, backView.width-30, 65);
                [firstChargeBtn addTarget:self action:@selector(firstChargeBtnClick) forControlEvents:UIControlEventTouchUpInside];
                [firstChargeBtn setImage:[UIImage imageNamed:getImagename(@"firstchargeImg")] forState:0];
                [backView addSubview:firstChargeBtn];
                
                lastBottom =firstChargeBtn.bottom;
            }
            for (int j = 0; j < array.count; j++) {
                
                UIButton *btn = [UIButton buttonWithType:0];
                btn.frame = CGRectMake(15+j%3 * (btnWidth+speace), lastBottom+15+(j/3)*(btnSH + 30), btnWidth, btnSH);

                [btn addTarget:self action:@selector(coinBtnClick:) forControlEvents:UIControlEventTouchUpInside];
                btn.clipsToBounds = NO;
                [btn setBackgroundImage:[UIImage imageNamed:@"paycoin_normal"] forState:0];
                [btn setBackgroundImage:[UIImage imageNamed:@"paycoin_sel"] forState:UIControlStateSelected];
                btn.tag = 3000+j;
                [backView addSubview:btn];
                NSString *give = minstr([array[j] valueForKey:@"give"]);
                if (![give isEqual:@"0"]) {
                    CGFloat widddth = [[YBToolClass sharedInstance] widthOfString:[NSString stringWithFormat:@"%@%@%@",YZMsg(@"赠送"),give,[common name_coin]] andFont:SYS_Font(10) andHeight:15];
                    UIImageView *giveImgV = [[UIImageView alloc]initWithFrame:CGRectMake(btn.left-5, btn.top-7.5, widddth+10, 20)];

                    giveImgV.image = [UIImage imageNamed:@"recharge_send"];
                    [backView addSubview:giveImgV];
                    UILabel *giveLabel = [[UILabel alloc]initWithFrame:CGRectMake(5, 0, widddth, 15)];
                    giveLabel.text = [NSString stringWithFormat:@"%@%@%@",YZMsg(@"赠送"),give,[common name_coin]];
                    giveLabel.font = SYS_Font(10);
                    giveLabel.textColor = [UIColor whiteColor];
                    [giveImgV addSubview:giveLabel];
                }
                if (j == 0) {
                    btn.selected = YES;
                    _seleDic = array[j];

                }
                UILabel *titleL = [[UILabel alloc]init];
                titleL.font = SYS_Font(15);
                titleL.textColor = RGB_COLOR(@"#323232", 1);
                if ([payTypeID isEqual:@"apple"]) {
                    titleL.text = minstr([array[j] valueForKey:@"coin_ios"]);
                }else{
                    titleL.text = minstr([array[j] valueForKey:@"coin"]);
                }
                titleL.tag = btn.tag + 3000;
                [btn addSubview:titleL];
                [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.centerY.equalTo(btn).multipliedBy(0.73);
                    make.centerX.equalTo(btn);
                }];
                UIImageView *imgV = [[UIImageView alloc]init];
                imgV.image = [UIImage imageNamed:@"logFirst_钻石"];
                [btn addSubview:imgV];
                [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.centerY.equalTo(titleL);
                    make.height.width.mas_equalTo(12);
                    make.left.equalTo(titleL.mas_right).offset(5);
                }];
                UILabel *moneyL = [[UILabel alloc]init];
                moneyL.font = SYS_Font(12);
                moneyL.textColor = RGB_COLOR(@"#666666", 1);
                moneyL.tag = btn.tag +4000;
                moneyL.text = [NSString stringWithFormat:@"¥%@",minstr([array[j] valueForKey:@"money"])];
                [btn addSubview:moneyL];
                [moneyL mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.centerY.equalTo(btn).multipliedBy(1.3);
                    make.centerX.equalTo(btn);
                }];
                [coinArray addObject:btn];
                if (j == array.count-1) {
                    [backView layoutIfNeeded];
                    y = btn.bottom + 20;
                }

            }

        }else{
            payTypeSelDic = [array firstObject];
            if (array.count == 1 && [minstr([array[0] valueForKey:@"id"]) isEqual:@"apple"]) {
                payTypeID = @"apple";
                continue;
            }

            label.text = YZMsg(@"请选择支付方式");
            for (int j = 0; j < array.count; j++) {
                UIButton *btn = [UIButton buttonWithType:0];
                btn.frame = CGRectMake(15, label.bottom+10+(42+10)*j, backView.width-30, 42);
                [btn addTarget:self action:@selector(payTypeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
                btn.clipsToBounds = NO;
                btn.layer.cornerRadius = 21;
                btn.layer.masksToBounds = YES;
                [btn setBackgroundImage:[UIImage imageNamed:@"paytype_normal"] forState:0];
                [btn setBackgroundImage:[UIImage imageNamed:@"paytype_sel"] forState:UIControlStateSelected];
                btn.tag = 2000+j;
                [backView addSubview:btn];
                
                UIImageView *imgV = [[UIImageView alloc]init];
                [imgV sd_setImageWithURL:[NSURL URLWithString:minstr([array[j] valueForKey:@"thumb"])]];
                [btn addSubview:imgV];
                [imgV mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.centerY.equalTo(btn);
                    make.height.width.mas_equalTo(16);
                    make.left.equalTo(btn.mas_left).offset(15);
                }];

                UILabel *titleL = [[UILabel alloc]init];
                titleL.font = SYS_Font(13);
                titleL.textColor = RGB_COLOR(@"#323232", 1);
                titleL.text = minstr([array[j] valueForKey:@"name"]);
                [btn addSubview:titleL];
                [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.centerY.equalTo(btn);
                    make.left.equalTo(imgV.mas_right).offset(10);
                }];

                if (j == 0) {
                    btn.selected = YES;
                    payTypeSelDic = array[j];
                    payTypeID = minstr([array[j] valueForKey:@"id"]);

                }
                if (j == array.count-1) {
                    [backView layoutIfNeeded];
                    y = btn.bottom + 20;
                }
                [payTypeArray addObject:btn];
            }
        }
    }

    paybtn = [UIButton buttonWithType:0];
    paybtn.frame = CGRectMake(15, y+20, backView.width-30, 46);
    [paybtn setTitle:YZMsg(@"确认充值") forState:0];
    paybtn.layer.cornerRadius = 23;
    paybtn.layer.masksToBounds =YES;
    CAGradientLayer *gradientLayer =  [CAGradientLayer layer];
    gradientLayer.frame = paybtn.bounds;
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1, 0);
    gradientLayer.locations = @[@(0),@(1.0)];//渐变点！
    [gradientLayer setColors:@[(id)[RGB(0,129,255) CGColor],(id)[RGB(107,195,255) CGColor]]];//渐变数组
    [paybtn.layer addSublayer:gradientLayer];
    [paybtn addTarget:self action:@selector(payBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backView addSubview:paybtn];

    UILabel *youngLb = [[UILabel alloc]init];
    youngLb.text = YZMsg(@"未成年人禁止充值消费");
    youngLb.textColor = normalColors;
    youngLb.font =[UIFont systemFontOfSize:12];
    [backView addSubview:youngLb];
    [youngLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(backView);
        make.top.equalTo(paybtn.mas_bottom).offset(10);
    }];
    
    UIImageView *tanImg =[[UIImageView alloc]init];
    tanImg.image = [UIImage imageNamed:@"young-叹号"];
    [backView addSubview:tanImg];
    [tanImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(youngLb);
        make.width.height.mas_equalTo(13);
        make.right.equalTo(youngLb.mas_left);
    }];

    
    
    NSString *xieyiStr = YZMsg(@"《用户充值协议》");
    UILabel *label = [[UILabel alloc] init];
    label.frame = CGRectMake(0, paybtn.bottom+30, backView.width, 20);
    label.text = [NSString stringWithFormat:@"%@%@",YZMsg(@"已阅读并同意"),xieyiStr];
    label.textColor = RGB_COLOR(@"#646464", 1);
    label.textAlignment = NSTextAlignmentCenter;
    label.font = [UIFont systemFontOfSize:13];
    [backView addSubview:label];
//    [label mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerX.equalTo(backView);
//        make.top.equalTo(paybtn.mas_bottom).offset(20);
//    }];
    NSRange range = [label.text rangeOfString:xieyiStr];
    NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:label.text];
    [str addAttribute:NSForegroundColorAttributeName value:RGB(50, 160, 255) range:range];
    label.attributedText = str;
    label.userInteractionEnabled = YES;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(eula)];
    [label addGestureRecognizer:tap];

    backView.size = CGSizeMake(_window_width-30, y + 30+50+30+70);

    
    
    CGFloat bottomLY;
    if (backView.bottom > backScroll.height - 40 -ShowDiff) {
        bottomLY = backView.bottom + 40;
    }else{
        bottomLY = backScroll.height - 40 -ShowDiff;
    }
    backScroll.contentSize = CGSizeMake(0, bottomLY);

    
    NSString *payTitle;
    if ([payTypeID isEqual:@"paypal"]) {
        payTitle = [NSString stringWithFormat:@"%@($%@)",YZMsg(@"确认充值"),minstr([_seleDic valueForKey:@"money"])];

    }else{
        payTitle = [NSString stringWithFormat:@"%@(¥%@)",YZMsg(@"确认充值"),minstr([_seleDic valueForKey:@"money"])];

    }
    [paybtn setTitle:payTitle forState:0];

}
-(void)payBtnClick{
    if (minstr([payTypeSelDic valueForKey:@"href"]).length > 6) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:minstr([payTypeSelDic valueForKey:@"href"])]];
    }else{
        if ([payTypeID isEqual:@"ali"]) {
            [self doAlipayPay];
        }
        if ([payTypeID isEqual:@"wx"]) {
            [self WeiXinPay];
        }
        if ([payTypeID isEqual:@"apple"]) {
            [applePays applePay:_seleDic];
        }
        if ([payTypeID isEqual:@"paypal"]) {
            [self getBraintreeToken];
        }
    }

}
#pragma mark----首充
-(void)firstChargeBtnClick{
    YBWeakSelf;
    _chargeView = [[FirstChargeView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    _chargeView.btnEvent = ^{
        [weakSelf.chargeView removeFromSuperview];
        weakSelf.chargeView = nil;
    };
    [self.view addSubview:_chargeView];
    
}
- (void)payTypeBtnClick:(UIButton *)sender{
//    if (sender.selected) {
//        return;
//    }
    for (UIButton *btn in payTypeArray) {
        if (btn == sender) {
            btn.selected = YES;
        }else{
            btn.selected = NO;
        }
    }
    NSArray *typearr = allArray[1];
    payTypeSelDic = typearr[sender.tag - 2000];
    payTypeID = minstr([payTypeSelDic valueForKey:@"id"]);
    for (int i = 0; i < coinArray.count; i++) {
        UIButton *btn = coinArray[i];
        UILabel *label = (UILabel *)[btn viewWithTag:btn.tag+3000];
        UILabel *moneyLb = (UILabel *)[btn viewWithTag:btn.tag+4000];

        if ([payTypeID isEqual:@"apple"]) {
            label.text = minstr([allArray[0][i] valueForKey:@"coin_ios"]);
            moneyLb.text =[NSString stringWithFormat:@"¥%@",minstr([allArray[0][i] valueForKey:@"money"])];

        }else if ([payTypeID isEqual:@"paypal"]){
            label.text = minstr([allArray[0][i] valueForKey:@"coin_paypal"]);
            moneyLb.text =[NSString stringWithFormat:@"$%@",minstr([allArray[0][i] valueForKey:@"money"])];
        }else{
            label.text = minstr([allArray[0][i] valueForKey:@"coin"]);
            moneyLb.text =[NSString stringWithFormat:@"¥%@",minstr([allArray[0][i] valueForKey:@"money"])];

        }
    }
    NSString *payTitle;
    if ([payTypeID isEqual:@"paypal"]) {
        payTitle = [NSString stringWithFormat:@"%@($%@)",YZMsg(@"确认充值"),minstr([_seleDic valueForKey:@"money"])];

    }else{
        payTitle = [NSString stringWithFormat:@"%@(¥%@)",YZMsg(@"确认充值"),minstr([_seleDic valueForKey:@"money"])];

    }
    [paybtn setTitle:payTitle forState:0];

}
- (void)coinBtnClick:(UIButton *)sender{
    for (UIButton *btn in coinArray) {
        if (btn == sender) {
            btn.selected = YES;
        }else{
            btn.selected = NO;
        }
    }
    _seleDic = allArray[0][sender.tag-3000];
    NSString *payTitle;
    if ([payTypeID isEqual:@"paypal"]) {
        payTitle = [NSString stringWithFormat:@"%@($%@)",YZMsg(@"确认充值"),minstr([_seleDic valueForKey:@"money"])];

    }else{
        payTitle = [NSString stringWithFormat:@"%@(¥%@)",YZMsg(@"确认充值"),minstr([_seleDic valueForKey:@"money"])];

    }
    [paybtn setTitle:payTitle forState:0];
}
- (void)eula{
    NSString *language = [PublicObj getCurrentLanguage];

    YBWebViewController *VC = [[YBWebViewController alloc]init];
    NSString *urlsss = [NSString stringWithFormat:@"/portal/page/index?id=6&language=%@",language];
    NSString *paths = [h5url stringByAppendingString:urlsss];
    VC.urls = paths;
    [[MXBADelegate sharedAppDelegate]pushViewController:VC animated:YES];

}

/*payPal支付*/
#pragma mark - PayPal
-(void)getBraintreeToken{
    [MBProgressHUD showMessage:@""];
    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             };
    [YBToolClass postNetworkWithUrl:@"User.getBraintreeToken" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            NSDictionary *infos =  [info firstObject];
            _braintreeStr =minstr([infos valueForKey:@"braintreeToken"]);
            [self dopayPal:_braintreeStr];
        }
        else{
            [MBProgressHUD showError:msg];
        }

    } fail:^{

    }];

}
-(void)dopayPal:(NSString *)braintreeToken{
    NSLog(@"paypal支付");
    
    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             @"changeid":[_seleDic valueForKey:@"id"],
                             @"coin":[_seleDic valueForKey:@"coin_paypal"],
                             @"money":[_seleDic valueForKey:@"money"]
                             };
    [YBToolClass postNetworkWithUrl:@"Charge.getBraintreePaypalOrder" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            shortDesc = [NSString stringWithFormat:@"%@%@",minstr([_seleDic valueForKey:@"coin_paypal"]),[common name_coin]];

            NSDictionary *dict = [info firstObject];
            
            //调起paypal支付
            [self selPaypalParameter:minstr([dict valueForKey:@"orderid"])];
        }
        else{
            [MBProgressHUD showError:msg];
        }

    } fail:^{
        [MBProgressHUD hideHUD];

    }];
}
- (BTPayPalDriver *)payPalDriver
{
    if (!_payPalDriver) {
        //TODO 替换为自己的 token
        BTAPIClient *braintreeClient = [[BTAPIClient alloc] initWithAuthorization:_braintreeStr];
        _payPalDriver = [[BTPayPalDriver alloc] initWithAPIClient:braintreeClient];
        _payPalDriver.viewControllerPresentingDelegate = self;
    }
    return _payPalDriver;
}

-(void)selPaypalParameter:(NSString *)orderIDStr{
    NSString *price = minstr([_seleDic valueForKey:@"money"]);
    NSString *orderNo = orderIDStr;
    BTPayPalRequest *request = [[BTPayPalRequest alloc] initWithAmount:price];
    request.currencyCode = @"USD";
    
    BTPayPalLineItem *item = [[BTPayPalLineItem alloc] initWithQuantity:@"1" unitAmount:price name:shortDesc kind:BTPayPalLineItemKindDebit];
    item.productCode = orderNo; //订单编号
    request.lineItems = @[item];

    [self.payPalDriver requestOneTimePayment:request completion:^(BTPayPalAccountNonce * _Nullable tokenizedPayPalAccount, NSError * _Nullable error) {
          
        if (tokenizedPayPalAccount) {
            NSLog(@"-->> paypal 支付成功 nonce:%@", tokenizedPayPalAccount.nonce);
            [self BraintreeCallback:orderIDStr nonce:tokenizedPayPalAccount.nonce];
            
            //todo 调用后台接口，传递 tokenizedPayPalAccount.nonce
            
        } else if (error) {
            // Handle error here...
            NSLog(@"paypal 支付失败 ：%@", error);
            [MBProgressHUD showError:YZMsg(@"支付失败")];
            
        } else {
            // Buyer canceled payment approval
            [MBProgressHUD showError:YZMsg(@"支付取消")];
        }
        
        [MBProgressHUD hideHUD];
    }];


}

-(void)BraintreeCallback:(NSString *)orderno nonce:(NSString *)nonce{
    
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"ordertype":@"coin_charge",@"orderno":orderno,@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],@"nonce":nonce};
    NSString *sign = [YBToolClass dynamicSortString:signdic];

    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             @"orderno":orderno,
                             @"ordertype":@"coin_charge",
                             @"nonce":nonce,
                             @"money":minstr([_seleDic valueForKey:@"money"]),
                             @"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],
                             @"sign":sign
                             };
    [YBToolClass postNetworkWithUrl:@"User.BraintreeCallback" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            [MBProgressHUD showError:@"支付成功"];
            [self requestData];
        }
        else{
            [MBProgressHUD showError:msg];
        }

    } fail:^{
        [MBProgressHUD hideHUD];

    }];

}
#pragma mark - BTViewControllerPresentingDelegate
// Required
- (void)paymentDriver:(id)paymentDriver requestsPresentationOfViewController:(UIViewController *)viewController
{
    [MBProgressHUD hideHUD];
    viewController.modalPresentationStyle = 0;
    [[[MXBADelegate sharedAppDelegate] topViewController]presentViewController:viewController animated:YES completion:nil];
}

// Required
- (void)paymentDriver:(id)paymentDriver requestsDismissalOfViewController:(UIViewController *)viewController
{
    [viewController dismissViewControllerAnimated:YES completion:^{
        [MBProgressHUD hideHUD];
    }];
}

/******************   内购  ********************/
-(void)applePayHUD{
    dispatch_async(dispatch_get_main_queue(), ^{
        [MBProgressHUD hideHUDForView:self.view animated:YES];
    });
}
-(void)applePayShowHUD{
    dispatch_async(dispatch_get_main_queue(), ^{

        [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    });
}

//内购成功
-(void)applePaySuccess{
    NSLog(@"苹果支付成功");
    [self requestData];
}
//微信支付*****************************************************************************************************************
-(void)WeiXinPay{
    NSLog(@"微信支付");
    [MBProgressHUD showMessage:@""];
    
    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"changeid":[_seleDic valueForKey:@"id"],
                             @"coin":[_seleDic valueForKey:@"coin"],
                             @"money":[_seleDic valueForKey:@"money"]
                             };
    [YBToolClass postNetworkWithUrl:@"Charge.getWxOrder" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD hideHUD];
        if (code == 0) {
            
            NSDictionary *dict = [info firstObject];
            //调起微信支付
            NSString *times = [dict objectForKey:@"timestamp"];
            PayReq* req             = [[PayReq alloc] init];
            req.partnerId           = [dict objectForKey:@"partnerid"];
            NSString *pid = [NSString stringWithFormat:@"%@",[dict objectForKey:@"prepayid"]];
            if ([pid isEqual:[NSNull null]] || pid == NULL || [pid isEqual:@"null"]) {
                pid = @"123";
            }
            req.prepayId            = pid;
            req.nonceStr            = [dict objectForKey:@"noncestr"];
            req.timeStamp           = times.intValue;
            req.package             = [dict objectForKey:@"package"];
            req.sign                = [dict objectForKey:@"sign"];
            [WXApi sendReq:req completion:^(BOOL success) {
                NSLog(@"wxapi调用 %d",success);
            }];
        }
        else{
            [MBProgressHUD showError:msg];
        }

    } fail:^{
        [MBProgressHUD hideHUD];

    }];
}
-(void)onResp:(BaseResp *)resp{
    //支付返回结果，实际支付结果需要去微信服务器端查询
    NSString *strMsg = [NSString stringWithFormat:@"支付结果"];
    switch (resp.errCode) {
        case WXSuccess:
            strMsg = @"支付结果：成功！";
            NSLog(@"支付成功－PaySuccess，retcode = %d", resp.errCode);
            [self requestData];
            break;
        default:
            strMsg = [NSString stringWithFormat:@"支付结果：失败！retcode = %d, retstr = %@", resp.errCode,resp.errStr];
            NSLog(@"错误，retcode = %d, retstr = %@", resp.errCode,resp.errStr);
            break;
    }
}
//微信支付*****************************************************************************************************************
//支付宝支付*****************************************************************************************************************
- (void)doAlipayPay
{
    NSString *partner = _aliapp_partner;
    NSString *seller =  _aliapp_seller_id;
    NSString *privateKey = _aliapp_key_ios;
    
    
    
    //partner和seller获取失败,提示
    if ([partner length] == 0 ||
        [seller length] == 0 ||
        [privateKey length] == 0){
//        [MBProgressHUD showError:@"缺少partner或者seller或者私钥"];
        [MBProgressHUD showError:YZMsg(@"支付宝未配置")];

        return;
    }
    /*
     *生成订单信息及签名
     */
    //将商品信息赋予AlixPayOrder的成员变量
    Order *order = [[Order alloc] init];
    order.partner = partner;
    order.seller = seller;
    //获取订单id
    //将商品信息拼接成字符串
    
    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"changeid":[_seleDic valueForKey:@"id"],
                             @"coin":[_seleDic valueForKey:@"coin"],
                             @"money":[_seleDic valueForKey:@"money"]
                             };
    
    [YBToolClass postNetworkWithUrl:@"Charge.getAliOrder" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSString *infos = [[info firstObject] valueForKey:@"orderid"];
            order.tradeNO = infos;
            order.notifyURL = [h5url stringByAppendingString:@"/appapi/Pay/notify_ali"];
            order.amount = [_seleDic valueForKey:@"money"];
            order.productName = [NSString stringWithFormat:@"%@%@",[_seleDic valueForKey:@"coin"],[common name_coin]];
            order.productDescription = @"productDescription";
            //以下配置信息是默认信息,不需要更改.
            order.service = @"mobile.securitypay.pay";
            order.paymentType = @"1";
            order.inputCharset = @"utf-8";
            order.itBPay = @"30m";
            order.showUrl = @"m.alipay.com";
            //应用注册scheme,在AlixPayDemo-Info.plist定义URL types,用于快捷支付成功后重新唤起商户应用
            NSString *appScheme = [[NSBundle mainBundle] bundleIdentifier];
            //将商品信息拼接成字符串
            NSString *orderSpec = [order description];
            NSLog(@"orderSpec = %@",orderSpec);
            //获取私钥并将商户信息签名,外部商户可以根据情况存放私钥和签名,只需要遵循RSA签名规范,并将签名字符串base64编码和UrlEncode
            id<DataSigner> signer = CreateRSADataSigner(privateKey);
            NSString *signedString = [signer signString:orderSpec];
            //将签名成功字符串格式化为订单字符串,请严格按照该格式
            NSString *orderString = nil;
            if (signedString != nil) {
                orderString = [NSString stringWithFormat:@"%@&sign=\"%@\"&sign_type=\"%@\"",
                               orderSpec, signedString, @"RSA"];
                
                [[AlipaySDK defaultService] payOrder:orderString fromScheme:appScheme callback:^(NSDictionary *resultDic) {
                    NSLog(@"reslut = %@",resultDic);
                    NSInteger resultStatus = [resultDic[@"resultStatus"] integerValue];
                    NSLog(@"#######%ld",(long)resultStatus);
                    // NSString *publicKey = alipaypublicKey;
                    NSLog(@"支付状态信息---%ld---%@",resultStatus,[resultDic valueForKey:@"memo"]);
                    // 是否支付成功
                    if (9000 == resultStatus) {
                        /*
                         *用公钥验证签名
                         */
                        [self requestData];
                        
                    }else{
                        [MBProgressHUD showError:YZMsg(@"支付失败")];
                    }
                }];
            }
        

        }
    } fail:^{
        
    }];
    
    
    
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
