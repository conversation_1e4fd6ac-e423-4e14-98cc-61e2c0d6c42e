//
//  otherUserMsgVC.m
//  YBLive
//
//  Created by Boom on 2018/10/7.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "otherUserMsgVC.h"
#import "personLiveCell.h"
#import "LiveNodeModel.h"
#import "impressVC.h"
#import "hietoryPlay.h"
#import "fenXiangView.h"
#import "guardRankVC.h"
#import "MessageListModel.h"
#import "mineVideoCell.h"
#import "NearbyVideoModel.h"
#import "YBLookVideoVC.h"
#import "fansViewController.h"
#import "attrViewController.h"
#import "myInfoEdit.h"
#import "UIImage+GradientColor.h"
#import "LivePlay.h"
#import "impressVC.h"
#import "shopDetailVC.h"
#import "userCenterDTview.h"
#import "jubaoVC.h"
#import "UserRoomViewController.h"
#import "CenterPayView.h"
#import "HeaderBackImgView.h"
#import "TZImagePickerController.h"
#import "TChatC2CController.h"
#import "TConversationCell.h"

@interface otherUserMsgVC ()<UICollectionViewDelegate,UICollectionViewDataSource,TZImagePickerControllerDelegate,UINavigationControllerDelegate,UIImagePickerControllerDelegate,UIGestureRecognizerDelegate>{
    //导航栏控件
    
    UIButton *returnBtn;
    UIButton *shareBtn;
    UIButton *reportBtn;

    UIButton *editBtn;
    UILabel *titleLabel;
    fenXiangView *shareView;

    //底部d三个按钮
    UIButton *attionBtn;
    UIButton *jmsgBtn;
    UIButton *blackBtn;
    //CollectionView
    UICollectionView *personCollection;
    UIView *collectionHeader;
    NSMutableArray *liveArray;
    int livePage;
    
    NSMutableArray *videoArray;
    int videoPage;

    //
    NSDictionary *infoDic;
    
    UIView *switchLine;
    UIView *segmentView;
    
    BOOL isVideo;
    UIButton *videoButton;
    UIButton *dynamicButton;

    UIButton *liveButton;
    UIButton *payButton;

    NSMutableArray *itemButtonArray;
    
    UIButton *livingBtn;
    
    NSString *type_val;//
    NSString *livetype;//
    UIAlertController *md5AlertController;
    NSString *_sdkType;//0-金山  1-腾讯
    NSDictionary *selectedDic;
    moviePlay *temp;
    UserRoomViewController *chatTemp;
    UIView *impressview;
    UIImageView *iconImgView;
    UIImage *headBgimg;
    NSString *headBgStr;
    UIView *backView;
}
@property (nonatomic,strong) UIView *naviView;
@property (nonatomic,strong) UIScrollView *dataScrollV;
@property (nonatomic,strong) userCenterDTview *dtView;
@property (nonatomic,strong) CenterPayView *payView;
@property (nonatomic,strong) NSArray *paidprogram_list;
@property (nonatomic,strong)UIView *nothingView;
@property(nonatomic,strong)HeaderBackImgView *headImgView;

@end

@implementation otherUserMsgVC
#pragma mark ================ 导航栏 ===============
//创建l资料卡
//创建导航栏
-(UIView *)naviView{
    if (!_naviView) {
        _naviView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64+statusbarHeight)];
        _naviView.backgroundColor = [UIColor clearColor];
//        [self.view addSubview:_naviView];
        UITapGestureRecognizer *headTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(headerImgTap)];
        headTap.delegate = self;
        [_naviView addGestureRecognizer:headTap];

        titleLabel = [[UILabel alloc]initWithFrame:CGRectMake(50, 34+statusbarHeight, _window_width-100, 20)];
        titleLabel.textAlignment = NSTextAlignmentCenter;
        titleLabel.font = navtionTitleFont;
        titleLabel.textColor = [UIColor blackColor];
        [_naviView addSubview:titleLabel];
        
        returnBtn = [UIButton buttonWithType:0];
        returnBtn.frame = CGRectMake(0, 24+statusbarHeight, 40, 40);
        [returnBtn setImage:[UIImage imageNamed:@"person_back_white"] forState:0];
        [returnBtn addTarget:self action:@selector(doReturn) forControlEvents:UIControlEventTouchUpInside];
        returnBtn.imageEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
        [_naviView addSubview:returnBtn];
        
        shareBtn = [UIButton buttonWithType:0];
        shareBtn.frame = CGRectMake(_window_width-40, 24+statusbarHeight, 40, 40);
        [shareBtn setImage:[UIImage imageNamed:@"person_share_white"] forState:0];
        [shareBtn addTarget:self action:@selector(doShare) forControlEvents:UIControlEventTouchUpInside];
        shareBtn.imageEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
        [_naviView addSubview:shareBtn];

        reportBtn = [UIButton buttonWithType:0];
        reportBtn.frame = CGRectMake(shareBtn.left-40, 24+statusbarHeight, 40, 40);
        [reportBtn setImage:[UIImage imageNamed:@"person_report"] forState:0];
        reportBtn.imageEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
        [reportBtn addTarget:self action:@selector(reportBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [_naviView addSubview:reportBtn];
        
        if ([_userID isEqual:[Config getOwnID]]) {
            reportBtn.hidden = YES;
        }else{
            reportBtn.hidden = NO;
        }
    }
    return _naviView;
}
-(void)reportBtnClick{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    jubaoVC *vc = [[jubaoVC alloc]init];
    vc.dongtaiId = _userID;
    vc.isLive = YES;
    [self presentViewController:vc animated:YES completion:nil];

}
- (void)doEdit:(UIButton *)sender{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if ([sender.titleLabel.text isEqual:YZMsg(@"关注")] ||[sender.titleLabel.text isEqual:YZMsg(@"已关注")]) {
        [YBToolClass postNetworkWithUrl:@"User.setAttent" andParameter:@{@"touid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            if (code == 0) {
                NSString *isattent = [NSString stringWithFormat:@"%@",[[info firstObject] valueForKey:@"isattent"]];
                NSDictionary *subdic = [info firstObject];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"reloadLiveplayAttion" object:subdic];
                if (self.videoBlock) {
                    self.videoBlock(isattent);
                }
                if ([isattent isEqual:@"1"]) {
                    [editBtn setTitle:YZMsg(@"已关注") forState:0];
                    [editBtn setBackgroundColor:RGB(225, 225, 225)];
                    [editBtn setTitleColor:RGB(125, 125, 125) forState:0];
                    blackBtn.selected = NO;
                    NSLog(@"关注成功");
                    if (self.block) {
                        self.block();
                    }
                }
                else
                {
                    [editBtn setTitle:YZMsg(@"关注") forState:0];
                    [editBtn setBackgroundColor:normalColors];
                    [editBtn setTitleColor:UIColor.whiteColor forState:0];
                    NSLog(@"取消关注成功");
                }
            }
        } fail:^{
        }];
    }else{
        myInfoEdit *info = [[myInfoEdit alloc]init];
        [[MXBADelegate sharedAppDelegate] pushViewController:info animated:YES];

    }
}
//分享
- (void)doShare{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (infoDic) {
        if (!shareView) {
            shareView = [[fenXiangView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
            [shareView GetDIc:infoDic];
            [[UIApplication sharedApplication].keyWindow addSubview:shareView];
        }else{
            [shareView show];
        }
    }
}
//返回
- (void)doReturn{
    [self.navigationController popViewControllerAnimated:YES];
}
#pragma mark ================ 底部按钮 ===============
- (void)creatBottomView{
//    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height-60-ShowDiff, _window_width, 60+ShowDiff)];
//    view.backgroundColor = [UIColor whiteColor];
//    [self.view addSubview:view];
////    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(0, 0, _window_width, 1) andColor:RGB_COLOR(@"#f4f5f6", 1) andView:view];
//    
//    NSArray *bottomTitleArr;
//    NSArray *btnTypeArr;
//    if ([[common letter_switch] isEqual:@"1"]) {
//        bottomTitleArr = @[@"center-私信",@"center-拉黑"];
//        btnTypeArr = @[@(BottomType_Chat), @(BottomType_Black)];
//    }else{
//        
//        bottomTitleArr = @[@"center-拉黑"];
//        btnTypeArr = @[@(BottomType_Black)];
//    }
//        for (int i = 0; i< bottomTitleArr.count; i++) {
//            UIButton *btn= [UIButton buttonWithType:0];
//            btn.frame = CGRectMake(view.width-12-(125*(i+1)), 0, 125, 57);
//            [btn setImage:[UIImage imageNamed:getImagename(bottomTitleArr[i])] forState:0];
//            [btn addTarget:self action:@selector(bottomBtnClick:) forControlEvents:UIControlEventTouchUpInside];
//            [view addSubview:btn];
//            _bottomBtnType =[btnTypeArr[i] intValue];
//            if (_bottomBtnType == BottomType_Chat) {
//                jmsgBtn = btn;
//            }
//            if (_bottomBtnType == BottomType_Black) {
//                blackBtn = btn;
//                [btn setImage:[UIImage imageNamed:@"center-已拉黑"] forState:UIControlStateSelected];
//    
//            }
//
//        }
//    for (int i = 0; i< bottomTitleArr.count; i++) {
//        UIButton *btn = [UIButton buttonWithType:0];
//        btn.frame = CGRectMake(_window_width/bottomTitleArr.count*i, 0, _window_width/bottomTitleArr.count, 40);
//        NSString *str = bottomTitleArr[i];
//        [btn setTitle:[NSString stringWithFormat:@" %@",YZMsg(str)] forState:0];
//        [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"person-%@",str]] forState:0];
//        [btn setTitleColor:RGB_COLOR(@"#333333", 1) forState:0];
//        btn.titleLabel.font = [UIFont systemFontOfSize:14];
//        [btn addTarget:self action:@selector(bottomBtnClick:) forControlEvents:UIControlEventTouchUpInside];
//        [view addSubview:btn];
//
//        if ([bottomTitleArr[i] isEqual:@"关注"]) {
//            attionBtn = btn;
//            [btn setTitle:YZMsg(@" 已关注") forState:UIControlStateSelected];
//            [btn setImage:[UIImage imageNamed:@"person-已关注"] forState:UIControlStateSelected];
//
//        }
//        if ([bottomTitleArr[i] isEqual:@"私信"]) {
//            jmsgBtn = btn;
//        }
//        if ([bottomTitleArr[i] isEqual:@"拉黑"]) {
//            blackBtn = btn;
//            [btn setTitle:YZMsg(@" 解除拉黑") forState:UIControlStateSelected];
//            [btn setImage:[UIImage imageNamed:@"person-拉黑"] forState:UIControlStateSelected];
//
//        }
//        if (i != bottomTitleArr.count - 1) {
//            [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(btn.right-0.5, 12, 1, 16) andColor:RGB_COLOR(@"#f4f5f6", 1) andView:view];
//        }
//    }
}
- (void)bottomBtnClick:(UIButton *)sender{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    //关注
    if (sender == attionBtn) {
        [YBToolClass postNetworkWithUrl:@"User.setAttent" andParameter:@{@"touid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            if (code == 0) {
                NSString *isattent = [NSString stringWithFormat:@"%@",[[info firstObject] valueForKey:@"isattent"]];
                NSDictionary *subdic = [info firstObject];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"reloadLiveplayAttion" object:subdic];
                if (self.videoBlock) {
                    self.videoBlock(isattent);
                }

                if ([isattent isEqual:@"1"]) {
//                    [attionBtn setTitle:YZMsg(@"已关注") forState:UIControlStateNormal];
//                    [blackBtn setTitle:YZMsg(@"拉黑") forState:UIControlStateNormal];
                    if (![_userID isEqual:[Config getOwnID]]) {
                        [editBtn setTitle:YZMsg(@"已关注") forState:0];

                    }
                    blackBtn.selected = NO;
                    NSLog(@"关注成功");
                    if (self.block) {
                        self.block();
                    }
                }
                else
                {
//                    [attionBtn setTitle:YZMsg(@"关注") forState:UIControlStateNormal];
                    attionBtn.selected = NO;
                    if (![_userID isEqual:[Config getOwnID]]) {
                        [editBtn setTitle:YZMsg(@"关注") forState:0];

                    }

                    NSLog(@"取消关注成功");
                }
            }
        } fail:^{
            
        }];

    }
    //私信
    if (sender == jmsgBtn) {
        if ([[common letter_switch] isEqual:@"0"]) {
            [MBProgressHUD showError:YZMsg(@"私信对话平台已关闭,暂时无法使用")];
            return;
        }

        TConversationCellData *data = [[TConversationCellData alloc] init];
        data.convId = self.userID;
        data.convType = TConv_Type_C2C;
        data.title = minstr(self.chatname);
        data.userHeader = minstr(self.icon);
        data.userName = minstr(self.chatname);
        data.isAtt = [NSString stringWithFormat:@"%d",attionBtn.selected];

        TChatC2CController *chat = [[TChatC2CController alloc] init];
        chat.conversation = data;
        [self.navigationController pushViewController:chat animated:YES];

    }
    //拉黑
    if (sender == blackBtn) {
        [YBToolClass postNetworkWithUrl:@"User.setBlack" andParameter:@{@"touid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            if (code == 0) {
                if (!blackBtn.selected) {
                    blackBtn.selected  = YES;
                    if (![_userID isEqual:[Config getOwnID]]) {
                        [editBtn setTitle:YZMsg(@"关注") forState:0];
                        [editBtn setBackgroundColor:normalColors];
                        [editBtn setTitleColor:UIColor.whiteColor forState:0];

                    }
                    [MBProgressHUD showError:YZMsg(@"已拉黑")];
                }
                else{
                    blackBtn.selected = NO;
                    [MBProgressHUD showError:YZMsg(@"已解除拉黑")];
                }

            }
        } fail:^{
            
        }];
    }
}
#pragma mark ================ viewdidload ===============
- (void)viewDidLoad {
    [super viewDidLoad];
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = [UIColor whiteColor];
    liveArray = [NSMutableArray array];
    livePage = 1;
    _paidprogram_list = [NSArray array];
    videoArray = [NSMutableArray array];
    videoPage = 1;
    [self.view addSubview:self.naviView];
    [self requestData];
    
}
//请求直播记录分页
- (void)pullInternet{
    [YBToolClass postNetworkWithUrl:@"User.getLiverecord" andParameter:@{@"touid":_userID,@"p":@(livePage)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [personCollection.mj_footer endRefreshing];
        if (code == 0) {
            [liveArray addObjectsFromArray:info];
            [personCollection reloadData];
            if ([info count] == 0) {
                [personCollection.mj_footer endRefreshingWithNoMoreData];
            }
        }
    } fail:^{
        [personCollection.mj_footer endRefreshing];
    }];
}
- (void)pullVideoList{
    [YBToolClass postNetworkWithUrl:@"video.gethomevideo" andParameter:@{@"touid":_userID,@"p":@(videoPage)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [personCollection.mj_footer endRefreshing];
        if (code == 0) {
            [videoArray addObjectsFromArray:info];
            [personCollection reloadData];
            if ([info count] == 0) {
                [personCollection.mj_footer endRefreshingWithNoMoreData];
            }
        }
    } fail:^{
        [personCollection.mj_footer endRefreshing];
    }];

}
- (void)requestData{
    [YBToolClass postNetworkWithUrl:@"User.getUserHome" andParameter:@{@"touid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *subDic = [info firstObject];
            infoDic = subDic;
            [self creatCollectionHeader:subDic];
            [liveArray addObjectsFromArray:[subDic valueForKey:@"liverecord"]];
            [videoArray addObjectsFromArray:[subDic valueForKey:@"videolist"]];

            _paidprogram_list =[subDic valueForKey:@"paidprogram_list"];
//            [personCollection reloadData];
            self.chatname = [subDic valueForKey:@"user_nickname"];
            self.icon = [subDic valueForKey:@"avatar"];

            if ([[subDic valueForKey:@"isattention"] isEqual:@"0"]) {
                attionBtn.selected = NO;
            }
            else{
                attionBtn.selected = YES;
            }
            if ([[subDic valueForKey:@"isblack"] isEqual:@"1"]) {
                blackBtn.selected = YES;
            }
            else{
                blackBtn.selected = NO;
            }

        }
    } fail:^{
        
    }];
}
-(void)headerImgTap {
    YBWeakSelf;
    if (!_headImgView) {
        _headImgView = [[HeaderBackImgView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andHeadUrl:minstr([infoDic valueForKey:@"bg_img"]) andUerid:_userID];
        _headImgView.tapEvent = ^(NSString *types) {
            if ([types isEqual:@"hide"]) {
            }else if ([types isEqual:@"拍照"]){
                [weakSelf clickTake];
            }else if ([types isEqual:@"相册"]){
                [weakSelf clickSel];
            }
            [weakSelf.headImgView removeFromSuperview];
            weakSelf.headImgView = nil;
        };
    }
    [[UIApplication sharedApplication].keyWindow addSubview:_headImgView];

}

- (void)creatCollectionHeader:(NSDictionary *)dic{
    if (!collectionHeader) {
        CGFloat shopHeight = [minstr([dic valueForKey:@"isshop"]) isEqual:@"1"] ? 60.0 : 0.0;
        
        CGFloat height_header = _window_height *0.5+50;//270

        if(IS_BIG_SCREEN){
            height_header = _window_height *0.35+50;//270
        }
        if(IS_IPHONE_6P){
            height_header = _window_height *0.5+50;
        }
        if ([[YBYoungManager shareInstance]isOpenYoung]) {
            collectionHeader = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, height_header+statusbarHeight)];
        }else{
            if (shopHeight == 60) {
                collectionHeader = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, height_header+20+statusbarHeight + shopHeight)];
            }else{
                collectionHeader = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, height_header+statusbarHeight + shopHeight)];
            }
        }
        collectionHeader.backgroundColor = RGB_COLOR(@"#f4f5f6", 1);
        [self.view addSubview:collectionHeader];
        [self.view sendSubviewToBack:collectionHeader];
        iconImgView = [[UIImageView alloc]init];
        [iconImgView sd_setImageWithURL:[NSURL URLWithString:minstr([dic valueForKey:@"bg_img"])] placeholderImage:[UIImage imageNamed:@"bg1"]];
        iconImgView.contentMode = UIViewContentModeScaleAspectFill;
        iconImgView.clipsToBounds = YES;
        iconImgView.userInteractionEnabled = YES;
        [collectionHeader addSubview:iconImgView];
        [iconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.width.height.equalTo(collectionHeader);
        }];
        UITapGestureRecognizer *headTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(headerImgTap)];
        headTap.delegate = self;
        [iconImgView addGestureRecognizer:headTap];
            
        backView = [[UIView alloc]init];
        backView.backgroundColor =UIColor.whiteColor;// RGB_COLOR(@"#000000", 0.7);
        [iconImgView addSubview:backView];
        [backView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.width.equalTo(iconImgView);
            make.top.equalTo(iconImgView.mas_centerY);
            make.bottom.equalTo(iconImgView);//.offset(shopHeight+50)
        }];
        UIImageView *iconImgView2 = [[UIImageView alloc]init];
        [iconImgView2 sd_setImageWithURL:[NSURL URLWithString:minstr([dic valueForKey:@"avatar"])] placeholderImage:[UIImage imageNamed:@"bg1"]];
        iconImgView2.contentMode = UIViewContentModeScaleToFill;
        iconImgView2.clipsToBounds = YES;
        iconImgView2.layer.cornerRadius = 50;
        iconImgView2.layer.masksToBounds = YES;
        [iconImgView addSubview:iconImgView2];
        [iconImgView2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(15);
            make.width.height.mas_equalTo(100);
            make.centerY.equalTo(backView.mas_top).offset(20);//.offset(-6-shopHeight/2);
        }];

        UILabel *nameLabel = [[UILabel alloc]init];
        nameLabel.textColor = [UIColor blackColor];
        nameLabel.font = [UIFont boldSystemFontOfSize:18];
        nameLabel.text = minstr([dic valueForKey:@"user_nickname"]);
        [iconImgView addSubview:nameLabel];
        [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(iconImgView2.mas_bottom).offset(15);
            make.left.equalTo(iconImgView2.mas_left);//.offset(15);
            make.height.mas_equalTo(25);
        }];
        UIImageView *sexImgView = [[UIImageView alloc]init];
        if ([minstr([dic valueForKey:@"sex"]) isEqual:@"1"]) {
            sexImgView.image = [UIImage imageNamed:@"sex_man"];
        }else{
            sexImgView.image = [UIImage imageNamed:@"sex_woman"];
        }
        [iconImgView addSubview:sexImgView];
        [sexImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(nameLabel.mas_right).offset(5);
            make.centerY.equalTo(nameLabel.mas_centerY);
            make.height.mas_equalTo(15);
            make.width.mas_equalTo(18);

        }];
        UIImageView *hostImgView = [[UIImageView alloc]init];
        NSDictionary *levelDic1 = [common getAnchorLevelMessage:minstr([dic valueForKey:@"level_anchor"])];
        [hostImgView sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic1 valueForKey:@"thumb"])]];

        [iconImgView addSubview:hostImgView];
        [hostImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.height.equalTo(sexImgView);
            make.left.equalTo(sexImgView.mas_right).offset(4);
            make.width.equalTo(hostImgView.mas_height).multipliedBy(2);
        }];

        UIImageView *levelImgView = [[UIImageView alloc]init];
        NSDictionary *levelDic = [common getUserLevelMessage:minstr([dic valueForKey:@"level"])];
        [levelImgView sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic valueForKey:@"thumb"])]];

        [iconImgView addSubview:levelImgView];
        [levelImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.height.equalTo(sexImgView);
            make.left.equalTo(hostImgView.mas_right).offset(4);
            make.width.equalTo(hostImgView.mas_height).multipliedBy(2);
        }];
//
        UILabel *IDLabel = [[UILabel alloc]init];
        IDLabel.textColor = [UIColor grayColor];
        IDLabel.font = [UIFont systemFontOfSize:12];
        NSString *laingname = minstr([[dic valueForKey:@"liang"] valueForKey:@"name"]);
        if ([laingname isEqual:@"0"]) {
            IDLabel.text = [NSString stringWithFormat:@"ID:%@",minstr([dic valueForKey:@"id"])];
        }
        else{
            IDLabel.text = [NSString stringWithFormat:@"%@:%@",YZMsg(@"靓"),laingname];
        }
        [iconImgView addSubview:IDLabel];
        [IDLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(nameLabel);
            make.top.equalTo(nameLabel.mas_bottom);
            make.height.mas_equalTo(18);
        }];
        livingBtn = [UIButton buttonWithType:0];
        [livingBtn addTarget:self action:@selector(domoviePlay) forControlEvents:UIControlEventTouchUpInside];
            NSURL *imgUrl = [[NSBundle mainBundle] URLForResource:getImagename(@"person_living") withExtension:@"gif"];
        [livingBtn sd_setImageWithURL:imgUrl forState:0];
        [iconImgView addSubview:livingBtn];
        if ([minstr([dic valueForKey:@"islive"]) isEqual:@"1"]) {
            livingBtn.hidden = NO;
        }else{
            livingBtn.hidden = YES;
        }
        [livingBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(IDLabel.mas_bottom);
            make.right.equalTo(backView).offset(-15);
            make.width.mas_equalTo(90);
            make.height.mas_equalTo(26);
        }];
        UIButton *fansBtn = [UIButton buttonWithType:0];
        NSString *fansStr =[NSString stringWithFormat:@"%@ \n %@",minstr([dic valueForKey:@"fans"]),YZMsg(@"粉丝")];
        fansBtn.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
        fansBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        fansBtn.titleLabel.font = [UIFont systemFontOfSize:12];
        [fansBtn setTitleColor:UIColor.grayColor forState:0];
        NSMutableAttributedString *contentStr = [[NSMutableAttributedString alloc] initWithString:fansStr];
        NSRange redRange =[[contentStr string]rangeOfString:minstr([dic valueForKey:@"fans"])];
        [contentStr addAttributes:@{NSForegroundColorAttributeName:[UIColor blackColor],NSFontAttributeName:[UIFont boldSystemFontOfSize:14]} range:redRange];
        [fansBtn setAttributedTitle:contentStr forState:0];
        fansBtn.tag = 181221;
        [fansBtn addTarget:self action:@selector(fansOrFollowBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        [backView addSubview:fansBtn];
        [fansBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(backView.mas_centerX);
            make.top.equalTo(backView.mas_top);
            make.height.mas_equalTo(50);
        }];

        UIView *lineView = [[UIView alloc]init];
        lineView.backgroundColor = RGB(238, 238, 238);
        [backView addSubview:lineView];
        [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(fansBtn.mas_centerY);
            make.height.equalTo(fansBtn).multipliedBy(0.6);
            make.width.mas_equalTo(1);
            make.left.equalTo(fansBtn.mas_right).offset(15);
        }];

        UIButton *followBtn = [UIButton buttonWithType:0];
        NSString *followStr =[NSString stringWithFormat:@"%@ \n %@",minstr([dic valueForKey:@"follows"]),YZMsg(@"关注")];
        followBtn.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
        followBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        followBtn.titleLabel.font = [UIFont systemFontOfSize:12];
        [followBtn setTitleColor:UIColor.grayColor forState:0];
        NSMutableAttributedString *followcontent = [[NSMutableAttributedString alloc] initWithString:followStr];
        NSRange flowRange =[[followcontent string]rangeOfString:minstr([dic valueForKey:@"follows"])];
        [followcontent addAttributes:@{NSForegroundColorAttributeName:[UIColor blackColor],NSFontAttributeName:[UIFont boldSystemFontOfSize:14]} range:flowRange];
        [followBtn setAttributedTitle:followcontent forState:0];
        followBtn.tag = 181222;
        [followBtn addTarget:self action:@selector(fansOrFollowBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        [backView addSubview:followBtn];
        [followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.height.equalTo(fansBtn);
            make.left.equalTo(lineView.mas_right).offset(15);
        }];
        UIView *lineView2 = [[UIView alloc]init];
        lineView2.backgroundColor = RGB(238, 238, 238);
        [backView addSubview:lineView2];
        [lineView2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(followBtn.mas_centerY);
            make.height.equalTo(followBtn).multipliedBy(0.6);
            make.width.mas_equalTo(1);
            make.left.equalTo(followBtn.mas_right).offset(15);
        }];
        UIButton *zanBtn = [UIButton buttonWithType:0];
        NSString *zanStr =[NSString stringWithFormat:@"%@ \n %@",minstr([dic valueForKey:@"praise_num"]),YZMsg(@"赞")];
        zanBtn.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
        zanBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
        zanBtn.titleLabel.font = [UIFont systemFontOfSize:12];
        [zanBtn setTitleColor:UIColor.grayColor forState:0];
        NSMutableAttributedString *zancontent = [[NSMutableAttributedString alloc] initWithString:zanStr];
        NSRange zanRange =[[zancontent string]rangeOfString:minstr([dic valueForKey:@"follows"])];
        [zancontent addAttributes:@{NSForegroundColorAttributeName:[UIColor blackColor],NSFontAttributeName:[UIFont boldSystemFontOfSize:14]} range:zanRange];
        [zanBtn setAttributedTitle:zancontent forState:0];
        zanBtn.tag = 181223;
        [backView addSubview:zanBtn];
        [zanBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.height.equalTo(fansBtn);
            make.left.equalTo(lineView2.mas_right).offset(15);
        }];

        editBtn = [UIButton buttonWithType:0];
            [editBtn addTarget:self action:@selector(doEdit:) forControlEvents:UIControlEventTouchUpInside];
        [editBtn setBackgroundColor:RGB(225, 225, 225)];
        [editBtn setTitleColor:RGB(125, 125, 125) forState:0];
        editBtn.layer.cornerRadius = 5;
        editBtn.layer.masksToBounds = YES;
            editBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        [backView addSubview:editBtn];
        [editBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(fansBtn.mas_bottom).offset(5);
            make.left.equalTo(fansBtn.mas_left);
            make.right.equalTo(zanBtn.mas_right);
            make.height.mas_equalTo(28);
        }];

        if ([_userID isEqual:[Config getOwnID]]) {
            [editBtn setTitle:YZMsg(@"编辑资料") forState:0];

        }else{
            if ([[infoDic valueForKey:@"isattention"] isEqual:@"0"]) {
                [editBtn setTitle:YZMsg(@"关注") forState:UIControlStateNormal];
                [editBtn setBackgroundColor:normalColors];
                [editBtn setTitleColor:UIColor.whiteColor forState:0];

            }
            else{
                [editBtn setTitle:YZMsg(@"已关注") forState:UIControlStateNormal];
                [editBtn setBackgroundColor:RGB(225, 225, 225)];
                [editBtn setTitleColor:RGB(125, 125, 125) forState:0];

            }

        }
        UIView *itemView = [[UIView  alloc]init];
        itemView.backgroundColor = [UIColor whiteColor];
        [backView addSubview:itemView];

        if (![[YBYoungManager shareInstance]isOpenYoung]) {

            if (shopHeight == 60) {
                [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.right.bottom.equalTo(backView);
                    make.height.mas_equalTo(50 + shopHeight);
                }];

                UIButton *storeButton = [UIButton buttonWithType:0];
                [storeButton addTarget:self action:@selector(doUserShoreHome) forControlEvents:UIControlEventTouchUpInside];
                [itemView addSubview:storeButton];
                [storeButton mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.right.top.equalTo(itemView);
                    make.height.mas_equalTo(shopHeight);
                }];
                UIImageView *storeImgV = [[UIImageView alloc]init];
                storeImgV.image = [UIImage imageNamed:@"shopicon_小店"];
                [storeButton addSubview:storeImgV];
                [storeImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(storeButton).offset(15);
                    make.centerY.equalTo(storeButton);
                    make.width.height.mas_equalTo(40);
                }];

                UILabel *storeNameL = [[UILabel alloc]init];
                storeNameL.font = [UIFont boldSystemFontOfSize:13];
                storeNameL.text = minstr([[dic valueForKey:@"shop"] valueForKey:@"name"]);
                [storeButton addSubview:storeNameL];
                [storeNameL mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(storeImgV.mas_right).offset(10);
                    make.centerY.equalTo(storeImgV).multipliedBy(0.75);
                }];

                UILabel *titlelb = [[UILabel alloc]init];
                titlelb.backgroundColor = normalColors;
                titlelb.font = [UIFont systemFontOfSize:10];
                titlelb.textColor = [UIColor whiteColor];
                titlelb.textAlignment = NSTextAlignmentCenter;
                titlelb.layer.cornerRadius = 2;
                titlelb.layer.masksToBounds = YES;
                titlelb.text = YZMsg(@"进入小店");
                [storeButton addSubview:titlelb];
                [titlelb mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(storeNameL.mas_bottom).offset(5);
                    make.left.equalTo(storeImgV.mas_right).offset(10);
                    make.height.mas_equalTo(14);
                    make.width.mas_equalTo(50);
                }];

                UILabel *storeNumL = [[UILabel alloc]init];
                storeNumL.font = [UIFont systemFontOfSize:10];
                storeNumL.textColor = RGB_COLOR(@"#969696", 1);
                storeNumL.text = [NSString stringWithFormat:@"%@%@%@",YZMsg(@"共"),minstr([[dic valueForKey:@"shop"] valueForKey:@"nums"]),YZMsg(@"件商品")];
                [storeButton addSubview:storeNumL];
                [storeNumL mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(titlelb.mas_right).offset(5);
        //            make.centerY.equalTo(storeImgV).multipliedBy(1.25);
                    make.centerY.equalTo(titlelb.mas_centerY);
                }];

                UIImageView *storeRightImgV = [[UIImageView alloc]init];
                storeRightImgV.image = [UIImage imageNamed:@"profit_right"];
                [storeButton addSubview:storeRightImgV];
                [storeRightImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.right.equalTo(storeButton).offset(-12);
                    make.centerY.equalTo(storeButton);
                    make.width.height.mas_equalTo(15);
                }];
                [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(0, 59, _window_width, 1) andColor:RGB_COLOR(@"#f0f0f0", 1) andView:storeButton];
            }else{
                [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.right.equalTo(backView);
                    make.height.mas_equalTo(50);
                    make.top.equalTo(IDLabel.mas_bottom);
                }];
            }
        }else{
            [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.right.equalTo(backView);
                make.height.mas_equalTo(50);
                make.top.equalTo(IDLabel.mas_bottom);
            }];
        }
        NSArray *array;
        if ([[YBYoungManager shareInstance]isOpenYoung]) {
            array = @[YZMsg(@"资料"),[NSString stringWithFormat:@"视频 %@",minstr([dic valueForKey:@"videonums"])],[NSString stringWithFormat:@"动态 %@",minstr([dic valueForKey:@"dynamicnums"])],[NSString stringWithFormat:@"直播 %@",minstr([dic valueForKey:@"livenums"])]];

        }else{
            array = @[YZMsg(@"资料"),[NSString stringWithFormat:@"视频 %@",minstr([dic valueForKey:@"videonums"])],[NSString stringWithFormat:@"动态 %@",minstr([dic valueForKey:@"dynamicnums"])],[NSString stringWithFormat:@"直播 %@",minstr([dic valueForKey:@"livenums"])],[NSString stringWithFormat:@"付费内容 %@",minstr([dic valueForKey:@"paidprogram_nums"])]];
        }
        itemButtonArray = [NSMutableArray array];
        for (int i = 0; i < array.count; i ++) {
            UIButton *btn = [UIButton buttonWithType:0];
            [btn addTarget:self action:@selector(itemBUttonCLick:) forControlEvents:UIControlEventTouchUpInside];
            [btn setTitle:array[i] forState:0];
            [btn setTitleColor:RGB_COLOR(@"#323232", 1) forState:UIControlStateSelected];
            [btn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
            btn.titleLabel.font = [UIFont systemFontOfSize:13];
            [itemView addSubview:btn];
            [btn mas_makeConstraints:^(MASConstraintMaker *make) {
    //            make.centerY.height.equalTo(itemView);
                make.bottom.equalTo(itemView);
                make.height.mas_equalTo(50);
                make.centerX.equalTo(backView).multipliedBy((i+1)*0.32);
                make.width.equalTo(backView).multipliedBy(0.2);
            }];
            if (i == 0) {
                btn.selected = YES;
            }else if (i == 1) {
                videoButton = btn;
            }else if (i ==2 ) {
                dynamicButton = btn;
            }else if (i == 3) {
                liveButton = btn;
            }else if (i == 4){
                payButton = btn;
            }
            [itemButtonArray addObject:btn];
        }
        switchLine = [[UIView alloc]init];
        switchLine.backgroundColor = normalColors;
        switchLine.layer.cornerRadius = 1;
        switchLine.layer.masksToBounds = YES;
        [itemView addSubview:switchLine];
        [switchLine mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(10);
            make.height.mas_equalTo(2);
            make.centerX.equalTo(itemView).multipliedBy(0.35);
            make.bottom.equalTo(backView).offset(-13);
        }];
        [self.view layoutIfNeeded];

        _dataScrollV = [[UIScrollView alloc]init];
        _dataScrollV.backgroundColor = [UIColor whiteColor];
        [self.view addSubview:_dataScrollV];
        [_dataScrollV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(collectionHeader.mas_bottom);
            make.bottom.equalTo(self.view).offset([_userID isEqual:[Config getOwnID]] ? 0 : -(60+ShowDiff));
        }];
        NSArray *array2 = @[@"person_paihang_back",@"person_shouhu_back"];
        NSString *votes = [NSString stringWithFormat:@"%@%@",[common name_votes],YZMsg(@"贡献榜")];
        NSArray *array3 = @[votes,YZMsg(@"守护榜")];
        NSArray *perpleArr;
        UIImageView *rankImgView;
        for (int i = 0; i < 2; i ++) {
            UIImageView *imgv = [[UIImageView alloc]init];
            imgv.image = [UIImage imageNamed:array2[i]];
            imgv.userInteractionEnabled = YES;
            [_dataScrollV addSubview:imgv];
            [imgv mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(_dataScrollV);
                make.width.mas_equalTo((_window_width-40)/2);
                make.height.equalTo(imgv.mas_width).multipliedBy(0.5);
                make.centerX.equalTo(_dataScrollV).multipliedBy(0.5+i*1);
            }];
            UILabel *label = [[UILabel alloc]init];
            label.textColor = [UIColor whiteColor];
            label.font = [UIFont boldSystemFontOfSize:13];
            label.text = array3[i];
            [imgv addSubview:label];
            [label mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(imgv).offset(15);
                make.centerY.equalTo(imgv).multipliedBy(0.4625);
            }];
            if (i == 0) {
                perpleArr = [dic valueForKey:@"contribute"];
                rankImgView = imgv;
            }else{
                perpleArr = [dic valueForKey:@"guardlist"];
            }
            UIImageView *linshiImgV;
            for (int j = 0; j < 3; j++) {
                if (j == 0) {
                    linshiImgV  = [[UIImageView alloc]init];
                    [imgv addSubview:linshiImgV];
                }

                UIImageView *iconImgV = [[UIImageView alloc]init];
                iconImgV.layer.masksToBounds = YES;
                if (j < perpleArr.count) {
                    [iconImgV sd_setImageWithURL:[NSURL URLWithString:minstr([perpleArr[j] valueForKey:@"avatar"])]];
                }else{
                    iconImgV.image = [UIImage imageNamed:getImagename(@"person_kong")];
                }
                [imgv addSubview:iconImgV];
                [iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.width.height.equalTo(imgv.mas_height).multipliedBy(0.375);
                    make.centerY.equalTo(imgv).multipliedBy(1.475);
                    make.left.equalTo(linshiImgV.mas_right).offset(10);
                }];
                linshiImgV = iconImgV;
                UIImageView *rankImgV = [[UIImageView alloc]init];
                rankImgV.image = [UIImage imageNamed:[NSString stringWithFormat:@"userlist_no%d",j+1]];
                [imgv addSubview:rankImgV];
                [rankImgV mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.width.height.equalTo(iconImgV).multipliedBy(1.4);
                    make.centerY.equalTo(imgv).multipliedBy(1.35).offset(1);
                    make.centerX.equalTo(iconImgV);
                }];
                [iconImgV layoutIfNeeded];
                iconImgV.layer.cornerRadius = iconImgV.width/2;
            }
            UIButton *rankBtn = [UIButton buttonWithType:0];
            [rankBtn addTarget:self action:@selector(rankBtnClick:) forControlEvents:UIControlEventTouchUpInside];
            rankBtn.tag = 1000+i;
            [_dataScrollV addSubview:rankBtn];
            [rankBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.left.width.height.equalTo(imgv);
            }];
        }
        NSArray *array4 = @[YZMsg(@"主播印象"),YZMsg(@"个性签名"),YZMsg(@"个人信息")];
        MASViewAttribute *bottomSpeace = rankImgView.mas_bottom;
        for (int i = 0; i < array4.count; i++) {
            UIView *lineV = [[UIView alloc]init];
            lineV.backgroundColor = normalColors;
            [_dataScrollV addSubview:lineV];
            [lineV mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(rankImgView);
                make.top.equalTo(bottomSpeace).offset(15);
                make.width.mas_equalTo(2);
                make.height.mas_equalTo(13);
            }];
            UILabel *label = [[UILabel alloc]init];
            label.text = array4[i];
            label.font = [UIFont systemFontOfSize:13];
            [_dataScrollV addSubview:label];
            [label mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(lineV);
                make.left.equalTo(lineV.mas_right).offset(8);
            }];
            if (i == 0) {
                UIView *view = [[UIView alloc]init];
                [_dataScrollV addSubview:view];
                [view mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(lineV);
                    make.top.equalTo(lineV.mas_bottom).offset(5);
                    make.height.mas_equalTo(26);
                    make.width.equalTo(_dataScrollV).offset(-15);
                }];
                UIButton *addImpBtn = [UIButton buttonWithType:0];
                [addImpBtn setTitle:YZMsg(@"添加印象") forState:0];
                [addImpBtn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
                addImpBtn.titleLabel.font = [UIFont systemFontOfSize:11];
                [addImpBtn addTarget:self action:@selector(addImpBtnClick) forControlEvents:UIControlEventTouchUpInside];
                addImpBtn.layer.cornerRadius = 13;
                addImpBtn.layer.masksToBounds = YES;
                [addImpBtn setBackgroundColor:RGB_COLOR(@"#f0f0f0", 1)];
                [view addSubview:addImpBtn];
                if ([_userID isEqual:[Config getOwnID]]) {
                    addImpBtn.hidden = YES;
                }
                NSArray *labelArray = [dic valueForKey:@"label"];

                if (labelArray.count>0) {
                    CGFloat jianju = 0;
                    for (int j = 0; j < labelArray.count; j ++) {
                        UILabel *label = [[UILabel alloc]init];
                        label.font = [UIFont systemFontOfSize:11];
                        label.textAlignment = NSTextAlignmentCenter;
                        label.text = minstr([labelArray[j] valueForKey:@"name"]);
                        UIColor *color = RGB_COLOR(minstr([labelArray[j] valueForKey:@"colour"]), 1);
                        UIColor *color2 = RGB_COLOR(minstr([labelArray[j] valueForKey:@"colour2"]), 1);

                        label.textColor = [UIColor whiteColor];
                        [view addSubview:label];
                        CGFloat yinxiangWidth = [[YBToolClass sharedInstance] widthOfString:label.text andFont:[UIFont systemFontOfSize:11] andHeight:26];
                        [label mas_makeConstraints:^(MASConstraintMaker *make) {
                            make.left.equalTo(view).offset(jianju);
                            make.width.mas_equalTo(yinxiangWidth + 20);
                            make.height.mas_equalTo(26);
                            make.top.equalTo(view).offset(5);
                        }];
                        jianju = yinxiangWidth+ 20 + 10 + jianju;
                        if (j == labelArray.count - 1) {
                            [addImpBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                                make.left.equalTo(label.mas_right).offset(10);
                                make.height.mas_equalTo(30);
                                make.width.mas_equalTo(60+10);
                                make.top.equalTo(label);
                            }];
                        }
                        [label layoutIfNeeded];
                        UIImageView *gImgv = [[UIImageView alloc]init];
                        gImgv.image = [UIImage gradientColorImageFromColors:@[color,color2] gradientType:GradientTypeLeftToRight imgSize:label.size];
                        gImgv.layer.cornerRadius = 13;
                        gImgv.layer.masksToBounds = YES;
                        [view addSubview:gImgv];
                        [view sendSubviewToBack:gImgv];
                        [gImgv mas_makeConstraints:^(MASConstraintMaker *make) {
                            make.left.right.top.bottom.equalTo(label);
                        }];
                    }
                }else{
                    if ([_userID isEqual:[Config getOwnID]]) {
                        UILabel *nolabel = [[UILabel alloc]init];
                        nolabel.textColor = RGB_COLOR(@"#646464", 1);
                        nolabel.font = [UIFont systemFontOfSize:13];
                        nolabel.text = @"你还没有收到印象哦";
                        [view addSubview:nolabel];
                        [nolabel mas_makeConstraints:^(MASConstraintMaker *make) {
                            make.left.equalTo(lineV);
                            make.top.equalTo(view);
                            make.height.equalTo(view);
                        }];
                    }
                    [addImpBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.left.equalTo(view);
                        make.height.mas_equalTo(26);
                        make.width.mas_equalTo(75);
                        make.top.equalTo(view).offset(5);
                    }];
                }
                if ([_userID isEqual:[Config getOwnID]]) {
                    UIImageView *rightImgv = [[UIImageView alloc]init];
                    rightImgv.image = [UIImage imageNamed:@"profit_right"];
                    [view addSubview:rightImgv];
                    [rightImgv mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.centerY.equalTo(view);
                        make.right.equalTo(view).offset(-10);
                        make.width.height.mas_equalTo(13);
                    }];
                    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(impressGoSelf)];
                    [view addGestureRecognizer:tap];
                }
                impressview = view;
                bottomSpeace = view.mas_bottom;
            }else{
                UILabel *labelllll = [[UILabel alloc]init];
                labelllll.textColor = RGB_COLOR(@"#646464", 1);
                labelllll.font = [UIFont systemFontOfSize:13];
                labelllll.numberOfLines = 0;
                [_dataScrollV addSubview:labelllll];
                [labelllll mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(lineV);
                    make.top.equalTo(lineV.mas_bottom).offset(10);
                    make.height.mas_greaterThanOrEqualTo(34);
                    make.width.equalTo(_dataScrollV);
                }];
                if (i == 1) {
                    labelllll.text = minstr([dic valueForKey:@"signature"]);
                }else{
                    labelllll.text = [NSString stringWithFormat:@"生日：%@\n\n所在地：%@",minstr([dic valueForKey:@"birthday"]),minstr([dic valueForKey:@"location"])];
                }
                bottomSpeace = labelllll.mas_bottom;
            }
        }
        [_dataScrollV layoutIfNeeded];
        _dataScrollV.contentSize = CGSizeMake(0, _dataScrollV.height+200);
        if (![_userID isEqual:[Config getOwnID]]) {
            [self creatBottomView];
        }
        }else{
            [impressview removeAllSubViews];

                UIButton *addImpBtn = [UIButton buttonWithType:0];
                [addImpBtn setTitle:YZMsg(@"添加印象") forState:0];
                [addImpBtn setTitleColor:RGB_COLOR(@"#969696", 1) forState:0];
                addImpBtn.titleLabel.font = [UIFont systemFontOfSize:11];
                [addImpBtn addTarget:self action:@selector(addImpBtnClick) forControlEvents:UIControlEventTouchUpInside];
                addImpBtn.layer.cornerRadius = 13;
                addImpBtn.layer.masksToBounds = YES;
                [addImpBtn setBackgroundColor:RGB_COLOR(@"#f0f0f0", 1)];
                [impressview addSubview:addImpBtn];
                if ([_userID isEqual:[Config getOwnID]]) {
                    addImpBtn.hidden = YES;
                }
                NSArray *labelArray = [dic valueForKey:@"label"];

                if (labelArray.count>0) {
                    CGFloat jianju = 0;
                    for (int j = 0; j < labelArray.count; j ++) {
                        UILabel *label = [[UILabel alloc]init];
                        label.font = [UIFont systemFontOfSize:11];
                        label.textAlignment = NSTextAlignmentCenter;
                        label.text = minstr([labelArray[j] valueForKey:@"name"]);
                        UIColor *color = RGB_COLOR(minstr([labelArray[j] valueForKey:@"colour"]), 1);
                        UIColor *color2 = RGB_COLOR(minstr([labelArray[j] valueForKey:@"colour2"]), 1);

                        label.textColor = [UIColor whiteColor];
    //                    label.backgroundColor = color;
                        [impressview addSubview:label];
                        CGFloat yinxiangWidth = [[YBToolClass sharedInstance] widthOfString:label.text andFont:[UIFont systemFontOfSize:11] andHeight:26];
                        [label mas_makeConstraints:^(MASConstraintMaker *make) {
                            make.left.equalTo(impressview).offset(jianju);
                            make.width.mas_equalTo(yinxiangWidth + 20);
                            make.height.mas_equalTo(26);
                            make.top.equalTo(impressview).offset(5);
                        }];
                        jianju = yinxiangWidth+ 20 + 10 + jianju;
                        if (j == labelArray.count - 1) {
                            [addImpBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                                make.left.equalTo(label.mas_right).offset(10);
                                make.height.mas_equalTo(30);
                                make.width.mas_equalTo(60+10);
                                make.top.equalTo(label);
                            }];
                        }
                        [label layoutIfNeeded];
                        UIImageView *gImgv = [[UIImageView alloc]init];
                        gImgv.image = [UIImage gradientColorImageFromColors:@[color,color2] gradientType:GradientTypeLeftToRight imgSize:label.size];
                        gImgv.layer.cornerRadius = 13;
                        gImgv.layer.masksToBounds = YES;
                        [impressview addSubview:gImgv];
                        [impressview sendSubviewToBack:gImgv];
                        [gImgv mas_makeConstraints:^(MASConstraintMaker *make) {
                            make.left.right.top.bottom.equalTo(label);
                        }];
                    }
                }else{
                    if ([_userID isEqual:[Config getOwnID]]) {
                        UILabel *nolabel = [[UILabel alloc]init];
                        nolabel.textColor = RGB_COLOR(@"#646464", 1);
                        nolabel.font = [UIFont systemFontOfSize:13];
                        nolabel.text = @"你还没有收到印象哦";
                        [impressview addSubview:nolabel];
                        [nolabel mas_makeConstraints:^(MASConstraintMaker *make) {
                            make.left.equalTo(self.view).offset(15);
                            make.top.equalTo(impressview);
                            make.height.equalTo(impressview);
                        }];
                    }
                    [addImpBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.left.equalTo(impressview);
                        make.height.mas_equalTo(26);
                        make.width.mas_equalTo(75);
                        make.top.equalTo(impressview).offset(5);
                    }];
                }
                if ([_userID isEqual:[Config getOwnID]]) {
                    UIImageView *rightImgv = [[UIImageView alloc]init];
                    rightImgv.image = [UIImage imageNamed:@"profit_right"];
                    [impressview addSubview:rightImgv];
                    [rightImgv mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.centerY.equalTo(impressview);
                        make.right.equalTo(impressview).offset(-10);
                        make.width.height.mas_equalTo(13);
                    }];
                    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(impressGoSelf)];
                    [impressview addGestureRecognizer:tap];
                }
        }
}
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    if (isVideo) {
        return videoArray.count;
    }
    return liveArray.count;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    if (isVideo) {
        mineVideoCell *cell = (mineVideoCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"mineVideoCELL" forIndexPath:indexPath];
        cell.model = [[NearbyVideoModel alloc]initWithDic:videoArray[indexPath.row]];
        return cell;

    }else{
        personLiveCell *cell = (personLiveCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"personLiveCELL" forIndexPath:indexPath];
        cell.model = [[LiveNodeModel alloc]initWithDic:liveArray[indexPath.item]];
        return cell;
    }
    
}
- (void) collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    if (isVideo) {
//        mineVideoCell *cell = (mineVideoCell *)[collectionView cellForItemAtIndexPath:indexPath];
//        LookVideo *video = [[LookVideo alloc]init];
//        if ([_userID isEqual:[Config getOwnID]]) {
//            video.fromWhere = @"myVideoV";
//        }else{
//            video.fromWhere = @"otherVideoV";
//        }
//        video.curentIndex = indexPath.row;
//        video.videoList = videoArray;
//        video.pages = videoPage;
//        video.firstPlaceImage = cell.thumbImgView.image;
//        video.requestUrl = [NSString stringWithFormat:@"%@/?service=video.gethomevideo&uid=%@&touid=%@",purl,[Config getOwnID],[Config getOwnID]];
//        video.block = ^(NSMutableArray *array, NSInteger page,NSInteger index) {
//            videoPage = (int)page;
//            videoArray = array;
//            [personCollection reloadData];
//            [personCollection scrollToItemAtIndexPath:[NSIndexPath indexPathForItem:index inSection:0] atScrollPosition:UICollectionViewScrollPositionBottom animated:NO];
//        };
//        video.hidesBottomBarWhenPushed = YES;
//        [[MXBADelegate sharedAppDelegate] pushViewController:video animated:YES];

        
        YBLookVideoVC *ybLook = [[YBLookVideoVC alloc]init];
        if ([_userID isEqual:[Config getOwnID]]) {
            ybLook.fromWhere = @"myVideoV";
        }else{
            ybLook.fromWhere = @"otherVideoV";
        }

        ybLook.firstPush = YES;
        ybLook.pushPlayIndex = indexPath.row;
        ybLook.sourceBaseUrl = [NSString stringWithFormat:@"%@/?service=video.gethomevideo&uid=%@&touid=%@",purl,[Config getOwnID],[Config getOwnID]];
        ybLook.videoList = videoArray;
        ybLook.pages =videoPage;
        ybLook.hidesBottomBarWhenPushed = YES;
        [[MXBADelegate sharedAppDelegate] pushViewController:ybLook animated:YES];

        
    }else{
        NSDictionary *subdics = liveArray[indexPath.item];
        [MBProgressHUD showMessage:@""];

        [YBToolClass postNetworkWithUrl:@"User.getAliCdnRecord" andParameter:@{@"id":minstr([subdics valueForKey:@"id"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            [MBProgressHUD hideHUD];

            if (code == 0) {
                NSDictionary *userDic = [NSDictionary dictionaryWithObjectsAndKeys:minstr([infoDic valueForKey:@"user_nickname"]),@"name",minstr([infoDic valueForKey:@"avatar"]),@"icon",minstr([infoDic valueForKey:@"id"]),@"id",minstr([infoDic valueForKey:@"level_anchor"]),@"level", nil];
                hietoryPlay *history = [[hietoryPlay alloc]init];
                history.url = [[info firstObject] valueForKey:@"url"];
                history.selectDic = userDic;
                history.shareDic = infoDic;
    //            [self presentViewController:history animated:YES completion:nil];
                [[MXBADelegate sharedAppDelegate]pushViewController:history animated:YES];

            }else{
                [MBProgressHUD showError:msg];
            }
        } fail:^{
            [MBProgressHUD hideHUD];
        }];
    }
}
-(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    if (isVideo) {
        return CGSizeMake((_window_width-2)/3, (_window_width-2)/3/25*33);
    }else{
        return CGSizeMake(_window_width, 50);

    }
}

#pragma mark ================ collectionview头视图 ===============


- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath{
    UICollectionReusableView *header = [collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"hotHeaderV" forIndexPath:indexPath];
    header.backgroundColor = [UIColor whiteColor];
    [header addSubview:collectionHeader];
    return header;

}
//添加印象
- (void)addImpBtnClick{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    YBWeakSelf;
    impressVC *vc = [[impressVC alloc]init];
    vc.isAdd = YES;
    vc.touid = _userID;
    vc.refreshEvent = ^{
        [weakSelf requestData];
    };
    [[MXBADelegate sharedAppDelegate]pushViewController:vc animated:YES];

}
//贡献榜
- (void)rankBtnClick:(UIButton *)sender{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (sender.tag == 1000) {
        //贡献榜
        NSString *language = [PublicObj getCurrentLanguage];
        YBWebViewController *rank = [[YBWebViewController alloc]init];
        rank.urls = [NSString stringWithFormat:@"%@/appapi/contribute/index?uid=%@&language=%@",h5url,self.userID,language];
        [[MXBADelegate sharedAppDelegate]pushViewController:rank animated:YES];

    }else{
        //守护榜
        guardRankVC *rank = [[guardRankVC alloc]init];
        rank.liveUID = self.userID;
        [[MXBADelegate sharedAppDelegate]pushViewController:rank animated:YES];

    }
}
//切换直播视频
- (void)live_videoSwitch:(UIButton *)sender{
    [UIView animateWithDuration:0.3 animations:^{
        [switchLine mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(sender.mas_left).offset(10);
            make.width.mas_equalTo(10);
            make.height.mas_equalTo(2);
            make.bottom.equalTo(segmentView).offset(-2);
        }];
    }];

    if (sender.tag == 1213) {
        //视频
        isVideo = YES;
    }else{
        //直播
        isVideo = NO;
    }
    [personCollection reloadData];
}
#pragma mark ================ scrollviewDelegate ===============
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
//    if (scrollView.contentOffset.y > 64+statusbarHeight) {
//        naviView.backgroundColor = RGB_COLOR(@"#ffffff", 1);
//        [returnBtn setImage:[UIImage imageNamed:@"person_back_black"] forState:0];
//        [shareBtn setImage:[UIImage imageNamed:@"person_share_black"] forState:0];
//        titleLabel.text = minstr(self.chatname);
//    }else{
//        naviView.backgroundColor = RGB_COLOR(@"#ffffff", scrollView.contentOffset.y/(64.00000+statusbarHeight));
//        [returnBtn setImage:[UIImage imageNamed:@"person_back_white"] forState:0];
//        [shareBtn setImage:[UIImage imageNamed:@"person_share_white"] forState:0];
//        titleLabel.text = @"";
//    }
    
}
#pragma 粉丝 关注
- (void)fansOrFollowBtnClick:(UIButton *)sender{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (sender.tag == 181221) {
        fansViewController *fans = [[fansViewController alloc]init];
        fans.fensiUid = _userID;
        [[MXBADelegate sharedAppDelegate]pushViewController:fans animated:YES];

    }else{
        attrViewController *att = [[attrViewController alloc]init];
        att.guanzhuUID = _userID;
        [[MXBADelegate sharedAppDelegate]pushViewController:att animated:YES];

    }
}
#pragma mark ============顶部选项卡点击=============

- (void)itemBUttonCLick:(UIButton *)sender{
    if (sender.selected) {
        return;
    }
    switchLine.centerX = sender.centerX;
    for (UIButton *button in itemButtonArray) {
        if (button == sender) {
            button.selected = YES;
        }else{
            button.selected = NO;
        }
    }
    if (sender == videoButton) {
        //视频
        if (!personCollection) {
            [self creatCollectionView];
        }
        isVideo = YES;
        _dataScrollV.hidden = YES;
        personCollection.hidden = NO;
        if (_dtView) {
            _dtView.hidden = YES;
        }
        [personCollection reloadData];
        if (videoArray.count < 1) {
            if ([_userID isEqual:[Config getOwnID]]) {
                [self addnothingWithUpTitle:YZMsg(@"你还没有视频作品") andDownTitle:YZMsg(@"赶快去拍摄上传吧")];
                self.nothingView.hidden = NO;
            }else{
                [self addnothingWithUpTitle:YZMsg(@"TA还没有视频作品") andDownTitle:@""];
                self.nothingView.hidden = NO;

            }

        }else{
            self.nothingView.hidden = YES;
        }
        self.dtView.hidden = YES;
        self.payView.hidden = YES;
    }else if (sender == liveButton) {
        //直播
        if (!personCollection) {
            [self creatCollectionView];
        }
        isVideo = NO;
        _dataScrollV.hidden = YES;
        personCollection.hidden = NO;
        if (_dtView) {
            _dtView.hidden = YES;
        }
        [personCollection reloadData];
        if (liveArray.count < 1) {
            if ([_userID isEqual:[Config getOwnID]]) {
                [self addnothingWithUpTitle:YZMsg(@"你还没有开过直播") andDownTitle:YZMsg(@"赶快去开场直播体验下吧")];
                self.nothingView.hidden = NO;
            }else{
                [self addnothingWithUpTitle:YZMsg(@"TA最近没有开过直播") andDownTitle:@""];
                self.nothingView.hidden = NO;

            }
        }else{
            self.nothingView.hidden = YES;
        }
        self.dtView.hidden = YES;
        self.payView.hidden = YES;
    }else if (sender == dynamicButton){
        if (_dtView) {
            [_dtView removeFromSuperview];
            _dtView = nil;
        }
        [self.view addSubview:self.dtView];
        [self.dtView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(collectionHeader.mas_bottom);
            make.bottom.equalTo(self.view).offset([_userID isEqual:[Config getOwnID]] ? 0 : -(60+ShowDiff));
        }];

        self.nothingView.hidden = YES;

        _dataScrollV.hidden = YES;
        personCollection.hidden = YES;
    }else if (sender == payButton){
        if (_payView) {
            [_payView removeFromSuperview];
            _payView = nil;
        }
        [self.view addSubview:self.payView];
        [self.payView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(collectionHeader.mas_bottom);
            make.bottom.equalTo(self.view).offset([_userID isEqual:[Config getOwnID]] ? 0 : -(60+ShowDiff));
        }];
        self.nothingView.hidden = YES;

        _dataScrollV.hidden = YES;
        personCollection.hidden = YES;
        self.dtView.hidden = YES;
    } else{
        self.nothingView.hidden = YES;

        //资料
        personCollection.hidden = YES;
        if (_dtView) {
            _dtView.hidden = YES;
        }
        _dataScrollV.hidden = NO;
        self.dtView.hidden = YES;
        self.payView.hidden = YES;
    }
}
-(void)addnothingWithUpTitle:(NSString *)uptitle andDownTitle:(NSString *)downtitle{
    if (_nothingView) {
        [_nothingView removeFromSuperview];
        _nothingView = nil;
    }
        _nothingView = [[UIView alloc]initWithFrame:CGRectMake(0, 140, _window_width, 40)];
        _nothingView.hidden = YES;
        _nothingView.backgroundColor = [UIColor clearColor];
        [personCollection addSubview:_nothingView];
        UILabel *label1 = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, _window_width, 20)];
        label1.font = [UIFont systemFontOfSize:14];
        label1.text = uptitle;
        label1.textAlignment = NSTextAlignmentCenter;
        label1.textColor = RGB_COLOR(@"#333333", 1);
        [_nothingView addSubview:label1];
        UILabel *label2 = [[UILabel alloc]initWithFrame:CGRectMake(0, 20, _window_width, 20)];
        label2.font = [UIFont systemFontOfSize:13];
        label2.text = downtitle;
        label2.textAlignment = NSTextAlignmentCenter;
        label2.textColor = RGB_COLOR(@"#969696", 1);
        [_nothingView addSubview:label2];
}
//
- (userCenterDTview *)dtView{
    if (!_dtView) {
        _dtView = [[userCenterDTview alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height-ShowDiff-collectionHeader.height-60) andTouserID:_userID andRequestUrl:@"Dynamic.getHomeDynamic" andTopicId:@""];
    }
    return _dtView;
}
-(CenterPayView *)payView
{
    if (!_payView) {
        _payView = [[CenterPayView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height-ShowDiff-collectionHeader.height-60) andData:_paidprogram_list andUserid:_userID];
    }
    return _payView;
}
//创建collectionview
- (void)creatCollectionView{
    UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
    flow.scrollDirection = UICollectionViewScrollDirectionVertical;
    flow.minimumLineSpacing = 0;
    flow.minimumInteritemSpacing = 0;
    personCollection = [[UICollectionView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height-ShowDiff-60) collectionViewLayout:flow];
    personCollection.delegate   = self;
    personCollection.dataSource = self;
    personCollection.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:personCollection];
    [personCollection mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(collectionHeader.mas_bottom);
        make.bottom.equalTo(self.view).offset([_userID isEqual:[Config getOwnID]] ? 0 : -(60+ShowDiff));
    }];

    [personCollection registerNib:[UINib nibWithNibName:@"personLiveCell" bundle:nil] forCellWithReuseIdentifier:@"personLiveCELL"];
    [personCollection registerNib:[UINib nibWithNibName:@"mineVideoCell" bundle:nil] forCellWithReuseIdentifier:@"mineVideoCELL"];
    [personCollection registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"hotHeaderV"];

    if (@available(iOS 11.0, *)) {
        personCollection.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        // Fallback on earlier versions
        self.automaticallyAdjustsScrollViewInsets = NO;
    }

    personCollection.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
        if (isVideo) {
            videoPage++;
            [self pullVideoList];
        }else{
            livePage ++;
            [self pullInternet];
        }
    }];

}



#pragma mark ============进入直播间=============
- (void)domoviePlay{
    BOOL isShowLive = [[NSUserDefaults standardUserDefaults]boolForKey:@"isShowChatLive"];
    if (isShowLive) {
        [[NSNotificationCenter defaultCenter]postNotificationName:@"HIDELIVEVIEW" object:nil];
    }

    for (UIViewController *tempVc in self.navigationController.viewControllers) {
        if ([tempVc isKindOfClass:[moviePlay class]]) {
            temp = (moviePlay *)tempVc;
        }else if ([tempVc isKindOfClass:[UserRoomViewController class]]){
            chatTemp = (UserRoomViewController *)tempVc;
        }
        
    }
    if (temp) {
        NSLog(@"%@",temp.playDoc) ;
        if ([minstr([temp.playDoc valueForKey:@"uid"]) isEqual:self.userID]) {
            [self.navigationController popToViewController:temp animated:YES];
        }else{
            [self goNewMoviePlay];
        }
    }else if (chatTemp){
        if ([minstr([chatTemp.playDoc valueForKey:@"uid"]) isEqual:self.userID]) {
            [self.navigationController popToViewController:chatTemp animated:YES];
        }else{
            [self goNewMoviePlay];
        }

    }
    else{
        [self goNewMoviePlay];
    }

}
- (void)goNewMoviePlay{
    [YBToolClass postNetworkWithUrl:@"Live.getLiveInfo" andParameter:@{@"liveuid":_userID} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            selectedDic = [info firstObject];
            [self checklive:[selectedDic valueForKey:@"stream"] andliveuid:[selectedDic valueForKey:@"uid"]];
        }else{
            [MBProgressHUD showError:msg];
        }
        
    } fail:^{
        
    }];

}
-(void)checklive:(NSString *)stream andliveuid:(NSString *)liveuid{
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.checkLive"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.timeoutInterval = 5.0;
    request.HTTPMethod = @"post";
    NSString *language = [PublicObj getCurrentLanguage];
    NSString *param = [NSString stringWithFormat:@"uid=%@&token=%@&liveuid=%@&stream=%@&language=%@",[Config getOwnID],[Config getOwnToken],liveuid,stream,language];
    request.HTTPBody = [param dataUsingEncoding:NSUTF8StringEncoding];
    NSURLResponse *response;
    NSError *error;
    NSData *backData = [NSURLConnection sendSynchronousRequest:request returningResponse:&response error:&error];
    if (error) {
        [MBProgressHUD showError:@"无网络"];
    }
    else{
        
        
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:backData options:NSJSONReadingMutableContainers error:nil];
        NSNumber *number = [dic valueForKey:@"ret"];
        
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [dic valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                NSString *type = [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                
                type_val =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type_val"]];
                livetype =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                _sdkType = minstr([info valueForKey:@"live_sdk"]);
                NSString *live_type =minstr([info valueForKey:@"live_type"]);
                if ([live_type isEqual:@"1"]) {
                    [[YBSmallLiveWindow shareInstance]closeBtnClick];
                    UserRoomViewController *chatroom = [[UserRoomViewController alloc]init];
                    chatroom.playDoc = selectedDic;
                    chatroom.sdkType = _sdkType;
                    [[MXBADelegate sharedAppDelegate] pushViewController:chatroom animated:YES];

                }else{
                    if ([type isEqual:@"0"]) {
                        [self pushMovieVC];
                    }
                    else if ([type isEqual:@"1"]){
                        NSString *_MD5 = [NSString stringWithFormat:@"%@",[info valueForKey:@"type_msg"]];
                        //密码
                        md5AlertController = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"本房间为密码房间，请输入密码") preferredStyle:UIAlertControllerStyleAlert];
                        //添加一个取消按钮
                        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            [self.navigationController popViewControllerAnimated:YES];
                            [self dismissViewControllerAnimated:NO completion:nil];
                        }];
                        [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                        [md5AlertController addAction:cancelAction];

                        //在AlertView中添加一个输入框
                        [md5AlertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
                            textField.secureTextEntry = YES;
                        }];
                        
                        //添加一个确定按钮 并获取AlertView中的第一个输入框 将其文本赋值给BUTTON的title
                        UIAlertAction *sureAction =[UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            UITextField *alertTextField = md5AlertController.textFields.firstObject;
                            //                        [self checkMD5WithText:envirnmentNameTextField.text andMD5:_MD5];
                            //输出 检查是否正确无误
                            NSLog(@"你输入的文本%@",alertTextField.text);
                            if ([_MD5 isEqualToString:[self stringToMD5:alertTextField.text]]) {
                                [self pushMovieVC];
                            }else{
                                alertTextField.text = @"";
                                [MBProgressHUD showError:YZMsg(@"密码错误")];
                                [self presentViewController:md5AlertController animated:true completion:nil];
                                return ;
                            }
                            
                        }];
                        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                        [md5AlertController addAction:sureAction];

                        
                        //present出AlertView
                        dispatch_async(dispatch_get_main_queue(), ^{
                            [self presentViewController:md5AlertController animated:true completion:nil];
                        });
                    }
                    else if ([type isEqual:@"2"] || [type isEqual:@"3"]){
                        if ([[YBYoungManager shareInstance]isOpenYoung]) {
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"青少年模式下不支持该功能") preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"知道了") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [self.navigationController popViewControllerAnimated:YES];
                                [self dismissViewControllerAnimated:NO completion:nil];

                            }];
                            [cancleAction setValue:[UIColor grayColor] forKey:@"_titleTextColor"];
                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"去关闭") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [[YBYoungManager shareInstance]checkYoungStatus:YoungFrom_Center];

                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                            [alertContro addAction:sureAction];
                            [self presentViewController:alertContro animated:YES completion:nil];
                        }else{
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:minstr([info valueForKey:@"type_msg"]) preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [self.navigationController popViewControllerAnimated:YES];
                                [self dismissViewControllerAnimated:NO completion:nil];
                                
                            }];
                            [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];

                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                if ([[Config getOwnID] intValue] <= 0) {
                                    [[YBToolClass sharedInstance]waringLogin];
                                    return;
                                }

                                [self doCoast];
                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];

                            [alertContro addAction:sureAction];
                            dispatch_async(dispatch_get_main_queue(), ^{
                                [self presentViewController:alertContro animated:YES completion:nil];
                            });
                            

                        }

                    }

                }
                
            }
            else{
                NSString *msg = [NSString stringWithFormat:@"%@",[data valueForKey:@"msg"]];
                [MBProgressHUD showError:msg];
            }
        }
        
    }
    
}
-(void)pushMovieVC{
    if (temp) {
        NSDictionary *dic = @{
                              @"dic":selectedDic,
                              @"type_val":type_val,
                              @"livetype":livetype,
                              @"sdkType":_sdkType
                              };
        [[NSNotificationCenter defaultCenter] postNotificationName:@"changePlayRoom" object:nil userInfo:dic];
        [self.navigationController popToViewController:temp animated:YES];

    }else{
        [[YBSmallLiveWindow shareInstance]closeBtnClick];
        
        moviePlay *player = [[moviePlay alloc]init];
        player.scrollarray = nil;
        player.scrollindex = 0;
        player.playDoc = selectedDic;
        player.type_val = type_val;
        player.livetype = livetype;
        player.sdkType = _sdkType;
        [[MXBADelegate sharedAppDelegate] pushViewController:player animated:YES];
    }
}
- (NSString *)stringToMD5:(NSString *)str
{
    
    //1.首先将字符串转换成UTF-8编码, 因为MD5加密是基于C语言的,所以要先把字符串转化成C语言的字符串
    const char *fooData = [str UTF8String];
    
    //2.然后创建一个字符串数组,接收MD5的值
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    
    //3.计算MD5的值, 这是官方封装好的加密方法:把我们输入的字符串转换成16进制的32位数,然后存储到result中
    CC_MD5(fooData, (CC_LONG)strlen(fooData), result);
    /**
     第一个参数:要加密的字符串
     第二个参数: 获取要加密字符串的长度
     第三个参数: 接收结果的数组
     */
    
    //4.创建一个字符串保存加密结果
    NSMutableString *saveResult = [NSMutableString string];
    
    //5.从result 数组中获取加密结果并放到 saveResult中
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [saveResult appendFormat:@"%02x", result[i]];
    }
    /*
     x表示十六进制，%02X  意思是不足两位将用0补齐，如果多余两位则不影响
     NSLog("%02X", 0x888);  //888
     NSLog("%02X", 0x4); //04
     */
    return saveResult;
}
//执行扣费
-(void)doCoast{
    [YBToolClass postNetworkWithUrl:@"Live.roomCharge" andParameter:@{@"liveuid":minstr([selectedDic valueForKey:@"uid"]),@"stream":minstr([selectedDic valueForKey:@"stream"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            NSDictionary *infos = [info firstObject];
            LiveUser *user = [Config myProfile];
            user.coin = [NSString stringWithFormat:@"%@",[infos valueForKey:@"coin"]];
            user.level = [NSString stringWithFormat:@"%@",[infos valueForKey:@"level"]];
            [Config updateProfile:user];

            [self pushMovieVC];
            //计时扣费
            
        }else{
            [MBProgressHUD showError:msg];
        }
        
    } fail:^{
    }];
    
}
- (void)impressGoSelf{
    impressVC *vc = [[impressVC alloc]init];
    vc.isAdd = NO;
    vc.touid = @"0";
    [[MXBADelegate sharedAppDelegate] pushViewController:vc animated:YES];

}
#pragma mark ============店铺主页=============
- (void)doUserShoreHome{
    shopDetailVC *store = [[shopDetailVC alloc]init];
    store.toUserID = _userID;
    [[MXBADelegate sharedAppDelegate] pushViewController:store animated:YES];
}

#pragma  mark  --更新背景图--
-(void)clickTake{
    
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.allowsEditing = YES;
    imagePickerController.delegate = self;
    imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
    imagePickerController.allowsEditing = YES;
    imagePickerController.showsCameraControls = YES;
    imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    //imagePickerController.mediaTypes = @[(NSString *)kUTTypeImage];
    imagePickerController.modalPresentationStyle = 0;
    [[[MXBADelegate sharedAppDelegate]topViewController] presentViewController:imagePickerController animated:YES completion:nil];

}
-(void)clickSel {
    TZImagePickerController *imagePC = [[TZImagePickerController alloc]initWithMaxImagesCount:1 delegate:self];
    imagePC.preferredLanguage = [lagType isEqual:ZH_CN] ? @"zh-Hans":@"en";
    imagePC.modalPresentationStyle = 0;
    imagePC.showSelectBtn = YES;
    imagePC.allowCrop = NO;
    imagePC.allowPickingOriginalPhoto = NO;
    imagePC.oKButtonTitleColorNormal = normalColors;
    imagePC.allowTakePicture = YES;
    imagePC.allowTakeVideo = NO;
    imagePC.allowPickingVideo = NO;
    imagePC.allowPickingMultipleVideo = NO;
    [[[MXBADelegate sharedAppDelegate] topViewController]presentViewController:imagePC animated:YES completion:nil];
}

- (void)imagePickerController:(TZImagePickerController *)picker didFinishPickingPhotos:(NSArray<UIImage *> *)photos sourceAssets:(NSArray *)assets isSelectOriginalPhoto:(BOOL)isSelectOriginalPhoto{
    NSLog(@"------多选择图片--：%@",photos);
    UIImage *img = photos[0];
    headBgimg = img;
    [self updateBgImg];
}
-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    if (@available(iOS 11, *)) {
        UIScrollView.appearance.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"]) {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerEditedImage"];
        headBgimg = image;
        [self updateBgImg];
        [picker dismissViewControllerAnimated:YES completion:^{
            [UIApplication sharedApplication].statusBarHidden=NO;
        }];
        
    }
}
-(void)updateBgImg {
    [MBProgressHUD showMessage:@""];
    YBWeakSelf;
    [[YBStorageManage shareManage]getCOSInfo:^(int code) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (code == 0) {
                [weakSelf uploadHeadBgImg];
            }
        });
    }];
}
-(void)uploadHeadBgImg{
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);

    if (headBgimg) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"_centerHeaderBg.png"];
            [[YBStorageManage shareManage]yb_storageImg:headBgimg andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                //图片成功
                headBgStr = key;
                dispatch_semaphore_signal(semaphore);
            }];

            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self setBgService];
        });
        NSLog(@"任务完成执行");
    });

}
-(void)setBgService{
    //YBWeakSelf;
    NSDictionary *parDic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"img":headBgStr};
    NSString *url = [purl stringByAppendingFormat:@"?service=User.updateBgImg"];

    
    [YBNetworking postWithUrl:url Dic:parDic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [MBProgressHUD hideHUD];

        if ([code isEqual:@"0"]) {
            NSDictionary*infos = [[data valueForKey:@"info"] firstObject];
            NSString *newUrl = minstr([infos valueForKey:@"bg_img"]);
            NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:infoDic];
            [m_dic setObject:newUrl forKey:@"bg_img"];
            infoDic = [NSDictionary dictionaryWithDictionary:m_dic];
            [iconImgView sd_setImageWithURL:[NSURL URLWithString:newUrl]];

        }
        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];

        }];
}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch{
//     if ([NSStringFromClass([touch.view class]) isEqual:@"UIView"]) {
//        return NO;
//    }
    if ([touch.view isDescendantOfView:backView]) {
        return NO;
    }

    return YES;
}

@end
