<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="personLiveCell">
            <rect key="frame" x="0.0" y="0.0" width="374" height="68"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="374" height="68"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1IC-tc-8w8">
                        <rect key="frame" x="10" y="11.5" width="309" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jv1-2m-6Xo">
                        <rect key="frame" x="10" y="41" width="28.5" height="13.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <color key="textColor" red="0.40784313725490196" green="0.40784313725490196" blue="0.40784313725490196" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mKZ-2L-D9b">
                        <rect key="frame" x="326.5" y="11.5" width="37.5" height="18"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="37.5" id="wkH-nI-tRN"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="人看过" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XHY-U2-bNa">
                        <rect key="frame" x="330" y="41" width="34" height="13.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <color key="textColor" red="0.40784313729999999" green="0.40784313729999999" blue="0.40784313729999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="q9k-hI-sOD">
                        <rect key="frame" x="0.0" y="67" width="374" height="1"/>
                        <color key="backgroundColor" red="0.95686274509803915" green="0.96078431372549022" blue="0.96470588235294119" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="H55-Ni-CAs"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="mKZ-2L-D9b" firstAttribute="leading" secondItem="1IC-tc-8w8" secondAttribute="trailing" constant="7.5" id="0eh-PC-bi5"/>
                <constraint firstItem="1IC-tc-8w8" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="0pR-BZ-2lB"/>
                <constraint firstItem="mKZ-2L-D9b" firstAttribute="centerY" secondItem="1IC-tc-8w8" secondAttribute="centerY" id="2N9-QD-dhH"/>
                <constraint firstItem="jv1-2m-6Xo" firstAttribute="leading" secondItem="1IC-tc-8w8" secondAttribute="leading" id="4Fg-kf-ZYu"/>
                <constraint firstItem="XHY-U2-bNa" firstAttribute="trailing" secondItem="mKZ-2L-D9b" secondAttribute="trailing" id="6e0-G0-BDL"/>
                <constraint firstAttribute="bottom" secondItem="q9k-hI-sOD" secondAttribute="bottom" id="Jun-L3-bkc"/>
                <constraint firstItem="1IC-tc-8w8" firstAttribute="centerY" secondItem="gTV-IL-0wX" secondAttribute="centerY" multiplier="0.6" id="KZp-oC-gWb"/>
                <constraint firstAttribute="trailing" secondItem="mKZ-2L-D9b" secondAttribute="trailing" constant="10" id="P8c-qz-vyO"/>
                <constraint firstItem="jv1-2m-6Xo" firstAttribute="centerY" secondItem="gTV-IL-0wX" secondAttribute="centerY" multiplier="1.4" id="RhP-jy-MwF"/>
                <constraint firstItem="q9k-hI-sOD" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="eHh-Ul-7iO"/>
                <constraint firstItem="XHY-U2-bNa" firstAttribute="centerY" secondItem="jv1-2m-6Xo" secondAttribute="centerY" id="giB-mK-ES2"/>
                <constraint firstAttribute="trailing" secondItem="q9k-hI-sOD" secondAttribute="trailing" id="rJu-yK-8U8"/>
            </constraints>
            <size key="customSize" width="374" height="68"/>
            <connections>
                <outlet property="labNums" destination="mKZ-2L-D9b" id="2HG-6y-3ii"/>
                <outlet property="labStartTime" destination="jv1-2m-6Xo" id="tTV-YJ-ttD"/>
                <outlet property="labTitle" destination="1IC-tc-8w8" id="6Ti-Jo-c1J"/>
                <outlet property="lookCountLb" destination="XHY-U2-bNa" id="Fup-zh-Jyu"/>
            </connections>
            <point key="canvasLocation" x="193.59999999999999" y="128.63568215892056"/>
        </collectionViewCell>
    </objects>
</document>
