//
//  CenterPayView.m
//  YBLive
//
//  Created by ybRRR on 2022/6/7.
//  Copyright © 2022 cat. All rights reserved.
//

#import "CenterPayView.h"
#import "MyUpPaidCell.h"
#import "PayVideoDetailVC.h"
@interface CenterPayView ()<UITableViewDelegate,UITableViewDataSource>
{
    int pageIndex;
    NSString *touid;

}
@property (nonatomic, strong)UITableView *upTable;
@property (nonatomic, strong)NSMutableArray *listArr;
@end
@implementation CenterPayView

-(instancetype)initWithFrame:(CGRect)frame andData:(NSArray *)dataArr andUserid:(NSString *)userId
{
    self = [super initWithFrame:frame];
    if (self) {
        pageIndex = 1;
        touid = userId;
        _listArr = [NSMutableArray array];
        [_listArr addObjectsFromArray: dataArr];
        [self addSubview:self.upTable];
        if (_listArr.count < 1) {
            [PublicView showImgNoData:self.upTable name:@"shop_无数据" text:YZMsg(@"TA还没有上传过付费内容")];
        }else{
            [PublicView hiddenImgNoData:self.upTable];
        }
        [self.upTable reloadData];

    }
    return self;
}
-(void)getpayListData{
    NSDictionary *parDic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"touid":touid,@"p":@(pageIndex)};
    NSString *url = [purl stringByAppendingFormat:@"?service=Paidprogram.getHomePaidprogram"];
    [YBNetworking postWithUrl:url Dic:parDic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
            [_upTable.mj_header endRefreshing];
            [_upTable.mj_footer endRefreshing];
            if ([code isEqual:@"0"]) {
                NSArray *infos = [data valueForKey:@"info"];
                if (pageIndex == 1) {
                    [_listArr removeAllObjects];
                }
                [_listArr addObjectsFromArray:infos];
                [self.upTable reloadData];

            }
        } Fail:^(id fail) {

        }];

}
-(UITableView *)upTable{
    if (!_upTable) {
        _upTable = [[UITableView alloc]initWithFrame:CGRectMake(0,0, _window_width, self.height) style:UITableViewStylePlain];
        _upTable.delegate = self;
        _upTable.dataSource = self;
        _upTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _upTable.backgroundColor = UIColor.whiteColor;
        _upTable.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            pageIndex = 1;
            [self getpayListData];

        }];
        _upTable.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
            pageIndex ++;
            [self getpayListData];

        }];
    }
    return _upTable;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.listArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 100;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    MyUpPaidCell *cell = [MyUpPaidCell cellWithTab:tableView andIndexPath:indexPath];
    NSDictionary *dataDic = self.listArr[indexPath.row];
    [cell.imgView sd_setImageWithURL:[NSURL URLWithString:minstr([dataDic valueForKey:@"thumb_format"])]];
    cell.titleLb.text = minstr([dataDic valueForKey:@"title"]);
    cell.countLb.text = minstr([dataDic valueForKey:@"video_num"]);
    cell.priceLb.text = minstr([dataDic valueForKey:@"money"]);
    cell.statusLb.text = [NSString stringWithFormat:@"%@%@",minstr([dataDic valueForKey:@"sale_nums"]),YZMsg(@"人已购买")];

    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    PayVideoDetailVC *detail = [[PayVideoDetailVC alloc]init];
    NSDictionary *dataDic = self.listArr[indexPath.row];
    detail.object_id = minstr([dataDic valueForKey:@"id"]);
    [[MXBADelegate sharedAppDelegate]pushViewController:detail animated:YES];

}
@end
