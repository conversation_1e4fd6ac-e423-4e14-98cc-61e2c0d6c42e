//
//  otherUserMsgVC.h
//  YBLive
//
//  Created by <PERSON> on 2018/10/7.
//  Copyright © 2018年 cat. All rights reserved.
//

#import <UIKit/UIKit.h>


//typedef NS_ENUM(NSInteger,BottomType) {
//    BottomType_Chat,     //私信
//    BottomType_Black,   // 拉黑
//};

typedef void (^chatFollowBlock)();
typedef void (^videoFollowBlock)(NSString *attStr);

NS_ASSUME_NONNULL_BEGIN

@interface otherUserMsgVC : UIViewController
@property(nonatomic,strong)NSString *userID;
@property(nonatomic,strong)NSString *chatname;
@property(nonatomic,strong)NSString *icon;
@property (nonatomic,copy) chatFollowBlock block;
@property (nonatomic,copy) videoFollowBlock videoBlock;

//@property(nonatomic,assign)BottomType bottomBtnType;

@end

NS_ASSUME_NONNULL_END
