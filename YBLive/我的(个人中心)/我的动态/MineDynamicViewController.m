//
//  MineDynamicViewController.m
//  YBPlaying
//
//  Created by IOS1 on 2019/11/5.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import "MineDynamicViewController.h"
#import "userCenterDTview.h"
#import "PublishDynamicViewController.h"
#import "userCenterDTCell.h"
#import "VideoDynamicViewController.h"
#import "PicDynamicDeatileViewController.h"

@interface MineDynamicViewController ()<UITableViewDelegate,UITableViewDataSource,userDtDelegate>{
    NSMutableArray *infoArray;
    UIView *nothingView;

    int page;
}
@property (nonatomic,strong) UITableView *dtTableView;
@property (nonatomic,strong) userCenterDTview *dtView;

@end

@implementation MineDynamicViewController
-(void)rightBtnClick{
//    [MBProgressHUD showMessage:@""];
//    [YBToolClass postNetworkWithUrl:@"Skill.GetMySkill" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
//        [MBProgressHUD hideHUD];
//        if (code == 0) {
            PublishDynamicViewController *vc = [[PublishDynamicViewController alloc]init];
//            if ([info count] > 0) {
//                vc.isAuth = YES;
//            }else{
//                vc.isAuth = NO;
//            }
            [[MXBADelegate sharedAppDelegate] pushViewController:vc animated:YES];

//        }
//    } fail:^{
//            [MBProgressHUD hideHUD];
//    }];

}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = _titleStr;
    [self.rightBtn setImage:[UIImage imageNamed:@"addDynamic"] forState:0];
    self.rightBtn.hidden = NO;
    [self.view addSubview:self.dtView];

}

- (userCenterDTview *)dtView{
    if (!_dtView) {
        _dtView = [[userCenterDTview alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight-ShowDiff) andTouserID:[Config getOwnID] andRequestUrl:@"Dynamic.getHomeDynamic" andTopicId:@""];
    }
    return _dtView;
}


@end
