<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" selectionStyle="none" indentationWidth="10" reuseIdentifier="userCenterDTCELL" rowHeight="339" id="KGk-i7-Jjw" customClass="userCenterDTCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="339"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="339"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="thumb_1" translatesAutoresizingMaskIntoConstraints="NO" id="xcI-X6-1dN">
                        <rect key="frame" x="15" y="15" width="36" height="36"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="36" id="4sG-JM-Qf9"/>
                            <constraint firstAttribute="width" constant="36" id="Uc2-Y7-0gT"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="18"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9a5-zx-KDd">
                        <rect key="frame" x="61" y="16.5" width="36" height="17"/>
                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="W8e-rk-iiW">
                        <rect key="frame" x="61" y="37" width="38.5" height="14"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="sex_nv.png" translatesAutoresizingMaskIntoConstraints="NO" id="UO3-0J-pmt">
                                <rect key="frame" x="4" y="1" width="15" height="12"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="12" id="avG-Ux-kCV"/>
                                    <constraint firstAttribute="width" constant="15" id="qP4-S2-8wM"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="26" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ltP-Sd-Njp">
                                <rect key="frame" x="20" y="1.5" width="12.5" height="11"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="9"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="ltP-Sd-Njp" firstAttribute="leading" secondItem="UO3-0J-pmt" secondAttribute="trailing" constant="1" id="5H9-0H-gS1"/>
                            <constraint firstItem="UO3-0J-pmt" firstAttribute="centerY" secondItem="W8e-rk-iiW" secondAttribute="centerY" id="EX7-UH-Lm8"/>
                            <constraint firstItem="ltP-Sd-Njp" firstAttribute="centerY" secondItem="UO3-0J-pmt" secondAttribute="centerY" id="YHV-ko-pyP"/>
                            <constraint firstItem="UO3-0J-pmt" firstAttribute="leading" secondItem="W8e-rk-iiW" secondAttribute="leading" constant="4" id="jbc-nt-r2H"/>
                            <constraint firstAttribute="trailing" secondItem="ltP-Sd-Njp" secondAttribute="trailing" constant="6" id="sZz-vK-xjA"/>
                            <constraint firstAttribute="height" constant="14" id="w1y-7k-SoQ"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="7"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="gFn-Qi-uSn" customClass="YYLabel">
                        <rect key="frame" x="15" y="66" width="289" height="0.0"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="numberOfLines">
                                <integer key="value" value="0"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="fontSize_">
                                <real key="value" value="14"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="LD8-IO-DVb">
                        <rect key="frame" x="15" y="76" width="289" height="182"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MjA-Nh-39M">
                        <rect key="frame" x="15" y="268" width="158" height="20"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="userDT位置.png" translatesAutoresizingMaskIntoConstraints="NO" id="JKD-qe-sHv">
                                <rect key="frame" x="5" y="5" width="10" height="10"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="10" id="8PI-KX-3Y6"/>
                                    <constraint firstAttribute="width" constant="10" id="CtX-y3-Pns"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="万达嘉华酒店-商务中心" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fob-Zi-kVV">
                                <rect key="frame" x="20" y="3.5" width="118" height="13.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                <color key="textColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" red="0.94509803921568625" green="0.94901960784313721" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="fob-Zi-kVV" secondAttribute="trailing" constant="20" id="7Pe-pu-MmL"/>
                            <constraint firstItem="fob-Zi-kVV" firstAttribute="leading" secondItem="JKD-qe-sHv" secondAttribute="trailing" constant="5" id="CZ1-NG-VsI"/>
                            <constraint firstItem="JKD-qe-sHv" firstAttribute="centerY" secondItem="MjA-Nh-39M" secondAttribute="centerY" id="GYz-ZI-JkE"/>
                            <constraint firstAttribute="height" constant="20" id="MCC-O6-Tkk"/>
                            <constraint firstItem="JKD-qe-sHv" firstAttribute="leading" secondItem="MjA-Nh-39M" secondAttribute="leading" constant="5" id="cf8-p0-cNN"/>
                            <constraint firstItem="fob-Zi-kVV" firstAttribute="centerY" secondItem="JKD-qe-sHv" secondAttribute="centerY" id="mv9-qr-Zng"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="北京 | 1小时前" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8zm-5O-JEj">
                        <rect key="frame" x="193" y="271.5" width="71" height="13.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <color key="textColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qCY-nu-iUa">
                        <rect key="frame" x="0.0" y="293" width="320" height="1"/>
                        <color key="backgroundColor" red="0.94509803920000002" green="0.94901960780000005" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="0ok-Vv-d1s"/>
                        </constraints>
                    </view>
                    <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="k9U-RF-Gxz">
                        <rect key="frame" x="15" y="303" width="80" height="22"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="22" id="BkL-ld-QH6"/>
                            <constraint firstAttribute="width" constant="80" id="T8G-gM-NXh"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <state key="normal" title="评论" image="userDT-评论.png">
                            <color key="titleColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                        </state>
                    </button>
                    <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qvG-ba-Cy6">
                        <rect key="frame" x="120" y="303" width="80" height="22"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="22" id="WXD-NO-S91"/>
                            <constraint firstAttribute="width" constant="80" id="rFq-aL-pqT"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <state key="normal" title="点赞" image="home_zan_nor.png">
                            <color key="titleColor" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                        </state>
                        <state key="selected" image="home_dt_zan_sel.png"/>
                        <connections>
                            <action selector="zanBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="1MN-ju-egz"/>
                        </connections>
                    </button>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XXN-Bg-sXk">
                        <rect key="frame" x="0.0" y="335" width="320" height="5"/>
                        <color key="backgroundColor" red="0.94509803920000002" green="0.94901960780000005" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="5" id="LHp-Bb-36Y"/>
                        </constraints>
                    </view>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QE0-DN-q5Z">
                        <rect key="frame" x="224" y="303" width="80" height="22"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="80" id="NkD-XE-6k3"/>
                            <constraint firstAttribute="height" constant="22" id="c7K-tS-A37"/>
                        </constraints>
                        <state key="normal" image="dt举报删除.png"/>
                        <connections>
                            <action selector="moreBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="hIy-h4-Tat"/>
                        </connections>
                    </button>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VEo-gS-9mA">
                        <rect key="frame" x="253" y="16.5" width="44" height="18"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="44" id="R5r-dN-B8Y"/>
                            <constraint firstAttribute="height" constant="18" id="uAs-TQ-mXL"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <state key="normal" title="关注">
                            <color key="titleColor" systemColor="systemRedColor"/>
                        </state>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" systemColor="systemRedColor"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="1"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="9"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                        <connections>
                            <action selector="followBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="1tR-Cw-OEf"/>
                        </connections>
                    </button>
                </subviews>
                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="XXN-Bg-sXk" secondAttribute="bottom" constant="-1" id="0EC-hi-ObF"/>
                    <constraint firstItem="VEo-gS-9mA" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="16.5" id="1La-rd-NIe"/>
                    <constraint firstItem="qvG-ba-Cy6" firstAttribute="centerX" secondItem="H2p-sc-9uM" secondAttribute="centerX" id="5yy-dX-rFD"/>
                    <constraint firstItem="gFn-Qi-uSn" firstAttribute="top" secondItem="xcI-X6-1dN" secondAttribute="bottom" constant="15" id="EDC-qU-Ym3"/>
                    <constraint firstItem="9a5-zx-KDd" firstAttribute="leading" secondItem="xcI-X6-1dN" secondAttribute="trailing" constant="10" id="Hfc-4c-S08"/>
                    <constraint firstItem="W8e-rk-iiW" firstAttribute="leading" secondItem="9a5-zx-KDd" secondAttribute="leading" id="KlS-CK-zHL"/>
                    <constraint firstItem="8zm-5O-JEj" firstAttribute="leading" secondItem="MjA-Nh-39M" secondAttribute="trailing" constant="20" id="Lxi-zA-krQ"/>
                    <constraint firstAttribute="trailing" secondItem="QE0-DN-q5Z" secondAttribute="trailing" constant="16" id="P28-LZ-VnR"/>
                    <constraint firstItem="XXN-Bg-sXk" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="RYs-kh-Rs1"/>
                    <constraint firstItem="gFn-Qi-uSn" firstAttribute="leading" secondItem="xcI-X6-1dN" secondAttribute="leading" id="Wbi-9n-3u8"/>
                    <constraint firstAttribute="trailing" secondItem="gFn-Qi-uSn" secondAttribute="trailing" constant="16" id="X2f-SE-Z8w"/>
                    <constraint firstItem="QE0-DN-q5Z" firstAttribute="centerY" secondItem="k9U-RF-Gxz" secondAttribute="centerY" id="YE2-nd-1yI"/>
                    <constraint firstItem="qvG-ba-Cy6" firstAttribute="centerY" secondItem="k9U-RF-Gxz" secondAttribute="centerY" id="YIZ-3f-XtW"/>
                    <constraint firstItem="k9U-RF-Gxz" firstAttribute="leading" secondItem="MjA-Nh-39M" secondAttribute="leading" id="fMf-M7-efv"/>
                    <constraint firstItem="qCY-nu-iUa" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" id="fZA-zl-ibU"/>
                    <constraint firstItem="MjA-Nh-39M" firstAttribute="top" secondItem="LD8-IO-DVb" secondAttribute="bottom" constant="10" id="gmI-5j-cqx"/>
                    <constraint firstItem="k9U-RF-Gxz" firstAttribute="top" secondItem="qCY-nu-iUa" secondAttribute="bottom" constant="9" id="gx2-8b-QxH"/>
                    <constraint firstItem="xcI-X6-1dN" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="15" id="hAE-nR-f0s"/>
                    <constraint firstItem="LD8-IO-DVb" firstAttribute="leading" secondItem="gFn-Qi-uSn" secondAttribute="leading" id="hmc-YP-pSJ"/>
                    <constraint firstItem="xcI-X6-1dN" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="iEZ-Db-MXU"/>
                    <constraint firstItem="qCY-nu-iUa" firstAttribute="top" secondItem="MjA-Nh-39M" secondAttribute="bottom" constant="5" id="jTg-I2-7hC"/>
                    <constraint firstAttribute="trailing" secondItem="VEo-gS-9mA" secondAttribute="trailing" constant="23" id="kMP-rz-e6k"/>
                    <constraint firstItem="LD8-IO-DVb" firstAttribute="trailing" secondItem="H2p-sc-9uM" secondAttribute="trailing" constant="-16" id="kbI-Bz-uCS"/>
                    <constraint firstItem="qCY-nu-iUa" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="mwE-nf-xcP"/>
                    <constraint firstItem="8zm-5O-JEj" firstAttribute="centerY" secondItem="MjA-Nh-39M" secondAttribute="centerY" id="oWI-Ex-dyq"/>
                    <constraint firstItem="9a5-zx-KDd" firstAttribute="top" secondItem="xcI-X6-1dN" secondAttribute="top" constant="1.5" id="qrj-Ud-MV1"/>
                    <constraint firstItem="MjA-Nh-39M" firstAttribute="leading" secondItem="LD8-IO-DVb" secondAttribute="leading" id="qzN-lM-ZFn"/>
                    <constraint firstItem="W8e-rk-iiW" firstAttribute="bottom" secondItem="xcI-X6-1dN" secondAttribute="bottom" id="uhV-k2-ebk"/>
                    <constraint firstItem="LD8-IO-DVb" firstAttribute="top" secondItem="gFn-Qi-uSn" secondAttribute="bottom" constant="10" id="vdJ-G6-RYf"/>
                    <constraint firstItem="XXN-Bg-sXk" firstAttribute="top" secondItem="k9U-RF-Gxz" secondAttribute="bottom" constant="10" id="yCY-Bd-ONW"/>
                    <constraint firstItem="XXN-Bg-sXk" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" id="yTk-pN-z8N"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="addressL" destination="fob-Zi-kVV" id="E8D-dX-Mdc"/>
                <outlet property="ageL" destination="ltP-Sd-Njp" id="sOY-yG-ihG"/>
                <outlet property="commentNumsBtn" destination="k9U-RF-Gxz" id="Dw8-DQ-p1Z"/>
                <outlet property="contentLb" destination="gFn-Qi-uSn" id="wim-rX-qY4"/>
                <outlet property="contentShowView" destination="LD8-IO-DVb" id="loI-1a-pBe"/>
                <outlet property="followBtn" destination="VEo-gS-9mA" id="wLr-BO-T7R"/>
                <outlet property="likeNumsBtn" destination="qvG-ba-Cy6" id="cla-i3-Rzk"/>
                <outlet property="locationView" destination="MjA-Nh-39M" id="dYL-Nw-Gmp"/>
                <outlet property="moreBtn" destination="QE0-DN-q5Z" id="tZJ-Mm-rgJ"/>
                <outlet property="nameL" destination="9a5-zx-KDd" id="xM9-w9-chw"/>
                <outlet property="sexAndAgeView" destination="W8e-rk-iiW" id="t0A-7S-Zev"/>
                <outlet property="sexImgV" destination="UO3-0J-pmt" id="loO-Ko-mgX"/>
                <outlet property="timeL" destination="8zm-5O-JEj" id="esA-jW-oKe"/>
                <outlet property="userIconImgV" destination="xcI-X6-1dN" id="4e7-Wj-xvq"/>
            </connections>
            <point key="canvasLocation" x="107.24637681159422" y="77.34375"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="dt举报删除.png" width="22" height="4"/>
        <image name="home_dt_zan_sel.png" width="22" height="22"/>
        <image name="home_zan_nor.png" width="22" height="22"/>
        <image name="sex_nv.png" width="8" height="8"/>
        <image name="thumb_1" width="1920" height="1200"/>
        <image name="userDT-评论.png" width="22" height="22"/>
        <image name="userDT位置.png" width="20" height="24"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemRedColor">
            <color red="1" green="0.23137254901960785" blue="0.18823529411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
