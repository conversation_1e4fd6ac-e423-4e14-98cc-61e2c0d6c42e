//
//  userCenterDTModel.m
//  live1v1
//
//  Created by IOS1 on 2019/9/10.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import "userCenterDTModel.h"

@implementation userCenterDTModel
-(instancetype)initWithDic:(NSDictionary *)dic andIsHome:(BOOL)ishome{
    self = [super init];
    if (self) {
        _time = minstr([dic valueForKey:@"datetime"]);
        _age = minstr([dic valueForKey:@"age"]);
        _user_nickname = minstr([[dic valueForKey:@"userinfo"] valueForKey:@"user_nickname"]);
        _addr = minstr([dic valueForKey:@"city"]);
        _sex = minstr([[dic valueForKey:@"userinfo"]valueForKey:@"sex"]);
        _content = minstr([dic valueForKey:@"title"]);
        _type = minstr([dic valueForKey:@"type"]);
        _location = minstr([dic valueForKey:@"address"]);
        _userID = minstr([dic valueForKey:@"uid"]);
        _isattent = minstr([[dic valueForKey:@"userinfo"] valueForKey:@"isAttention"]);
        _islike = minstr([dic valueForKey:@"islike"]);
        _likes = minstr([dic valueForKey:@"likes"]);
        _dyID = minstr([dic valueForKey:@"id"]);
        _voice = minstr([dic valueForKey:@"voice"]);
        _voice_l = minstr([dic valueForKey:@"length"]);
        _video = minstr([dic valueForKey:@"href"]);
        _video_t = minstr([dic valueForKey:@"video_thumb"]);
        _thumbs = [dic valueForKey:@"thumbs"];
        _avatar = minstr([[dic valueForKey:@"userinfo"] valueForKey:@"avatar"]);
//        _skillid = minstr([dic valueForKey:@"skillid"]);
        _comments = minstr([dic valueForKey:@"comments"]);
        _topicStr = minstr([dic valueForKey:@"label_name"]);
        _topicID = minstr([dic valueForKey:@"labelid"]);
        _goodsInfoDic = [dic valueForKey:@"goodsinfo"];
        if ([_comments isEqual:@"0"]) {
            _comments = YZMsg(@"评论");
        }
        if ([_likes isEqual:@"0"]) {
            _likes = YZMsg(@"点赞");
        }
//        if ([_skillid isEqual:@"0"]) {
//            _skillName = @"";
//            _skillCoin = @"";
//            _skillThumb = @"";
//            _skillMethod = @"";
//        }else{
//            NSDictionary *skillinfo = [dic valueForKey:@"skillinfo"];
//            _skillName = minstr([skillinfo valueForKey:@"name"]);
//            _skillCoin = minstr([skillinfo valueForKey:@"coin"]);
//            _skillThumb = minstr([skillinfo valueForKey:@"thumb"]);
//            _skillMethod = minstr([skillinfo valueForKey:@"method"]);
//        }
        if (ishome) {
            [self getCollectHeight];
        }else{
            [self getRowHeight];
        }
    }
    return self;
}
- (void)getRowHeight{
    CGFloat hhhh = 152 + 15;
    CGFloat wordH = [[YBToolClass sharedInstance] heightOfString:_content andFont:SYS_Font(14) andWidth:_window_width-30];
    hhhh += wordH;
    //1  图片。 2视频。3音频。0文字

    if ([_type isEqual:@"0"]) {
        
    }else if ([_type isEqual:@"1"]){
        if (_thumbs.count == 1) {
            _picW = _window_width * 0.6;
            _picH = _picW;
            hhhh += _picH;
        }else if (_thumbs.count == 2){
            _picW = (_window_width-35)/2;
            _picH = _picW;
            hhhh += _picH;
        }else{
            _picW = (_window_width- 40)/3;
            _picH = _picW;
            if (_thumbs.count % 3 == 0) {
                hhhh += (_picH * (_thumbs.count / 3) + (_thumbs.count / 3 - 1)*5);
            }else{
                hhhh += (_picH * (_thumbs.count / 3 + 1) + (_thumbs.count / 3)*5);
            }
        }
    }else if ([_type isEqual:@"2"]){
        _picW = _window_width * 0.6;
        _picH = _picW;
        hhhh += _picH;
    }else if ([_type isEqual:@"4"]){
        
       hhhh += _window_width*0.6 + 10;
    }else{
        hhhh += 36;
    }
    
    _rowH = hhhh;

}
- (void)getCollectHeight{
    _rowH = (_window_width-26)/2 + 38;
    if (_content.length > 0) {
        CGFloat textH = [[YBToolClass sharedInstance] heightOfString:_content andFont:[UIFont fontWithName:@"PingFangTC-Medium" size:13] andWidth:(_window_width-26)/2-18];

        CGFloat lineHeight = [UIFont fontWithName:@"PingFangTC-Medium" size:13].lineHeight;

        NSInteger lineCount = textH / lineHeight;
        if (lineCount == 1) {
            _rowH += (textH + 8);
        }else{
            _rowH += (lineHeight*2 + 8);
        }
    }
//    if (![_skillid isEqual:@"0"]) {
//        _rowH += 30;
//    }
}
@end
