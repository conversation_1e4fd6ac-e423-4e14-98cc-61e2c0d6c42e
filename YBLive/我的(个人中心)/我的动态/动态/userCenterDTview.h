//
//  userCenterDTview.h
//  YBPlaying
//
//  Created by IOS1 on 2019/10/31.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol dtHideTabBarDelegate <NSObject>

-(void)hideTabBar:(BOOL)ishide;

@end
@interface userCenterDTview : UIView
@property (nonatomic,strong) UITableView *dtTableView;

@property(nonatomic, assign)id<dtHideTabBarDelegate>delegate;

- (instancetype)initWithFrame:(CGRect)frame andTouserID:(NSString *)uid andRequestUrl:(NSString *)urlStr andTopicId:(NSString *)topicid;

-(void)reloadDataWithService:(NSString *)urls;
@end

NS_ASSUME_NONNULL_END
