//
//  userCenterDTModel.h
//  live1v1
//
//  Created by IOS1 on 2019/9/10.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface userCenterDTModel : NSObject
@property (nonatomic,strong) NSString *user_nickname;
@property (nonatomic,strong) NSString *age;
@property (nonatomic,strong) NSString *sex;
@property (nonatomic,strong) NSString *addr;
@property (nonatomic,strong) NSString *content;
@property (nonatomic,strong) NSString *type;
@property (nonatomic,strong) NSString *location;
@property (nonatomic,strong) NSString *isattent;
@property (nonatomic,strong) NSString *islike;
@property (nonatomic,strong) NSString *likes;
@property (nonatomic,strong) NSString *userID;
@property (nonatomic,strong) NSString *dyID;
@property (nonatomic,strong) NSString *avatar;
@property (nonatomic,strong) NSString *voice;
@property (nonatomic,strong) NSString *voice_l;
@property (nonatomic,strong) NSString *video;
@property (nonatomic,strong) NSString *video_t;
@property (nonatomic,strong) NSArray *thumbs;
@property (nonatomic,strong) NSString *topicStr;
@property (nonatomic,strong) NSString *topicID;
@property (nonatomic,strong) NSDictionary *goodsInfoDic;

//@property (nonatomic,strong) NSString *skillid;
//@property (nonatomic,strong) NSString *skillMethod;
//@property (nonatomic,strong) NSString *skillName;
//@property (nonatomic,strong) NSString *skillThumb;
//@property (nonatomic,strong) NSString *skillCoin;
@property (nonatomic,strong) NSString *time;
@property (nonatomic,strong) NSString *comments;
@property (nonatomic,assign) CGFloat rowH;
@property (nonatomic,assign) CGFloat picW;
@property (nonatomic,assign) CGFloat picH;
-(instancetype)initWithDic:(NSDictionary *)dic andIsHome:(BOOL)ishome;
@end

NS_ASSUME_NONNULL_END
