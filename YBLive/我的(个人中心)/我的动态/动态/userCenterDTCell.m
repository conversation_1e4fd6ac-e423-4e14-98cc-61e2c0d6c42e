//
//  userCenterDTCell.m
//  YBPlaying
//
//  Created by IOS1 on 2019/10/31.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import "userCenterDTCell.h"
#import "audioShowView.h"
#import "YBImageView.h"
#import "ShowDetailVC.h"
#import "ReportViewController.h"
#import "PersonHomeVC.h"
#import "YBTopicListVC.h"
#import "ShareGoodView.h"
@implementation userCenterDTCell{
    audioShowView *audioPlayView;
    UIImageView *videoImg;
    ShareGoodView *goodsView;
}
- (void)awakeFromNib {
    [super awakeFromNib];
    _userIconImgV.userInteractionEnabled = YES;
    UITapGestureRecognizer *userTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(gouserHome)];
    [_userIconImgV addGestureRecognizer:userTap];
    _contentLb.font = SYS_Font(16);
    self.contentLb.numberOfLines = 0;
    self.contentLb.preferredMaxLayoutWidth = SCREEN_WIDTH-30;//设置最大宽度
    _commentNumsBtn.titleLabel.adjustsFontSizeToFitWidth = YES  ;
}
+(userCenterDTCell *)cellWithTableView:(UITableView *)tableView{
    
    userCenterDTCell *cell = [tableView dequeueReusableCellWithIdentifier:@"userCenterDTCell"];
    
    if (!cell) {
        cell = [[NSBundle mainBundle]loadNibNamed:@"userCenterDTCell" owner:self options:nil].lastObject;
    }
    return cell;
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
- (void)setModel:(userCenterDTModel *)model{
    _model = model;
    [_userIconImgV sd_setImageWithURL:[NSURL URLWithString:_model.avatar]];
    
     if ([_model.sex isEqual:@"1"]){
        self.sexImgV.image = [UIImage imageNamed:@"sex_man"];
    }else{
        self.sexImgV.image = [UIImage imageNamed:@"sex_woman"];
    }
    if([_model.userID isEqual:[Config getOwnID]]){
        self.followBtn.hidden = YES;
    }else{
        self.followBtn.hidden = NO;
    }
    if ([_model.isattent isEqual:@"0"]) {
            [self.followBtn setTitle:YZMsg(@"关注") forState:0];
            self.followBtn.layer.borderColor  = normalColors.CGColor;
            [self.followBtn setTitleColor:normalColors forState:0];
        }else{
            [self.followBtn setTitle:YZMsg(@"已关注") forState:0];
            self.followBtn.layer.borderColor  = [UIColor grayColor].CGColor;
            [self.followBtn setTitleColor:[UIColor grayColor] forState:0];

    }
    NSString *contentStr =[NSString stringWithFormat:@"%@ %@",_model.topicStr,_model.content] ;
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:contentStr];
    attrStr.yy_font = [UIFont systemFontOfSize:14];
    
    if (_model.topicStr.length > 0) {
        NSRange topicRange = [contentStr rangeOfString:_model.topicStr];
        [attrStr addAttribute:NSForegroundColorAttributeName value:[UIColor blackColor] range:attrStr.yy_rangeOfAll];
        [attrStr yy_setTextHighlightRange:topicRange color:normalColors backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
            NSLog(@"------点了话题-----");
            NSDictionary *topicDic = @{@"name":_model.topicStr,@"id":_model.topicID};
            YBTopicListVC *list = [[YBTopicListVC alloc]init];
            list.topicDic = topicDic;
            [[MXBADelegate sharedAppDelegate]pushViewController:list animated:YES];

        }];
    }
    _contentLb.attributedText = attrStr;

    _nameL.text = _model.user_nickname;
    if (audioPlayView) {
        [audioPlayView resetPlayer];
    }
    _likeNumsBtn.selected = [_model.islike intValue];
    [_likeNumsBtn setTitle:_model.likes forState:0];
    [_commentNumsBtn setTitle:_model.comments forState:0];
    _addressL.text = _model.location;

    if (_model.location.length > 0) {
        _locationView.hidden = NO;
        [_timeL mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_locationView.mas_right).offset(5);
        }];
    }else{
        _locationView.hidden = YES;
        [_timeL mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(15);
        }];

    }
    _timeL.text = [NSString stringWithFormat:@"%@%@",_model.addr.length > 0 ? [NSString stringWithFormat:@"%@|",_model.addr] : @"",_model.time];
    [_contentShowView removeAllSubViews];
    switch ([_model.type intValue]) {
        case 1:
            [self creatPicView];
            break;
        case 2:
            [self creatVideoView];
            break;
        case 3:
            [self creatAudioView];
            break;
        case 4:
            [self creatGoodsView];
            break;
        default:
            break;
    }
}
- (void)creatPicView{
    for (int i = 0; i < _model.thumbs.count; i ++) {
        UIImageView *imageView = [[UIImageView alloc]initWithFrame:CGRectMake((i%3)*(_model.picW+5), (i/3)*(_model.picW+5), _model.picW, _model.picH)];
        imageView.tag = i;
        imageView.userInteractionEnabled = YES;
        imageView.contentMode = UIViewContentModeScaleAspectFill;
        imageView.clipsToBounds = YES;
        imageView.layer.cornerRadius = 5;
        imageView.layer.masksToBounds = YES;
        [imageView sd_setImageWithURL:[NSURL URLWithString:_model.thumbs[i]]];
        [_contentShowView addSubview:imageView];
        UITapGestureRecognizer*tapGesture = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(onPressImage:)];
        [imageView addGestureRecognizer:tapGesture];
        
    }

}
- (void)onPressImage:(UITapGestureRecognizer *)sender{
    [[NSNotificationCenter defaultCenter]postNotificationName:VOICEPAUSE object:nil];
    [[NSNotificationCenter defaultCenter]postNotificationName:@"hideToolBarFeild" object:nil];
    UIImageView *imageview = (UIImageView *)sender.view;
    NSInteger index = imageview.tag;
    YBImageView *imgView = [[YBImageView alloc] initWithImageArray:_model.thumbs andIndex:index andMine:NO isDtCell:YES andBlock:^(NSArray * _Nonnull array) {
    }];
    [[UIApplication sharedApplication].keyWindow addSubview:imgView];
}

- (void)creatVideoView{
    videoImg = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _model.picW, _model.picH)];
    
    [videoImg sd_setImageWithURL:[NSURL URLWithString:minstr(_model.video_t)]];
    videoImg.userInteractionEnabled = YES;
    videoImg.backgroundColor = YBRandomColor;
    videoImg.layer.cornerRadius = 5;
    videoImg.layer.masksToBounds = YES;
    videoImg.contentMode = UIViewContentModeScaleAspectFill;
    videoImg.clipsToBounds = YES;
    [_contentShowView addSubview:videoImg];
    UIImageView *_pauseIV = [[UIImageView alloc]init];
    _pauseIV.image = [UIImage imageNamed:@"ask_play"];//publish_video_play
    [videoImg addSubview:_pauseIV];
    [_pauseIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@30);
        make.centerX.centerY.equalTo(videoImg);
    }];
    
    UIButton *viodeBtn = [UIButton buttonWithType:0];
    viodeBtn.frame = CGRectMake(0, 0, videoImg.width, videoImg.height);
    [viodeBtn addTarget:self action:@selector(videoClick) forControlEvents:UIControlEventTouchUpInside];
    [videoImg addSubview:viodeBtn];
}
-(void)gouserHome{

    PersonHomeVC  *person = [[PersonHomeVC alloc]init];
    person.userID = _model.userID;
    [[MXBADelegate sharedAppDelegate]pushViewController:person animated:YES];
}
#pragma mark--创建商品view
-(void)creatGoodsView{
    CommodityDetailModel *goodsModel = [[CommodityDetailModel alloc]init];
    goodsModel.thumbs_format =[_model.goodsInfoDic valueForKey:@"thumbs_format"];
    goodsModel.name = minstr([_model.goodsInfoDic valueForKey:@"name"]);
    goodsModel.sale_nums = minstr([_model.goodsInfoDic valueForKey:@"sale_nums"]);
    goodsModel.present_price = minstr([_model.goodsInfoDic valueForKey:@"present_price"]);
    goodsModel.type =minstr([_model.goodsInfoDic valueForKey:@"type"]);
    goodsModel.price = minstr([[[_model.goodsInfoDic valueForKey:@"specs_format"]firstObject] valueForKey:@"price"]);
    goodsModel.shop_uid = minstr(_model.userID);
    goodsModel.commission = minstr([_model.goodsInfoDic valueForKey:@"share_income"]);
    goodsModel.goodsid = minstr([_model.goodsInfoDic valueForKey:@"id"]);
    goodsView = [[ShareGoodView alloc]initWithFrame:CGRectMake(-10, 10, _window_width, _window_width*0.6 + 10)andGoodsData:goodsModel];
    goodsView.shareUserid =_model.userID;
    [_contentShowView addSubview:goodsView];

}
#pragma mark---看视频
- (void)videoClick{
    [[NSNotificationCenter defaultCenter]postNotificationName:VOICEPAUSE object:nil];

    ShowDetailVC *detail = [[ShowDetailVC alloc]init];
    detail.fromStr = @"trendlist";
    NSLog(@"=-=-=-=-=-=-=-:%@",_model.video);
    detail.videoPath =_model.video;
    detail.thumbImg = videoImg.image;
    detail.deleteEvent = ^(NSString *type) {
    };
    [[MXBADelegate sharedAppDelegate]pushViewController:detail animated:NO];

}
- (void)creatAudioView{
    audioPlayView = [[audioShowView alloc]initWithFrame:CGRectMake(0, 0, 180, 36) andAudioPath:[NSURL URLWithString:_model.voice] andAudioTimeCount:[_model.voice_l intValue]];
    NSLog(@"userCenterCell======:voice:%@",_model.voice);
    [_contentShowView addSubview:audioPlayView];

}
#pragma mark---更多
- (IBAction)moreBtnClick:(id)sender {
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    YBWeakSelf;
        UIAlertController *alertControl = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];
        UIAlertAction *action1;
        if ([_model.userID isEqual:[Config getOwnID]]) {
            action1= [UIAlertAction actionWithTitle:YZMsg(@"删除") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                [weakSelf.delegate deleteMyDt:_model.dyID];
            }];
        }else{
            action1 = [UIAlertAction actionWithTitle:YZMsg(@"举报") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                ReportViewController *jubao = [[ReportViewController alloc]init];
                jubao.dongtaiId =_model.dyID;
                [[MXBADelegate sharedAppDelegate]pushViewController:jubao animated:YES];

            }];
        }
    UIAlertAction *cancel = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {

        }];
        [action1 setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
        [cancel setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
        [alertControl addAction:action1];
        [alertControl addAction:cancel];
        [[[MXBADelegate sharedAppDelegate] topViewController] presentViewController:alertControl animated:YES completion:nil];

}
#pragma mark---赞
- (IBAction)zanBtnClick:(id)sender {
    
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }
    if ([_model.userID isEqual:[Config getOwnID]]) {
        [MBProgressHUD showError:YZMsg(@"不能给自己的动态点赞")];
        return;
    }
    _likeNumsBtn.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        _likeNumsBtn.userInteractionEnabled = YES;
    });
    NSMutableDictionary *dic = @{
        @"uid":[Config getOwnID],
        @"dynamicid":_model.dyID
    }.mutableCopy;
    NSString *sign = [YBToolClass dynamicSortString:dic];
    [dic setObject:[Config getOwnID] forKey:@"uid"];
    [dic setObject:[Config getOwnToken] forKey:@"token"];
    [dic setObject:_model.dyID forKey:@"dynamicid"];
    [dic setObject:sign forKey:@"sign"];


    [YBToolClass postNetworkWithUrl:@"Dynamic.addLike" andParameter:dic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            _model.islike = minstr([infoDic valueForKey:@"islike"]);
            _model.likes = minstr([infoDic valueForKey:@"likes"]);
            if ([_model.likes isEqual:@"0"]) {
                _model.likes = YZMsg(@"点赞");
            }
            [_likeNumsBtn setTitle:_model.likes forState:0];
            if ([_model.islike isEqual:@"1"]) {
                [self showZanAnimations];
            }else{
                _likeNumsBtn.selected = NO;
            }
            if ([self.fromStr isEqual:@"dtdetail"]) {
                [self.delegate isZanOrFollow:YES];
            }

        }else{
            [MBProgressHUD showError:msg];
        }
    } fail:^{
        
    }];
    
}
- (void)showZanAnimations{
    NSArray *array = @[[UIImage imageNamed:@"zan_animation1"],[UIImage imageNamed:@"zan_animation2"],[UIImage imageNamed:@"zan_animation3"],[UIImage imageNamed:@"zan_animation4"],[UIImage imageNamed:@"zan_animation5"]];
    _likeNumsBtn.imageView.animationImages = array;
    _likeNumsBtn.imageView.animationDuration = 0.8;
    _likeNumsBtn.imageView.animationRepeatCount = 1;
    [_likeNumsBtn.imageView startAnimating];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [_likeNumsBtn.imageView stopAnimating];
    });
    _likeNumsBtn.selected = YES;

}
#pragma mark---关注

- (IBAction)followBtnClick:(UIButton *)sender {
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    self.followBtn.userInteractionEnabled = NO;
    NSDictionary *subdic = @{
                             @"touid":_model.userID
                             };
    [YBToolClass postNetworkWithUrl:@"User.setAttent" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        self.followBtn.userInteractionEnabled = YES;

        if (code == 0) {
            [self layoutIfNeeded];
            NSDictionary *infos = [info firstObject];
            _model.isattent = minstr([infos valueForKey:@"isattent"]);

            if ([minstr([infos valueForKey:@"isattent"])isEqual:@"0"]) {
                [self.followBtn setTitle:YZMsg(@"关注") forState:0];
                self.followBtn.layer.borderColor  = normalColors.CGColor;
                [self.followBtn setTitleColor:normalColors forState:0];
            }else{
                [self.followBtn setTitle:YZMsg(@"已关注") forState:0];
                self.followBtn.layer.borderColor  = [UIColor grayColor].CGColor;
                [self.followBtn setTitleColor:[UIColor grayColor] forState:0];

            }
//            [[NSNotificationCenter defaultCenter]postNotificationName:@"reloadRecommendData" object:nil];
            if ([self.fromStr isEqual:@"dtdetail"]) {
                [self.delegate isZanOrFollow:YES];

            }
//            [self.frontviewDelegate guanzhuZhuBo];
        }
    } fail:^{
        self.followBtn.userInteractionEnabled = YES;

    }];

}

@end
