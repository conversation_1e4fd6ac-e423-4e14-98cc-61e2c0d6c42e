//
//  userCenterDTCell.h
//  YBPlaying
//
//  Created by IOS1 on 2019/10/31.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "userCenterDTModel.h"
#import <YYText/NSAttributedString+YYText.h>
#import <YYText/YYLabel.h>
@protocol userDtDelegate <NSObject>
-(void)deleteMyDt:(NSString *_Nullable)dtId;
-(void)isZanOrFollow:(BOOL)isBool;
@end
NS_ASSUME_NONNULL_BEGIN

@interface userCenterDTCell : UITableViewCell
@property (weak, nonatomic) IBOutlet UIImageView *userIconImgV;
@property (weak, nonatomic) IBOutlet UILabel *nameL;
@property (weak, nonatomic) IBOutlet UIView *sexAndAgeView;
@property (weak, nonatomic) IBOutlet UIImageView *sexImgV;
@property (weak, nonatomic) IBOutlet UILabel *ageL;
@property (weak, nonatomic) IBOutlet UILabel *addressL;
@property (strong, nonatomic) IBOutlet YYLabel *contentLb;
@property (weak, nonatomic) IBOutlet UILabel *timeL;
@property (weak, nonatomic) IBOutlet UIButton *commentNumsBtn;
@property (weak, nonatomic) IBOutlet UIButton *likeNumsBtn;
@property (weak, nonatomic) IBOutlet UIView *contentShowView;
@property (nonatomic,strong) userCenterDTModel *model;
@property (weak, nonatomic) IBOutlet UIView *locationView;
@property (weak, nonatomic) IBOutlet UIButton *moreBtn;
@property (weak, nonatomic) IBOutlet UIButton *followBtn;
@property (assign,nonatomic)id<userDtDelegate>delegate;
@property (nonatomic, strong)NSString *fromStr;

+(userCenterDTCell *)cellWithTableView:(UITableView *)tableView;
@end

NS_ASSUME_NONNULL_END
