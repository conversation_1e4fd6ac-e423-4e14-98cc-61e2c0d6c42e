//
//  userCenterDTview.m
//  YBPlaying
//
//  Created by IOS1 on 2019/10/31.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import "userCenterDTview.h"
#import "userCenterDTCell.h"
#import "VideoDynamicViewController.h"
#import "PicDynamicDeatileViewController.h"
#import "DynamicDetailVC.h"
@interface userCenterDTview ()<UITableViewDelegate,UITableViewDataSource,userDtDelegate>{
    NSMutableArray *infoArray;
    NSString *touserID;
    NSString *requstURL;
    NSString *topicIdStr;
    UIView *nothingView;

    int page;
    
    CGFloat oldOffset;
    
    UILabel *nothingLabel;
    UILabel *label2;
}

@end

@implementation userCenterDTview
-(UITableView *)dtTableView{
    if (!_dtTableView) {
        infoArray = [NSMutableArray array];
        page = 1;
        _dtTableView = [[UITableView alloc]initWithFrame:CGRectMake(0, 0, self.width, self.height)];
        _dtTableView.delegate = self;
        _dtTableView.dataSource = self;
        _dtTableView.separatorStyle = 0;
        _dtTableView.estimatedRowHeight = 0.0;
        _dtTableView.estimatedSectionHeaderHeight = 0.0;
        _dtTableView.estimatedSectionFooterHeight = 0.0;
        _dtTableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            page = 1;
            [self requestData];
        }];
        _dtTableView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
            page ++;
            [self requestData];
        }];
        nothingView = [[UIView alloc]initWithFrame:CGRectMake(0, 80, _window_width, 85)];
        nothingView.hidden = YES;
        [_dtTableView addSubview:nothingView];
        UIImageView *nothingImgV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"dynamic-nothing"]];
        [nothingView addSubview:nothingImgV];
        [nothingImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(nothingImgV.superview);
            make.top.equalTo(nothingImgV.superview);
            make.width.mas_equalTo(65);
            make.height.mas_equalTo(56);
        }];
        nothingLabel = [[UILabel alloc]init];
        nothingLabel.font = SYS_Font(14);
        nothingLabel.textColor = RGB_COLOR(@"#333333", 1);
        
        label2 = [[UILabel alloc]init];//WithFrame:CGRectMake(0, 20, _window_width, 20)];
        label2.font = [UIFont systemFontOfSize:13];
        label2.textAlignment = NSTextAlignmentCenter;
        label2.textColor = RGB_COLOR(@"#969696", 1);

        if ([touserID isEqual:[Config getOwnID]]) {
            nothingLabel.text = YZMsg(@"你还没有发布过动态");
            label2.text = YZMsg(@"快去分享一下你的心情吧");

        }else{
            nothingLabel.text = YZMsg(@"TA还没有发布过动态");
        }
        [nothingView addSubview:nothingLabel];
        [nothingView addSubview:label2];

        [nothingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(nothingLabel.superview);
            make.bottom.equalTo(nothingLabel.superview);
        }];
        [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(nothingLabel);
            make.top.equalTo(nothingLabel.mas_bottom).offset(5);
        }];

        _dtTableView.contentInset = UIEdgeInsetsMake(64+statusbarHeight, 0, 55, 0);

    }
    return _dtTableView;
}
- (instancetype)initWithFrame:(CGRect)frame andTouserID:(NSString *)uid andRequestUrl:(NSString *)urlStr andTopicId:(NSString *)topicid{
    self = [super initWithFrame:frame];
    touserID = uid;
    requstURL = urlStr;
    topicIdStr = topicid;
    if (self) {
        oldOffset = 0;

        [self addSubview:self.dtTableView];
        [self requestData];
    }
    return self;
}
-(void)reloadDataWithService:(NSString *)urls{
    requstURL = urls;
    [self requestData];
}
- (void)requestData{
    NSDictionary *paranmeter;
    //个人中心动态列表
    if ([requstURL isEqual:@"Dynamic.getHomeDynamic"]) {
        paranmeter = @{@"touid":touserID,
                       @"p":@(page)
        };
        _dtTableView.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
        if ([touserID isEqual:[Config getOwnID]]) {
            nothingLabel.text = YZMsg(@"暂无动态");
            label2.text = YZMsg(@"快去分享一下你的心情吧");
        }else{
            nothingLabel.text = YZMsg(@"TA还没有发布过动态");
        }

    }else if ([requstURL isEqual:@"Dynamic.getRecommendDynamics"]){
        //推荐列表
        paranmeter = @{@"touid":touserID,
                       @"p":@(page)
        };
    }else if ([requstURL isEqual:@"Dynamic.getAttentionDynamic"]){
        //关注列表
       paranmeter = @{@"uid":[Config getOwnID],
                      @"token":[Config getOwnToken],
                      @"p":@(page)
       };
        nothingLabel.text = YZMsg(@"暂无动态");
        label2.text = YZMsg(@"去推荐页看看吧");
        if ([[Config getOwnID] intValue] <= 0) {
            [_dtTableView.mj_header endRefreshing];
            [_dtTableView.mj_footer endRefreshing];

            [PublicView showWaitLogin:self.dtTableView centerY:0.2];
            return;
        }else{

            [PublicView hiddenWaitLogin:self.dtTableView];

        }

    }else if ([requstURL isEqual:@"Dynamic.getNewDynamic"]){
        //最新
        paranmeter = @{@"uid":[Config getOwnID],
                     @"lat":[cityDefault getMylat],
                     @"lng":[cityDefault getMylng],
                     @"p":@(page)
        };
        nothingLabel.text = YZMsg(@"暂无动态");
        label2.text = YZMsg(@"去推荐页看看吧");

    }else if ([requstURL isEqual:@"Dynamic.getLabelDynamic"]){
        
        paranmeter = @{@"uid":[Config getOwnID],
                     @"token":[Config getOwnToken],
                     @"labelid":topicIdStr,
                     @"p":@(page)
        };
        nothingLabel.text = YZMsg(@"暂无动态");
        label2.text = YZMsg(@"去推荐页看看吧");
        _dtTableView.contentInset = UIEdgeInsetsMake(0, 0, 55, 0);

    }
    
    [YBToolClass postNetworkWithUrl:requstURL andParameter:paranmeter success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [_dtTableView.mj_header endRefreshing];
        [_dtTableView.mj_footer endRefreshing];
        if (code == 0) {
            if (page == 1) {
                [infoArray removeAllObjects];
            }
            for (NSDictionary *dic in info) {
                userCenterDTModel *model = [[userCenterDTModel alloc]initWithDic:dic andIsHome:NO];
                [infoArray addObject:model];
            }
            if ([info count] == 0) {
                [_dtTableView.mj_footer endRefreshingWithNoMoreData];
            }
            [_dtTableView reloadData];
            if (infoArray.count == 0) {
                nothingView.hidden = NO;
                [_dtTableView.mj_footer setHidden:YES];
            }else{
                nothingView.hidden = YES;
                [_dtTableView.mj_footer setHidden:NO];
            }
        }
    } fail:^{
        [_dtTableView.mj_header endRefreshing];
        [_dtTableView.mj_footer endRefreshing];

    }];
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return infoArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
//    userCenterDTCell *cell = [tableView dequeueReusableCellWithIdentifier:@"userCenterDTCELL"];
//    if (!cell) {
//        cell = [[[NSBundle mainBundle] loadNibNamed:@"userCenterDTCell" owner:nil options:nil] lastObject];
//    }
    userCenterDTCell *cell = [userCenterDTCell cellWithTableView:tableView];

    cell.model = infoArray[indexPath.row];
    cell.delegate = self;
    return cell;

}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    userCenterDTModel *model = infoArray[indexPath.row];
    return model.rowH;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    userCenterDTModel *model = infoArray[indexPath.row];
    DynamicDetailVC *detail = [[DynamicDetailVC alloc]init];
    detail.dtmodel = model;
    detail.reloadEvent = ^(BOOL isdel) {
        if (isdel) {
            [infoArray removeObject:model];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            [_dtTableView reloadData];

        });

    };
    [[MXBADelegate sharedAppDelegate]pushViewController:detail animated:YES];

}
-(void)deleteMyDt:(NSString *)dtId
{
    UIAlertController *alertControl = [UIAlertController alertControllerWithTitle:nil message:@"确定删除此条动态？" preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *sure = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    UIAlertAction *cancel = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
        NSDictionary *parmateDic = @{@"uid":[Config getOwnID],
                                     @"token":[Config getOwnToken],
                                     @"dynamicid":dtId
        };
        NSString *url = [purl stringByAppendingFormat:@"?service=Dynamic.del"];
        
        [YBNetworking postWithUrl:url Dic:parmateDic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
            if ([code isEqual:@"0"]) {
                [MBProgressHUD showError:@"删除成功"];
                for (int i = 0; i < infoArray.count; i ++) {
                    userCenterDTModel *models = infoArray[i];
                    if ([models.dyID isEqual:dtId]) {
                        [infoArray removeObject:models];
                        NSIndexPath *index  = [NSIndexPath indexPathForRow:i inSection:0];
                        [_dtTableView deleteRowsAtIndexPaths:[NSMutableArray arrayWithObject:index] withRowAnimation:UITableViewRowAnimationAutomatic];
                    }
                }
            }
        } Fail:^(id fail) {
            
        }];

        
        
    }];
    [alertControl addAction:sure];
    [alertControl addAction:cancel];
    [[[MXBADelegate sharedAppDelegate]topViewController]presentViewController:alertControl animated:YES completion:nil];
}


#pragma mark 滑动监听
- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate
{
    if (decelerate == NO) {
        NSArray *aaa = [_dtTableView indexPathsForVisibleRows];
        NSLog(@"aaaaaaa=%@",aaa);
        if (aaa.count == 0) {
            return;
        }
        NSIndexPath *idexxx;
        if (aaa.count == 3) {
            idexxx = aaa[1];
        }else{
            int yyyyy = (int)scrollView.contentOffset.y - (130 + _window_width / 6);
            NSLog(@"YYYY=%d\nscrollView.contentOffset.y=%.2f",yyyyy,scrollView.contentOffset.y);
            if (scrollView.contentOffset.y < 130 + _window_width / 6+(_window_width+60)/2) {
                if (aaa.count >= 1) {
                    idexxx = aaa[0];
                }
            }else{
                if (yyyyy % (int)(_window_width+60) > (_window_width+60)/2) {
                    if (aaa.count >= 2) {
                        idexxx = aaa[1];
                    }
                }else{
                    if (aaa.count >= 1) {
                        idexxx = aaa[0];
                    }
                }
            }
        }
        for (userCenterDTCell *ce in [self cellsForTableView:_dtTableView]) {
            NSIndexPath *indexp = [_dtTableView indexPathForCell:ce];
            if (![indexp isEqual:idexxx]) {
                if ([ce.model.type isEqual:@"3"]) {
                    NSLog(@"---------====滑出屏幕停止======----------");
                    [[NSNotificationCenter defaultCenter]postNotificationName:VOICEPAUSE object:nil];
                }else{

                }

            }
            else{
                NSLog(@"1111---%@播放",indexp);
                if ([ce.model.type isEqual:@"3"]) {
                    NSLog(@"---------====播放======----------");
                }
            }

        }
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    NSArray *aaa = [_dtTableView indexPathsForVisibleRows];
    NSLog(@"aaaaaaa=%@",aaa);
    if (aaa.count == 0) {
        return;
    }
    NSIndexPath *idexxx;
    if (aaa.count == 3) {
        idexxx = aaa[1];
    }else{
        int yyyyy = (int)scrollView.contentOffset.y - (130 + _window_width / 6);
        NSLog(@"YYYY=%d\nscrollView.contentOffset.y=%.2f",yyyyy,scrollView.contentOffset.y);
        if (scrollView.contentOffset.y < 130 + _window_width / 6+(_window_width+60)/2) {
            if (aaa.count >= 1) {
                idexxx = aaa[0];
            }
        }else{
            if (yyyyy % (int)(_window_width+60) > (_window_width+60)/2) {
                if (aaa.count >= 2) {
                    idexxx = aaa[1];
                }
            }else{
                if (aaa.count >= 1) {
                    idexxx = aaa[0];
                }
            }
        }
    }
    for (userCenterDTCell *ce in [self cellsForTableView:_dtTableView]) {
        NSIndexPath *indexp = [_dtTableView indexPathForCell:ce];
        if (![indexp isEqual:idexxx]) {
            if ([ce.model.type isEqual:@"3"]) {
                NSLog(@"---------====滑出屏幕停止======----------");
                [[NSNotificationCenter defaultCenter]postNotificationName:VOICEPAUSE object:nil];
            }else{

            }

        }
        else{
            NSLog(@"1111---%@播放",indexp);
            if ([ce.model.type isEqual:@"3"]) {
                NSLog(@"---------====播放======----------");
            }
        }

    }

}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    oldOffset = scrollView.contentOffset.y;
}
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    if (scrollView.contentOffset.y > oldOffset) {
        if (scrollView.contentOffset.y > 0) {
//            [self.delegate hideTabBar:YES];
        }
    }else{
//        [self.delegate hideTabBar:NO];
    }
}

- (NSArray *)cellsForTableView:(UITableView *)tableView
{
    NSInteger sections = tableView.numberOfSections;
    NSMutableArray *cells = [[NSMutableArray alloc]init];
    for (int section = 0; section < sections; section++) {
        NSInteger rows =[tableView numberOfRowsInSection:section];
        for (int row = 0; row < rows; row++) {
                NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row inSection:section];
            if ([tableView cellForRowAtIndexPath:indexPath]) {
                [cells addObject:[tableView cellForRowAtIndexPath:indexPath]];
            }
          }
        }
    return cells;

}

@end
