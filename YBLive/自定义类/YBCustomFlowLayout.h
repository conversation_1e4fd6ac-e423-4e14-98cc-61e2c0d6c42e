//
//  YBCustomFlowLayout.h
//  YBLive
//
//  Created by ybRRR on 2021/4/29.
//  Copyright © 2021 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger,AlignType){
    AlignWithLeft,
    AlignWithCenter,
    AlignWithRight
};

@interface YBCustomFlowLayout : UICollectionViewFlowLayout
//两个Cell之间的距离
@property (nonatomic,assign)CGFloat betweenOfCell;
//cell对齐方式
@property (nonatomic,assign)AlignType cellType;
-(instancetype)initWthType : (AlignType)cellType;

@end
