//
//  YBPageControl.m
//  YBLive
//
//  Created by ybRRR on 2020/9/1.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBPageControl.h"

@implementation YBPageControl

#define dotW 6
#define activeDotW 4
#define margin 5
- (void)layoutSubviews
{
    [super layoutSubviews];
    
    //计算圆点间距
    CGFloat marginX = margin + 5;
    
    //计算整个pageControll的宽度
//    CGFloat newW = (self.subviews.count - 1 ) * marginX;
    
//    //设置新frame
//    self.frame = CGRectMake(self.frame.origin.x, self.frame.origin.y, newW, self.frame.size.height);
//
//    //设置居中
//    CGPoint center = self.center;
//    center.x = self.superview.center.x;
//    self.center = center;
    
    //遍历subview,设置圆点frame
    for (int i=0; i<[self.subviews count]; i++) {
        UIImageView* dot = [self.subviews objectAtIndex:i];
        //[dot setFrame:CGRectMake((_window_width-dotW*self.subviews.count)/2+i * marginX, dot.frame.origin.y, dotW, dotW)];
        if (@available(iOS 14.0,*)) {
            [dot mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.width.height.mas_equalTo(dotW);
                make.left.equalTo(self).offset((self.width-dotW*self.subviews.count)/2+i * marginX);
                make.centerY.equalTo(self);
            }];
        }else {
            [dot setFrame:CGRectMake((self.width-dotW*self.subviews.count)/2+i * marginX, dot.frame.origin.y, dotW, dotW)];
        }
    }
}

//重写setCurrentPage方法，可设置圆点大小
- (void) setCurrentPage:(NSInteger)page {
    [super setCurrentPage:page];

    for (NSUInteger subviewIndex = 0; subviewIndex < [self.subviews count]; subviewIndex++) {
        UIImageView* subview = [self.subviews objectAtIndex:subviewIndex];
        CGSize size;
        size.height = 6;
        size.width = 6;
        [subview setFrame:CGRectMake(subview.frame.origin.x, subview.frame.origin.y,
                                     size.width,size.height)];
    }
}

@end
