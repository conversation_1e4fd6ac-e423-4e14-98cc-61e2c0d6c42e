//
//  YBAlertActionSheet.h
//  YBLive
//
//  Created by ybRRR on 2020/5/29.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^btnClickEvent)(NSString *btnTitle);
NS_ASSUME_NONNULL_BEGIN

@interface YBAlertActionSheet : UIView
{
    UIButton *cancelBtn;
    UIView *titleBack;
    UIColor *otherSelColor;
}
@property (nonatomic, copy)btnClickEvent btnEvent;
-(instancetype)initWithFrame:(CGRect)frame cancelTitle:(NSString *)cancelText cancelColor:(UIColor *)cancelcolors andRowHeight:(int)rowH andOtherTitle:(NSArray *)otherArr;

-(void)setSelectIndex:(int)index;
@end

NS_ASSUME_NONNULL_END
