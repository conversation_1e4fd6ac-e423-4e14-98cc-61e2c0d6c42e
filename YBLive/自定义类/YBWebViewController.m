//
//  YBWebViewController.m
//  live1v1
//
//  Created by IOS1 on 2019/3/30.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import "YBWebViewController.h"
#import "PhoneLoginVC.h"
#import "ZYTabBarController.h"
#import "AppDelegate.h"
#import "RechargeViewController.h"
#import "fenXiangView.h"
#import "shareImgView.h"
#import "AuthenticationVC.h"
#import <WebKit/WebKit.h>
#import "CreateFamilyViewController.h"

@interface YBWebViewController ()<WKNavigationDelegate>{
    fenXiangView *shareView;
    UIImage *shareImage;

    NSString *currentUrl;
}
@property (nonatomic,strong) WKWebView *WKWebView;
@property (nonatomic,strong) CALayer *progresslayer;

@end

@implementation YBWebViewController
-(void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
        self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    }
}
- (void)viewDidLoad {
    [super viewDidLoad];
    if ([_itemID isEqual:@"8"]) {
        self.rightBtn.hidden = NO;
        [self.rightBtn setImage:[UIImage imageNamed:@"web_share"] forState:0];
    }

    self.WKWebView = [[WKWebView alloc] initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight)];
    self.WKWebView.navigationDelegate = self;
    [self.view addSubview:self.WKWebView];
    self.progresslayer = [[CALayer alloc]init];
    self.progresslayer.frame = CGRectMake(0, 0, _window_width*0.1, 2);
    self.progresslayer.backgroundColor = normalColors.CGColor;
    [self.WKWebView.layer addSublayer:self.progresslayer];
    
    [self.WKWebView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionNew context:nil];
    [self.WKWebView addObserver:self forKeyPath:@"title" options:NSKeyValueObservingOptionNew context:NULL];

    [self.WKWebView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:_urls]]];
    
    NSLog(@"webviewrequest-----:%@",_urls);
    if ([_urls containsString:@"Family/index"]) {
        self.rightBtn.hidden = NO;
        [self.rightBtn setTitle:YZMsg(@"创建家族") forState:0];
        [self.rightBtn setTitleColor:[UIColor blackColor] forState:0];
    }

}
// 观察者
-(void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context{
    
    if ([keyPath isEqualToString:@"estimatedProgress"]) {
        
        self.progresslayer.opacity = 1;
        float floatNum = [[change objectForKey:@"new"] floatValue];
        self.progresslayer.frame = CGRectMake(0, 0, _window_width*floatNum, 2);
        if (floatNum == 1) {
            
            __weak __typeof(self)weakSelf = self;
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                weakSelf.progresslayer.opacity = 0;
            });
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                
                weakSelf.progresslayer.frame = CGRectMake(0, 0, 0, 3);
            });
        }
        
    }else if ([keyPath isEqualToString:@"title"]){//网页title
        if (object == self.WKWebView){
            self.titleL.text = self.WKWebView.title;
            if ([self.titleL.text isEqual:@"家族主页"]) {
                self.rightBtn.hidden = YES;
            }
        }else{
            [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
        }
    }else{
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
    
}
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler{
    
    NSString *url = navigationAction.request.URL.absoluteString;
    if (navigationAction.targetFrame.isMainFrame) {
        NSLog(@"target is main ... %@",url);
        if (navigationAction.sourceFrame.mainFrame) {
            NSLog(@"source is main...%@",url);
            //是原始url 放行
            if ([_urls isEqualToString:url]) {
                decisionHandler(WKNavigationActionPolicyAllow);
                NSLog(@"放行bbbbbbbbbbbbbbbbb...%@",url);
                return;
            }
            if ([url hasPrefix:@"copy://"]) {
                NSString *results = [url substringFromIndex:7];
                UIPasteboard *paste = [UIPasteboard generalPasteboard];
                paste.string = results;
                [MBProgressHUD showError:YZMsg(@"复制成功")];
                decisionHandler(WKNavigationActionPolicyCancel);
                
                return;
            }
             if ([url containsString:@"phonelive://pay"]) {
                 RechargeViewController *coins = [[RechargeViewController alloc]init];
                 [[MXBADelegate sharedAppDelegate]pushViewController:coins animated:YES];

                 decisionHandler(WKNavigationActionPolicyCancel);
                
                return;
            }
            if ([url containsString:@"auth://"]) {
                AuthenticationVC *auth = [[AuthenticationVC alloc]init];
                [[MXBADelegate sharedAppDelegate]pushViewController:auth animated:YES];

                decisionHandler(WKNavigationActionPolicyCancel);
               
               return;
           }
//            if ([url containsString:@"familycreate://"]) {
//                //创建家族
//                CreateFamilyViewController *family = [[CreateFamilyViewController alloc]init];
//                [[MXBADelegate sharedAppDelegate]pushViewController:family animated:YES];
//
//                decisionHandler(WKNavigationActionPolicyCancel);
//
//               return;
//           }

            if ([url hasPrefix:@"tel://"]) {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:url]];
                decisionHandler(WKNavigationActionPolicyCancel);
                
                return;
            }
            NSLog(@"JJJJJJJJJJJ-------:%@",url);
            if ([url containsString:@"familyindex://"]) {
                NSArray *urlArr = [url componentsSeparatedByString:@"://"];
                NSLog(@"ybweb------：%@",urlArr);
//                NSString *last = urlArr[1];
                if (urlArr.count > 0) {
                    NSString *last = urlArr[1];
                    last = [last stringByReplacingOccurrencesOfString:@"host" withString:h5url];
                    last = [self addurl:last];
                    [self.WKWebView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:last]]];
                    self.rightBtn.hidden = NO;
                    [self.rightBtn setTitle:YZMsg(@"创建家族") forState:0];
                    [self.rightBtn setTitleColor:[UIColor blackColor] forState:0];
                    currentUrl = last;
                }
                
                
                decisionHandler(WKNavigationActionPolicyCancel);

               return;
           }


            
        } else {
            NSLog(@"source is not main...%@",url);
        }
    } else {
        NSLog(@"target is not main ... %@",url);
    }
    decisionHandler(WKNavigationActionPolicyAllow);
    NSLog(@"在发送请求之前：%@",navigationAction.request.URL.absoluteString);
    if ([navigationAction.request.URL.absoluteString containsString:@"Family/index"]) {
        self.rightBtn.hidden = NO;
        [self.rightBtn setTitle:YZMsg(@"创建家族") forState:0];

    }

}


-(void)dealloc{
    NSLog(@"WKWebView dealloc------------");
    [self.WKWebView removeObserver:self forKeyPath:@"estimatedProgress"];
    [self.WKWebView removeObserver:self forKeyPath:@"title"];

}
- (void)doReturn{
    if (_isGuide) {
        UIApplication *app =[UIApplication sharedApplication];
        AppDelegate *app2 = (AppDelegate *)app.delegate;
        UINavigationController *nav;
        if ([Config getOwnID]) {
            nav = [[UINavigationController alloc]initWithRootViewController:[[ZYTabBarController alloc]init]];
        }else{
            nav = [[UINavigationController alloc]initWithRootViewController:[[PhoneLoginVC alloc]init]];
        }
        app2.window.rootViewController = nav;

    }else{
        if ([currentUrl containsString:@"Family/index"]) {
            [self.navigationController popViewControllerAnimated:YES];
            [self dismissViewControllerAnimated:YES completion:nil];

        }else  if ([self.titleL.text isEqual:@"提交成功"] ||[self.titleL.text isEqual:@"申请进度"]) {
            [self.navigationController popViewControllerAnimated:YES];
            [self dismissViewControllerAnimated:YES completion:nil];

        }else{
            if ([_WKWebView canGoBack]) {
                [_WKWebView goBack];
            }else{
                [self.navigationController popViewControllerAnimated:YES];
                [self dismissViewControllerAnimated:YES completion:nil];
            }

        }
       
    }
}
//分享
- (void)rightBtnClick{
    if ([self.rightBtn.titleLabel.text isEqual:YZMsg(@"创建家族")]) {
            //创建家族
            CreateFamilyViewController *family = [[CreateFamilyViewController alloc]init];
            [[MXBADelegate sharedAppDelegate]pushViewController:family animated:YES];
    }else{
        if (!shareImage) {
            [MBProgressHUD showMessage:@""];
            [YBToolClass postNetworkWithUrl:@"Agent.getCode" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
                if (code == 0) {
                    NSDictionary *infoDic = [info firstObject];
                    shareImgView *shareV = [[NSBundle mainBundle] loadNibNamed:@"shareImgView" owner:nil options:nil].lastObject;
                    shareV.iconImgV.image = [PublicObj getAppIcon];
                    shareV.appNameL.text = [[[NSBundle mainBundle] infoDictionary] valueForKey:@"CFBundleDisplayName"];
                    UIImage *img = [UIImage imageWithData:[NSData dataWithContentsOfURL:[NSURL URLWithString:[Config getavatarThumb]]]];
                    shareV.userIcon.image = img;
                    shareV.userIconSmall.image = img;
                    shareV.userNameL.text = [Config getOwnNicename];
                    shareV.userIDL.text = [NSString stringWithFormat:@"ID:%@",[Config getOwnID]];
                    shareV.codeImgV.image = [self creatCodeImage:minstr([infoDic valueForKey:@"href"])];
                    shareV.invitationL.text = minstr([infoDic valueForKey:@"code"]);
                    [shareV layoutIfNeeded];
                    shareImage = [self getImage:shareV];
                    [self showShareView];
                }else{
                    [MBProgressHUD hideHUD];
                    [MBProgressHUD showError:msg];
                }
            } fail:^{
                [MBProgressHUD hideHUD];
            }];
        }else{
            [self showShareView];
        }

    }
}
- (void)showShareView{
    [MBProgressHUD hideHUD];
    if (!shareView) {
        shareView = [[fenXiangView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
        [shareView GetDIc:@{@"id":@"fenxiao",@"image":shareImage}];
        [self.view addSubview:shareView];
    }else{
        [shareView show];
    }
    
}
- (UIImage *)creatCodeImage:(NSString *)url{
    //创建过滤器
    CIFilter *filter = [CIFilter filterWithName:@"CIQRCodeGenerator"];
    //过滤器恢复默认
    [filter setDefaults];
    //将NSString格式转化成NSData格式
    NSData *data = [url dataUsingEncoding:NSUTF8StringEncoding allowLossyConversion:YES];
    [filter setValue:data forKeyPath:@"inputMessage"];
    //获取二维码过滤器生成的二维码
    CIImage *image = [filter outputImage];
    return [self createNonInterpolatedUIImageFormCIImage:image withSize:190];//重绘二维码,使其显示清晰
    
}
/**
 * 根据CIImage生成指定大小的UIImage
 *
 * @param image CIImage
 * @param size 图片宽度
 */
- (UIImage *)createNonInterpolatedUIImageFormCIImage:(CIImage *)image withSize:(CGFloat) size
{
    CGRect extent = CGRectIntegral(image.extent);
    CGFloat scale = MIN(size/CGRectGetWidth(extent), size/CGRectGetHeight(extent));
    // 1.创建bitmap;
    size_t width = CGRectGetWidth(extent) * scale;
    size_t height = CGRectGetHeight(extent) * scale;
    CGColorSpaceRef cs = CGColorSpaceCreateDeviceGray();
    CGContextRef bitmapRef = CGBitmapContextCreate(nil, width, height, 8, 0, cs, (CGBitmapInfo)kCGImageAlphaNone);
    CIContext *context = [CIContext contextWithOptions:nil];
    CGImageRef bitmapImage = [context createCGImage:image fromRect:extent];
    CGContextSetInterpolationQuality(bitmapRef, kCGInterpolationNone);
    CGContextScaleCTM(bitmapRef, scale, scale);
    CGContextDrawImage(bitmapRef, extent, bitmapImage);
    // 2.保存bitmap到图片
    CGImageRef scaledImage = CGBitmapContextCreateImage(bitmapRef);
    CGContextRelease(bitmapRef);
    CGImageRelease(bitmapImage);
    return [UIImage imageWithCGImage:scaledImage];
}
- (UIImage *)getImage:(UIView *)shareView

{
    
    UIGraphicsBeginImageContextWithOptions(CGSizeMake(shareView.frame.size.width,shareView.frame.size.height ), NO, 0.0); //currentView 当前的view  创建一个基于位图的图形上下文并指定大小为
    
    [shareView.layer renderInContext:UIGraphicsGetCurrentContext()];
    //      renderInContext呈现接受者及其子范围到指定的上下文
    
    UIImage *viewImage = UIGraphicsGetImageFromCurrentImageContext();//返回一个基于当前图形上下文的图片
    
    UIGraphicsEndImageContext();//移除栈顶的基于当前位图的图形上下文
    
    //     UIImageWriteToSavedPhotosAlbum(viewImage, nil, nil, nil);//然后将该图片保存到图片图
    
    return viewImage;
    
}

//所有h5需要拼接uid和token
-(NSString *)addurl:(NSString *)url{
    NSString *language = [PublicObj getCurrentLanguage];
    return [url stringByAppendingFormat:@"&uid=%@&token=%@&language=%@",[Config getOwnID],[Config getOwnToken],language];
}

@end
