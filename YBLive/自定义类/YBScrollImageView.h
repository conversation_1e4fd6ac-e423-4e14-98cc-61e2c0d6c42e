//
//  YBScrollImageView.h
//  live1v1
//
//  Created by ybRRR on 2020/11/20.
//  Copyright © 2020 IOS1. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
typedef void(^YBImageViewBlock)(NSArray *array);

@interface YBScrollImageView : UIView
- (instancetype)initWithImageArray:(NSArray *)array andIndex:(NSInteger)index andMine:(BOOL)ismine andBlock:(YBImageViewBlock)block;

-(void)hideDelete;

@end

NS_ASSUME_NONNULL_END
