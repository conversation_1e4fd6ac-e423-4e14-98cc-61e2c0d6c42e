/*
  Info.strings
  yunbaolive

  Created by IOS1 on 2019/2/26.
  Copyright © 2019 cat. All rights reserved.
*/
"NSCameraUsageDescription" = "Access your camera, allow to enable live broadcast and record video";
"NSLocationWhenInUseUsageDescription" = "Access your location, allowing access to local live and video";
"NSMicrophoneUsageDescription" = "Access your microphone, allow to record sound";
"NSPhotoLibraryAddUsageDescription" = "Read your album, allowing you to upload the contents of the album to the server";
"NSPhotoLibraryUsageDescription" = "Access your photo album, allow to save the video in the photo album";

"访问您的相机，允许可以开启直播和录制视频"= "Access your camera, allow to enable live broadcast and record video";
"访问您的位置，允许可以获取当地的直播和视频"= "Access your location, allowing access to local live and video";
"访问您的麦克风，允许可以录制声音"= "Access your microphone, allow to record sound";
"读取您的相册，允许可以把相册的内容上传到服务器"= "Read your album, allowing you to upload the contents of the album to the server";
"访问您的相册,允许可以把视频保存在相册"= "Access your photo album, allow to save the video in the photo album";

//共通
"确定"= "Sure";
"取消"= "Cancel";
"网络错误"= "Network error";
"提示"= "Hint";
"全部"= "All";
"热门"= "hot";
"删除"= "Delete";
"拒绝"= "Reject";
"接受"= "Accept";
"好像在火星"= "Like mars";
"火星" = "Mars";
"保存"= "Save";
"正在上传"= "uploading";
"上传失败"= "upload failed";


"语言"= "language";
"切换语言"= "Switch language";
"中文"= "Chinese";

"首页"= "Home";
"附近"= "Nearby";
"排行"= "Ranking";
"我的"= "Mine";
"我的 " = "My ";
"使用旧版"= "Use old version";
"前往更新"= "Go to update";
"维护通知" = "Maintenance notice";
"该房间为密码房间"= "This room is a password room";
"密码错误"= "Wrong password";


"是否退出直播间"= "Do you want to exit the live broadcast room";
"正在直播中，无法退出"= "Live broadcasting, unable to exit";
"注册登录后体验更多精彩"= "Register and log in to experience more exciting";
"请填写手机号"= "Please fill in your phone number";
"请输入密码"= "Please enter password";
"立即登录"= "Log in immediately";
"立即注册"= "Sign up now";
"忘记密码"= "Forgot  password";
"忘记密码?" ="Forgot  password?";
"其他登录方式"= "Other login methods";
"登录即代表你同意服务和隐私条款"= "Logging in means you agree to the terms of service and privacy";
"服务和隐私条款"= "Terms of Service and Privacy";
"未获取到授权，请重试"= "Authorization not obtained, please try again";
"正在登录"= "Logging in";

"注册"= "Register";
"请输入验证码"= "Please enter verification code";
"获取验证码"= "Verification code";
"请填写密码"= "Please fill in the password";
"请确认密码"= "Please confirm your password";
"注册并登录"= "Register and log in";
"正在注册"= "Registering";
"手机号输入错误"= "Incorrect phone number input";
"重新获取"= "Reacquire";

"立即找回"= "Get it back now";
"发送成功"= "Sent successfully";
"密码重置成功"= "Password reset successful";
"密码不一致"= "Passwords do not match";
"邮箱登录"= "Email Login";
"请输入邮箱账号"= "Please enter your email address";

"邮箱注册"= "email registration";
"发送验证码"= "Send the verification code";
"再次确认密码"= "Confirm password again";
"正在找回密码"= "retrieving password";
"找回密码"= "Retrieve password";

"重置密码"= "Reset password";
"旧密码"= "Old password";
"新密码"= "New password";
"确认密码"= "Confirm password";
"立即修改"= "Modify now";
"请输入旧密码"= "Please enter the old password";
"请填写新密码"= "Please fill in the new password";
"确认新密码"= "Confirm the new password";

"立即签到"= "Sign in now";
"已连续签到"= "Signed in for";
"签到" = "Sign in";
"天"= "days";
"关注"= "Follows";
"直播"= "Live";
"视频"= "Video";
"暂时没有主播开播"= "There is currently no anchor broadcasting";
"赶快去其他频道逛逛吧"= "Hurry up and browse other channels";
"跳过"= "Skip";
"为你推荐"= "Recommended for you";
"点击进入"= "Click to enter";
"现在还没有主播"= "There is no anchor yet";
"快快开启你的直播吧"= "Hurry up and start your live broadcast";
"在看"= "look in";
"请输入邀请码"= "Please enter the invitation code";
"请填写您的邀请码"= "Please fill in your invitation code";

"不能关注自己"= "Can't focus on myself";
"暂时空缺"= "temporary vacancy";
"收益榜"= "Revenue list";
"贡献榜"= "Contribution List";
"日榜"= "Daily List";
"周榜"= "Weekly List";
"月榜"= "Monthly List";
"总榜"= "General List";

"附近没有主播开播"= "There is no anchor nearby";
"去首页看看其他主播的直播吧"= "Go to the homepage to watch other anchors’ live broadcasts";

"直播记录"= "Live recording";
"你最近没有开过直播"= "You haven't started a live broadcast recently";
"赶快去开场直播体验一下吧"= "Hurry up and go to the opening live broadcast to experience it";
"粉丝"= "Fans";
"无标题"= "Untitled";
"人看过直播"= "People watched the live broadcast";
"靓"= "Pretty";
"万"= "W";
"复制成功"= "Copy successful";
"提交成功"= "Submitted successfully";
"申请进度"= "Application Progress";
"我的家族"= "my family";
"总"= "Total ";
"数"= "";
"可提取"= "Extractable ";
"输入要提取的"= "Enter the number";
"可到账金额"= "Amount available";
"请选择提现账户"= "Please select a withdrawal account";
"立即提现"= "Withdraw immediately";
"未选择提现方式"= "Withdrawal method not selected";
"请选择提现账号"= "Please select a withdrawal account";
"提现账户"= "Withdrawal account";
"添加"= "Add";
"您当前还没设置提现账户"= "You have not yet set up a withdrawal account";
"温馨提示"= "Kind tips";
"是否确定删除此提现账号？"= "Are you sure to delete this withdrawal account?";
"添加提现账户"= "Add withdrawal account";
"账户类型"= "Account type";
"支付宝"= "Alipay";
"请输入支付宝账号"= "Please enter Alipay account number";
"请输入支付宝账号姓名"= "Please enter Alipay account name";
"请输入微信账号"= "Please enter your WeChat ID";
"请输入银行名称"= "Please enter the bank name";
"请输入银行卡账号"= "Please enter the bank card account number";
"请输入持卡人姓名"= "Please enter the cardholder's name";
"微信"= "WeChat";
"银行卡"= "Bank card";
"无法获取商品信息"= "Unable to get product information";
"请查看网站是否开启了调试模式"= "Please check if the website is in debug mode";
"充值成功"= "Recharge successful";
"用户已恢复购买"= "User restored purchase";
"设置"= "set up";
"当前已是最新版本"= "Currently the latest version";
"缓存已清除"= "Cache cleared";
"赠送"= "Gift";
"选择支付方式"= "Select Payment Method";
"微信支付"= "WeChat Pay";
"苹果支付"= "Apple Pay";
"缺少partner或者seller或者私钥"= "Missing partner or seller or private key";

"这里什么也没有"= "There is nothing here";
"黑名单"= "blacklist";
"取消拉黑"= "Unblock";
"对方将移除你的黑名单"= "The other party will remove your blacklist";
"操作成功"= "Successful operation";
"退出登录"= "Sign out";

"私信"= "Private letter";
"拉黑"= "pull black";
"已关注"= "Followed";
" 已关注"= "Followed";
"对方未注册私信"= "The other party has not registered private message";
"已拉黑"= "Blocked";
"已解除拉黑"= "Unblocked";
"主播印象"= "Anchor impression";
"+添加印象"= "+Add";
" 榜"= "Guardian list";
"成为TA的第一个守护"= "Become TA's first guardian";
"本周贡献 "= "Contribution this week: ";
"本周贡献"= "Contribution this week:";
"直播封面"= "Live Cover";
"频道"= "channel";
"直播标题"= "Live title";
"给直播写个标题吧"= "Write a title for the live";
"分享到"= "Share to";
"分享至"= "Share to";
"开始直播"= "Start live";
"房间类型"= "Room type";
"请选择频道"= "Please select a channel";
"权限受阻"= "Permission Blocked";
"请在设置中开启相机权限"= "Please turn on the camera permission in the settings";
"请在设置中开启麦克风权限"= "Please turn on the microphone permission in the settings";
"分享成功"= "Sharing Success";
"分享失败"= "Sharing failed";
"选择上传方式"= "Choose upload method";
"相册"= "Album";
"拍照"= "Photograph";
"更换封面"= "Change cover";
"直播已结束"= "The live broadcast has ended";
"直播时长"= "Live time";
"收获"= "Income";
"观看人数"= "Viewers";
"返回首页"= "Return to homepage";
" 进入了直播间"= "entered the live room";
"直播间消息"= "Live Room News";
"主播回来了"= "The host is back";
"主播离开一下，精彩不中断，不要走开哦"= "Anchor leave for a while, the excitement will not be interrupted, don't go away";
"确定退出直播吗？"= "Are you sure you want to quit the live broadcast?";
"是否要结束直播？"= "Do you want to end the live?";
"和大家说些什么"= "Tell me something";
"守护 虚位以待>"= "Guard  Vacant waiting>";
"守护 虚位以待  "= "Guard  Vacant waiting";
"连麦状态下不能进行游戏哦"= "The game cannot be played in the state of continuous mic";
"当前正在进行连麦"= "Currently connecting wheat";
"字数最多50字"= "Maximum 50 characters";
"请等待游戏结束"= "Please wait for the game to end";
"当前账号已在其他设备登录"= "The current account is already logged in on other devices";
"开启大喇叭"= "Turn on the loudspeaker";
"开启弹幕"= "Open barrage";
"条"= "item";
"暂无歌词"= "No lyrics yet";
"结束"= "End";
"麦克风音量"= "Mic Volume";
"背景音乐音量"= "Background music volume";
"主播"= "Anchor";
"翻转"= "Flip";
"闪光灯"= "Flash";
"伴奏"= "Accompaniment";
"分享"= "Share";
"游戏"= "Game";
"红包"= "Red envelope";
"连麦"= "Link mic";
"你已被踢出房间"= "You have been kicked out of the room";
"主播连麦中，请等会儿再试哦"= "The host is in Lianmai, please try again later";
"连麦请求已发送"= "Connection request has been sent";
"您已申请，请稍等"= "You have applied, please wait";
"是否断开连麦"= "Whether to disconnect the microphone";
"推流失败"= "Push stream failed";
"直播间红包"= "Live room red envelope";
"给当前直播间观众发红包"= "Send red envelopes to the audience in the current live broadcast room";
"拼手气红包"= "Lucky";
"平均红包"= "Average";
"数量"= "Quantity";
"  数量"= "  Quantity" ;
"恭喜发财，大吉大利"= "Congratulations on getting rich and good luck";
"发红包"= "Red envelopes";
"个"= "Indivual";
"请输入红包金额"= "Please enter the red envelope amount";
"请输入红包数量"= "Please enter the number of red envelopes";
"个红包"= "a red envelope";
"抢"= "Rob";
"倒计时结束后可抢"= "You can grab it after the countdown ends";
"查看领取详情》"= "View Receipt Details";
"手慢了，红包派完了"= "Hands are slow, the red envelope distribution is over";
"恭喜你!"= "congratulations!";
"抢到"= "grab";
"派发的"= "distributed";
"红包"= "Red envelope";
"派发了一个延时红包"= "Distributed a delayed red envelope";
"派发了一个即时红包"= "Distributed an instant red envelope";
"的红包"= "The Red Envelope";
"未抢到"= "not grabbed";
"已存入「我的"= "Deposited in My";
"已领取"= "Collected";
"共"= "Total";

"当前在线主播"= "Current online listings";
"请输入您要搜索的主播昵称或ID"= "Please enter the nickname or ID of the host you want to search for";
"没有搜索到相关内容"= "No related content found";
"对不起~"= "Sorry~";
"已邀请"= "Invited";
"邀请连麦"= "Invite";
"时间"= "time";
"惩罚时间"= "punishment time";
"我方"= "our side";
"对方"= "other side";
"对方发起PK"= "The opponent initiates a PK";

"选择守护时长"= "Choose the guard duration";
"守护特权"= "Guardian Privileges";
"立即开通"= "Open now";
"您当前为主播的月守护，开通年守护将覆盖您的月守护剩余时长，是否开通？"= "You are currently the host's monthly guard, and the activation of the annual guard will cover the remaining time of your monthly guard. Is it activated?";
"您当前为主播的年守护\n无法开通7天/月守护"= "You are currently the broadcaster's annual guard\n cannot activate the 7-day/month guard";
"您将花费"= "you will spend";
"为主播开通7天守护"= "Activate 7-day protection for the broadcaster";
"为主播开通月守护"= "Enable Moon Guardian for the host";
"为主播开通年守护"= "Guardian for the Opening Year of the Broadcaster";
"您是当前主播的月守护\n守护日期截止到"= "You are the monthly guardian of the current host\nThe guardian date is up to";
"续费守护"= "Renewal guard";
"您是当前主播的年守护\n守护日期截止到"= "You are the year guardian of the current host\nThe guardian date expires";
"快去为喜欢的主播开通守护吧"= "Go and activate Guardian for your favorite streamer";
"开通守护"= "Activate guard";
"守护"= "Guard";
"你还没有守护哦"= "You haven't guarded yet";
"选择直播频道"= "Select live channel";
"注意选择适合自己的频道。直播过程中，若运营人员发现选择的频道和直播内容不相符的情况，会调整您的直播频道。"= "Pay attention to choosing a channel that suits you. During the live broadcast, if the operator finds that the selected channel does not match the live broadcast content, he will adjust your live broadcast channel.";

"恭喜"= "Congratulations";
"上庄"= "Shangzhuang";
"下庄"= "Xiazhuang";
"吕布"= "Lu Bu";
"上庄押金"= "Shangzhuang Deposit";
"请输入押金,最低"= "Please enter a deposit, minimum";
"申请上庄"= "Application for Shangzhuang";
"上庄列表"= "Shangzhuang List";
"庄家流水"= "The banker's flow";
"顺序"= "order";
"玩家"= "player";
"游戏币"= "Game currency";
"庄"= "village";
"胜败乃兵家常事" = "Victory or defeat is standard issue";
"分享至"= "share to";
"分享至(左右滑动更多分享)"= "Share to (Swipe left or right for more shares)";
"有"= "have";
"粉丝，快来围观呀!"= "Fans, come and watch!";
"也在"= "also";
"点击查看TA的故事"= "Click to view TA's story";
"请输入您要搜索的昵称或ID"= "Enter the nickname or ID";

"智勇三张"= "Three card poker";
"海盗船长"= "Pirate Captain";
"幸运抽奖"= "Lucky draw";
"开心牛仔"= "Happy Cowboy";
"二八贝"= "twenty eight shellfish";
"选择游戏"= "Select game";
"幸运转盘" = "Wheel of Fortune";
"即将开始，请稍后"= "Starting soon, please wait";
"即将开始" ="Starting soon";
"赢了哦"= "Won";
"开始支持"= "Start supporting";
"揭晓结果"= "Reveal the result";
"充值"= "Recharge";

"大乔"= "Big Joe";
"大乔X2"= "Big Joe X2";
"貂蝉"= "Diao Chan";
"貂蝉X2"= "Diao Chan X2";
"小乔"= "Little Joe";
"小乔X2"= "Little Joe X2";
"星星"= "Star";
"星星X2"= "Star X2";
"球球"= "Ball";
"球球X2"= "Ball X2";
"仔仔"= "Zai Zai";
"仔仔X2"= "Zaizai X2";
"罗伯茨"= "roberts";
"罗伯茨X2"= "Roberts X2";
"平局"= "draw";
"平局X2"= "Draw X2";
"基德"= "kidd";
"基德X2"= "Kidd X2";
"您的余额不足"= "Your balance is insufficient";
"将被下庄"= "will be lowered";
"本家"= "Home";
"庄家无法下注"= "The dealer cannot bet";
"历史记录"= "history record";

"香蕉"= "banana";
"萝卜"= "radish";
"豌豆"= "pea";
"柿子"= "persimmon";

"   选择举报的理由"= "Choose the reason for reporting";
"更多详细信息请在说明框中描述（选填）"= "Please describe in the description box for more details (optional)";
"提交"= "Submit";
"请选择举报理由"= "Please select the reason for reporting";
"举报成功"= "Report successful";
"确定举报？"= "Are you sure to report?";
"举报"= "Report";
"送出"= "Send";
"收入"= "Income";
"踢人"= "Kick";
"禁言"= "Forbidden";
"设为管理"= "Set to manage";
"取消管理"= "Unmanage";
"管理员列表"= "Administrator list";
"关闭直播"= "Close live";
"封禁直播间" = "Ban live  room";
"请选择封禁时间" = "Please select the ban time";
"禁用直播"= "Disable Live Streaming";
"+ 添加印象"= "+ Add";
"主页"= "Home";
"直播结束"= "The end of the broadcast";

"送了一个"= "sent one";
"发送礼物"= "Send a gift";
"年守护专属礼物"= "Year Guardian Exclusive Gift";
"连送"= "Send";
"送了"= "delivered";

"对方还未注册私信，无法发送"= "The other party has not registered for private messages and cannot send them";
"对方暂时拒绝接收您的消息"= "The other party temporarily refuses to receive your message";
"发送"= "send";
"已经忽略未读消息"= "Unread messages have been ignored";
"暂无未读消息"= "No unread messages yet";
"消息"= "Message";
"忽略未读"= "Ignore unread";
"你还没有收到任何消息"= "You have not received any message";
"暂无消息"= "No news yet";
"系统消息无法删除"= "System message cannot be deleted";
"系统消息"= "System messages";
"Hi～我是主播，快来和我聊天吧。"= "Hi~ I'm an anchor, come and chat with me.";

"暂无收到消息"= "No message received yet";
"数据加载中..."= "loading...";
"没有更多了哦~"= "No more~";
"点击关注，可及时看到对方动态"= "Click to follow and you can see each other’s updates in real time";
"关注对方成功"= "Focus on each other's success";
"没有相册权限"= "No album permissions";
"请到设置页面获取相册权限"= "Please go to the settings page to obtain the album permission";
"不支持视频发送"= "Video sending is not supported";
"对方未关注你,最多只能发送"= "The other party has not followed you, and can only send at most";
"条信息"= "a message";
"拉黑成功"= "Successfully blocked";
"取消拉黑成功"= "Unblocked successfully";
"初始化中..."= "Initializing...";
"长按识别..."= "Long press to identify...";
"解析错误"= "parsing error";
"请说话"= "please speak";
"请说话..."= "please speak..." ;
"清除"= "clear";
"发送消息..."= "Send a message...";
"拍摄"= "shooting";
"重新拍摄"= "Reshoot";
"图片"= "picture";
"语音输入"= "Voice input";
"位置"= "Location";
"按住 说话"= "Hold to talk";
"松开 结束"= "Release end";
"无法录音"= "Unable to record";
"请在“设置-隐私-麦克风”选项中，允许IM访问你的手机麦克风。"= "Please allow IM to access your phone's microphone in the \"Settings-Privacy-Microphone\" option.";
"关闭"= "closure";
"手指上滑，取消发送"= "Swipe up to cancel sending";
"松开手指，取消发送"= "Release your finger to cancel sending";
"刚刚"= "just";
"分钟前"= "minutes ago";
"搜索地点"= "Search Places";
"私聊"= "Chat";

"竞拍剩余时间"= "Auction Time Remaining";
"最终出价"= "Final Bid";
"商品详情"= "Product details";
"竞拍投注成功"= "Auction betting success";
"竞拍剩余时间"= "Auction Time Remaining";
"付款时间已过，付款失败"= "Payment time has passed, payment failed";
"自动关闭"= "Auto shut off";
"付款"= "Payment";
"恭喜你付款成功"= "Congratulations on your successful payment";

"当前管理员"= "Current administrator";
"是否确定取消"= "Are you sure you want to cancel";
"的管理员身份"= "is administrator status";
"不了"= "no";

"原始美白"= "Original whitening";
"原始美白(√)"= "Original whitening (√)";
"美颜"= "Beauty";
"美颜(√)"= "Beauty (√)";
"白皙"= "White";
"白皙(√)"= "White(√)";
"美颜+"= "Beauty+";
"美颜+(√)"= "Beauty+(√)";
"不使用美颜"= "I do not use the beauty face";
"不使用美颜+(√)"= "Do not use beauty +(√)";
"无"= "None";
"外星人"= "Alien";
"梨梨脸"= "Pear face";
"瘦瘦脸"= "Thin face";
"方方脸"= "Square face";
"炫彩抖动"= "Colorful jitter";
"轻彩抖动"= "Light color shake";
"头晕目眩"= "Dizzy";
"灵魂出窍"= "Out of body";
"暗黑魔法"= "Dark magic";
"虚拟镜像"= "Virtual mirror";
"动感分屏"= "Dynamic split screen";
"黑白电影"= "Black and white film";
"瞬间石化"= "Instant petrification";
"魔法镜面"= "Magic Mirror";

"进入了直播间"= "entered the live room";

"下载"= "download";
"选择"= "Select";
"TA还没有关注任何人"= "TA hasn't followed anyone yet";
"你还没有关注任何人"= "You are not following anyone yet";
"赶快去关注自己感兴趣的人吧"= "Hurry up and follow the people you are interested in";
"你还没有粉丝"= "You don't have fans yet";
"完善个人信息会让更多的人关注到你"= "Improving your personal information will allow more people to pay attention to you";

"请选择你对主播的印象"= "Please choose your impression of the anchor";
"你收到的主播印象"= "The anchor impression you received";
"你暂时还没有收到主播印象"= "You haven't received the impression of the anchor yet";
"最多选择三项"= "Choose up to three items";
"最少选择一项"= "Choose at least one item";
"修改昵称"= "Change username";
"昵称最多8个字"= "Nickname can be up to 8 characters long";
"字数超出限制"= "Word limit exceeded";
"修改签名"= "Modify Signature";
"最多可输入20个字"= "Enter up to 20 characters";
"昵称"= "Nick name";
"签名"= "Sign";
"生日"= "Birthday";
"性别"= "Gender";
"我的主播印象"= "My anchor impression";
"编辑资料"= "Edit information";
"头像"= "Avatar";
"男"= "male";
"女"= "female";

"被禁言"= "banned";
"被踢出房间"= "was kicked out of the room";
"踢人成功"= "Successful kick";
"被取消管理员"= "was removed as administrator";
"取消管理员成功"= "Cancel administrator successfully";
"被设为管理员"= "is set as administrator";
"设置管理员成功"= "Set administrator successfully";
"直播关闭"= "Live Off";
"涉嫌违规，被下播"= "Suspected of violating regulations, was downloaded";
"你已被禁言"= "You have been banned";
"已下麦"= "Wheat has been dropped";
"对方主播拒绝了你的连麦申请"= "The host of the other party rejected your application for Lianmai";
"连麦已断开"= "Lianmai has been disconnected";
"对方主播拒绝了您的PK请求"= "The other anchor rejected your PK request";
"对方正忙碌"= "The other party is busy";
"对方无响应"= "There is no response from the other party";
"发起连麦请求"= "Initiate a Lianmai request";
"发起PK请求"= "Initiate PK request";
"在直播间发红包啦！快去抢哦"= "Anchor  give out red envelopes in the live broadcast room! Go and grab it~";

"主播已把你下麦"= "The host has dropped you";
"主播拒绝了您的连麦请求"= "The host has rejected your mic connection request";
"主播正忙碌"= "The anchor is busy";
"当前主播暂时无法接通"= "The current anchor is temporarily unavailable";
"我点亮了"= "I light up";
"关注了主播"= "Followed the anchor";

"已经到顶了哦"= "It's reached the top";
"没有更多了哦"= "No More";
"没有数据"= "no data";
"请添加内容后再尝试"= "Please add content and try again";
"不用给自己回复"= "Don't reply to yourself";
"回复"= "Reply";
"回复给"= "Reply to";
"全部回复"= "Reply all";
"查看回复"= "View reply";
"不能给自己的评论点赞"= "Can't like my own comments";
"说点什么..."= "Say something...";
"  说点什么..." = "Say something...";
"评论"= "Comments";
"你还没有收到评论"= "You haven't received a comment yet";
"你还没有被@哦"= "You haven't been @oh";
"@我的"= "@Me";
"@好友" = "Friends";
"赞"= "Like";
"你还没有被赞哦"= "You haven't been liked yet";
"总金额"= "Lump sum";
"  总金额"= "  Lump sum" ;
"单个金额"= "Single amount";
"  单个金额"= "Single amount";
"视频转码中"= "Video transcoding";
"音乐"= "Music";
"上传"= "upload";
"至少要录到这里"= "At least it's going to be recorded here";
"转码失败,请更换视频"= "Transcoding failed, please change the video";
"至少要录够5秒"= "Record at least 5 seconds";
"退出"= "Exit";
"闪光灯启动失败"= "Flash failed to start";
"录制失败"= "Recording failed";
"确定删除上一段视频"= "OK to delete the last video";
"极慢"= "Extremely slow";
"慢"= "Slow";
"正常"= "Normal";
"快"= "Fast";
"极快"= "Extremely fast";
"音量"= "Volume";
"裁剪"= "Crop";
"特效"= "Effects";
"下一步"= "Next";
"请拖拽两侧滑块选择裁剪区域"= "Please drag the sliders on both sides to select the cropping area";
"是否退出视频编辑"= "Whether to quit video editing";
"确定退出"= "OK to exit";
"长按可添加特效"= "Long press to add special effects";
"视频文件过大,超过200M！"= "The video file is too large, exceeding 200M!";
"视频生成中"= "Video is being generated";
"保存成功"= "Saved successfully";
"视频生成失败"= "Video generation failed";
"知道了"= "knew";
"中途切后台或则被电话，闹钟等打断导致,请重新生成"= "Cut to the background midway or was interrupted by a phone call, alarm clock, etc., please regenerate";
"设置背景音乐失败"= "Failed to set background music";
"不支持当前格式的背景音乐!"= "The background music in the current format is not supported!";
"发布中，请稍后"= "Publishing, please wait";
"网络连接断开，视频上传失败"= "Network connection disconnected, video upload failed";
"发布中"= "announcing";
"发布成功"= "Published successfully";
"上传成功，请等待审核"= "Uploaded successfully, please wait for review";
"添加视频描述~"= "Add video description~";
"是否放弃发布此条视频"= "Do you want to give up publishing this video";
"发布视频"= "Publish video";
"放弃"= "Give up";
"立即直播"= "Live now";
"本场禁言"= "Banned for this session";
"永久禁言"= "Permanent ban";
"禁用账户"= "Disable account";
"所在地"= "Location";
"你还没有上传过付费内容"= "You have not uploaded paid content";
"TA还没有上传过付费内容" = "TA have not uploaded paid content";
"收银台"= "Cashier";
"¥"= "¥";
"立即支付"= "Pay immediately";
"余额"= "Balance";
"支付"= "Pay";
"支付成功"= "Payment successful";
"评价视频"= "Evaluate video";
"极不满意"= "Very dissatisfied";
"不满意"= "Dissatisfied";
"满意"= "Satisfy";
"很满意"= "Very satisfied";
"非常满意"= "Very satisfied";
"视频详情"= "Video details";
"观看视频"= "Watch video";
"立即购买"= "Buy now";
"购买付费内容"= "Purchasing Paid Content";
"选集"= "Selections";
"时长:"= "duration:";
"集"= "sets";
"人已购买"= "people have purchased";
"视频简介"= "Video introduction";
"观众评价"= "Audience comments";
"人已评价"= "people have rated";
"播放"= "play";
"试看"= "Preview";
"作者信息"= "Author information";
"作者" = "Author";
"上传视频"= "Upload video";
"单视频"= "Single video";
"多视频"= "Multiple videos";
"请选择视频"= "Please select a video";
"请先上传视频"= "Please upload the video first";
"请添加视频标题"= "Please add video title";
"新增视频"= "Add video";
"请重新选择(iCloud视频请先在本地相册下载后上传)"= "Please re-select (iCloud videos, please download from the local album first and then upload)";
"小标题"= "Subtitle";
"最多15个字"= "Up to 15 characters";
"上传付费内容"= "Upload paid content";
"提交审核"= "Submit review";
"商品类别"= "Product category";
"请选择"= "Please select";
"内容标题"= "Content title";
"建议填写关键词、属性词、营销词等，不超过15个字"= "It is recommended to fill in keywords, attribute words, marketing words, etc., no more than 15 characters";
"内容封面"= "Content cover";
"内容简介"= "Brief introduction";
"建议通过视频内容、故事情节、学习技巧等方面说明内容特点,不超过100字"= "It is recommended to explain the characteristics of the content through video content, storyline, learning skills, etc., no more than 100 words";
"个人介绍"= "Self introduction";
"建议填写作者身份、取得的成就、过往作品等，不超过50个字"= "It is recommended to fill in the author's identity, achievements, past works, etc., no more than 50 characters";
"内容价格"= "Content price";
"价格(元)"= "Price(￥)";
"最少1.00元"= "Minimum ¥1.00";
"元"= "￥" ;
"请选择商品类别"= "Please select a product category";
"请重新选择"= "Please choose again";
"提交失败"= "Submission Failed";
"请填写标题"= "Please fill in the title";
"选择分类"= "Choose category";
"所有分类"= "All Categories";
"请同意条款"= "Please agree to the terms";
"我已阅读并同意"= "I have read and agree";
"我知道了"= "I see";
"你还没有购买过付费内容"= "You have not purchased paid content yet";
"我购买的"= "I purchased";
"申请"= "Apply";
"我上传的"= "I uploaded";
"实名认证"= "Real-name certification";
"审核中"= "Under review";
"分享商品" = "Share products";
"审核未通过"= "Verification failed";
"小店商品"= "Shop";
"付费内容"= "Paid";
"请输入商品名称"= "Please enter product name";
"请输入付费内容名称"= "Please enter the paid content name";
"提取收益"= "Extract income";
"累计收入"= "Cumulative income";
"可提取金额"= "Extractable";
"输入提取金额"= "Enter withdrawal amount";
"价格与库存"= "Price and inventory";
"规格"= "Specification";
"单价(元)"= "Unit price (￥)";
"库存(件)"= "Inventory (pieces)";
"资质证明"= "Qualification certificate";
"选择商品类别"= "Select product category";
"商品标题"= "Product title";
"建议填写产品词、属性词、营销词等，不超过15个字"= "Key words for goods, within 15 words";
"商品视频"= "Product Video";
"商品图"= "Product picture";
"建议通过细节说明、规格、搭配、购买须知等多方面描述商品的详情，不超过300个字"= "It is recommended to describe the details of the product through detailed descriptions, specifications, matching, purchase instructions, etc., no more than 300 words";
"详情图"= "Detailed pictures";
"最多15个字符"= "Up to 15 characters";
"最多9999999件"= "Up to 9999999 pieces";
"上传图片"= "Upload image";
"删除规格"= "Delete specification";
"编辑商品"= "Edit Product";
"添加商品"= "Adding goods";
"编辑规格"= "Edit specifications";
"新增规格"= "New specifications";
"运费设置"= "Freight settings";
"包邮"= "Free shipping";
"运费(元)"= "Freight (￥)";
"请先上传图片信息"= "Please upload the picture information first";
"请先上传规格图片"= "Please upload the specification picture first";
"图片错误"= "Picture error";
"编辑收货地址"= "Edit shipping address";
"这是第"= "This is the first";
"行"= "OK";
"选择地区"= "Select Region";
"在售"= "On sale";
"审核"= "Review";
"已下架"= "Removed";
"你还没有相关商品"= "You don’t have any related products yet";
"商品管理"= "Goods management";
"上架"= "On shelves";
"下架"= "Removal";
"编辑"= "Edit";
"站内商品"= "Site goods";
"站外商品"= "Off-site goods";
"确定删除商品?"= "Confirm to delete product?";
"确定上架商品?"= "Confirm products to be put on shelves?";
"确定下架商品?"= "Are you sure you want to remove the product?";
"已拒绝"= "Rejected";
"已售"= "Sold ";
"件"= " piece";
"退款详情"= "Refund details";
"同意"= "Agree";
"协商历史"= "Negotiation history";
"退款信息"= "Refund information";
"订单状态:"= "Order Status:";
"退款方式:"= "Refund Method:";
"退款金额:"= "Refund Amount:";
"退款原因:"= "Reason for return:";
"申请时间:"= "Application time:";
"退款单号:"= "Refund Order ID:";
"问题描述:"= "Problem description:";
"仅退款"= "Only refund";
"退货退款"= "Return and refund";
"联系买家"= "Contact buyer";
"拨打电话"= "Dial number";
"选择拒绝前请先尝试与买家进行充分沟通"= "Please try to fully communicate with the buyer before choosing to reject";
"继续"= "continue";
"拒绝退款"= "Refusal to refund";
"拒绝原因"= "Denial reason";
"拒绝原因:"= "Denial reason:" ;
"拒绝描述:"= "Reject description:";
"提交申请"= "submit application";
"请填写详细原因与证据描述,有助于快速解决你的问题"= "Please fill in the detailed reason and evidence description, which will help to solve your problem quickly";
"完成"= "Done";
"运费"= "Freight";
"需付款/实付款"= "Payment Required / Actual Payment";
"订单详情"= "Order details";
"复制地址"= "Copy address";
"填写物流单号"= "Please fill in the logistics order number";
"选择物流公司"= "Choose a logistics company";
"确认发货"= "Confirm Shipment";
"订单编号:"= "Order ID:";
"订单编号"= "Order ID";
"下单时间"= "Order time";
"支付方式"= "Payment method";
"支付时间"= "Payment time";
"复制"= "Copy";
"删除订单"= "Delete order";
"你还没有相关订单"= "You have no related orders";
"退款订单"= "Refund order";
"已发货订单"= "Order shipped";
"已签收订单"= "Order signed";
"已完成订单"= "Order completed";
"已关闭订单"= "Order closed";
"全部订单"= "All orders";
"待发货"= "To be delivered";
"等待退款"= "Waiting for refund";
"待支付"= "To be paid";
"其他"= "Other";
"订单管理"= "Order management";
"确定删除订单?"= "Are you sure to delete the order?";
"查看物流"= "View logistics";
"已发货"= "Shipped";
"已退款"= "refunded";
"已签收"= "Received";
"交易成功"= "Transaction Successful";
"已关闭"= "Closed";
"订单号:"= "Order ID:";
"件商品"= "items";
"买家昵称:"= "Buyer name:";
"去发货"= "Go to ship";
"物流信息"= "Logistics Information";
"无相关结算记录"= "No relevant settlement records";
"账单管理"= "Bill management";
"小店账户余额"= "Store balance";
"累计收入 (元)"= "Cumulative income (￥)";
"交易中"= "In transaction";
"结算记录"= "Settlement Record";
"提取记录"= "Extract records";
"小店详情"= "Shop details";
"基础信息"= "Basic information";
"查看资质证明"= "View qualification certificate";
"已认证"= "Authenticated";
"店铺保证金"= "Store deposit";
"已缴纳"= "Paid";
"预览商品"= "Preview Product";
"预览不支持该功能"= "Preview does not support this feature";
"店铺"= "Shop";
"客服"= "Service";
"个人主页"= "Homepage";
"此商品属于站外链接商品,可能存在一定风险,请谨慎购买"= "This product is an off-site linked product and may involve certain risks. Please purchase with caution";
"继续购买"= "Continue to buy";
"全部评价"= "All reviews";
"有图"= "With pictures";
"追加"= "Append";
"追评"= "Review";
"平台介入"= "Platform";
"申诉原因"= "Reason for appeal";
"请填写详细原因与证据描述,有助于快速解决你的问题"= "Please fill in the detailed reason and evidence description, which will help to solve your problem quickly";
"退款原因"= "Reason for return";
"重新申请"= "Re-apply";
"取消申请"= "Cancel";
"取消退款申请后，该订单无法再次申请退款,确定取消?"= "After canceling the refund application, the order cannot be refunded again. Are you sure you want to cancel?";
"退款申请"= "Refund application";
"退款方式"= "Refund method";
"退款原因"= "Reason for return";
"退款金额"= "Refund amount";
"请填写10个字以上的问题描述以便我们提供更好的帮助"= "Please fill in a problem description of more than 10 characters so that we can provide better help";
"发表追评"= "Follow-up comment";
"评价内容不能为空" = "Evaluation content cannot be empty";
"发布"= "Release";
"已经用了一段时间了,有更多宝贝使用心得？分享给想买的他们吧"= "I have been using it for a while, do you have more experience in using it? Share it with those who want to buy it";
"添加图片"= "add pictures";
"添加视频"= "Add Video";
"发表评价"= "Leave a review";
"请填写评价内容"= "Please fill in the evaluation content";
"描述相符"= "Matches description";
"宝贝满足你的期望吗？说说它的优点和不足吧"= "Did the baby meet your expectations? Tell me about its strengths and weaknesses";
"公开"= "Public";
"匿名"= "Anonymous";
"店铺评价"= "Store evaluation";
"送货服务"= "Delivery service";
"服务态度"= "Service attitude";
"确认订单"= "Confirm order";
"应付:"= "Payable:";
"提交订单"= "Submit order";
"请设置收货人信息"= "Please set consignee information";
"规格:"= "Specification:";
"邮费"= "Postage";
"总计"= "Total";
"买家留言:"= "Buyer message:";
"建议留言前先与商家沟通确认"= "It is recommended to communicate with the merchant to confirm before leaving a message";
"我的地址"= "My address";
"最小数量为1件"= "Minimum quantity is 1 piece";
"订单提交成功"= "Orders submitted successfully";
"库存:"= "Inventory:";
"已选:"= "Selected:";
"服务保障"= "Service guarantee";
"已交保证金"= "Deposit paid";
"商家已向平台缴纳保证金，交易产生纠纷时用于保证买家的权益"= "The merchant has paid a deposit to the platform, which is used to protect the buyer's rights when there is a dispute in the transaction";
"资质保障"= "Quality assurance";
"商家已向平台提交经营执照、许可资质等相关资质证明"= "The merchant has submitted relevant qualification certificates such as business license and license qualification to the platform";
"运费:"= "Freight:";
"地点"= "Place";
"请选择规格"= "Please select specifications";
"服务"= "Serve";
"进店逛逛"= "Go to shop";
"总销量"= "Total volume";
"商品质量"= "Product quality";
"服务态度"= "Service attitude";
"物流服务"= "Logistics services";
"宝贝评价"= "Goods evaluation";
"宝贝详情"= "Goods details";
"还没有收到任何评价"= "No reviews received yet";
"查看更多评价"= "View more reviews";
"经营类目设置"= "Business category settings";
"请选择经营类目"= "Please select a business category";
"请谨慎选择，主营类目设置成功后将不可更改"= "Please choose carefully, after the main category is set successfully, it cannot be changed";
"选择主营类目"= "Select the main category";
"主营类目最多可选择三项"= "Main business category can choose up to three items";
"开店保证金"= "Store opening deposit";
"需要缴纳金额"= "Amount to be paid";
"保证金说明"= "Margin Description";
"2、用户撤销"= "2. User cancellation";
"时，可申请退还保证金。"= ", you can apply for a refund of the security deposit.";
"1、保证金由商户交由平台暂时保管，用于约束商户行为，保障消 费者权益。"= "1. The security deposit is temporarily kept by the platform by the merchant, which is used to restrain the behavior of the merchant and protect the rights and interests of consumers.";
"3、当用户开通店铺后，若存在欺骗消费者、售卖假冒伪劣产品等一切违反国家法律法规以及平台规定的等行为，平台有权强制关闭店铺，保证金不予退还。"= "3. After the user opens the store, if there is any behavior that violates national laws and regulations and platform regulations, such as deceiving consumers, selling fake and shoddy products, etc., the platform has the right to forcibly close the store, and the deposit will not be refunded.";
"4、店铺保证金最终解释权归平台所有。"= "4. The final interpretation right of store deposit belongs to the platform.";
"确认缴纳"= "Confirm payment";
"开通小店"= "Open a store";
"信息确认"= "Information confirmed";
"账号ID:"= "Account ID:";
"姓名:"= "Name:";
"身份证号:"= "ID number:";
"1.请确认上述账号ID、姓名、身份证号为本人信息。若非本人信息请停止操作。"= "1. Please confirm that the above account ID, name, and ID number are your own information. If it is not your own information, please stop operating.";
"2.您同意授权本平台公司获取并保存账号ID、姓名、身份证号个人信息用于身份认证备案。"= "2. You agree to authorize the platform company to obtain and store account ID, name, and ID number personal information for identity authentication and filing.";
"3.您承诺通过本平台小店开展的经营活动为根据法律法规规定不需要办理市场主体登记的经营行为。"= "3. You promise that the business activities carried out through the store on this platform are business behaviors that do not need to be registered as market entities in accordance with laws and regulations.";
"经营类目"= "Business category";
"经营者联系方式"= "Operator Contact Information";
"联系人"= "Contact";
"手机号"= "Phone number";
"所在地区"= "Region";
"详细地址"= "Detailed address";
"客服信息"= "Customer service";
"(不填默认设置为联系人手机号)"= "(Leave blank for contact information)";
"退货信息"= "Return information";
"(不填默认设置为经营地址、电话信息)"= "(Leave blank for operator information)";
"营业执照"= "Business license";
"其他证件"= "Other documents";
"缴纳保证金"= "Pay deposit";
"已选择"= "chosen";
"客服电话"= "consumer hotline";
"收货人"= "Receiver";
"收货人手机号"= "Consignee phone number";
"信息审核中..."= "Information review...";
"身份信息审核未通过"= "Identity information review failed";
"3个工作日内会有审核结果,请耐心等待"= "There will be an audit result within 3 working days, please wait patiently";
"证件照片信息模糊，请上传清晰照片"= "The ID photo information is blurry, please upload a clear photo";
"重新认证"= "Recertification";
"近期暂无浏览记录"= "No recent browsing history";
"浏览记录"= "Browsing history";
"管理"= "Manage";
"全选"= "Select all";
"该商品已下架"= "This item has been discontinued";
"请先选择要删除内容"= "Please select the content to delete first";
"请输入姓名"= "Please enter your name";
"请输入联系电话"= "Please type your phone number";
"请输入所在地区"= "Please enter your region";
"请输入详细地址"= "Please enter the detailed address";
"还没有退货地址"= "No return address yet";
"新增收货地址"= "Add shipping address";
"默认"= "Sefault";
"账户余额"= "Account balance";
"提取余额"= "Withdraw balance";
"提现记录"= "Withdrawals record";
"账户余额 (元)"= "Account Balance (￥)";
"退款记录"= "Refund Record";
"买家留言"= "Buyer Message";
"退款"= "Refund";
"需付款/实付款"= "Payment required/Actual payment";
"联系客服"= "Contact customer service";
"取消订单"= "Cancel order";
"评价"= "Evaluate";
"已评价" = "Rated";
"追加评价"= "Add evaluation";
"待付款"= "Pending payment";
"待收货"= "Awaiting receipt";
"待评价"= "Be evaluated";
"退款"= "Refund";
"确定删除订单？"= "Are you sure you want to delete the order?";
"确定取消订单？"= "Are you sure you want to cancel the order?";
"确定取消订单?"= "Are you sure you want to cancel the order?";
"确认收货"= "Confirm receipt";
"确定已收到商品？"= "Are you sure you have received the item?";
"件商品,合计¥"= "items, total ¥";
"暂无信息"= "no information";
"查看全部订单"= "View all orders";
"地址管理"= "Address management";
"退货地址管理"= "Return address management";
"我的订单"= "My order";
"我要开店"= "I want to open a store";
"开小店,来赚钱"= "Open a small shop to make money";
"(买家端)"= "(Buyer side)";
"(卖家端)"= "(Seller side)";
"展开更多回复"= "Expand more replies";
"收起"= "put away";
"评论成功"= "Comment successful";
"确定删除此条动态？"= "Are you sure to delete this post?";
"删除成功"= "Successfully deleted";
"删除失败"= "Failed to delete";
"全部评论"= "All comments";
"要删除此视频吗？"= "Do you want to delete this video?";
"推荐"= "Recommend";
"最新"= "Newest";
"不能给自己的动态点赞"= "Can't give likes to my dynamic";
"点赞"= "Like";
"预览"= "Preview";
"确定放弃当前编辑内容？"= "Are you sure you want to discard the current editing content?";
"发布动态"= "Publish updates";
"分享你的想法..."= "Share your thoughts...";
"所在位置"= "Location";
"关联技能"= "Associated skills";
"（未开启）"= "(Unopened)";
"录音"= "Recording";
"点击可录音"= "Click to record";
"点击可暂停"= "Click to Pause";
"搜索分类"= "Search Category";
"关联商品"= "Related products";
"确认发布"= "Confirm release";
"去看看"= "Go and see";
"查看同款商品"= "View the same item";
"您当前余额不足无法观看"= "Your current balance is insufficient to watch";
"动作" = "Action";
"贴纸"= "Sticker";
"哈哈镜"= "Distorting mirror";
"基础面具"= "Basic mask";
"基础" = "Basic";
"基础贴纸"= "Basic stickers";
"高级面具"= "Advanced mask";
"高级贴纸"= "Premium stickers";
"水印"= "Watermark";
"镜像脸"= "Mirror face";
"片段脸"= "Fragment face";
"水面倒影"= "Water reflection";
"螺旋镜面"= "Spiral mirror";
"鱼眼相机"= "Fisheye camera";
"左右镜像"= "Mirror left and right";
"原图"= "Original image";

"美白"= "Whitening";
"磨皮"= "Dermabrasion";
"红润"= "Rosy";
"亮度"= "Brightness";
"美妆"=  "Makeups";
"浪漫"= "Romantic";
"清新"= "Fresh";
"唯美"= "Beautiful";
"粉嫩"= "Pink";
"怀旧"= "Nostalgia";
"蓝调"= "Blues";
"清凉"= "Cool";
"日系"= "Japanese";
"城市"= "City";
"初恋"= "First love";
"初心"= "First heart";
"单色"= "Monochrome";
"反差色"= "Contrasting color";
"琥珀"= "Amber";
"美味"= "Tasty";
"蜜桃粉"= "Peach powder";
"奶茶"= "Milk tea";
"拍立得"= "Polaroid";
"乌托邦"= "Utopia";
"西柚"= "Grapefruit";
"日杂"= "Miscellaneous";
"黑猫"= "Black cat";
"黑白"= "Black and white";
"布鲁克林"= "Brooklyn";
"平静"= "Calm";
"冷酷"= "Cold";
"凯文"= "Kevin";
"恋爱"= "In love";

"大眼"= "Big eyes";
"瘦脸"= "Face-lift";
"嘴型"= "Mouth type";
"瘦鼻"= "Thin nose";
"下巴"= "Jaw";
"额头"= "Forehead";
"眉毛"= "Eyebrow";
"眼角"= "Eye corner";
"眼距"= "Eye distance";
"开眼角"= "Open eyes";
"削脸"= "Cut face";
"长鼻"= "Proboscis";

"抖动"= "Jitter";
"闪白"= "Flash white";
"毛刺"= "Glitch";
"幻觉"= "Illusion";
"马赛克"= "Mosaic";
"圆形马赛克"= "Round mosaic";
"三角马赛克"= "Triangle mosaic";
"六边马赛克"= "Six sided mosaic";

"优雅"= "Grace";
"精致"= "Exquisite";
"可爱"= "Cute";
"自然"= "Nature";
"网红"= "Internet celebrity";
"脱俗"= "Refinement";
"高雅"= "Elegant";
"标准"= "Standard";

"请抬头" = "Please look up";
"请张嘴" = "Please open your mouth";
"请眨眼" = "Please blink";
"抬头" = "Look up";
"张嘴" = "Open your mouth";
"眨眼" = "Blink";
"睫毛" = "Eyelash";
"唇彩" = "Lip gloss";
"腮红" = "Blush";

"商城"= "Mall";
"动态"= "Dynamic";
"推荐主播"= "Recommended anchor";
"查看更多"= "see more";
"热门主播"= "Popular anchor";
"话题"= "Topic";
"全部话题"= "All topics";
"搜索您感兴趣的话题"= "Search topics";
"参与话题，让更多人看到"= "Participate in the topic";
"请输入您要搜索的商品名称"= "Please enter the product name";
"钱包"= "Wallet";
"明细"= "Details";
"道具"= "Props";
"夜间模式"= "Night Mode";
"确定退出登录吗？"= "Are you sure you want to log out?";
"版本更新"= "Version Update";
"暂不更新"= "Not updated yet";
"立即使用"= "Use immediately";
"说说你的想法..."= "Tell me what you think...";
"动态详情"= "Dynamic details";
"新品"= "New product";
"销量"= "Sales";
"价格"= "price";
"资料"= "Data";
"映票贡献榜"= "Contribution list";
"个性签名"= "Signature";
"个人信息"= "Personal information";
"你还没有收到印象哦"= "You haven't received the impression yet";
"我的明细"= "My Details";
"在线商城"= "online shopping mall";
" 请输入您的手机号"= "Please enter phone number";
"请输入您的手机号"= "Please enter phone number";
"链接不存在"= "Link does not exist";
"请选择支付方式"= "Select payment method";
"请选择充值金额"= "Select amount";
"已阅读并同意"= "Read and agree";
"《用户充值协议》"= "User Recharge Agreement";
"美型"= "Beautiful";
"一键美颜"= "One-click beauty";
"滤镜"= "Filter";
"普通房间"= "Ordinary";
"密码房间"= "Password";
"门票房间"= "Ticket";
"计时房间"= "Timed";
"镜像"= "Mirror";
"暂时没有主播"= "Temporarily no anchor";
"用户等级"= "User level";
"主播等级"= "Anchor level";
"添加印象"= "Add";
"*短信验证保障账户安全的同时短信费用将由平台支付"= "*SMS verification ensures account security and SMS fees will be paid by the platform";
"暂不使用"= "Think again";
"已通过"= "passed";
"未通过"= "Failed";
"注销条件"= "Logout condition";
"满足以下条件，才能注销当前账户"= "The current account can only be canceled if the following conditions are met";
"注销账号"= "Logout";
"确认注销"= "Confirm logout";
"点击确认注销后,账号将立即注销"= "After clicking to confirm the cancellation, the account will be canceled immediately";
"每日任务"= "Daily tasks";
"你还没有视频作品"= "You have no video works yet";
"赶快去拍摄上传吧"= "Hurry up and shoot and upload";
"TA还没有视频作品"= "TA has no video works yet";
"你赞过的作品都会放到这里" = "The works you like will be placed here";
"暂无动态"= "No activity yet";
"快去分享一下你的心情吧"= "Go and share your feelings";
"TA还没有发布过动态"= "TA hasn't posted anything yet";
"去推荐页看看吧"= "Go to the recommendation page and have a look";
"我的收藏"= "My collection";
"暂无收藏"= "No favorites yet";
"还没有收货地址"= "No shipping address yet";
"退货"= "return the goods";
"姓名"= "Name";
"联系电话"= "Contact number";
"设为默认地址"= "Set as  default address";
"保存并使用"= "Save and use";
"无相关退款记录"= "No related refund record";
"立即申请"= "Apply now";
"我的直播间"= "My live room";
"我的房间"= "My room";
"管理员"= "Administrator";
"禁言用户"= "Banned user";
"拉黑用户"= "Block user";
"的直播间"= "is live room";
"列表"= " list";
"没有被"= "not been";
"解除禁言"= "Unban";
"解除拉黑"= "Unblock";
" 解除拉黑"= " Unblock";
"是否解除对该用户的踢出"= "Whether to cancel the kick of the user";
"是否解除对该用户的禁言"= "Whether to unban the user";
"你还不是管理员"= "You are not an administrator";
"礼物"= "Gift";
"背包"= "Backpack";
"道具礼物说明"= "Explanation of item gift";
"幸运礼物说明"= "Lucky gift explanation";
"涂鸦"= "Graffiti";
"最少绘制"= "least drawn";
"个礼物"= "a gift";
"绘制礼物,进入涂鸦模式"= "Draw gifts and enter graffiti mode";
"余额不足"= "Insufficient balance";
"确认支付"= "Confirm payment";
"支付未开启"= "Payment not enabled";
"支付失败"= "Payment failed";
"打开“定位服务”来允许“%@”确定您的位置"= "Turn on Location Services to allow “%@” to determine your location";
"打开“定位服务”来允许“"= "Turn on \"Location Services\" to allow";
"确定您的位置"= "Confirm your location";
"关闭定位，直播不会被附近的人看到，直播间人数可能会减少，确认关闭吗？"= "Turn off the positioning, the live broadcast will not be seen by people nearby, and the number of people in the live broadcast room may decrease. Are you sure to close it?";
"坚决关闭"= "resolutely closed";
"请输入商品链接"= "Please enter the product link";
"请输入商品价格"= "Please enter the product price";
"请输入商品简介"= "Please enter product description";
"商品简介最多50字"= "Product introduction up to 50 characters";
"请上传商品封面"= "Please upload the product cover";
"网络连接断开，上传失败"= "Network disconnected, upload failed";
"请输入商品ID"= "Please enter the product ID";
"经营类别"= "Business category";
"平台商品"= "Platform goods";
"综合分"= "Score";
"代卖"= "Consignment";
"取消代卖"= "Cancel sales agency";
"确定取消代卖该商品？" = "Confirm to cancel the sale of this product?";
"内容类别"= "Content Category";
"请选择类别"= "Please select a category";
"请填写内容标题"= "Please fill in the content title";
"请上传内容封面"= "Please upload the content cover";
"请填写内容简介"= "Please fill in the content introduction";
"请填写个人介绍"= "Please fill in your personal introduction";
"请填写价格"= "Please fill in the price";
"请上传视频"= "Please upload a video";

"生成视频成功"= "Generating video successfully";
"视频发布"= "Video release";
"视频分类"= "Video category";
"添加视频描述"= "Add video description";
"请选择视频分类"= "Please select video category";
"选择视频分类"= "Select video category";
"暂无评论，快来抢沙发吧"= "No comments yet, come and grab the sofa";
"原声"= "Original Sound";
"配乐"= "Soundtrack";
"选择音乐"= "Choose music";
"热门歌曲"= "Popular song";
"请稍后."= "please wait.";
"确认使用并开拍"= "Confirm use and start shooting";
"截取所需音频片段"= "Intercept the desired audio segment";
"暂无商品"= "There are currently no products";
"我的收益"= "My income";
"提现成功"= "Withdrawal successful";
"在售商品"= "Goods on sale";
"推荐商品"= "Recommended product";
"重新上架"= "restock";
"去购买"= "to buy";
"下架后用户将无法购买此款商品 确定要下架此款商品吗？"= "Users will not be able to purchase this product after it is taken off the shelf. Are you sure you want to take this product off the shelf?";
"删除后店铺内将不再显示此款商品 确定要删除此款商品吗？"= "After deletion, this product will no longer be displayed in the store. Are you sure you want to delete this product?";
"已添加"= "Added";
"设置成功"= "Setting succeeded";
"暂无视频"= "No video yet";
"去看看其他分类的视频吧"= "Go and see videos in other categories";
"暂无此音乐类型"= "There is no such music type";
"去看看其他音乐类型吧"= "Go and see other music genres";
"当前暂无在售商品\n点击右上角添加商品到购物车"= "Currently there are no products for sale\nClick the upper right corner to add products to the shopping cart";
"当前暂无在售商品"= "Currently there are no products for sale";
"佣"= "Reward";
"可提现"= "Withdrawable";
"聊天室"= "Chatroom" ;
"开启购物车"= "Open shopping cart";
"关闭购物车"= "Close cart";
"生成视频成功"= "Generating video successfully";
"视频发布"= "Video release";
"视频分类"= "Video Category";
"添加视频描述"= "Add video description";
"暂无评论，快来抢沙发吧"= "No comments yet, hurry up and grab the sofa";
"原声"= "Original Sound";
"配乐"= "Soundtrack";
"选择音乐"= "Select Music";
"热门歌曲"= "Popular song";
"请稍后."= "please wait.";
"确认使用并开拍"= "Confirm use and start shooting";
"截取所需音频片段"= "Intercept the desired audio segment";
"暂无商品"= "There are currently no products";
"提现成功"= "Withdrawal successful";
"推荐商品"= "Recommended product";
"重新上架"= "restock";
"去购买"= "to buy";
"下架后用户将无法购买此款商品 确定要下架此款商品吗？"= "Users will not be able to purchase this product after it is taken off the shelf. Are you sure you want to take this product off the shelf?";
"删除后店铺内将不再显示此款商品 确定要删除此款商品吗？"= "After deletion, this product will no longer be displayed in the store. Are you sure you want to delete this product?";
"设置成功"= "Setting succeeded";
"暂无视频"= "No video yet";
"去看看其他分类的视频吧"= "Go and see videos in other categories";
"暂无此音乐类型"= "There is no such music type";
"去看看其他音乐类型吧"= "Go and see other music genres";
"当前暂无在售商品\n点击右上角添加商品到购物车"= "Currently there are no products for sale\nClick the upper right corner to add products to the shopping cart";
"当前暂无在售商品"= "Currently there are no products for sale";
"可提现"= "Withdrawable";
"不显示位置"= "Do not show location";
"请前往'个人中心-联系我们'中联系平台"= "Please go to 'Personal Center - Contact Us' to contact the platform";
"*商品链接(可粘贴淘宝店铺商品链接)"= "*Commodity link (you can paste Taobao store product link)";
"*商品链接"= "*Product link";
"*商品名称(最多可输入20个字)"= "*Product name (up to 20 characters)";
"*原价"= "*Original price";
"*现价"= "*Current price";
"*商品简介(最多可输入50个字)"= "*Product introduction (Enter up to 50 characters)";
"*商品图片"= "*Product picture";
"已收藏"= "Collected";
"已取消收藏"= "Unbooked";
"主播未开启连麦功能哦"= "The anchor has not turned on the mic connection function";
"请设置房间密码"= "Please set the room password";
"请输入正确的密码"= "Please enter the correct password";
"请输入价格"= "Please enter a price";
"请输入正确的门票价格"= "Please enter the correct ticket price";
"当前房间收费"= "Current Room Charges";
"分钟"= "Minute";
"付费"= "paid";
"守护了主播"= "guarded the anchor";
"平台自营"= "Platform";
"开启聊天室"= "Chat room";
"等待上麦"= "Waiting";
"退出直播间"= "Exit the live  room";
"聊天室最小化"= "Chat room minimized";
"上麦成功"= "Wheat successfully";
"您已被下麦"= "You have been dropped";
"已取消静音"= "Unmuted";
"已设置为静音"= "Already set to mute";
"取消禁麦"= "Cancel";
"禁麦"= "Ban";
"下麦"= "Exit";
"开麦"= "open wheat";
"闭麦"= "Closed wheat";
"控麦"= "Control wheat";
"您当前的顺位"= "Your current order";
"家族申请"= "Family Application";
"为保证家族申请顺利通过，请如实填写下列信息"= "In order to ensure the smooth approval of the family application, please fill in the following information truthfully";
"请输入您要创建的家族名称"= "Please enter family name";
"个人姓名"= "Personal name";
"请输入您的姓名"= "Please enter your name";
"身份证号"= "ID number";
"请输入您的身份证号码"= "please enter your identity card number";
"抽成比例"= "Rake ratio";
"请填写0-100之间的整数"= "Please fill in an integer between 0-100";
"家族简介"= "Family profile";
"请简单介绍下您的家族"= "Please introduce your family";
"证件图片"= "Certificate Picture";
"手持证件正面照"= "Handheld front view";
"手持证件背面照"= "Hand-held rear view";
"家族图片"= "Family pictures";
"提交申请"= "Submit application";
"申请认证"= "Apply for certification";
"以下信息均为必填项，为保证您的利益，请如实填写"= "The following information is required. To ensure your interests, please fill it in truthfully";
"真实姓名"= "Real name";
"请填写您的真实姓名"= "Please fill in your real name";
"手机号码"= "Mobile";
"请填写您的手机号"= "Please fill in your phone number";
"身份证号"= "ID card";
"请填写您的身份证号"= "Please fill in your ID number";
"证件正面"= "Front of ID";
"证件背面"= "Back sides of ID";
"手持证件正面照"= "Holding ID photo";
"手持证件照示例"= "Example of holding ID photo";
"提交认证"= "Submit certification";
"请完善证件信息"= "Please complete the certificate information";
"语言切换"= "Language switch";
"手机号归属地"= "Mobile phone number location";
//2.3
"虚位以待"= "Waiting for a space";
"请设置收费金额"= "Please set the charge amount";
"(收益以直播结束显示为准)"= "(Revenue is subject to the display at the end of the live broadcast)";
"收费金额"= "Charge amount" ;
"选择收费"= "Choose charge";
"我的视频"= "My video";
"我的动态"= "My News";
"选择话题"= "Select topic";
"主持"= "Host";
"全麦"= "All";
"麦"= "No.";
"赶快开启你的直播吧"= "Hurry up and start your live broadcast";
"语音直播"= "Voice live";
"当前上麦申请"= "Current application";
"送给"= "send";
"送"= "send" ;
"申请说明"= "Application Instructions";
"你没有收藏任何音乐"= "You don't have any music favorites";
"搜索歌曲名称"= "Search Song Title";
"修改成功"= "Successfully modified" ;
"请填写邀请码"= "Please fill in the invitation code";
"TA最近没有开过直播"= "TA hasn't started a live broadcast recently";
"你还没有开过直播"= "You haven't started a live broadcast yet";
"赶快去开场直播体验下吧"= "Hurry up and experience the opening live broadcast";
"当前奖池等级"= "Current Prize Pool Level";
"当前奖池金额"= "Current Prize Pool Amount";
"确定删除吗？"= "Confirm to delete?" ;
"网络请求失败"= "Network request failed";
"请检查网络链接后重试"= "Please check the network connection and try again";
"重试"= "Retry" ;
"是否要删除该账户？"= "Do you want to delete this account?";
"请输入地区名"= "Please enter the region name";
"您的登录状态失效，请重新登录！"= "Your login status is invalid, please log in again!";
"家族名称" = "Family name";
"请输入家族名称"= "Please enter the family name";
"请输入身份证号码"= "Please enter your ID number";
"请输入抽成比例"= "Please enter the ratio";
"请输入家族简介"= "Please enter a family profile";
"推荐话题"= "Recommended topics";
"只有后置摄像头才能开启闪光灯"= "Only the rear camera can turn on the flash";
"中奖记录"= "Winning Record";
"游戏规则"= "Game rules" ;
"转"= "Turn" ;
"次"= "times" ;
"对方主播接受了您的连麦请求，开始连麦"= "The host of the other party has accepted your mic connection request and started mic connection";
"PK请求已发送"= "PK request sent";
"您已开启镜像"= "You have turned on mirroring";
"您已关闭镜像"= "You have turned off mirroring";
"正在下载视频"= "Downloading video";
"请选择送的人"= "Please choose the person to send";
"直播间开启期间不可使用该操作"= "This operation cannot be used when the live broadcast room is open";
"上麦被拒绝"= "Wheat was rejected";
"没有更多视频"= "No more videos";
"*商品链接（可粘贴其它商城商品链接）"= "*Commodity link (you can paste other mall product links)";
"没有相关商品"= "No related products";
"本房间为密码房间，请输入密码"= "This room is a password room, please enter the password";
"上麦互动"= "Interactive";
"申请上麦"= "Apply";
"上麦"= "Agree";
"取消排队"= "Cancel queue";
"修改所在地"= "Modify location";
"人看过"= "people watched";
"确认添加商品"= "Confirm to add goods";
"我的商品"= "My products";
"你还没有发布过付费内容"= "You haven't posted paid content yet";
"你还没有发布过动态"= "You haven't posted anything yet";
"倒计时"= "Countdown";
"已缴纳保证金"= "Security Deposit Paid";
"大眼"= "big eyes" ;
"瘦脸"= "face-lift";
"嘴形"= "mouth shape";
"瘦鼻"= "thin nose";
"下巴"= "jaw" ;
"额头"= "forehead";
"眉毛"= "Eyebrow" ;
"眼角"= "eye corner";
"眼距"= "eye distance";
"开眼角"= "open eyes";
"削脸"= "cut face";
"长鼻"= "proboscis";
"请输入房间密码"= "Please enter the room code";
"移除"= "Remove";
"展示"= "Display" ;
"查看付费内容"= "View paid content";
"的直播间送出"= "Sent from the live broadcast room";
"在"= "exist" ;
"查看商品详情"= "View product details";
"对方刚开播，请稍后连麦"= "The other party just started broadcasting, please connect to the mic later";
"订单消息"= "Order Message";
"下载失败"= "download failed" ;
"请稍后重试"= "Please try again later" ;
"他的小店"= "His little shop";
"创建家族"= "Creating a family";
"第七天"= "the seventh day";
"连续签到领取额外奖励"= "Sign in continuously to receive additional rewards";
"第"= "";
"下一个"= "Next" ;
"基本信息"= "Basic information" ;
"上传身份信息"= "Upload identity information";
"手持证件照"= "Holding ID photo";
"(手持证件照示例)"= "(Example of holding ID photo)";
"请仔细阅读用户协议并勾选"= "Please read the user agreement carefully and tick";
"*短信验证保障账户安全的同时短信费用将由平台支付"= "*SMS verification ensures account security and SMS fees will be paid by the platform";
"确认充值"= "Confirm payment";
"请输入签名"= "Please enter your signature";
"收藏"= "Collect" ;
"关闭定位，直播不会被附近的人看到，直播间人数可能会减少，确认关闭吗？"= "Turn off the positioning, the live broadcast will not be seen by people nearby, and the number of people in the live broadcast room may decrease. Are you sure to close it?";
"坚决关闭"= "resolutely closed";
"开定位"= "Open positioning";
"发送了"= "sent";
"道具礼物"= "prop gift";
"个人中心"= "Personal Center";
"还没有收藏任何音乐"= "I haven't favorited any music yet";
"你还没有收藏任何音乐" = "You haven't collected any music yet";
"账号已被封禁"= "Account has been banned";
"封禁说明:"= "Ban description:";
"封禁时长:"= "Block duration:";
"确认"= "Sure" ;
"管理员正在飞速审核中~"= "Administrator is under rapid review~";
"您的审核没有通过，请重新提交"= "Review failed, please resubmit";
"重新提交"= "resubmit" ;
"金额¥"= "Amount ¥";
"点击查看"= "Click to view";
"请前往个人中心-联系我们中联系平台"= "Please go to the personal center-contact us to contact the platform";
"请填写商品标题"= "Please fill in the product title";
"请填写商品详情"= "Please fill in the product details";
"请填写规格名称"= "Please fill in the specification name";
"请填写库存"= "Please fill in the inventory";
"请填写单价"= "Please fill in the unit price";
"请输入运费"= "Please enter shipping cost";
"开通店铺保证金不足\n请先进行充值"= "Insufficient balance\n please recharge first";
"立刻充值"= "Recharge now";
"已经是最新版本"= "Already the latest version";
"点击跳转第三方应用"= "Click to jump to the third-party application";
"TA还没有任何粉丝"= "TA doesn't have any fans yet";
"录音时间太短"= "Recording time is too short";
"暂无数据"= "No data" ;
"请选择印象"= "Please select an impression";
"空空如也"= "Empty and empty";
"再接再厉"= "Keep it up";
"确认删除?"= "Confirm deletion?" ;
"启动失败"= "Startup failed" ;
"喜中"= "Happy";
"获得"= "get" ;
"倍"= "fold";
"视频保存成功"= "Video saved successfully";
"视频保存失败"= "Failed to save video";
"连麦时无法开启伴奏"= "The accompaniment cannot be turned on when connecting mics";
"连麦时需要关闭背景音乐"= "Background music needs to be turned off when connecting mics";
"游戏状态下不能进行连麦哦"= "Unable to connect mics in the game state";
"前去购买"= "Go and buy";



//新添
"我的关注"= "My focus";
"还没有聊天室开启喔~"= "No chat room has been opened yet~";
"人正在直播"= "People are live streaming";
"全部查看"= "View all";
"热门话题"= "Hot topic";
"查看更多 >"= "See more >";
"更换"= "Replace";
"认证"= "certification";
"前去认证"= "Go to authentication";
"关注了你"= "followed you";
"搜索内容..."= "Search content...";
"多人PK"= "Multiplayer PK";
"组队PK"= "Team PK";
"PK时长300s"= "PK duration 300s";
"超过两位主播可开启组队PK"= "More than two anchors can start team PK";
"立即开始"= "start immediately";
"开始PK"= "Start PK";
"换队伍"= "Change teams";
"分组至少有1人"= "Group with at least 1 person";
"请输入您要搜索得昵称或ID"= "Please enter the nickname or ID you want to search for";
"评论了您的作品"= "commented on your work";
"赞了您的评论"= "liked your comment";
"赞了您的作品"= "liked your work";
"@了你"= "@ you in";
"青少年模式"= "Teen Mode";
"开启青少年模式"= "Turn on teen mode";
"关闭青少年模式"= "Turn off teen mode";
"修改密码"= "Change password";
"青少年模式已开启"= "Youth mode is on";
"青少年模式未开启"= "Youth mode is disabled";
"设置密码"= "Set password";
"请设置新密码"= "Please set a new password";
"输入密码"= "Enter password";
"密码设置成功，请记住您设置的密码"= "The password is set successfully, please remember the password you set";
"重新设置"= "Reset";
"请输入当前密码"= "Please enter the current password";
"请输入新的密码"= "Please enter a new password";
"请确定新的密码"= "Please confirm the new password";
"确定修改"= "Confirm modification";
"输入密码不一致，请重新输入"= "Input passwords are inconsistent, please re-enter";
"开启青少年模式，需先设置独立密码"= "Enable the youth mode, you need to set an independent password first";
"无法进行充值、打赏等操作"= "Unable to perform operations such as recharge and tipping";
"自动开启时间锁，每天使用时长不超过 40分钟，每日晚22时至次日6时期间 无法使用"= "Automatically turn on the time lock, the daily use time does not exceed 40 minutes, and it cannot be used from 22:00 every night to 6:00 the next day";
"知道了"= "Knew";
"去关闭"= "Go off";
"青少年模式下不支持该功能" = "This feature is not supported in youth mode";
"未成年人禁止充值消费"= "Not for use by minors";
"《用户充值协议》"= "《User Recharge Agreement》";
"清空"= "Clear";
"确定清空所有中奖记录?"= "Are you sure you want to clear all winning records?";
//jia
"充值奖励"= "Recharge Rewards";
"直播明细"= "Live Details";
"查看我的明细"= "View details";
"认证"= "Certification";
"前去认证"= "Go for certification";
"当前暂无未读消息"= "There are currently no unread messages";
"会员专享礼包来了"= "Member-exclusive gift pack is here";
"到期时间"= "Expire date";
"续费会员"= "Renewal membership";
"首充豪华大礼包"= "First charge luxury gift package";
"立即充值"= "Recharge Now";
"用户" = "User";
"暂无更多视频哦~" = "No more videos available at the moment~";
"您还未登录" = "You have not logged in yet";
"登录账号，查看精彩内容" = "Log in to your account and view exciting content";
"进入青少年模式＞" = "Enter teen mode＞";
"声音音效" = "Sound effects";
"已忽略未读消息" = "Unread messages ignored";
"Hi~欢迎体验系统" = "Hi~Welcome to experience the system";
"进入小店" = "Enter the store";
"全部分类" = "All categories";
"直播小店" = "Live store";
"分享佣金设置" = "Share reward settings";
"佣金(元)" = "Commission(￥)";
"在线观众" = "Online viewers";
"人在看" = "Viewers";
"手游直播" = "Game live";
"请选择直播内容" = "Select live content";
"大咖榜" = "BigShots";
"土豪榜" = "Rich List";
"官方直营 · 正牌保障" = "Official Direct Sales · Genuine Guarantee";
"直播购" = "Live purchase";
"私信对话平台已关闭,暂时无法使用" = "The private message conversation platform has been closed and is temporarily unavailable for use";
"开启定位服务" = "Enable location services";
"设置" = "Set up";
"星球探宝" = "Treasure hunt";
"幸运大转盘" = "Lucky turn";
"获得的礼物将存放在礼物背包中" = "Gifts obtained are stored in the backpack";
"玩法介绍" = "Game rules";
"参与记录" = "Game records";
"单击" = "click";
"连击" = "batter";
"很遗憾\n大奖与您擦肩而过" ="Unfortunately\nThe grand prize passes you by";
"跳过动画" = "Skip animation";
"奖品累计价值" = "Total value";
"用户信息" = "User";
"排名" = "Rank";
"小窗模式" = "Small window mode";
"我的喜欢" = "My like";
"申请认证" = "Apply for certification";
"购物车" = "Shopping cart";
"分享到动态" = "Share to news";
"分享给好友" = "Share with friends";
"分享赚佣金¥" = "Share and earn commission¥";
"[商品]" = "[commodity]";
"[图片]" = "[picture]";
"[语音]" = "[voice]";
"[视频]" = "[video]";
"[动画表情]" = "[animated expression]";
"[文件]" = "[document]";
"[位置]" = "[Location]";
"为了向您推荐附近直播和视频，帮助您在使用相关服务时展示定位，开启定位，发现身边生活" = "In order to recommend nearby live broadcasts and videos to you, help you display your positioning when using related services, enable positioning, and discover the life around you";
"用户连麦" = "Link mic";
"规则说明" = "Rule description";
"中奖记录" = "Winning record";
"暂无中奖记录" = "No winning record yet";
"您还没有中奖记录哦" = "You have no winning record yet";
"大表情" = "Emoji";
"回复了您的评论" = "replied to your comment";
"评论了您的作品" = "commented on your work";
"保存到本地相册" = "Save to local  album";
"守护榜" = "Guardian list";
"支付宝未配置" = "Alipay is not configured";
"请选择要删除的商品" = "Please select the product you want to delete";
"确定删除所选内容?" = "Confirm deletion of selection?";
"《用户充值协议》" = "User recharge agreement";
"撤回" = "Withdraw";
"你撤回了一条消息" = "Youhave withdrew a message";
"对方撤回了一条消息"  = "The other party withdrew a message";
"该消息发送时间已超过两分钟,无法撤回" = "This message has been sent for more than two minutes and cannot be sent";
"按住说话" = "Press and hold to speak";
"松开结束" = "Unpress end";
"说话时间太短" = "Recording time is too short";
"Apple地图" = "Apple Maps";
"首充机会已用完" = "The first recharge opportunity has been used up";
"广告" = "Advertising";
"已取消支付" = "Payment canceled";
"支付取消" = "Payment canceled";
"禁言成功" = "Banned successfully";
"被跟买得" = "The follower bought";
"青少年模式下暂无商品信息" = "No product information in youth mode";
"相机" = "Photograph";
"直播类型：" = "Live type:";
"语音聊天室" = "Voice chat room";
"视频聊天室" = "Video chat room";
