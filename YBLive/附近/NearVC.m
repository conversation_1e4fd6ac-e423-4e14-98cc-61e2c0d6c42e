//
//  NearVC.m
//  YBLive
//
//  Created by <PERSON><PERSON><PERSON> on 2018/2/1.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "NearVC.h"

#import "LivePlay.h"
#import "AppDelegate.h"
#import "HotCollectionViewCell.h"
#import "searchVC.h"
#import "hotModel.h"
#import "ZYTabBar.h"
#import "MessageListVC.h"
#import "UserRoomViewController.h"
#import "THeader.h"

@import CoreLocation;

@interface NearVC ()<UICollectionViewDataSource,UICollectionViewDelegate,CLLocationManagerDelegate,UICollectionViewDelegateFlowLayout,V2TIMConversationListener>
{
    CLLocationManager   *_NearByManager;
    UIView *nothingView;
    NSDictionary *selectedDic;
    int selected;
    UIAlertView *coastAlert;
    UIAlertView *secretAlert;
    NSString *type_val;//
    NSString *livetype;//
    int page;
    UIView *navi;
    CGFloat oldOffset;
    YBNoWordView *noNetwork;
    NSString *_sdkType;//0-金山  1-腾讯

    UIView *headView;
    UIButton *liveBtn;
    UIButton *chatBtn;
    NSString *roomtype;
    
    UIAlertController *md5AlertController;
}
@property(nonatomic,strong)NSMutableArray *allArray;
@property(nonatomic,strong)UICollectionView *collectionView;
@property(nonatomic,strong)NSString *MD5;

@end

@implementation NearVC

-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [self pullInternetforNew];
    
    [self getUnreadCount];
}
-(void)getUnreadCount{
    [self labeiHid];
}
- (void)onTotalUnreadMessageCountChanged:(UInt64)totalUnreadCount {
    [self labeiHid];
}
-(void)labeiHid{
    dispatch_queue_t queue = dispatch_queue_create("GetIMMessage", DISPATCH_QUEUE_SERIAL);
    dispatch_async(queue, ^{
        [[YBImManager shareInstance]getAllUnredNumExceptUser:nil complete:^(int allUnread) {
            unRead =allUnread;
            dispatch_async(dispatch_get_main_queue(), ^{
                label.text = [NSString stringWithFormat:@"%d",unRead];
                if ([label.text isEqual:@"0"]) {
                    label.hidden =YES;
                }else{
                    label.hidden = NO;
                }
            });
        }];
    });
}
- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.navigationController.interactivePopGestureRecognizer.delegate = nil;
    self.navigationController.navigationBar.hidden = YES;
    oldOffset = 0;
    type_val = @"0";
    livetype = @"0";
    roomtype = @"0";
    page = 1;
    self.allArray = [NSMutableArray array];
    UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
    flow.scrollDirection = UICollectionViewScrollDirectionVertical;
    flow.itemSize = CGSizeMake(_window_width/2-4.5, (_window_width/2-4.5) );
    flow.minimumLineSpacing = 3;
    flow.minimumInteritemSpacing = 3;
    flow.sectionInset = UIEdgeInsetsMake(3, 3,3, 3);

//    self.collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight+50, _window_width, _window_height-64-statusbarHeight-49-50) collectionViewLayout:flow];
    self.collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight+50, _window_width, _window_height-64-statusbarHeight-49-50) collectionViewLayout:flow];

    [self.collectionView registerNib:[UINib nibWithNibName:@"HotCollectionViewCell" bundle:nil] forCellWithReuseIdentifier:@"HotCollectionViewCELL"];
    self.collectionView.delegate =self;
    self.collectionView.dataSource = self;
    _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        page = 1;
        [self pullInternetforNew];
    }];
    _collectionView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
        page ++;
        [self pullInternetforNew];
    }];
    [self.view addSubview:self.collectionView];
    
    if (@available(iOS 11.0, *)) {
        _collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    [self createView];
    self.view.backgroundColor = [UIColor whiteColor];
    self.collectionView.backgroundColor = [UIColor whiteColor];
    
    //获取所有未读消息
    [[V2TIMManager sharedInstance] addConversationListener:self];
    [self getUnreadCount];
//    _collectionView.contentInset = UIEdgeInsetsMake(64+statusbarHeight, 0, 0, 0);
//    [self creatNavi];
    
    headView = [[UIView alloc]init];
    headView.frame = CGRectMake(0, 64+statusbarHeight, _window_width, 50);
    headView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:headView];
    NSArray *arr = @[YZMsg(@"直播"),YZMsg(@"聊天室")];
    for (int i = 0; i < arr.count; i ++) {
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(15+i*65 +i*15, 13, 57, 28);
        btn.titleLabel.font = [UIFont systemFontOfSize:10];
        [btn setTitle:arr[i] forState:0];
        [btn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
        [btn setBackgroundImage:[UIImage imageNamed:@"neartitleBg"] forState:UIControlStateSelected];
        [btn setBackgroundImage:nil forState:UIControlStateNormal];
        [btn setBackgroundColor:RGB(240, 240, 240)];
        btn.layer.cornerRadius = 14;
        btn.layer.masksToBounds = YES;
        [btn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
        [headView addSubview:btn];
        if (i == 0) {
            liveBtn = btn;
            liveBtn.selected = YES;
        }else{
            chatBtn = btn;
        }
    }

}
-(void)btnClick:(UIButton *)sender{
    if (sender == liveBtn) {
        liveBtn.selected =YES;
        chatBtn.selected = NO;
        [chatBtn setBackgroundColor:RGB(250, 250, 250)];
        roomtype = @"0";
        page = 1;
    }else{
        liveBtn.selected =NO;
        chatBtn.selected = YES;
        [liveBtn setBackgroundColor:RGB(250, 250, 250)];
        roomtype = @"1";
        page = 1;
    }
    [self pullInternetforNew];
}

-(void)createView{
    nothingView = [[UIView alloc]initWithFrame:CGRectMake(0, 200, _window_width, 40)];
    nothingView.hidden = YES;
    nothingView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:nothingView];
    UILabel *label1 = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, _window_width, 20)];
    label1.font = [UIFont systemFontOfSize:14];
    label1.text = YZMsg(@"附近没有主播开播");
    label1.textAlignment = NSTextAlignmentCenter;
    label1.textColor = RGB_COLOR(@"#333333", 1);
    [nothingView addSubview:label1];
    UILabel *label2 = [[UILabel alloc]initWithFrame:CGRectMake(10, 20, _window_width-20, 40)];
    label2.font = [UIFont systemFontOfSize:13];
    label2.text = YZMsg(@"去首页看看其他主播的直播吧");
    label2.textAlignment = NSTextAlignmentCenter;
    label2.textColor = RGB_COLOR(@"#969696", 1);
    label2.numberOfLines = 0;
    [nothingView addSubview:label2];
    noNetwork = [[YBNoWordView alloc]initWithBlock:^(id msg) {
        [self pullInternetforNew];
    }];
    noNetwork.hidden = YES;
    [self.view addSubview:noNetwork];

}
-(void)pullInternetforNew{
    [YBToolClass postNetworkWithUrl:@"Home.getNearby" andParameter:[NSDictionary dictionaryWithObjectsAndKeys:[cityDefault getMylng],@"lng",[cityDefault getMylat],@"lat",[cityDefault getMyCity],@"city",@(page),@"p",roomtype,@"live_type", nil] success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        noNetwork.hidden = YES;
        if (code == 0) {
            NSArray *infos = info;
            if (page == 1) {
                [self.allArray removeAllObjects];
            }
            [self.allArray addObjectsFromArray:infos];
            if (self.allArray.count == 0) {
                nothingView.hidden = NO;
            }
            else{
                nothingView.hidden = YES;
            }
            //加载成功 停止刷新
            [self.collectionView reloadData];
            if (infos.count == 0) {
                [self.collectionView.mj_footer endRefreshingWithNoMoreData];
            }
        }

    } fail:^{
        [self.collectionView.mj_header endRefreshing];
        [self.collectionView.mj_footer endRefreshing];
        nothingView.hidden = YES;
        if (_allArray.count == 0) {
            noNetwork.hidden = NO;
        }
    }];
}
#pragma mark - Table view data source
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.allArray.count;
}

-(NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    
    HotCollectionViewCell *cell = (HotCollectionViewCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"HotCollectionViewCELL" forIndexPath:indexPath];
    NSDictionary *subdic = self.allArray[indexPath.row];
    cell.isNear = YES;
    cell.model = [[hotModel alloc]initWithDic:subdic];
    return cell;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    selected = (int)indexPath.row;
    selectedDic = self.allArray[indexPath.row];

    BOOL isShowLive = [[NSUserDefaults standardUserDefaults]boolForKey:@"isShowChatLive"];
    if (isShowLive) {
        [[NSNotificationCenter defaultCenter]postNotificationName:@"HIDELIVEVIEW" object:nil];
    }
    [self checklive:[selectedDic valueForKey:@"stream"] andliveuid:[selectedDic valueForKey:@"uid"]];

    
}
-(void)checklive:(NSString *)stream andliveuid:(NSString *)liveuid{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.checkLive"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.timeoutInterval = 5.0;
    request.HTTPMethod = @"post";
    NSString *language = [PublicObj getCurrentLanguage];
    NSString *param = [NSString stringWithFormat:@"uid=%@&token=%@&liveuid=%@&stream=%@&language=%@",[Config getOwnID],[Config getOwnToken],liveuid,stream,language];
    request.HTTPBody = [param dataUsingEncoding:NSUTF8StringEncoding];
    NSURLResponse *response;
    NSError *error;
    NSData *backData = [NSURLConnection sendSynchronousRequest:request returningResponse:&response error:&error];
    if (error) {
        
    }
    else{
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:backData options:NSJSONReadingMutableContainers error:nil];
        
        NSNumber *number = [dic valueForKey:@"ret"];
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [dic valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                NSString *type = [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                
                type_val =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type_val"]];
                livetype =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                _sdkType = minstr([info valueForKey:@"live_sdk"]);

                if ([roomtype isEqual:@"0"]) {
                    if ([type isEqual:@"0"]) {
                        [self pushMovieVC];
                    }
                    else if ([type isEqual:@"1"]){
                        NSString *_MD5 = [NSString stringWithFormat:@"%@",[info valueForKey:@"type_msg"]];
                        //密码
                        md5AlertController = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"请输入房间密码") preferredStyle:UIAlertControllerStyleAlert];
                        //添加一个取消按钮
                        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            [self.navigationController popViewControllerAnimated:YES];
                            [self dismissViewControllerAnimated:NO completion:nil];
                        }];
                        [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                        [md5AlertController addAction:cancelAction];

                        //在AlertView中添加一个输入框
                        [md5AlertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
                            textField.secureTextEntry = YES;
                        }];
                        
                        //添加一个确定按钮 并获取AlertView中的第一个输入框 将其文本赋值给BUTTON的title
                        UIAlertAction *sureAction =[UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            UITextField *alertTextField = md5AlertController.textFields.firstObject;
                            //                        [self checkMD5WithText:envirnmentNameTextField.text andMD5:_MD5];
                            //输出 检查是否正确无误
                            NSLog(@"你输入的文本%@",alertTextField.text);
                            if ([_MD5 isEqualToString:[self stringToMD5:alertTextField.text]]) {
                                [self pushMovieVC];
                            }else{
                                alertTextField.text = @"";
                                [MBProgressHUD showError:YZMsg(@"密码错误")];
                                [self presentViewController:md5AlertController animated:true completion:nil];
                                return ;
                            }
                        }];
                        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                        [md5AlertController addAction:sureAction];
                        //present出AlertView
                        dispatch_async(dispatch_get_main_queue(), ^{
                            [self presentViewController:md5AlertController animated:true completion:nil];
                        });

                    }else if ([type isEqual:@"2"] || [type isEqual:@"3"]){
                        if ([[YBYoungManager shareInstance]isOpenYoung]) {
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"青少年模式下不支持该功能") preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"知道了") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [self.navigationController popViewControllerAnimated:YES];
                                [self dismissViewControllerAnimated:NO completion:nil];

                            }];
                            [cancleAction setValue:[UIColor grayColor] forKey:@"_titleTextColor"];
                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"去关闭") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [[YBYoungManager shareInstance]checkYoungStatus:YoungFrom_Center];

                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                            [alertContro addAction:sureAction];
                            [self presentViewController:alertContro animated:YES completion:nil];
                        }else{
                            //                        //收费
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:minstr([info valueForKey:@"type_msg"]) preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [self.navigationController popViewControllerAnimated:YES];
                                [self dismissViewControllerAnimated:NO completion:nil];

                            }];
                            [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                if ([[Config getOwnID] intValue] <= 0) {
                                    [[YBToolClass sharedInstance]waringLogin];
                                    return;
                                }

                                [self doCoast];
                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];

                            [alertContro addAction:sureAction];
                            dispatch_async(dispatch_get_main_queue(), ^{
                                [self presentViewController:alertContro animated:YES completion:nil];
                            });
                        }
                    }
                }else{
                    [self pushChatRoom];
                }

            }
            else{
                NSString *msg = [NSString stringWithFormat:@"%@",[data valueForKey:@"msg"]];
                [MBProgressHUD showError:msg];
            }
        }
        
        
        
    }
}
-(void)pushChatRoom{
    [[YBSmallLiveWindow shareInstance]closeBtnClick];
    UserRoomViewController *chatRoom = [[UserRoomViewController alloc]init];
    chatRoom.playDoc = selectedDic;
    chatRoom.sdkType = _sdkType;
    [[MXBADelegate sharedAppDelegate]pushViewController:chatRoom animated:YES];
}
-(void)pushMovieVC{
    [[YBSmallLiveWindow shareInstance]closeBtnClick];
    YBWeakSelf;
    moviePlay *player = [[moviePlay alloc]init];
    player.scrollarray = self.allArray;
    player.scrollindex = selected;
    player.playDoc = selectedDic;
    player.type_val = type_val;
    player.livetype = livetype;
    player.sdkType = _sdkType;
    player.endEvent = ^{
        page = 1;
        [weakSelf pullInternetforNew];
    };

    [[MXBADelegate sharedAppDelegate] pushViewController:player animated:YES];
}
-(void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    if (alertView == coastAlert) {
        if (buttonIndex == 0) {
            return;
        }else if (buttonIndex == 1){
            [self doCoast];
        }
    }else    if (alertView == secretAlert) {
        if (buttonIndex == 0) {
            return;
        }else if (buttonIndex == 1){
            UITextField *field = [alertView textFieldAtIndex:0];
            
            NSString *MD5s = [self stringToMD5:field.text];
            if ([MD5s isEqual:_MD5]) {
                
                [self pushMovieVC];
            }else{
                [MBProgressHUD showError:YZMsg(@"密码错误")];
            }
            
        }
    }
}
- (NSString *)stringToMD5:(NSString *)str
{
    
    //1.首先将字符串转换成UTF-8编码, 因为MD5加密是基于C语言的,所以要先把字符串转化成C语言的字符串
    const char *fooData = [str UTF8String];
    
    //2.然后创建一个字符串数组,接收MD5的值
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    
    //3.计算MD5的值, 这是官方封装好的加密方法:把我们输入的字符串转换成16进制的32位数,然后存储到result中
    CC_MD5(fooData, (CC_LONG)strlen(fooData), result);
    /**
     第一个参数:要加密的字符串
     第二个参数: 获取要加密字符串的长度
     第三个参数: 接收结果的数组
     */
    
    //4.创建一个字符串保存加密结果
    NSMutableString *saveResult = [NSMutableString string];
    
    //5.从result 数组中获取加密结果并放到 saveResult中
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [saveResult appendFormat:@"%02x", result[i]];
    }
    /*
     x表示十六进制，%02X  意思是不足两位将用0补齐，如果多余两位则不影响
     NSLog("%02X", 0x888);  //888
     NSLog("%02X", 0x4); //04
     */
    return saveResult;
}
//执行扣费
-(void)doCoast{
    [YBToolClass postNetworkWithUrl:@"Live.roomCharge" andParameter:@{@"liveuid":minstr([selectedDic valueForKey:@"uid"]),@"stream":minstr([selectedDic valueForKey:@"stream"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            NSDictionary *infos = [info firstObject];
            LiveUser *user = [Config myProfile];
            user.coin = [NSString stringWithFormat:@"%@",[infos valueForKey:@"coin"]];
            user.level = [NSString stringWithFormat:@"%@",[infos valueForKey:@"level"]];
            [Config updateProfile:user];

            [self pushMovieVC];
            //计时扣费
            
        }else{
            [MBProgressHUD showError:msg];
        }
        
    } fail:^{
        [self.collectionView.mj_header endRefreshing];
    }];

}
#pragma mark -
#pragma mark - navi
-(void)creatNavi {
    
    navi = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 64+statusbarHeight)];
    navi.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:navi];
    
    //标题
    UILabel *midLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, 22+statusbarHeight, 60, 42)];
    midLabel.backgroundColor = [UIColor clearColor];
    midLabel.font = navtionTitleFont;
    midLabel.text = YZMsg(@"附近");
    midLabel.textAlignment = NSTextAlignmentCenter;
    [navi addSubview:midLabel];
    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(20, 36, 20, 3) andColor:normalColors andView:midLabel];

    
    //私信
    UIButton *messageBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    messageBtn.frame = CGRectMake(_window_width-40,24 +statusbarHeight,40,40);
    messageBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [messageBtn setImage:[UIImage imageNamed:@"home_message"] forState:UIControlStateNormal];
    [messageBtn addTarget:self action:@selector(messageBtnClick) forControlEvents:UIControlEventTouchUpInside];
    messageBtn.imageEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
    [navi addSubview:messageBtn];
    label = [[UILabel alloc]initWithFrame:CGRectMake(20,5 , 16, 16)];
    label.layer.masksToBounds = YES;
    label.layer.cornerRadius = 8;
    label.hidden = YES;
    label.backgroundColor = [UIColor redColor];
    label.textColor = [UIColor whiteColor];
    label.font = [UIFont systemFontOfSize:13];
    label.text = [NSString stringWithFormat:@"%d",unRead];
    label.textAlignment = NSTextAlignmentCenter;
    [messageBtn addSubview:label];

    
    UIButton *searchBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    [searchBTN setImage:[UIImage imageNamed:@"home_search"] forState:UIControlStateNormal];
    searchBTN.frame = CGRectMake(messageBtn.left-40,24 +statusbarHeight,40,40);
    searchBTN.contentMode = UIViewContentModeScaleAspectFit;
    [searchBTN addTarget:self action:@selector(search) forControlEvents:UIControlEventTouchUpInside];
    searchBTN.imageEdgeInsets = UIEdgeInsetsMake(10, 10, 10, 10);
    [navi addSubview:searchBTN];
    
    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(0, 63+statusbarHeight, _window_width, 1) andColor:RGB(244, 245, 246) andView:navi];
    
}

-(void)messageBtnClick{
    MessageListVC *MC = [[MessageListVC alloc]init];
    [[MXBADelegate sharedAppDelegate]pushViewController:MC animated:YES];
}
-(void)search{
    searchVC *search = [[searchVC alloc]init];
    UINavigationController *naviSearch = [[UINavigationController alloc]initWithRootViewController:search];
//    [self presentViewController:naviSearch animated:YES completion:nil];
    [[MXBADelegate sharedAppDelegate]pushViewController:search animated:YES];

}


- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    oldOffset = scrollView.contentOffset.y;
}
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    if (scrollView.contentOffset.y >= oldOffset) {
        if (scrollView.contentOffset.y > 0) {
            _pageView.hidden = YES;
            [self hideTabBar];
        }
    }else{
        _pageView.hidden = NO;
        [self showTabBar];
    }
}

//- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
//    if (scrollView.contentOffset.y > oldOffset) {
//        if (scrollView.contentOffset.y > 0) {
//            navi.hidden = YES;
//            [self hideTabBar];
//        }
//    }else{
//        navi.hidden = NO;
//        [self showTabBar];
//    }
//}
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    NSLog(@"%f",oldOffset);
}
#pragma mark ================ 隐藏和显示tabbar ===============
- (void)hideTabBar {
    headView.hidden = YES;
    headView.backgroundColor = [UIColor clearColor];

    if (self.tabBarController.tabBar.hidden == YES) {
        return;
    }
    self.tabBarController.tabBar.hidden = YES;

}
- (void)showTabBar
{
    headView.hidden = NO;
    headView.backgroundColor = [UIColor whiteColor];

    if (self.tabBarController.tabBar.hidden == NO)
    {
        return;
    }
    self.tabBarController.tabBar.hidden = NO;

}
@end
