<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="13771" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13772"/>
        <capability name="Constraints with non-1.0 multipliers" minToolsVersion="5.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="fujincell">
            <rect key="frame" x="0.0" y="0.0" width="146" height="163"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="146" height="163"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="JvG-er-FAU" userLabel="背景">
                        <rect key="frame" x="0.0" y="0.0" width="146" height="163"/>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="最新页－阴影" translatesAutoresizingMaskIntoConstraints="NO" id="7tm-7L-YaY" userLabel="阴影">
                        <rect key="frame" x="0.0" y="0.0" width="146" height="163"/>
                    </imageView>
                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="直播live" translatesAutoresizingMaskIntoConstraints="NO" id="qPF-Va-BI4">
                        <rect key="frame" x="76" y="15" width="60" height="22"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="22" id="Ics-Y5-Y9f"/>
                            <constraint firstAttribute="width" constant="60" id="bRj-SQ-K6M"/>
                        </constraints>
                    </imageView>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="near_location.png" translatesAutoresizingMaskIntoConstraints="NO" id="8Dh-q1-e1P" userLabel="定位图标">
                        <rect key="frame" x="5" y="146" width="12" height="12"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="12" id="MkQ-gE-OVA"/>
                            <constraint firstAttribute="height" constant="12" id="nYf-y7-7wE"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="泰安市泰安市泰安市泰安市泰安市泰安市" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zrk-kc-eoM" userLabel="城市">
                        <rect key="frame" x="19" y="146" width="73" height="12"/>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="YzX-dh-J0M" userLabel="类型">
                        <rect key="frame" x="5" y="15" width="40" height="40"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="40" id="mu8-5g-4MP"/>
                            <constraint firstAttribute="width" constant="40" id="rg9-QJ-FgW"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="玩笑呀" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3AJ-Yh-4Gs" userLabel="名字">
                        <rect key="frame" x="5" y="130.5" width="37" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="·&gt;1000km" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6xu-jA-wvH" userLabel="距离">
                        <rect key="frame" x="94" y="146" width="48.5" height="12"/>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="7tm-7L-YaY" firstAttribute="centerY" secondItem="JvG-er-FAU" secondAttribute="centerY" id="2PL-qd-ZkO"/>
                <constraint firstItem="6xu-jA-wvH" firstAttribute="leading" secondItem="zrk-kc-eoM" secondAttribute="trailing" constant="2" id="5EB-jp-8LL"/>
                <constraint firstAttribute="trailing" secondItem="qPF-Va-BI4" secondAttribute="trailing" constant="10" id="8vm-hk-n3l"/>
                <constraint firstItem="JvG-er-FAU" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="B49-ab-lbn"/>
                <constraint firstItem="zrk-kc-eoM" firstAttribute="width" relation="lessThanOrEqual" secondItem="JvG-er-FAU" secondAttribute="width" multiplier="0.5" id="DB2-LA-gnQ"/>
                <constraint firstItem="zrk-kc-eoM" firstAttribute="bottom" secondItem="JvG-er-FAU" secondAttribute="bottom" constant="-5" id="DvH-Fu-MO6"/>
                <constraint firstItem="zrk-kc-eoM" firstAttribute="leading" secondItem="8Dh-q1-e1P" secondAttribute="trailing" constant="2" id="KeN-Lw-2N2"/>
                <constraint firstItem="JvG-er-FAU" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="N8q-Zj-hId"/>
                <constraint firstItem="3AJ-Yh-4Gs" firstAttribute="leading" secondItem="JvG-er-FAU" secondAttribute="leading" constant="5" id="NnK-6b-bLZ"/>
                <constraint firstItem="3AJ-Yh-4Gs" firstAttribute="bottom" secondItem="8Dh-q1-e1P" secondAttribute="top" constant="-1" id="Oo3-ha-Y5T"/>
                <constraint firstItem="8Dh-q1-e1P" firstAttribute="leading" secondItem="JvG-er-FAU" secondAttribute="leading" constant="5" id="Rgn-Km-AQx"/>
                <constraint firstItem="7tm-7L-YaY" firstAttribute="width" secondItem="JvG-er-FAU" secondAttribute="width" id="VjZ-uL-PEh"/>
                <constraint firstAttribute="trailing" secondItem="JvG-er-FAU" secondAttribute="trailing" id="alM-e5-Msg"/>
                <constraint firstItem="7tm-7L-YaY" firstAttribute="height" secondItem="JvG-er-FAU" secondAttribute="height" id="eaf-70-Dvk"/>
                <constraint firstItem="7tm-7L-YaY" firstAttribute="centerX" secondItem="JvG-er-FAU" secondAttribute="centerX" id="fuk-nU-cf8"/>
                <constraint firstItem="YzX-dh-J0M" firstAttribute="top" secondItem="qPF-Va-BI4" secondAttribute="top" id="gvV-Mj-WA3"/>
                <constraint firstItem="YzX-dh-J0M" firstAttribute="leading" secondItem="JvG-er-FAU" secondAttribute="leading" constant="5" id="mOE-Dr-Hsr"/>
                <constraint firstItem="6xu-jA-wvH" firstAttribute="bottom" secondItem="JvG-er-FAU" secondAttribute="bottom" constant="-5" id="oUo-fB-aZw"/>
                <constraint firstItem="8Dh-q1-e1P" firstAttribute="bottom" secondItem="JvG-er-FAU" secondAttribute="bottom" constant="-5" id="rfu-3e-6eh"/>
                <constraint firstItem="JvG-er-FAU" firstAttribute="height" secondItem="gTV-IL-0wX" secondAttribute="height" id="tkB-fr-bM0"/>
                <constraint firstItem="qPF-Va-BI4" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="15" id="vMb-Ai-u7e"/>
            </constraints>
            <size key="customSize" width="146" height="163"/>
            <connections>
                <outlet property="cityl" destination="zrk-kc-eoM" id="1OA-hC-VMM"/>
                <outlet property="distanceL" destination="6xu-jA-wvH" id="Qe1-GS-vAP"/>
                <outlet property="namel" destination="3AJ-Yh-4Gs" id="gxq-8N-fOW"/>
                <outlet property="thumbimagev" destination="JvG-er-FAU" id="XGy-dF-JKK"/>
                <outlet property="typeimagev" destination="YzX-dh-J0M" id="ycb-3w-vFT"/>
            </connections>
            <point key="canvasLocation" x="-213" y="-13.5"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="near_location.png" width="32" height="32"/>
        <image name="最新页－阴影" width="348" height="341"/>
        <image name="直播live" width="104" height="38"/>
    </resources>
</document>
