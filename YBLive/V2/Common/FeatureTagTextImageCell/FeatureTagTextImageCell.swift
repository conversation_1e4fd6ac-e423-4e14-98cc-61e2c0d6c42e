//
//  FeatureTagTextImageCell.swift
//  YBLive
//
//  Created by hongyunios on 7/5/25.
//  Copyright © 2025 cat. All rights reserved.
//

import UIKit
import TXAppBasic
import SnapKit
import Then

@objcMembers
class FeatureTagTextImageItemData: NSObject {
    public let feature: String
    public let textColor: UIColor
    public let backColor: UIColor
    public let image: URL?
    
    public init(feature: String, textColor: UIColor, backColor: UIColor, image: URL?) {
        self.feature = feature
        self.textColor = textColor
        self.backColor = backColor
        self.image = image
    }
}

@objcMembers
class FeatureTagTextImageCell: UICollectionViewCell {
    
    private var backView = UIView().then {
        $0.layer.cornerRadius = 4.0
    }
    private var featureImageView = UIImageView()
    private var featureLabel = UILabel().then {
        $0.font = .systemFont(ofSize: 10.0, weight: .medium)
        $0.textAlignment = .center
    }
    
    var feature: FeatureTagTextImageItemData? {
        didSet {
            guard let feature else {
                return
            }
            self.featureLabel.text = feature.feature
            self.featureLabel.textColor = feature.textColor
            self.featureImageView.kf.setImage(with: feature.image)
            self.backView.backgroundColor = feature.backColor
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = .clear
        self.contentView.backgroundColor = .clear
        self.contentView.addSubview(self.backView)
        self.backView.addSubview(self.featureLabel)
        self.backView.addSubview(self.featureImageView)
        self.configConstraint()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    static func allFeaturesHeight(_ features: [FeatureTagTextImageItemData], maxWidth: CGFloat) -> Double {
        let featuresSingleLineWidthMax = maxWidth
        let featuresSingleLineHeight = FeatureTagTextImageCell.cellHeight(data: nil)
        let allFeaturesWidth = features.map { feature in
            feature.feature.width(constrain: CGFLOAT_MAX, font: UIFont.systemFont(ofSize: 10.0, weight: .medium)) + 18.0
        }.reduce(0.0, +)
        let featuresLine = ceil(Double(allFeaturesWidth/featuresSingleLineWidthMax))
        let featuresHeight = featuresLine * featuresSingleLineHeight + (featuresLine - 1) * 4.0
        
        return featuresHeight
    }
}

extension FeatureTagTextImageCell: UIAnyCellSizeExtension {
    static func cellSize(data: Any?) -> CGSize {
        guard let title = data as? String else {
            return .zero
        }
        let font = UIFont.systemFont(ofSize: 10.0, weight: .medium)
        return CGSize(width: 2.0 + 12.0 + title.width(constrain: CGFLOAT_MAX, font: font) + 2.0,
                      height: self.cellHeight(data: title))
    }
    
    static func cellHeight(data: Any?) -> CGFloat {
        2.0 + max(UIFont.systemFont(ofSize: 10.0, weight: .medium).lineHeight, 8.0) + 2.0
    }
}

extension FeatureTagTextImageCell {
    private func configConstraint() {
        self.backView.snp.makeConstraints { make in
            make.top.bottom.leading.trailing.equalToSuperview()
        }
        self.featureImageView.snp.makeConstraints { make in
            make.width.height.equalTo(12.0)
            make.leading.equalToSuperview().offset(2.0)
            make.centerY.equalToSuperview()
        }
        self.featureLabel.snp.makeConstraints { make in
            make.leading.equalTo(self.featureImageView.snp.trailing)
            make.trailing.equalToSuperview().offset(-2.0)
            make.centerY.equalToSuperview()
        }
    }
}
