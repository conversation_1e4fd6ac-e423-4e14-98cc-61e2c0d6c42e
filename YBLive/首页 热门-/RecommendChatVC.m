//
//  RecommendChatVC.m
//  YBLive
//
//  Created by ybRRR on 2022/1/21.
//  Copyright © 2022 cat. All rights reserved.
//

#import "RecommendChatVC.h"
#import "ChatListCell.h"
#import "UserRoomViewController.h"
@interface RecommendChatVC ()<UITableViewDelegate, UITableViewDataSource>
{
    int page;
    NSDictionary *selectedDic;
    NSString *_sdkType;
}
@property (nonatomic, strong)UITableView *listTable;
@property(nonatomic,strong)NSMutableArray *infoArray;//获取到的主播列表信息

@end

@implementation RecommendChatVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"语音直播");
    
    _infoArray = [NSMutableArray array];
    page = 1;

    [self.view addSubview:self.listTable];
    
    [self pullInternet];
}
- (void)pullInternet{
    [YBToolClass postNetworkWithUrl:@"Home.getVoiceLiveList" andParameter:@{@"p":@(page)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [_listTable.mj_header endRefreshing];
        [_listTable.mj_footer endRefreshing];

        if (code == 0) {
            if (page == 1) {
                [_infoArray removeAllObjects];
            }
            [_infoArray addObjectsFromArray:info];
            [_listTable reloadData];
            if ([info count] <= 0) {
                [_listTable.mj_footer endRefreshingWithNoMoreData];
            }
        }
        if (_infoArray.count == 0) {
            [PublicView showTextNoData:_listTable text1:YZMsg(@"暂时没有主播开播") text2:YZMsg(@"赶快去其他频道逛逛吧")];
        }else{
            [PublicView hiddenTextNoData:_listTable];
        }
    } fail:^{
        [_listTable.mj_header endRefreshing];
        [_listTable.mj_footer endRefreshing];
        [MBProgressHUD showError:YZMsg(@"网络请求失败")];
    }];
}


-(UITableView *)listTable{
    if (!_listTable) {
        _listTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:UITableViewStylePlain];
        _listTable.delegate = self;
        _listTable.dataSource = self;
        _listTable.separatorStyle = UITableViewCellSeparatorStyleNone;
        _listTable.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            page = 1;
            [self pullInternet];
        }];
        _listTable.mj_footer = [MJRefreshBackFooter footerWithRefreshingBlock:^{
            page ++;
            [self pullInternet];
        }];
    }
    return _listTable;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return _infoArray.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 120;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    ChatListCell *cell = [ChatListCell cellWithTab:tableView andIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.dataDic = _infoArray[indexPath.row];
    return cell;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    selectedDic = _infoArray[indexPath.row];

    BOOL isShowLive = [[NSUserDefaults standardUserDefaults]boolForKey:@"isShowChatLive"];
    if (isShowLive) {
        [[NSNotificationCenter defaultCenter]postNotificationName:@"HIDELIVEVIEW" object:nil];
    }

    [self checklive:[selectedDic valueForKey:@"stream"] andliveuid:[selectedDic valueForKey:@"uid"]];

}
-(void)checklive:(NSString *)stream andliveuid:(NSString *)liveuid{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.checkLive"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.timeoutInterval = 5.0;
    request.HTTPMethod = @"post";
    NSString *param = [NSString stringWithFormat:@"uid=%@&token=%@&liveuid=%@&stream=%@",[Config getOwnID],[Config getOwnToken],liveuid,stream];
    request.HTTPBody = [param dataUsingEncoding:NSUTF8StringEncoding];
    NSURLResponse *response;
    NSError *error;
    NSData *backData = [NSURLConnection sendSynchronousRequest:request returningResponse:&response error:&error];
    if (error) {
        
    }
    else{
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:backData options:NSJSONReadingMutableContainers error:nil];
        
        NSNumber *number = [dic valueForKey:@"ret"];
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [dic valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                _sdkType = minstr([info valueForKey:@"live_sdk"]);

                [self pushChatRoom];
            }
            else{
                NSString *msg = [NSString stringWithFormat:@"%@",[data valueForKey:@"msg"]];
                [MBProgressHUD showError:msg];
            }
        }
    }
}
-(void)pushChatRoom{
    [[YBSmallLiveWindow shareInstance]closeBtnClick];
    UserRoomViewController *chatroom = [[UserRoomViewController alloc]init];
    chatroom.playDoc = selectedDic;
    chatroom.sdkType = _sdkType;
    [[MXBADelegate sharedAppDelegate] pushViewController:chatroom animated:YES];
}

@end
