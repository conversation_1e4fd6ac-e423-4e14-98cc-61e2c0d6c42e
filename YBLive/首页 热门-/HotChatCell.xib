<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="HotChatCell">
            <rect key="frame" x="0.0" y="0.0" width="119" height="119"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="119" height="119"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="zyF-mH-g5j">
                        <rect key="frame" x="0.0" y="0.0" width="119" height="119"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="z3F-Wp-b0H">
                        <rect key="frame" x="6" y="93.5" width="22" height="22"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="22" id="fZe-Q7-8yt"/>
                            <constraint firstAttribute="width" constant="22" id="k4L-I7-qsV"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="11"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="100" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5bS-aY-Rdy">
                        <rect key="frame" x="93" y="97" width="18" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="aF5-ay-cpi"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <color key="textColor" red="0.96078431369999995" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="live_nums.png" translatesAutoresizingMaskIntoConstraints="NO" id="ZGW-ag-stM">
                        <rect key="frame" x="78" y="98" width="13" height="13"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="13" id="C0m-Ns-Ifn"/>
                            <constraint firstAttribute="width" constant="13" id="twP-0i-9a3"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wq9-b5-Eg6">
                        <rect key="frame" x="31" y="98" width="28.5" height="13.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <color key="textColor" red="0.96078431369999995" green="0.96078431369999995" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="语音-推荐.png" translatesAutoresizingMaskIntoConstraints="NO" id="jZL-N8-jEw">
                        <rect key="frame" x="65" y="8" width="47" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="8aK-iM-uSM"/>
                            <constraint firstAttribute="width" constant="47" id="e1v-rg-vez"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xhp-vV-nRb">
                        <rect key="frame" x="51.5" y="95" width="39.5" height="19.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="5bS-aY-Rdy" secondAttribute="trailing" constant="8" id="1Ay-H3-2ke"/>
                <constraint firstItem="jZL-N8-jEw" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="8" id="1bK-J7-mvs"/>
                <constraint firstItem="5bS-aY-Rdy" firstAttribute="leading" secondItem="Xhp-vV-nRb" secondAttribute="trailing" constant="2" id="24k-6d-Vof"/>
                <constraint firstAttribute="trailing" secondItem="zyF-mH-g5j" secondAttribute="trailing" id="8lp-pH-wSC"/>
                <constraint firstAttribute="bottom" secondItem="zyF-mH-g5j" secondAttribute="bottom" id="AKr-et-ulV"/>
                <constraint firstItem="z3F-Wp-b0H" firstAttribute="centerY" secondItem="ZGW-ag-stM" secondAttribute="centerY" id="BvR-DK-H9B"/>
                <constraint firstItem="zyF-mH-g5j" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Fbw-IN-Pno"/>
                <constraint firstItem="wq9-b5-Eg6" firstAttribute="centerY" secondItem="z3F-Wp-b0H" secondAttribute="centerY" id="K2Q-Ug-dK3"/>
                <constraint firstAttribute="bottom" secondItem="5bS-aY-Rdy" secondAttribute="bottom" constant="7" id="TwH-TO-79k"/>
                <constraint firstAttribute="trailing" secondItem="jZL-N8-jEw" secondAttribute="trailing" constant="7" id="Vij-Q1-dBe"/>
                <constraint firstItem="z3F-Wp-b0H" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="6" id="ZjC-gp-lPk"/>
                <constraint firstItem="ZGW-ag-stM" firstAttribute="centerY" secondItem="5bS-aY-Rdy" secondAttribute="centerY" id="c6K-LR-Ff0"/>
                <constraint firstItem="Xhp-vV-nRb" firstAttribute="centerY" secondItem="5bS-aY-Rdy" secondAttribute="centerY" id="cnk-Q0-6Fe"/>
                <constraint firstItem="zyF-mH-g5j" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="g58-gx-9uZ"/>
                <constraint firstItem="wq9-b5-Eg6" firstAttribute="leading" secondItem="z3F-Wp-b0H" secondAttribute="trailing" constant="3" id="gke-bu-veD"/>
                <constraint firstItem="5bS-aY-Rdy" firstAttribute="leading" secondItem="ZGW-ag-stM" secondAttribute="trailing" constant="2" id="sZP-BF-C3g"/>
            </constraints>
            <size key="customSize" width="119" height="119"/>
            <connections>
                <outlet property="headImg" destination="z3F-Wp-b0H" id="77H-jz-k2i"/>
                <outlet property="lookLb" destination="Xhp-vV-nRb" id="68x-x8-v1d"/>
                <outlet property="nameLb" destination="wq9-b5-Eg6" id="scs-fW-Rob"/>
                <outlet property="numLb" destination="5bS-aY-Rdy" id="1Ja-aN-3iT"/>
                <outlet property="thumbImg" destination="zyF-mH-g5j" id="9Bg-eJ-no8"/>
                <outlet property="tjImg" destination="jZL-N8-jEw" id="kia-ce-Sif"/>
            </connections>
            <point key="canvasLocation" x="181.8840579710145" y="101.45089285714285"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="live_nums.png" width="12" height="12"/>
        <image name="语音-推荐.png" width="45.5" height="21"/>
    </resources>
</document>
