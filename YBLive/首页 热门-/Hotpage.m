#import "Hotpage.h"
#import "hotModel.h"
#import "LivePlay.h"
#import "AppDelegate.h"
#import <CommonCrypto/CommonCrypto.h>
#import <AssetsLibrary/AssetsLibrary.h>
#import "tuijianwindow.h"
#import "HotCollectionViewCell.h"
#import "classVC.h"
#import "AllClassVC.h"
#import "YBWebViewController.h"
#import "SDCycleScrollView.h"
#import "RecommendLiveView.h"
#import "UserRoomViewController.h"
#import "ChatClassVC.h"
#import <CWStatusBarNotification/CWStatusBarNotification.h>
#import "OpenInstallSDK.h"
#import "AttLiveingView.h"
#import <TXLiteAVSDK_Professional/V2TXLivePlayer.h>
@interface Hotpage   ()<UICollectionViewDelegate,UICollectionViewDataSource,UIAlertViewDelegate,t<PERSON><PERSON>an,UIScrollViewDelegate,SDCycleScrollViewDelegate,TXLivePlayListener,V2TXLivePlayerObserver>
{
    NSDictionary *selectedDic;
    UIImageView *NoInternetImageV;//直播无网络的时候显示
    UIAlertController *alertController;//邀请码填写
    UITextField *codetextfield;
    NSString *type_val;//
    NSString *livetype;//
    tuijianwindow *tuijianw;
    CGFloat oldOffset;
    int page;
    UIView *collectionHeaderView;
    UIAlertController *md5AlertController;
//    allClassView *allClassV;
    YBNoWordView *noNetwork;
    NSString *_sdkType;//0-金山  1-腾讯

    NSInteger currentPlayIndex;
    UIView *noDataView;
    UIView *chatView;
    UIImageView *headImg;
    UILabel *nameLb;
    UILabel *idLb;
    NSDictionary *currentChatDic;
    
//    TXLivePlayer *_txLivePlayer;
//    TXLivePlayConfig * _config;
    CWStatusBarNotification *_notification;
    NSArray *liveClass;
    
    NSString *agent_switch;
    NSString *has_agent;
    NSString* agent_must;
    NSString* openinstall_switch;

}
@property(nonatomic,strong)V2TXLivePlayer *txLivePlayer;
@property(nonatomic,strong)NSMutableArray *zhuboModel;//主播模型
@property(nonatomic,strong)NSMutableArray *infoArray;//获取到的主播列表信息
@property(nonatomic,strong)UICollectionView *collectionView;
@property(nonatomic,strong)NSString *MD5;//加密密码
@property(nonatomic,strong)NSArray *CarouselArray;//轮播图
@property(nonatomic,strong)NSArray *recommendArray;//推荐主播
@property(nonatomic,strong)NSArray *attliveArray;//关注主播
@property(nonatomic,strong)NSString *attent_live_nums;
@property (nonatomic,strong)SDCycleScrollView *cycleScroll;
@property (nonatomic,strong)RecommendLiveView *recommendView;
@property (nonatomic,strong)AttLiveingView *attView;
@property (nonatomic, strong) UIPanGestureRecognizer *panGestureRecognizer;//添加在视图上的拖动手势

@end
@implementation Hotpage

-(void)checkYaoqingma{
    YBWeakSelf;
    NSString *url = [purl stringByAppendingFormat:@"?service=Agent.checkAgent"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
    };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            NSDictionary *infoDic = [[data valueForKey:@"info"] firstObject];
            agent_switch = minstr([infoDic valueForKey:@"agent_switch"]);
            has_agent = minstr([infoDic valueForKey:@"has_agent"]);
            agent_must = minstr([infoDic valueForKey:@"agent_must"]);
            openinstall_switch =minstr([infoDic valueForKey:@"openinstall_switch"]);
            /*
             has_agent 是否填写过邀请码
             agent_switch 手填邀请码开关
             openinstall_switch codeInstall 邀请码开关
             
             当has_agent=1.填写过邀请码，直接略过
             当has_agent !=1 判断codeinstall三方开关，当codeinstall开启时直接走免填SDK，当获取不到codeInstall的邀请码时，判断手填开关有没有开启，如果开启弹邀请码框，否则不弹。
             当codeinstall关闭时判断手填开关有没有开启，当不需要手填邀请码时直接略过，否则弹手填邀请码框
             
            */
            if ([has_agent isEqual:@"1"]) {
                [cityDefault saveisreg:@"0"];
            }else{
                if([openinstall_switch isEqual:@"1"]){
                    [[NSUserDefaults standardUserDefaults] setObject:@"0" forKey:@"isagent"];
                    [weakSelf executeStepOfSDKParams];
                }else{
                    if ([agent_switch isEqual:@"0"]) {
                        [cityDefault saveisreg:@"0"];
                    }else{
                        [weakSelf showHandWriting];
                    }
                }
            }
        }
    } Fail:^(id fail) {
    }];
}
-(void)executeStepOfSDKParams{
    YBWeakSelf;
    
    [[OpenInstallSDK defaultManager] getInstallParmsCompleted:^(OpeninstallData*_Nullable appData) {
        //在主线程中回调
        if (appData.data) {//(动态安装参数)
            //e.g.如免填邀请码建立邀请关系、自动加好友、自动进入某个群组或房间等
            [weakSelf uploadInvitationV:minstr([appData.data valueForKey:@"code"])];
            [cityDefault saveisreg:@"0"];
        }else {
            if ([agent_switch isEqual:@"0"]) {
                [cityDefault saveisreg:@"0"];
            }else{
                [weakSelf showHandWriting];
            }
        }
        if (appData.channelCode) {//(通过渠道链接或二维码安装会返回渠道编号)
            //e.g.可自己统计渠道相关数据等
        }
        NSLog(@"OpenInstallSDK:\n动态参数：%@;\n渠道编号：%@",appData.data,appData.channelCode);
    }];

    
    
    
//    //根据自身业务在所需处获取动态安装参数
//    [CodeInstallSDK getInstallParams:^(CodeInstallData * _Nullable appData) {
//        if (appData.data) {//(动态唤醒参数)
//            //e.g.如免填邀请码建立邀请关系、自动加好友、自动进入某个群组或房间等
//            [weakSelf uploadInvitationV:minstr([appData.data valueForKey:@"code"])];
//            [cityDefault saveisreg:@"0"];
//
//        }else {
//            if ([agent_switch isEqual:@"0"]) {
//                [cityDefault saveisreg:@"0"];
//            }else{
//                [weakSelf showHandWriting];
//            }
//        }
//        if (appData.channelNo) {//(通过渠道链接或二维码唤醒会返回渠道编号)
//            //e.g.可自己统计渠道相关数据等
//        }
//        NSLog(@"iviteView - CodeInstallSDK:\n动态参数：%@;\n渠道编号：%@",appData.data,appData.channelNo);
//    }];
}

-(void)showHandWriting{
    YBWeakSelf;
        alertController = [UIAlertController alertControllerWithTitle:YZMsg(@"请填写邀请码") message:@""preferredStyle:UIAlertControllerStyleAlert];
        [alertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
            codetextfield = textField;
        }];
        //修改按钮的颜色，同上可以使用同样的方法修改内容，样式
        UIAlertAction *defaultAction = [UIAlertAction actionWithTitle:YZMsg(@"确定")style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            if (codetextfield.text.length == 0) {
                [self presentViewController:alertController animated:YES completion:nil];
                [MBProgressHUD showError:YZMsg(@"请填写邀请码")];
                return;
            }
            [weakSelf uploadInvitationV:codetextfield.text];
        }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消")style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            
        }];
        NSString *version = [UIDevice currentDevice].systemVersion;
        
        if (version.doubleValue < 9.0) {
            
        }
        else{
            [defaultAction setValue:normalColors forKey:@"_titleTextColor"];
            [cancelAction setValue:[UIColor darkGrayColor] forKey:@"_titleTextColor"];
        }
        [alertController addAction:defaultAction];

        if ([agent_switch isEqual:@"1"] && [agent_must isEqual:@"1"]) {
            [self presentViewController:alertController animated:YES completion:nil];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [codetextfield resignFirstResponder];
            });

        }else if([agent_switch isEqual:@"1"] && [agent_must isEqual:@"0"]){
            if ([[cityDefault getreg] isEqual:@"1"]) {
                [alertController addAction:cancelAction];
                [self presentViewController:alertController animated:YES completion:nil];
                [[NSUserDefaults standardUserDefaults]setObject:@"1" forKey:@"agent_must"];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [codetextfield resignFirstResponder];
                });
            }
        }
    [cityDefault saveisreg:@"0"];
}

-(void)jump{
    tuijianw.hidden = YES;
    [tuijianw removeFromSuperview];
    tuijianw = nil;
}
-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
}
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    NSLog(@"%f",_collectionView.contentOffset.y);
}
- (void)viewDidLoad {
    [super viewDidLoad];
    
    oldOffset = 0;
    type_val = @"0";
    livetype = @"0";
    currentPlayIndex = 0;
    page = 1;
    self.infoArray    =  [NSMutableArray array];
    self.zhuboModel    =  [NSMutableArray array];
    self.view.backgroundColor =RGB_COLOR(@"#f5f5f5", 1);// [UIColor groupTableViewBackgroundColor];
    [self createCollectionView];
    [self nothingview];
    [self yaoqingma];
    if ([[Config getOwnID] intValue] <= 0) {
    }else{
        [self checkYaoqingma];
    }
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(homeConfig) name:@"home.getconfig" object:nil];
    [self createNoDataView];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillEnterForeground:) name:UIApplicationWillEnterForegroundNotification object:nil];

    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(showChatLiveView:) name:@"SHOWCHATLIVE" object:nil];
    [[NSUserDefaults standardUserDefaults]setBool:NO forKey:@"isShowChatLive"];
    
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(hideChatRoomLiewView) name:@"HIDELIVEVIEW" object:nil];
    [self ChatLiveView];
}
-(void)ChatLiveView{
    chatView = [[UIView alloc]init];
    chatView.frame = CGRectMake(_window_width-103, _window_height *0.7, 103, 32);
    chatView.backgroundColor = RGB(236, 87, 101);
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:chatView.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerBottomLeft cornerRadii:CGSizeMake(16, 16)];
    CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
    maskLayer.frame = chatView.bounds;
    maskLayer.path = maskPath.CGPath;
    chatView.layer.mask = maskLayer;
//    [self.view addSubview:chatView];
    
    CAGradientLayer *gradientLayer =  [CAGradientLayer layer];
    gradientLayer.frame = chatView.bounds;
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1, 0);
    gradientLayer.locations = @[@(0),@(1.0)];//渐变点
    [gradientLayer setColors:@[(id)[RGB(236,87,101) CGColor],(id)[RGB(252,176,141) CGColor]]];//渐变数组
    [chatView.layer addSublayer:gradientLayer];

    [[UIApplication sharedApplication].keyWindow addSubview:chatView];
    
    headImg = [[UIImageView alloc]init];
    headImg.frame = CGRectMake(2, 2, 28, 28);
    headImg.layer.cornerRadius = 14;
    headImg.layer.masksToBounds = YES;
    headImg.backgroundColor = RGB(250, 250, 250);
    [chatView addSubview:headImg];
    
    nameLb = [[UILabel alloc]init];
    nameLb.frame = CGRectMake(headImg.right+5, 2, chatView.width-40, 15);
    nameLb.font = [UIFont systemFontOfSize:10];
    nameLb.textColor = [UIColor whiteColor];
    nameLb.text = @"ssakjkjka";
    [chatView addSubview:nameLb];
    
    idLb = [[UILabel alloc]init];
    idLb.frame = CGRectMake(headImg.right+5, nameLb.bottom, chatView.width-40, 15);
    idLb.font = [UIFont systemFontOfSize:10];
    idLb.textColor = [UIColor whiteColor];
    idLb.text = @"ID:1112212";
    [chatView addSubview:idLb];

    chatView.hidden = YES;
    
    UIButton *roomBtn = [UIButton buttonWithType:0];
    roomBtn.frame = CGRectMake(0, 0, chatView.width, chatView.height);
    [roomBtn addTarget:self action:@selector(roomBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [chatView addSubview:roomBtn];

    _txLivePlayer = [[V2TXLivePlayer alloc] init];
    [_txLivePlayer setObserver:self];
    [_txLivePlayer enableObserveAudioFrame:YES];

    [chatView addGestureRecognizer:self.panGestureRecognizer];
}
//-(V2TXLivePlayer *)txLivePlayer{
//    if(!_txLivePlayer){
//        _txLivePlayer = [[V2TXLivePlayer alloc] init];
//        [_txLivePlayer setObserver:self];
//        [_txLivePlayer enableObserveAudioFrame:YES];
//    }
//    return _txLivePlayer;
//}
//

- (UIPanGestureRecognizer *)panGestureRecognizer
{
    if (!_panGestureRecognizer) {
        _panGestureRecognizer = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(dragViewMoved:)];
    }
    return _panGestureRecognizer;
}
- (void)dragViewMoved:(UIPanGestureRecognizer *)panGestureRecognizer
{
    if (panGestureRecognizer.state == UIGestureRecognizerStateChanged) {
        
        
            CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
           CGFloat screenHeight = [UIScreen mainScreen].bounds.size.height;
           //返回在横坐标上、纵坐标上拖动了多少像素
           CGPoint point = [panGestureRecognizer translationInView:self.view];
           NSLog(@"%f,%f",point.x,point.y);
           
           CGFloat centerX = panGestureRecognizer.view.center.x+point.x;
           CGFloat centerY = panGestureRecognizer.view.center.y+point.y;
           CGSize viewSize = panGestureRecognizer.view.frame.size;
           
           // top
           if (centerY - viewSize.height/2 < 0) {
               centerY = viewSize.height/2+statusbarHeight+ShowDiff+55;
           }
           // bottom
           if (centerY + viewSize.height/2 > screenHeight) {
               centerY = screenHeight - viewSize.height/2-55;
           }
           // left
           if (centerX - viewSize.width/2 < 0){
               centerX = viewSize.width/2;
           }
           // right
           if (centerX + viewSize.width/2 > screenWidth){
               centerX = screenWidth - viewSize.width/2;
           }
        panGestureRecognizer.view.center = CGPointMake(centerX, centerY);
           //拖动完之后，每次都要用setTranslation:方法置0这样才不至于不受控制般滑动出视图
        
        [panGestureRecognizer setTranslation:CGPointMake(0, 0) inView:self.view];
        
    }
}
-(void)roomBtnClick{
    [self stopConnect];
    chatView.hidden = YES;
    [self checklive:[currentChatDic valueForKey:@"stream"] andliveuid:[currentChatDic valueForKey:@"uid"]];

//    UserRoomViewController *chatroom = [[UserRoomViewController alloc]init];
//    chatroom.playDoc = currentChatDic;
//    [[MXBADelegate sharedAppDelegate] pushViewController:chatroom animated:YES];

}
- (void)stopConnect{
    [[NSUserDefaults standardUserDefaults]setBool:NO forKey:@"isShowChatLive"];

    if(_txLivePlayer != nil)
    {
//        _txLivePlayer.delegate = nil;
        [_txLivePlayer stopPlay];
    }
}
-(void)hideChatRoomLiewView{
    [self stopConnect];
    chatView.hidden = YES;

}
#pragma mark-------语音直播最小化-------
-(void)showChatLiveView:(NSNotification *)noti{
    chatView.frame = CGRectMake(_window_width-103, _window_height *0.7, 103, 32);
    currentChatDic = noti.userInfo;
    chatView.hidden = NO;
    NSLog(@"传回来的数据----:%@",noti.userInfo);
    [headImg sd_setImageWithURL:[NSURL URLWithString:minstr([currentChatDic valueForKey:@"thumb"])]];
    nameLb.text = minstr([currentChatDic valueForKey:@"user_nickname"]);
    idLb.text =[NSString stringWithFormat:@"ID:%@",minstr([currentChatDic valueForKey:@"uid"])];
    [[NSUserDefaults standardUserDefaults]setBool:YES forKey:@"isShowChatLive"];
    [[NSUserDefaults standardUserDefaults]setObject:currentChatDic forKey:@"SMALLCHATDATA"];
    if(_txLivePlayer != nil)
    {
//        _txLivePlayer.delegate = self;
        NSString *playUrl = minstr([currentChatDic valueForKey:@"pull"]);
//        NSInteger _playType = 0;
//        if ([playUrl hasPrefix:@"rtmp:"]) {
//            _playType = PLAY_TYPE_LIVE_RTMP;
//        } else if (([playUrl hasPrefix:@"https:"] || [playUrl hasPrefix:@"http:"]) && [playUrl rangeOfString:@".flv"].length > 0) {
//            _playType = PLAY_TYPE_LIVE_FLV;
//        }
//        else{
//
//        }
//        if ([playUrl rangeOfString:@".mp4"].length > 0) {
//            _playType = PLAY_TYPE_VOD_MP4;
//        }
//        if ([playUrl rangeOfString:@".m3u8"].length > 0) {
//            _playType = PLAY_TYPE_VOD_FLV;
//        }

//        int result = [_txLivePlayer startLivePlay:playUrl type:_playType];
        V2TXLiveCode result = [self.txLivePlayer startLivePlay:playUrl];
        NSLog(@"wangminxin%ld",result);
        if( result != 0)
        {
            [_notification displayNotificationWithMessage:@"视频流播放失败" forDuration:5];
        }
        if( result == 0){
            NSLog(@"播放视频");
        }
        [[UIApplication sharedApplication] setIdleTimerDisabled:YES];

    }

}
#pragma mark---liveplayObserver
- (void)onError:(id<V2TXLivePlayer>)player code:(V2TXLiveCode)code message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo;
{
    NSLog(@"liveplay-error");
}
- (void)onWarning:(id<V2TXLivePlayer>)player code:(V2TXLiveCode)code message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo;
{
    NSLog(@"liveplay-onWarning");
}
/**
 * 已经成功连接到服务器
 *
 * @param player    回调该通知的播放器对象。
 * @param extraInfo 扩展信息。
 */

- (void)onVideoPlaying:(id<V2TXLivePlayer>)player firstPlay:(BOOL)firstPlay extraInfo:(NSDictionary *)extraInfo;
{
    NSLog(@"liveplay-VideoPlaying");
}


//播放监听事件
-(void) onPlayEvent:(int)EvtID withParam:(NSDictionary*)param
{
    dispatch_async(dispatch_get_main_queue(), ^{
        if (EvtID == PLAY_EVT_CONNECT_SUCC) {
            NSLog(@"play_linkMic已经连接服务器");
        }
        else if (EvtID == PLAY_EVT_RTMP_STREAM_BEGIN){
            NSLog(@"play_linkMic已经连接服务器，开始拉流");
        }
        else if (EvtID == PLAY_EVT_PLAY_BEGIN){
            NSLog(@"play_linkMic视频播放开始");
        }
        else if (EvtID== PLAY_WARNING_VIDEO_PLAY_LAG){
            NSLog(@"play_linkMic当前视频播放出现卡顿（用户直观感受）");
        }
        else if (EvtID == PLAY_EVT_PLAY_END){
            NSLog(@"play_linkMic视频播放结束");
            [self hideChatRoomLiewView];
        }
        else if (EvtID == PLAY_ERR_NET_DISCONNECT) {
            NSLog(@"play_linkMic网络断连,且经多次重连抢救无效,可以放弃治疗,更多重试请自行重启播放");
            [self hideChatRoomLiewView];

        }
    });
}

- (void)appWillEnterForeground:(NSNotification*)note
{
    [_collectionView.mj_header beginRefreshing];
}
- (void)homeConfig{
    [self createCollectionHeaderView];
}
-(void)createNoDataView{
    noDataView = [[UIView alloc]initWithFrame:CGRectMake(0, 200, _window_width, 40)];
    noDataView.hidden = YES;
    noDataView.backgroundColor = [UIColor clearColor];
    [_collectionView addSubview:noDataView];
    
    UILabel *label1 = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, _window_width, 20)];
    label1.font = [UIFont systemFontOfSize:14];
    label1.text = YZMsg(@"暂时没有主播开播");
    label1.textAlignment = NSTextAlignmentCenter;
    label1.textColor = RGB_COLOR(@"#333333", 1);
    [noDataView addSubview:label1];
    UILabel *label2 = [[UILabel alloc]initWithFrame:CGRectMake(0, 20, _window_width, 20)];
    label2.font = [UIFont systemFontOfSize:13];
    label2.text = YZMsg(@"赶快开启你的直播吧");
    label2.textAlignment = NSTextAlignmentCenter;
    label2.textColor = RGB_COLOR(@"#969696", 1);
    [noDataView addSubview:label2];

    noDataView.hidden = YES;
}

- (void)createCollectionHeaderView{
    //ray416--begin----修改首页经常空数据问题
    if (collectionHeaderView) {
        [collectionHeaderView removeAllSubviews];
    }else{
        collectionHeaderView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_width/6+_window_width*0.3 +(_window_width/2)+95)];

        collectionHeaderView.backgroundColor =RGB_COLOR(@"#f5f5f5", 1);// [UIColor whiteColor];
        collectionHeaderView.clipsToBounds = YES;

    }
    CGFloat bottomFloat = 0;
    if (_CarouselArray.count > 0) {
        collectionHeaderView.height = _window_width/6+_window_width*0.3;
        _cycleScroll = [[SDCycleScrollView alloc]initWithFrame:CGRectMake(7, 0, _window_width-14, _window_width*0.3)];
        _cycleScroll.delegate = self;
        _cycleScroll.pageControlStyle = SDCycleScrollViewPageContolStyleAnimated;
        [collectionHeaderView addSubview:_cycleScroll];
        _cycleScroll.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        _cycleScroll.autoScrollTimeInterval = 3.0;//轮播时间间隔，默认1.0秒，可自定义
        _cycleScroll.currentPageDotColor = [UIColor whiteColor];
        _cycleScroll.pageDotColor = [[UIColor whiteColor] colorWithAlphaComponent:0.4];
        _cycleScroll.pageControlStyle = SDCycleScrollViewPageContolStyleAnimated;
        _cycleScroll.layer.cornerRadius = 10;
        _cycleScroll.layer.masksToBounds = YES;
        //ray416--begin----
        NSMutableArray *sliderMuArr = [NSMutableArray array];
        for (NSDictionary *dic in _CarouselArray) {
            [sliderMuArr addObject:minstr([dic valueForKey:@"slide_pic"])];
        }
        _cycleScroll.imageURLStringsGroup = sliderMuArr;
        //ray416--end---
        bottomFloat = _cycleScroll.bottom;
    }
    //ray416--end---
    NSMutableArray *allArr = [NSMutableArray array];
    [allArr addObjectsFromArray:[common liveclass]];
    
    liveClass =allArr;// [common liveclass];
    NSInteger count;
    if (liveClass.count>6) {
        count = 5;
        UIButton *allButton = [UIButton buttonWithType:0];
        allButton.frame = CGRectMake(_window_width/6*5, bottomFloat+10, _window_width/6, _window_width/6);
        allButton.tag = 10086;
        [allButton addTarget:self action:@selector(liveClassBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        [collectionHeaderView addSubview:allButton];
        UIImageView *imgView = [[UIImageView alloc]initWithFrame:CGRectMake(allButton.width*0.2, allButton.width*0.05, allButton.width*0.6, allButton.width*0.6)];
        imgView.contentMode = UIViewContentModeScaleAspectFit;
        [imgView setImage:[UIImage imageNamed:@"live_all"]];
        [allButton addSubview:imgView];
        UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(0, imgView.bottom, allButton.width, allButton.height - (imgView.bottom))];
        label.textColor = RGB(99, 99, 99);
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:10];
        [label setText:YZMsg(@"全部")];
        [allButton addSubview:label];
    }else{
        count = liveClass.count;
    }
    for (int i = 0; i < count; i++) {
        UIButton *button = [UIButton buttonWithType:0];
        button.frame = CGRectMake(i*(_window_width/6), bottomFloat+10   , _window_width/6, _window_width/6);
        button.tag = i + 20180922;
        [button addTarget:self action:@selector(liveClassBtnClick:) forControlEvents:UIControlEventTouchUpInside];
        [collectionHeaderView addSubview:button];
        UIImageView *imgView = [[UIImageView alloc]initWithFrame:CGRectMake(button.width*0.2, button.width*0.05, button.width*0.6, button.width*0.6)];
        imgView.contentMode = UIViewContentModeScaleAspectFit;
        [imgView sd_setImageWithURL:[NSURL URLWithString:minstr([liveClass[i] valueForKey:@"thumb"])] placeholderImage:[UIImage imageNamed:@"live_all"]];
        [button addSubview:imgView];
        UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(0, imgView.bottom, button.width, button.height-(imgView.bottom))];
        label.textColor = RGB(99, 99, 99);
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:10];
        [label setText:minstr([liveClass[i] valueForKey:@"name"])];
        [button addSubview:label];

    }
    bottomFloat = _cycleScroll.bottom+_window_width/6+10;

    if (_attliveArray.count > 0) {

        _attView = [[AttLiveingView alloc]initWithFrame:CGRectMake(0, bottomFloat, _window_width, (_window_width-20)/3+50) withNumb:_attent_live_nums andLiveArr:_attliveArray];
    }else{
        _attView = [[AttLiveingView alloc]initWithFrame:CGRectMake(0, bottomFloat, _window_width, 50) withNumb:_attent_live_nums andLiveArr:_attliveArray];

    }
    [collectionHeaderView addSubview:_attView];

    bottomFloat =_attView.bottom+5;

    if (_CarouselArray.count > 0) {
        if ( _recommendArray.count > 0) {
            collectionHeaderView.height =_window_width/6+_window_width*0.3 +(_window_width/2)+95+_attView.height;
            _recommendView = [[RecommendLiveView alloc]initWithFrame:CGRectMake(0, bottomFloat, _window_width, (_window_width/2)+95) andData:_recommendArray];
        }else{
            collectionHeaderView.height =_window_width/6+_window_width*0.3 +50+95+_attView.height;
            _recommendView = [[RecommendLiveView alloc]initWithFrame:CGRectMake(0, bottomFloat, _window_width, 50+95) andData:_recommendArray];
        }
    }else{
        if ( _recommendArray.count > 0) {
            
        collectionHeaderView.height =_window_width/6 +(_window_width/2)+95+_attView.height;
        _recommendView = [[RecommendLiveView alloc]initWithFrame:CGRectMake(0, bottomFloat, _window_width, (_window_width/2)+95) andData:_recommendArray];
        }else{
            collectionHeaderView.height =_window_width/6 +50+95+_attView.height;
            _recommendView = [[RecommendLiveView alloc]initWithFrame:CGRectMake(0, bottomFloat, _window_width, 50+95) andData:_recommendArray];
        }
    }
    [collectionHeaderView addSubview:_recommendView];
}
-(void)createCollectionView{

    UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
    flow.scrollDirection = UICollectionViewScrollDirectionVertical;
    flow.itemSize = CGSizeMake(_window_width/2-4.5, (_window_width/2-4.5));
    flow.minimumLineSpacing = 3;
    flow.minimumInteritemSpacing = 3;
    flow.sectionInset = UIEdgeInsetsMake(3, 3,3, 3);

    _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height) collectionViewLayout:flow];
    _collectionView.delegate   = self;
    _collectionView.dataSource = self;
    [self.view addSubview:_collectionView];
    [self.collectionView registerNib:[UINib nibWithNibName:@"HotCollectionViewCell" bundle:nil] forCellWithReuseIdentifier:@"HotCollectionViewCELL"];
    [self.collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"hotHeaderV"];
    self.collectionView.backgroundColor = RGB_COLOR(@"#f5f5f5", 1);
    if (@available(iOS 11.0, *)) {
        _collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        // Fallback on earlier versions
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        page = 1;
        [self pullInternet];
    }];
    _collectionView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
        page ++;
        [self pullInternet];
    }];
    _collectionView.contentInset = UIEdgeInsetsMake(64+statusbarHeight, 0, 0, 0);
    _collectionView.mj_header.ignoredScrollViewContentInsetTop = 64+statusbarHeight;

    [self pullInternet];

}
-(void)nothingview{
    noNetwork = [[YBNoWordView alloc]initWithBlock:^(id msg) {
        [self pullInternet];
    }];
    noNetwork.hidden = YES;
    [self.view addSubview:noNetwork];
}
-(void)yaoqingma{
    //主播推荐
    NSString *isregs = minstr([cityDefault getreg]);
    if ([isregs isEqual:@"1"]) {
        if (!tuijianw) {
            tuijianw = [[tuijianwindow alloc]initWithFrame:CGRectMake(0,0,_window_width,_window_height)];
            tuijianw.delegate = self;
            [tuijianw makeKeyAndVisible];
        }
    }else{
        [self jump];
    }
}
-(void)showyaoqingma{
    [cityDefault saveisreg:@"0"];
    [[NSUserDefaults standardUserDefaults] setObject:@"0" forKey:@"isagent"];
    alertController = [UIAlertController alertControllerWithTitle:YZMsg(@"请填写邀请码") message:@""preferredStyle:UIAlertControllerStyleAlert];
    [alertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
        codetextfield = textField;
    }];
    //修改按钮的颜色，同上可以使用同样的方法修改内容，样式
    UIAlertAction *defaultAction = [UIAlertAction actionWithTitle:YZMsg(@"确定")style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        if (codetextfield.text.length == 0) {
            [self presentViewController:alertController animated:YES completion:nil];
            [MBProgressHUD showError:YZMsg(@"请填写邀请码")];
            return;
        }
        [self uploadInvitationV:codetextfield.text];
    }];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消")style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    NSString *version = [UIDevice currentDevice].systemVersion;
    
    if (version.doubleValue < 9.0) {
        
    }
    else{
        [defaultAction setValue:normalColors forKey:@"_titleTextColor"];
        [cancelAction setValue:[UIColor darkGrayColor] forKey:@"_titleTextColor"];
    }
    [alertController addAction:defaultAction];
    NSString *agent_must = [[NSUserDefaults standardUserDefaults] objectForKey:@"agent_must"];
    if ([agent_must isEqual:@"0"]) {
        [alertController addAction:cancelAction];
    }
    [self presentViewController:alertController animated:YES completion:nil];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [codetextfield resignFirstResponder];
    });
}
//获取网络数据
-(void)pullInternet{
    NSLog(@"hotpage=------:%d",page);
    [YBToolClass postNetworkWithUrl:@"Home.getHot" andParameter:@{@"p":@(page)} success:^(int code,id info,NSString *msg) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
        noNetwork.hidden = YES;

        if (code == 0) {
                NSArray *infoA = [info objectAtIndex:0];
                NSArray *list = [infoA valueForKey:@"list"];
                if (page == 1) {
                    [_infoArray removeAllObjects];
                    [_zhuboModel removeAllObjects];
                    self.CarouselArray = [infoA valueForKey:@"slide"];
                    self.recommendArray = [infoA valueForKey:@"recommend"];
                    self.attliveArray = [infoA valueForKey:@"attent_list"];
                    self.attent_live_nums = [infoA valueForKey:@"attent_live_nums"];
                    [self createCollectionHeaderView];

                }
                [_infoArray addObjectsFromArray:list];
                for (NSDictionary *dic in list) {
                    hotModel *model = [[hotModel alloc]initWithDic:dic];
                    [_zhuboModel addObject:model];
                }
                [_collectionView reloadData];
                if (_infoArray.count < 1) {
                    noDataView.hidden = NO;
                    noDataView.frame = CGRectMake(0, collectionHeaderView.height+20, _window_width, 40);
                }else{
                    noDataView.hidden = YES;
                }
            if (list.count == 0) {
                [_collectionView.mj_footer endRefreshingWithNoMoreData];
            }
        }
        
    } fail:^{
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
        if (_infoArray.count == 0) {
            noNetwork.hidden = NO;
        }
    }];
}
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return _zhuboModel.count;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    currentPlayIndex = indexPath.row;
    selectedDic = _infoArray[indexPath.row];

    BOOL isShowLive = [[NSUserDefaults standardUserDefaults]boolForKey:@"isShowChatLive"];
    if (isShowLive) {
        [[NSNotificationCenter defaultCenter]postNotificationName:@"HIDELIVEVIEW" object:nil];
    }

    [self checklive:[selectedDic valueForKey:@"stream"] andliveuid:[selectedDic valueForKey:@"uid"]];

}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    HotCollectionViewCell *cell = (HotCollectionViewCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"HotCollectionViewCELL" forIndexPath:indexPath];
    cell.model = _zhuboModel[indexPath.row];
    
    return cell;
}

#pragma mark ================ collectionview头视图 ===============
- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath{
    if ([kind isEqualToString:UICollectionElementKindSectionHeader]) {

        UICollectionReusableView *header = [collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"hotHeaderV" forIndexPath:indexPath];

        header.backgroundColor = [UIColor whiteColor];
        [header addSubview:collectionHeaderView];
        return header;
    }else{
        return nil;
    }
}
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section{
    return  CGSizeMake(_window_width, collectionHeaderView.height);
}

-(void)checklive:(NSString *)stream andliveuid:(NSString *)liveuid{
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.checkLive"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.timeoutInterval = 5.0;
    request.HTTPMethod = @"post";
    NSString *language = [PublicObj getCurrentLanguage];
    NSString *param = [NSString stringWithFormat:@"uid=%@&token=%@&liveuid=%@&stream=%@&language=%@",[Config getOwnID],[Config getOwnToken],liveuid,stream,language];
    request.HTTPBody = [param dataUsingEncoding:NSUTF8StringEncoding];
    NSURLResponse *response;
    NSError *error;
    NSData *backData = [NSURLConnection sendSynchronousRequest:request returningResponse:&response error:&error];
    if (error) {
        [MBProgressHUD showError:@"无网络"];
    }
    else{
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:backData options:NSJSONReadingMutableContainers error:nil];
        NSNumber *number = [dic valueForKey:@"ret"];
        
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [dic valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                NSString *type = [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                
                type_val =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type_val"]];
                livetype =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                _sdkType = minstr([info valueForKey:@"live_sdk"]);
                NSString *live_type =minstr([info valueForKey:@"live_type"]);
                if ([live_type isEqual:@"1"]) {
                    [[YBSmallLiveWindow shareInstance]closeBtnClick];
                    UserRoomViewController *chatroom = [[UserRoomViewController alloc]init];
                    chatroom.playDoc = currentChatDic;
                    chatroom.sdkType = _sdkType;
                    [[MXBADelegate sharedAppDelegate] pushViewController:chatroom animated:YES];

                }else{
                    if ([livetype isEqual:@"0"]) {
                        [self pushMovieVC];
                    }
                    else if ([type isEqual:@"1"]){
                        NSString *_MD5 = [NSString stringWithFormat:@"%@",[info valueForKey:@"type_msg"]];
                        //密码
                        md5AlertController = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"请输入房间密码") preferredStyle:UIAlertControllerStyleAlert];
                        //添加一个取消按钮
                        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            [self.navigationController popViewControllerAnimated:YES];
                            [self dismissViewControllerAnimated:NO completion:nil];
                        }];
                        [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                        [md5AlertController addAction:cancelAction];

                        //在AlertView中添加一个输入框
                        [md5AlertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
                            textField.secureTextEntry = YES;
                        }];
                        
                        //添加一个确定按钮 并获取AlertView中的第一个输入框 将其文本赋值给BUTTON的title
                        UIAlertAction *sureAction =[UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            UITextField *alertTextField = md5AlertController.textFields.firstObject;
                            //                        [self checkMD5WithText:envirnmentNameTextField.text andMD5:_MD5];
                            //输出 检查是否正确无误
                            NSLog(@"你输入的文本%@",alertTextField.text);
                            if ([_MD5 isEqualToString:[self stringToMD5:alertTextField.text]]) {
                                [self pushMovieVC];
                            }else{
                                alertTextField.text = @"";
                                [MBProgressHUD showError:YZMsg(@"密码错误")];
                                [self presentViewController:md5AlertController animated:true completion:nil];
                                return ;
                            }
                        }];
                        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                        [md5AlertController addAction:sureAction];
                        //present出AlertView
                        dispatch_async(dispatch_get_main_queue(), ^{
                            [self presentViewController:md5AlertController animated:true completion:nil];
                        });
                    }
                    else if ([type isEqual:@"2"] || [type isEqual:@"3"]){
                        if ([[YBYoungManager shareInstance]isOpenYoung]) {
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"青少年模式下不支持该功能") preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"知道了") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [self.navigationController popViewControllerAnimated:YES];
                                [self dismissViewControllerAnimated:NO completion:nil];

                            }];
                            [cancleAction setValue:[UIColor grayColor] forKey:@"_titleTextColor"];
                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"去关闭") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [[YBYoungManager shareInstance]checkYoungStatus:YoungFrom_Center];

                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                            [alertContro addAction:sureAction];
                            [self presentViewController:alertContro animated:YES completion:nil];
                        }else{
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:minstr([info valueForKey:@"type_msg"]) preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [self.navigationController popViewControllerAnimated:YES];
                                [self dismissViewControllerAnimated:NO completion:nil];

                            }];
                            [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                if ([[Config getOwnID] intValue] <= 0) {
                                    [[YBToolClass sharedInstance]waringLogin];
                                    return;
                                }

                                [self doCoast];
                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];

                            [alertContro addAction:sureAction];
                            dispatch_async(dispatch_get_main_queue(), ^{
                                [self presentViewController:alertContro animated:YES completion:nil];
                            });

                        }

                    }

                }
            }
            else{
                NSString *msg = [NSString stringWithFormat:@"%@",[data valueForKey:@"msg"]];
                [MBProgressHUD showError:msg];
            }
        }else{
            [MBProgressHUD showError:@"无网络"];
        }
    }    
}
-(void)pushMovieVC{
    [[YBSmallLiveWindow shareInstance]closeBtnClick];
    
    YBWeakSelf;
    moviePlay *player = [[moviePlay alloc]init];
    player.scrollarray = _infoArray;
    player.scrollindex = currentPlayIndex;
    player.playDoc = selectedDic;
    player.type_val = type_val;
    player.livetype = livetype;
    player.sdkType = _sdkType;
    player.endEvent = ^{
        page = 1;
        [weakSelf pullInternet];
    };
    [[MXBADelegate sharedAppDelegate] pushViewController:player animated:YES];
}
- (NSString *)stringToMD5:(NSString *)str
{
    
    //1.首先将字符串转换成UTF-8编码, 因为MD5加密是基于C语言的,所以要先把字符串转化成C语言的字符串
    const char *fooData = [str UTF8String];
    
    //2.然后创建一个字符串数组,接收MD5的值
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    
    //3.计算MD5的值, 这是官方封装好的加密方法:把我们输入的字符串转换成16进制的32位数,然后存储到result中
    CC_MD5(fooData, (CC_LONG)strlen(fooData), result);
    /**
     第一个参数:要加密的字符串
     第二个参数: 获取要加密字符串的长度
     第三个参数: 接收结果的数组
     */
    
    //4.创建一个字符串保存加密结果
    NSMutableString *saveResult = [NSMutableString string];
    
    //5.从result 数组中获取加密结果并放到 saveResult中
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [saveResult appendFormat:@"%02x", result[i]];
    }
    /*
     x表示十六进制，%02X  意思是不足两位将用0补齐，如果多余两位则不影响
     NSLog("%02X", 0x888);  //888
     NSLog("%02X", 0x4); //04
     */
    return saveResult;
}
//执行扣费
-(void)doCoast{
    [YBToolClass postNetworkWithUrl:@"Live.roomCharge" andParameter:@{@"liveuid":minstr([selectedDic valueForKey:@"uid"]),@"stream":minstr([selectedDic valueForKey:@"stream"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            NSDictionary *infos = [info firstObject];
            LiveUser *user = [Config myProfile];
            user.coin = [NSString stringWithFormat:@"%@",[infos valueForKey:@"coin"]];
            user.level = [NSString stringWithFormat:@"%@",[infos valueForKey:@"level"]];
            [Config updateProfile:user];

            [self pushMovieVC];
            //计时扣费
        }else{
            [MBProgressHUD showError:msg];
        }
        
    } fail:^{
        [self.collectionView.mj_header endRefreshing];
    }];

}
-(void)uploadInvitationV:(NSString *)codeStr{
    [YBToolClass postNetworkWithUrl:@"User.setDistribut" andParameter:@{@"code":codeStr} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *subdic = [info firstObject];
            [MBProgressHUD showError:minstr([subdic valueForKey:@"msg"])];
        }else{
            [MBProgressHUD showError:msg];
            [self presentViewController:alertController animated:YES completion:nil];
        }
    } fail:^{
    }];
    
}
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    oldOffset = scrollView.contentOffset.y;
}
- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    NSLog(@"hotpage-----:%f  old:%f", scrollView.contentOffset.y, oldOffset);
    if (scrollView.contentOffset.y >= oldOffset) {
        if (scrollView.contentOffset.y > 0) {
            _pageView.hidden = YES;
            [self hideTabBar];
        }
    }else{
        _pageView.hidden = NO;
        [self showTabBar];
    }
}
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    NSLog(@"%f",oldOffset);
}
#pragma mark ================ 分类按钮点击事件 ===============
- (void)liveClassBtnClick:(UIButton *)sender{
    
    if (sender.tag == 10086) {
        AllClassVC *allClass = [[AllClassVC alloc]init];
        [[MXBADelegate sharedAppDelegate]pushViewController:allClass animated:YES];
    }else{
            NSDictionary *dic = liveClass[sender.tag - 20180922];
            [self pushLiveClassVC:dic];
    }
}
-(void)pushChatVC{
    ChatClassVC *chat = [[ChatClassVC alloc]init];
    [[MXBADelegate sharedAppDelegate]pushViewController:chat animated:YES];
}

- (void)pushLiveClassVC:(NSDictionary *)dic{
    if ([minstr([dic valueForKey:@"id"]) isEqual:@"0"]) {
        [self pushChatVC];
    }else{
        classVC *class = [[classVC alloc]init];
        class.titleStr = minstr([dic valueForKey:@"name"]);
        class.classID = minstr([dic valueForKey:@"id"]);
        [[MXBADelegate sharedAppDelegate] pushViewController:class animated:YES];

    }

}
#pragma mark ================ 隐藏和显示tabbar ===============
- (void)hideTabBar {
    
    if (self.tabBarController.tabBar.hidden == YES) {
        return;
    }
    self.tabBarController.tabBar.hidden = YES;
}
- (void)showTabBar

{
    if (self.tabBarController.tabBar.hidden == NO)
    {
        return;
    }
    self.tabBarController.tabBar.hidden = NO;

}
#pragma mark ============轮播图点击=============
-(void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index{
    NSString *uuuuuuu = minstr([_CarouselArray[index] valueForKey:@"slide_url"]);
    if ([YBToolClass isUrlString:uuuuuuu]) {
        YBWebViewController *web = [[YBWebViewController alloc]init];
        web.urls = uuuuuuu;
        [[MXBADelegate sharedAppDelegate] pushViewController:web animated:YES];
    }
}

@end
