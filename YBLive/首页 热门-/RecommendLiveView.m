//
//  RecommendLiveView.m
//  YBLive
//
//  Created by ybRRR on 2020/9/8.
//  Copyright © 2020 cat. All rights reserved.
//

#import "RecommendLiveView.h"
#import "hotModel.h"
#import "HotChatCell.h"
#import "RankVC.h"
#import "LivePlay.h"
#import "RecommendLiveVC.h"
#import "RecommendChatVC.h"
#import "UserRoomViewController.h"
@implementation RecommendLiveView

-(instancetype)initWithFrame:(CGRect)frame andData:(NSArray *)dataArr{
    self = [super initWithFrame:frame];
    if (self) {
        currentPlayIndex = 0;
        self.backgroundColor = RGB_COLOR(@"#f5f5f5", 1);
        dataArray = dataArr;
        type_val = @"0";
        livetype = @"0";

        recommendList = [NSMutableArray array];
        UIImageView *recommendImg = [[UIImageView alloc]init];
        recommendImg.frame = CGRectMake(12, 10, 22, 22);
        recommendImg.image = [UIImage imageNamed:@"首页-聊天室"];
        [self addSubview:recommendImg];
        
        UILabel *recommendtitle = [[UILabel alloc]init];
        recommendtitle.frame = CGRectMake(recommendImg.right+5, 10, 100, 30);
        recommendtitle.font  = [UIFont boldSystemFontOfSize:15];
        recommendtitle.textColor = [UIColor blackColor];
        recommendtitle.text = YZMsg(@"聊天室");
        [self addSubview:recommendtitle];

        
        UIButton *moreBtn = [UIButton buttonWithType:0];
        moreBtn.frame = CGRectMake(_window_width-95, 10, 80, 30);
        [moreBtn setImage:[UIImage imageNamed:@"rlive_right"] forState:0];
        [moreBtn setTitle:YZMsg(@"全部查看") forState:0];
        moreBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        [moreBtn setTitleColor:RGB(86, 86, 86) forState:0];
        [moreBtn addTarget:self action:@selector(moreLiveClick) forControlEvents:UIControlEventTouchUpInside];
        [moreBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, - moreBtn.imageView.image.size.width, 0, moreBtn.imageView.image.size.width)];
        [moreBtn setImageEdgeInsets:UIEdgeInsetsMake(0, moreBtn.titleLabel.bounds.size.width, 0, -moreBtn.titleLabel.bounds.size.width)];

        [self addSubview:moreBtn];
        
        for (NSDictionary *dic in dataArr) {
            hotModel *model = [[hotModel alloc]initWithDic:dic];
            [recommendList addObject:model];
        }
        UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
        flow.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        flow.itemSize = CGSizeMake(_window_width/2-4.5, (_window_width/2-4.5));
        flow.minimumLineSpacing = 3;
        flow.minimumInteritemSpacing = 3;
        flow.sectionInset = UIEdgeInsetsMake(3, 3,3, 3);
        
        if(dataArr.count > 0){
        _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,recommendImg.bottom+10, _window_width, (_window_width/2)) collectionViewLayout:flow];
        }else{
            _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,recommendImg.bottom+10, _window_width, 50) collectionViewLayout:flow];

        }
        _collectionView.delegate   = self;
        _collectionView.dataSource = self;
        _collectionView.backgroundColor = RGB_COLOR(@"#f5f5f5", 1);
        _collectionView.showsHorizontalScrollIndicator = NO;
        [self.collectionView registerNib:[UINib nibWithNibName:@"HotChatCell" bundle:nil] forCellWithReuseIdentifier:@"HotChatCELL"];
        [self.collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"hotHeaderV"];
        
        [self addSubview:_collectionView];
        
        if (recommendList.count < 1) {
            [PublicView showTextNoData:_collectionView text1:YZMsg(@"还没有聊天室开启喔~") text2:@""];
        }else{
            [PublicView hiddenTextNoData:_collectionView];
        }

        
        UIImageView *hotImg = [[UIImageView alloc]init];
        hotImg.frame = CGRectMake(12, _collectionView.bottom+9, 22, 22);
        hotImg.image = [UIImage imageNamed:@"hotLive"];
        [self addSubview:hotImg];
        
        UILabel *hotTitle = [[UILabel alloc]init];
        hotTitle.frame = CGRectMake(hotImg.right+5,_collectionView.bottom+9, 180, 22);
        hotTitle.font  = [UIFont boldSystemFontOfSize:15];
        hotTitle.textColor = [UIColor blackColor];
        hotTitle.text = YZMsg(@"热门主播");
        [self addSubview:hotTitle];

    }
    return self;
}
-(void)moreLiveClick{
    RecommendChatVC *list = [[RecommendChatVC alloc]init];
    [[MXBADelegate sharedAppDelegate]pushViewController:list animated:YES];
}

-(void)scrollViewDidScroll:(UIScrollView *)scrollView
{
}
#pragma mark--------排行按钮------------
-(void)rankBtnClick:(UIButton *)sender{
    if (sender.tag == 7777) {
        RankVC  *rank = [[RankVC alloc]init];
        rank.selIndex = 0;
        [[MXBADelegate sharedAppDelegate]pushViewController:rank animated:YES];
    }else{
        RankVC  *rank = [[RankVC alloc]init];
        rank.selIndex = 1;
        [[MXBADelegate sharedAppDelegate]pushViewController:rank animated:YES];
    }
}

#pragma mark------UICollectionViewDelegate
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return recommendList.count;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    HotChatCell *cell = (HotChatCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"HotChatCELL" forIndexPath:indexPath];
    cell.model = recommendList[indexPath.row];
    return cell;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    currentPlayIndex = indexPath.row;
    selectedDic = dataArray[indexPath.row];

    BOOL isShowLive = [[NSUserDefaults standardUserDefaults]boolForKey:@"isShowChatLive"];
    if (isShowLive) {
        [[NSNotificationCenter defaultCenter]postNotificationName:@"HIDELIVEVIEW" object:nil];
    }

    [self checklive:[selectedDic valueForKey:@"stream"] andliveuid:[selectedDic valueForKey:@"uid"]];

}
-(void)checklive:(NSString *)stream andliveuid:(NSString *)liveuid{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.checkLive"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.timeoutInterval = 5.0;
    request.HTTPMethod = @"post";
    NSString *language = [PublicObj getCurrentLanguage];
    NSString *param = [NSString stringWithFormat:@"uid=%@&token=%@&liveuid=%@&stream=%@&language=%@",[Config getOwnID],[Config getOwnToken],liveuid,stream,language];
    request.HTTPBody = [param dataUsingEncoding:NSUTF8StringEncoding];
    NSURLResponse *response;
    NSError *error;
    NSData *backData = [NSURLConnection sendSynchronousRequest:request returningResponse:&response error:&error];
    if (error) {
        
    }
    else{
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:backData options:NSJSONReadingMutableContainers error:nil];
        
        NSNumber *number = [dic valueForKey:@"ret"];
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [dic valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                _sdkType = minstr([info valueForKey:@"live_sdk"]);
                [self pushChatRoom];
            }
            else{
                NSString *msg = [NSString stringWithFormat:@"%@",[data valueForKey:@"msg"]];
                [MBProgressHUD showError:msg];
            }
        }
    }
}
-(void)pushChatRoom{
    [[YBSmallLiveWindow shareInstance]closeBtnClick];
    UserRoomViewController *chatroom = [[UserRoomViewController alloc]init];
    chatroom.playDoc = selectedDic;
    chatroom.sdkType = _sdkType;
    [[MXBADelegate sharedAppDelegate] pushViewController:chatroom animated:YES];

}

@end
