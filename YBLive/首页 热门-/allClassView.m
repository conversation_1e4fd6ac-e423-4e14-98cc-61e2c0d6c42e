//
//  allClassView.m
//  YBLive
//
//  Created by <PERSON> on 2018/9/22.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "allClassView.h"
#import "ChatClassVC.h"

@implementation allClassView{
    UIButton *hidBtn;
}

-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.3];
    if (self) {
    }
    return self;
}
//- (void)showWhiteView{
//    [UIView animateWithDuration:0.3 animations:^{
//        whiteView.frame = CGRectMake(0, 0, _window_width, count*_window_width/6+20);
//
//    } completion:^(BOOL finished) {
//        hidBtn.userInteractionEnabled = YES;
//
//    }];
//}
//- (void)hideSelf{
//    hidBtn.userInteractionEnabled = NO;
//    [UIView animateWithDuration:0.3 animations:^{
//        whiteView.frame = CGRectMake(0, -count*_window_width/6, _window_width, count*_window_width/6+20);
//    } completion:^(BOOL finished) {
//        self.hidden = YES;
//    }];
//
//}
@end
