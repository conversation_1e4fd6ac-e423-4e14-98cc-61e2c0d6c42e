//
//  AttLiveingView.m
//  YBLive
//
//  Created by ybRRR on 2022/5/31.
//  Copyright © 2022 cat. All rights reserved.
//

#import "AttLiveingView.h"
#import "attentionViewC.h"
#import "AllLiveingCell.h"
#import "UserRoomViewController.h"
#import "LivePlay.h"

@interface AttLiveingView ()<UICollectionViewDelegate, UICollectionViewDataSource>
{
    NSArray *_liveingArr;
    NSDictionary *_currentDic;
    
    NSString *type_val;//
    NSString *livetype;//
    NSString *_sdkType;//0-金山  1-腾讯
    UIAlertController *md5AlertController;

}
@property(nonatomic,strong)UICollectionView *collectionView;

@end

@implementation AttLiveingView

-(instancetype)initWithFrame:(CGRect)frame withNumb:(NSString *)number andLiveArr:(NSArray *)liveArr;
{
    self = [super initWithFrame:frame];
    if (self) {
        _liveingArr  = liveArr;
        self.backgroundColor = RGB_COLOR(@"#f5f5f5", 1);
        UIView *backView = [[UIView alloc]init];
        backView.frame = CGRectMake(10, 5, _window_width-20, self.height);
        backView.layer.cornerRadius = 5;
        backView.layer.masksToBounds = YES;
        backView.backgroundColor = UIColor.whiteColor;
        [self addSubview:backView];
        
        UILabel *mytitle = [[UILabel alloc]init];
        mytitle.font  = [UIFont boldSystemFontOfSize:15];
        mytitle.textColor = [UIColor blackColor];
        mytitle.text = YZMsg(@"我的关注");
        [backView addSubview:mytitle];
        if ([number intValue] == 0) {
            [mytitle mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(backView.mas_left).offset(10);
                make.centerY.equalTo(backView.mas_centerY);
            }];

        }else{
            [mytitle mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(backView.mas_left).offset(10);
                make.top.equalTo(backView.mas_top).offset(10);
            }];

        }
        
        UILabel *numLb = [[UILabel alloc]init];
        numLb.frame = CGRectMake(10, 10, 100, 30);
        numLb.font  = [UIFont systemFontOfSize:12];
        numLb.textColor = RGB(255, 72, 107);
        numLb.text = number;
        [backView addSubview:numLb];
        [numLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(mytitle);
            make.left.equalTo(mytitle.mas_right).offset(7);
        }];
        
        UILabel *subLb = [[UILabel alloc]init];
        subLb.frame = CGRectMake(10, 10, 100, 30);
        subLb.font  = [UIFont systemFontOfSize:12];
        subLb.textColor = UIColor.grayColor;
        subLb.text = YZMsg(@"人正在直播");
        [backView addSubview:subLb];
        [subLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(mytitle);
            make.left.equalTo(numLb.mas_right);
        }];

        UIButton *moreBtn = [UIButton buttonWithType:0];
        [moreBtn setImage:[UIImage imageNamed:@"rlive_right"] forState:0];
        [moreBtn addTarget:self action:@selector(moreAttClick) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:moreBtn];
        [moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(mytitle.mas_centerY);
            make.right.equalTo(backView.mas_right).offset(-10);
            make.width.height.mas_equalTo(30);
        }];
        
        if ([number intValue] > 0) {
            [self layoutIfNeeded];
            UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
            flow.scrollDirection = UICollectionViewScrollDirectionHorizontal;
            flow.itemSize = CGSizeMake(backView.width/3-4.5, (backView.width/3-4.5));
            flow.minimumLineSpacing = 3;
            flow.minimumInteritemSpacing = 3;
            flow.sectionInset = UIEdgeInsetsMake(3, 3,3, 3);
            
            _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,mytitle.bottom+10, backView.width, backView.width/3) collectionViewLayout:flow];
            _collectionView.delegate   = self;
            _collectionView.dataSource = self;
            _collectionView.backgroundColor =UIColor.whiteColor;
            _collectionView.showsHorizontalScrollIndicator = NO;
            [self.collectionView registerNib:[UINib nibWithNibName:@"AllLiveingCell" bundle:nil] forCellWithReuseIdentifier:@"AllLiveingCELL"];
            
            [backView addSubview:_collectionView];

        }
    }
    return self;
}

#pragma mark------UICollectionViewDelegate
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return _liveingArr.count;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    AllLiveingCell *cell = (AllLiveingCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"AllLiveingCELL" forIndexPath:indexPath];
    cell.dataDic = _liveingArr[indexPath.item];
    return cell;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    _currentDic = _liveingArr[indexPath.item];
    BOOL isShowLive = [[NSUserDefaults standardUserDefaults]boolForKey:@"isShowChatLive"];
    if (isShowLive) {
        [[NSNotificationCenter defaultCenter]postNotificationName:@"HIDELIVEVIEW" object:nil];
    }

    [self checklive:[_currentDic valueForKey:@"stream"] andliveuid:[_currentDic valueForKey:@"uid"]];

}
-(void)checklive:(NSString *)stream andliveuid:(NSString *)liveuid{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.checkLive"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.timeoutInterval = 5.0;
    request.HTTPMethod = @"post";
    NSString *param = [NSString stringWithFormat:@"uid=%@&token=%@&liveuid=%@&stream=%@",[Config getOwnID],[Config getOwnToken],liveuid,stream];
    request.HTTPBody = [param dataUsingEncoding:NSUTF8StringEncoding];
    NSURLResponse *response;
    NSError *error;
    NSData *backData = [NSURLConnection sendSynchronousRequest:request returningResponse:&response error:&error];
    if (error) {
    }
    else{
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:backData options:NSJSONReadingMutableContainers error:nil];
        
        NSNumber *number = [dic valueForKey:@"ret"];
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [dic valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"]){
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                NSString *type = [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                
                type_val =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type_val"]];
                 livetype =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                _sdkType = minstr([info valueForKey:@"live_sdk"]);

                if ([minstr([_currentDic valueForKey:@"live_type"]) isEqual:@"0"]) {
                    if ([type isEqual:@"0"]) {
                        [self pushMovieVC];
                    }
                    else if ([type isEqual:@"1"]){
                        //密码
                        NSString *_MD5 = [NSString stringWithFormat:@"%@",[info valueForKey:@"type_msg"]];
                        //密码
                        md5AlertController = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"本房间为密码房间，请输入密码") preferredStyle:UIAlertControllerStyleAlert];
                        //添加一个取消按钮
                        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                        }];
                        [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                        [md5AlertController addAction:cancelAction];

                        //在AlertView中添加一个输入框
                        [md5AlertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
                            textField.secureTextEntry = YES;
                        }];
                        
                        //添加一个确定按钮 并获取AlertView中的第一个输入框 将其文本赋值给BUTTON的title
                        UIAlertAction *sureAction =[UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            UITextField *alertTextField = md5AlertController.textFields.firstObject;
                            //                        [self checkMD5WithText:envirnmentNameTextField.text andMD5:_MD5];
                            //输出 检查是否正确无误
                            NSLog(@"你输入的文本%@",alertTextField.text);
                            if ([_MD5 isEqualToString:[self stringToMD5:alertTextField.text]]) {
                                [self pushMovieVC];
                            }else{
                                alertTextField.text = @"";
                                [MBProgressHUD showError:YZMsg(@"密码错误")];
                                [[[MXBADelegate sharedAppDelegate]topViewController] presentViewController:md5AlertController animated:true completion:nil];
                                return ;
                            }
                            
                        }];
                        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                        [md5AlertController addAction:sureAction];

                        [[[MXBADelegate sharedAppDelegate]topViewController] presentViewController:md5AlertController animated:YES completion:nil];

                        //present出AlertView
                    }else if ([type isEqual:@"2"] || [type isEqual:@"3"]){
                        if ([[YBYoungManager shareInstance]isOpenYoung]) {
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"青少年模式下不支持该功能") preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"知道了") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            }];
                            [cancleAction setValue:[UIColor grayColor] forKey:@"_titleTextColor"];
                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"去关闭") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [[YBYoungManager shareInstance]checkYoungStatus:YoungFrom_Center];

                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                            [alertContro addAction:sureAction];
                            dispatch_async(dispatch_get_main_queue(), ^{
                                [[[MXBADelegate sharedAppDelegate]topViewController] presentViewController:alertContro animated:YES completion:nil];
                            });
                        }else{
                            //收费
                            UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:minstr([info valueForKey:@"type_msg"]) preferredStyle:UIAlertControllerStyleAlert];
                            UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                
                            }];
                            [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                            [alertContro addAction:cancleAction];
                            UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                                [self doCoast];
                            }];
                            [sureAction setValue:normalColors forKey:@"_titleTextColor"];

                            [alertContro addAction:sureAction];
                            dispatch_async(dispatch_get_main_queue(), ^{
                                [[[MXBADelegate sharedAppDelegate]topViewController] presentViewController:alertContro animated:YES completion:nil];
                            });

                        }

                    }

                }else{
                    [self pushChatRoom];

                }

            }else{
                NSString *msg = [NSString stringWithFormat:@"%@",[data valueForKey:@"msg"]];
                [MBProgressHUD showError:msg];
            }
        }
    }
}

-(void)pushChatRoom{
    [[YBSmallLiveWindow shareInstance]closeBtnClick];
    UserRoomViewController *chatroom = [[UserRoomViewController alloc]init];
    chatroom.playDoc = _currentDic;
    chatroom.sdkType = _sdkType;
    [[MXBADelegate sharedAppDelegate] pushViewController:chatroom animated:YES];
}
-(void)moreAttClick{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    attentionViewC *attView = [[attentionViewC alloc]init];
    [[MXBADelegate sharedAppDelegate]pushViewController:attView animated:YES];
}

-(void)pushMovieVC{
    [[YBSmallLiveWindow shareInstance]closeBtnClick];

    YBWeakSelf;
    moviePlay *player = [[moviePlay alloc]init];
    player.scrollarray = _liveingArr;
    player.playDoc = _currentDic;
    player.type_val = type_val;
    player.livetype = livetype;
    player.sdkType = _sdkType;
//    player.endEvent = ^{
//        page = 1;
//        [weakSelf pullInternet];
//    };

    [[MXBADelegate sharedAppDelegate] pushViewController:player animated:YES];
}
- (NSString *)stringToMD5:(NSString *)str
{
    
    //1.首先将字符串转换成UTF-8编码, 因为MD5加密是基于C语言的,所以要先把字符串转化成C语言的字符串
    const char *fooData = [str UTF8String];
    
    //2.然后创建一个字符串数组,接收MD5的值
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    
    //3.计算MD5的值, 这是官方封装好的加密方法:把我们输入的字符串转换成16进制的32位数,然后存储到result中
    CC_MD5(fooData, (CC_LONG)strlen(fooData), result);
    /**
     第一个参数:要加密的字符串
     第二个参数: 获取要加密字符串的长度
     第三个参数: 接收结果的数组
     */
    
    //4.创建一个字符串保存加密结果
    NSMutableString *saveResult = [NSMutableString string];
    
    //5.从result 数组中获取加密结果并放到 saveResult中
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [saveResult appendFormat:@"%02x", result[i]];
    }
    /*
     x表示十六进制，%02X  意思是不足两位将用0补齐，如果多余两位则不影响
     NSLog("%02X", 0x888);  //888
     NSLog("%02X", 0x4); //04
     */
    return saveResult;
}
//执行扣费
-(void)doCoast{
    [YBToolClass postNetworkWithUrl:@"Live.roomCharge" andParameter:@{@"liveuid":minstr([_currentDic valueForKey:@"uid"]),@"stream":minstr([_currentDic valueForKey:@"stream"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            NSDictionary *infos = [info firstObject];
            LiveUser *user = [Config myProfile];
            user.coin = [NSString stringWithFormat:@"%@",[infos valueForKey:@"coin"]];
            user.level = [NSString stringWithFormat:@"%@",[infos valueForKey:@"level"]];
            [Config updateProfile:user];

            [self pushMovieVC];
            //计时扣费
            
        }else{
            [MBProgressHUD showError:msg];
        }
        
    } fail:^{
        [self.collectionView.mj_header endRefreshing];
    }];

}

@end
