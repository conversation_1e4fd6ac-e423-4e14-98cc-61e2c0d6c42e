<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="AllLiveingCell">
            <rect key="frame" x="0.0" y="0.0" width="95" height="99"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="95" height="99"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="AXJ-0J-jKr">
                        <rect key="frame" x="0.0" y="0.0" width="95" height="99"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="7"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="liveUser.png" translatesAutoresizingMaskIntoConstraints="NO" id="tDX-tP-A14">
                        <rect key="frame" x="7" y="77" width="15" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="S1Y-lU-7eo"/>
                            <constraint firstAttribute="width" constant="15" id="u26-gR-Abt"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="F0D-s3-0U8">
                        <rect key="frame" x="26" y="77.5" width="31" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="F0D-s3-0U8" firstAttribute="centerY" secondItem="tDX-tP-A14" secondAttribute="centerY" id="7vr-wi-sLg"/>
                <constraint firstAttribute="bottom" secondItem="AXJ-0J-jKr" secondAttribute="bottom" id="9tf-vY-cBn"/>
                <constraint firstItem="AXJ-0J-jKr" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="FQ6-JF-HCr"/>
                <constraint firstAttribute="bottom" secondItem="tDX-tP-A14" secondAttribute="bottom" constant="7" id="LNh-Vx-3jR"/>
                <constraint firstItem="tDX-tP-A14" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="7" id="QbY-w2-GOw"/>
                <constraint firstItem="AXJ-0J-jKr" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="ecS-rT-Fb3"/>
                <constraint firstAttribute="trailing" secondItem="AXJ-0J-jKr" secondAttribute="trailing" id="lSF-VE-avW"/>
                <constraint firstItem="F0D-s3-0U8" firstAttribute="leading" secondItem="tDX-tP-A14" secondAttribute="trailing" constant="4" id="v27-iG-Jy9"/>
            </constraints>
            <size key="customSize" width="95" height="99"/>
            <connections>
                <outlet property="numberLb" destination="F0D-s3-0U8" id="o0l-Lc-sWS"/>
                <outlet property="thumbImg" destination="AXJ-0J-jKr" id="f6b-qt-D9h"/>
            </connections>
            <point key="canvasLocation" x="163.04347826086959" y="94.754464285714278"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="liveUser.png" width="15" height="15"/>
    </resources>
</document>
