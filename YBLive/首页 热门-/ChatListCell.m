//
//  ChatListCell.m
//  YBLive
//
//  Created by ybRRR on 2022/1/21.
//  Copyright © 2022 cat. All rights reserved.
//

#import "ChatListCell.h"

@implementation ChatListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    _tjTipImg.image = [UIImage imageNamed:getImagename(@"语音直播-推荐")];

    _backView.layer.shadowColor = RGB(255, 243, 256).CGColor;
    //剪切边界 如果视图上的子视图layer超出视图layer部分就截取掉 如果添加阴影这个属性必须是NO 不然会把阴影切掉
    _backView.layer.masksToBounds = NO;
    //阴影半径，默认3
    _backView.layer.shadowRadius = 5;
    //shadowOffset阴影偏移，默认(0, -3),这个跟shadowRadius配合使用
    _backView.layer.shadowOffset = CGSizeMake(0.0f,0.0f);
    // 阴影透明度，默认0
    _backView.layer.shadowOpacity = 1;
    
    _animationView = [[YYAnimatedImageView alloc]init];
    NSURL *url = [[NSBundle mainBundle] URLForResource:@"首页-语音直播" withExtension:@"gif"];
    _animationView.yy_imageURL = url;
    [_thumbImg addSubview:_animationView];
    [_animationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_thumbImg).offset(8);
        make.bottom.equalTo(_thumbImg.mas_bottom).offset(-8);
        make.width.height.mas_equalTo(13);
    }];

}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}
+(ChatListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath {
    ChatListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ChatListCell"];
     if (!cell) {
             cell = [[[NSBundle mainBundle]loadNibNamed:@"ChatListCell" owner:nil options:nil]objectAtIndex:0];
     }
     return cell;

}
-(void)setDataDic:(NSDictionary *)dataDic
{
    [_thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr([dataDic valueForKey:@"thumb"])]];
    [_headImg sd_setImageWithURL:[NSURL URLWithString:minstr([dataDic valueForKey:@"avatar"])]];
    _titleLb.text = minstr([dataDic valueForKey:@"title"]);
    _nameLb.text = minstr([dataDic valueForKey:@"user_nickname"]);
    _numLb.text = minstr([dataDic valueForKey:@"nums"]);
    if ([minstr([dataDic valueForKey:@"isrecommend"]) isEqual:@"1"]) {
        _tjTipImg.hidden = NO;
    }else{
        _tjTipImg.hidden = YES;
    }
}
@end
