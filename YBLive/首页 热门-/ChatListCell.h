//
//  ChatListCell.h
//  YBLive
//
//  Created by ybRRR on 2022/1/21.
//  Copyright © 2022 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "hotModel.h"
#import <YYWebImage/YYWebImage.h>

@interface ChatListCell : UITableViewCell

@property (weak, nonatomic) IBOutlet UIView *backView;
@property (weak, nonatomic) IBOutlet UIImageView *thumbImg;
@property (weak, nonatomic) IBOutlet UILabel *titleLb;
@property (weak, nonatomic) IBOutlet UIImageView *headImg;
@property (weak, nonatomic) IBOutlet UILabel *nameLb;
@property (weak, nonatomic) IBOutlet UILabel *numLb;

@property (strong, nonatomic)  YYAnimatedImageView *animationView;

@property (nonatomic,strong)NSDictionary *dataDic;
@property (weak, nonatomic) IBOutlet UIImageView *tjTipImg;

+(ChatListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;
@end


