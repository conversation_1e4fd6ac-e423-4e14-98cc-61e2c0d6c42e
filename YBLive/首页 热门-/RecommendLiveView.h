//
//  RecommendLiveView.h
//  YBLive
//
//  Created by ybRRR on 2020/9/8.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
//#import <YYWebImage/YYWebImage.h>

NS_ASSUME_NONNULL_BEGIN

@interface RecommendLiveView : UIView<UICollectionViewDelegate, UICollectionViewDataSource>
{
    NSArray *dataArray;
    NSMutableArray *recommendList;
    NSInteger currentPlayIndex;

    NSDictionary *selectedDic;

    NSString *type_val;//
    NSString *livetype;//
    NSString *_sdkType;//0-金山  1-腾讯
    UIAlertController *md5AlertController;

}
@property(nonatomic,strong)UICollectionView *collectionView;
//@property (strong, nonatomic)  YYAnimatedImageView *animationView;

-(instancetype)initWithFrame:(CGRect)frame andData:(NSArray *)dataArr;
@end

NS_ASSUME_NONNULL_END
