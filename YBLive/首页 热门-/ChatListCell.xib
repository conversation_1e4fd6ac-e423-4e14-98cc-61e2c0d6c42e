<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="120" id="KGk-i7-Jjw" customClass="ChatListCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="120"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="120"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6a1-hb-Sn6">
                        <rect key="frame" x="10" y="7" width="300" height="106"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ggV-wU-wqe">
                                <rect key="frame" x="8" y="3" width="100" height="100"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="100" id="4yD-FR-Ejq"/>
                                    <constraint firstAttribute="height" constant="100" id="b09-8z-4u5"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kkB-Jr-T5O">
                                <rect key="frame" x="116" y="21" width="174" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="nuP-WG-dyG"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="LHn-UL-A5Z">
                                <rect key="frame" x="116" y="49" width="36" height="36"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="36" id="R0Y-AP-oLL"/>
                                    <constraint firstAttribute="height" constant="36" id="g6f-EE-qm4"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="18"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KsZ-X7-vHE">
                                <rect key="frame" x="157" y="58.5" width="35.5" height="17"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="twY-CS-MLw">
                                <rect key="frame" x="256.5" y="60.5" width="28.5" height="13"/>
                                <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chat-nums.png" translatesAutoresizingMaskIntoConstraints="NO" id="TcC-SY-txa">
                                <rect key="frame" x="236.5" y="59" width="16" height="16"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="16" id="8av-i6-bWd"/>
                                    <constraint firstAttribute="height" constant="16" id="fR9-RP-93R"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="LHn-UL-A5Z" firstAttribute="leading" secondItem="kkB-Jr-T5O" secondAttribute="leading" id="0Ab-4c-JLu"/>
                            <constraint firstItem="ggV-wU-wqe" firstAttribute="leading" secondItem="6a1-hb-Sn6" secondAttribute="leading" constant="8" id="4yl-OT-oY7"/>
                            <constraint firstItem="KsZ-X7-vHE" firstAttribute="centerY" secondItem="LHn-UL-A5Z" secondAttribute="centerY" id="DsB-eS-nOV"/>
                            <constraint firstItem="ggV-wU-wqe" firstAttribute="centerY" secondItem="6a1-hb-Sn6" secondAttribute="centerY" id="Glc-6V-M1c"/>
                            <constraint firstItem="kkB-Jr-T5O" firstAttribute="top" secondItem="6a1-hb-Sn6" secondAttribute="top" constant="21" id="Kv5-F1-1cD"/>
                            <constraint firstItem="kkB-Jr-T5O" firstAttribute="leading" secondItem="ggV-wU-wqe" secondAttribute="trailing" constant="8" id="PcN-5L-b2h"/>
                            <constraint firstItem="twY-CS-MLw" firstAttribute="centerY" secondItem="KsZ-X7-vHE" secondAttribute="centerY" id="QBM-Xs-MIE"/>
                            <constraint firstItem="twY-CS-MLw" firstAttribute="leading" secondItem="TcC-SY-txa" secondAttribute="trailing" constant="4" id="R7Q-cp-4Eh"/>
                            <constraint firstAttribute="trailing" secondItem="kkB-Jr-T5O" secondAttribute="trailing" constant="10" id="aXD-kf-Zdu"/>
                            <constraint firstItem="twY-CS-MLw" firstAttribute="top" secondItem="kkB-Jr-T5O" secondAttribute="bottom" constant="19.5" id="bku-LK-oMm"/>
                            <constraint firstAttribute="trailing" secondItem="twY-CS-MLw" secondAttribute="trailing" constant="15" id="fIP-4D-8j3"/>
                            <constraint firstItem="KsZ-X7-vHE" firstAttribute="leading" secondItem="LHn-UL-A5Z" secondAttribute="trailing" constant="5" id="mnn-UZ-d75"/>
                            <constraint firstItem="LHn-UL-A5Z" firstAttribute="top" secondItem="kkB-Jr-T5O" secondAttribute="bottom" constant="8" id="pC3-ek-Uaw"/>
                            <constraint firstItem="TcC-SY-txa" firstAttribute="centerY" secondItem="twY-CS-MLw" secondAttribute="centerY" id="qoV-d9-Q7o"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="语音直播-推荐.png" translatesAutoresizingMaskIntoConstraints="NO" id="khM-XR-UBS">
                        <rect key="frame" x="256" y="5" width="56" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="Mzi-Rf-LQo"/>
                            <constraint firstAttribute="width" constant="56" id="yw0-DI-TbZ"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="khM-XR-UBS" secondAttribute="trailing" constant="8" id="8DY-Q0-hJF"/>
                    <constraint firstAttribute="trailing" secondItem="6a1-hb-Sn6" secondAttribute="trailing" constant="10" id="CTf-83-hVu"/>
                    <constraint firstItem="khM-XR-UBS" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="5" id="He0-G0-qL7"/>
                    <constraint firstAttribute="bottom" secondItem="6a1-hb-Sn6" secondAttribute="bottom" constant="7" id="K67-9e-K8a"/>
                    <constraint firstItem="6a1-hb-Sn6" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="ccj-ra-yW3"/>
                    <constraint firstItem="6a1-hb-Sn6" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="7" id="xwW-Jl-q0m"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="backView" destination="6a1-hb-Sn6" id="or5-Id-Bko"/>
                <outlet property="headImg" destination="LHn-UL-A5Z" id="LHA-uZ-QId"/>
                <outlet property="nameLb" destination="KsZ-X7-vHE" id="4a1-nS-w1l"/>
                <outlet property="numLb" destination="twY-CS-MLw" id="N63-Af-Rk5"/>
                <outlet property="thumbImg" destination="ggV-wU-wqe" id="m5k-dJ-dlu"/>
                <outlet property="titleLb" destination="kkB-Jr-T5O" id="Pyj-Et-ZJk"/>
                <outlet property="tjTipImg" destination="khM-XR-UBS" id="b2u-Yv-RzR"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="87.053571428571431"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="chat-nums.png" width="16" height="16"/>
        <image name="语音直播-推荐.png" width="43.666667938232422" height="13.666666984558105"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
