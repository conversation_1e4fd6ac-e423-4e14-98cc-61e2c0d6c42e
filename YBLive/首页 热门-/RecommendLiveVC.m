//
//  RecommendLiveVC.m
//  YBLive
//
//  Created by ybRRR on 2020/9/8.
//  Copyright © 2020 cat. All rights reserved.
//

#import "RecommendLiveVC.h"
#import "hotModel.h"
#import "HotCollectionViewCell.h"
#import "LivePlay.h"

@interface RecommendLiveVC ()<UICollectionViewDelegate,UICollectionViewDataSource>
{
    int page;
    NSInteger currentPlayIndex;
    NSDictionary *selectedDic;
    UIAlertController *md5AlertController;
    NSString *type_val;//
    NSString *livetype;//
    NSString *_sdkType;//0-金山  1-腾讯

}
@property(nonatomic,strong)UICollectionView *collectionView;
@property(nonatomic,strong)NSMutableArray *zhuboModel;//主播模型
@property(nonatomic,strong)NSMutableArray *infoArray;//获取到的主播列表信息

@end

@implementation RecommendLiveVC

//获取网络数据
-(void)pullInternet{
    
    [YBToolClass postNetworkWithUrl:@"Home.getRecommendLive" andParameter:@{@"p":@(page)} success:^(int code,id info,NSString *msg) {
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];

        if (code == 0) {
                NSArray *infoA = info;
                if (page == 1) {
                    [_infoArray removeAllObjects];
                    [_zhuboModel removeAllObjects];
                }
                [_infoArray addObjectsFromArray:infoA];
                for (NSDictionary *dic in infoA) {
                    hotModel *model = [[hotModel alloc]initWithDic:dic];
                    [_zhuboModel addObject:model];
                }
                [_collectionView reloadData];
                if (_infoArray.count < 1) {
                    [PublicView showTextNoData:_collectionView text1:@"暂时没有主播开播" text2:@"赶快开启你的直播吧"];
                }else{
                    [PublicView hiddenTextNoData:_collectionView];
                }
            if (infoA.count == 0) {
                [_collectionView.mj_footer endRefreshingWithNoMoreData];
            }
        }
        
    } fail:^{
        [_collectionView.mj_header endRefreshing];
        [_collectionView.mj_footer endRefreshing];
    }];
    
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.titleL.text = YZMsg(@"推荐主播");
    page  = 1;
    currentPlayIndex = 0;
    type_val = @"0";
    livetype = @"0";

    self.infoArray    =  [NSMutableArray array];
    self.zhuboModel    =  [NSMutableArray array];

    [self createCollectionView];
}

-(void)createCollectionView{

    UICollectionViewFlowLayout *flow = [[UICollectionViewFlowLayout alloc]init];
    flow.scrollDirection = UICollectionViewScrollDirectionVertical;
    flow.itemSize = CGSizeMake(_window_width/2-6, (_window_width/2-6));
    flow.minimumLineSpacing = 3;
    flow.minimumInteritemSpacing = 3;
    flow.sectionInset = UIEdgeInsetsMake(3, 3,3, 3);

    _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,64+statusbarHeight, _window_width, _window_height-64) collectionViewLayout:flow];
    _collectionView.delegate   = self;
    _collectionView.dataSource = self;
    [self.view addSubview:_collectionView];
    [self.collectionView registerNib:[UINib nibWithNibName:@"HotCollectionViewCell" bundle:nil] forCellWithReuseIdentifier:@"HotCollectionViewCELL"];
    [self.collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"hotHeaderV"];

    if (@available(iOS 11.0, *)) {
        _collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        // Fallback on earlier versions
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    _collectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        page = 1;
        [self pullInternet];
    }];
    _collectionView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
        page ++;
        [self pullInternet];
    }];
    
    
    _collectionView.backgroundColor = [UIColor whiteColor];
    self.view.backgroundColor = [UIColor whiteColor];
//    _collectionView.contentInset = UIEdgeInsetsMake(64+statusbarHeight, 0, 0, 0);
    [self pullInternet];

}
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return _zhuboModel.count;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    currentPlayIndex = indexPath.row;
    selectedDic = _infoArray[indexPath.row];

    BOOL isShowLive = [[NSUserDefaults standardUserDefaults]boolForKey:@"isShowChatLive"];
    if (isShowLive) {
        [[NSNotificationCenter defaultCenter]postNotificationName:@"HIDELIVEVIEW" object:nil];
    }

    [self checklive:[selectedDic valueForKey:@"stream"] andliveuid:[selectedDic valueForKey:@"uid"]];

}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    HotCollectionViewCell *cell = (HotCollectionViewCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"HotCollectionViewCELL" forIndexPath:indexPath];
    cell.model = _zhuboModel[indexPath.row];
    
    return cell;
}
-(void)checklive:(NSString *)stream andliveuid:(NSString *)liveuid{
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.checkLive"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.timeoutInterval = 5.0;
    request.HTTPMethod = @"post";
    NSString *language = [PublicObj getCurrentLanguage];
    NSString *param = [NSString stringWithFormat:@"uid=%@&token=%@&liveuid=%@&stream=%@&language=%@",[Config getOwnID],[Config getOwnToken],liveuid,stream,language];
    request.HTTPBody = [param dataUsingEncoding:NSUTF8StringEncoding];
    NSURLResponse *response;
    NSError *error;
    NSData *backData = [NSURLConnection sendSynchronousRequest:request returningResponse:&response error:&error];
    if (error) {
        [MBProgressHUD showError:@"无网络"];
    }
    else{
        
        
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:backData options:NSJSONReadingMutableContainers error:nil];
        NSNumber *number = [dic valueForKey:@"ret"];
        
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [dic valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                NSString *type = [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                
                type_val =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type_val"]];
                livetype =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                _sdkType = minstr([info valueForKey:@"live_sdk"]);

                if ([type isEqual:@"0"]) {
                    [self pushMovieVC];
                }
                else if ([type isEqual:@"1"]){
                    NSString *_MD5 = [NSString stringWithFormat:@"%@",[info valueForKey:@"type_msg"]];
                    //密码
                    md5AlertController = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"请输入房间密码") preferredStyle:UIAlertControllerStyleAlert];
                    //添加一个取消按钮
                    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                        [self.navigationController popViewControllerAnimated:YES];
                        [self dismissViewControllerAnimated:NO completion:nil];
                    }];
                    [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                    [md5AlertController addAction:cancelAction];

                    //在AlertView中添加一个输入框
                    [md5AlertController addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
                        textField.secureTextEntry = YES;
//                        textField.placeholder = @"请输入密码";
                    }];
                    
                    //添加一个确定按钮 并获取AlertView中的第一个输入框 将其文本赋值给BUTTON的title
                    UIAlertAction *sureAction =[UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                        UITextField *alertTextField = md5AlertController.textFields.firstObject;
                        //                        [self checkMD5WithText:envirnmentNameTextField.text andMD5:_MD5];
                        //输出 检查是否正确无误
                        NSLog(@"你输入的文本%@",alertTextField.text);
                        if ([_MD5 isEqualToString:[self stringToMD5:alertTextField.text]]) {
                            [self pushMovieVC];
                        }else{
                            alertTextField.text = @"";
                            [MBProgressHUD showError:YZMsg(@"密码错误")];
                            [self presentViewController:md5AlertController animated:true completion:nil];
                            return ;
                        }
                        
                    }];
                    [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                    [md5AlertController addAction:sureAction];

                    
                    //present出AlertView
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [self presentViewController:md5AlertController animated:true completion:nil];
                    });
                }
                else if ([type isEqual:@"2"] || [type isEqual:@"3"]){
                    if ([[YBYoungManager shareInstance]isOpenYoung]) {
                        UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"青少年模式下不支持该功能") preferredStyle:UIAlertControllerStyleAlert];
                        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"知道了") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            [self.navigationController popViewControllerAnimated:YES];
                            [self dismissViewControllerAnimated:NO completion:nil];

                        }];
                        [cancleAction setValue:[UIColor grayColor] forKey:@"_titleTextColor"];
                        [alertContro addAction:cancleAction];
                        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"去关闭") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            [[YBYoungManager shareInstance]checkYoungStatus:YoungFrom_Center];

                        }];
                        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
                        [alertContro addAction:sureAction];
                        [self presentViewController:alertContro animated:YES completion:nil];
                    }else{
                        UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:minstr([info valueForKey:@"type_msg"]) preferredStyle:UIAlertControllerStyleAlert];
                        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            [self.navigationController popViewControllerAnimated:YES];
                            [self dismissViewControllerAnimated:NO completion:nil];

                        }];
                        [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
                        [alertContro addAction:cancleAction];
                        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                            if ([[Config getOwnID] intValue] <= 0) {
                                [[YBToolClass sharedInstance]waringLogin];
                                return;
                            }

                            [self doCoast];
                        }];
                        [sureAction setValue:normalColors forKey:@"_titleTextColor"];

                        [alertContro addAction:sureAction];
                        dispatch_async(dispatch_get_main_queue(), ^{
                            [self presentViewController:alertContro animated:YES completion:nil];
                        });

                    }


                }
                
            }
            else{
                NSString *msg = [NSString stringWithFormat:@"%@",[data valueForKey:@"msg"]];
                [MBProgressHUD showError:msg];
            }
        }else{
            [MBProgressHUD showError:@"无网络"];
        }
        
    }
    
}
-(void)pushMovieVC{
    [[YBSmallLiveWindow shareInstance]closeBtnClick];

    moviePlay *player = [[moviePlay alloc]init];
    player.scrollarray = _infoArray;
    player.scrollindex = currentPlayIndex;
    player.playDoc = selectedDic;
    player.type_val = type_val;
    player.livetype = livetype;
    player.sdkType = _sdkType;
    [[MXBADelegate sharedAppDelegate] pushViewController:player animated:YES];
}
- (NSString *)stringToMD5:(NSString *)str
{
    
    //1.首先将字符串转换成UTF-8编码, 因为MD5加密是基于C语言的,所以要先把字符串转化成C语言的字符串
    const char *fooData = [str UTF8String];
    
    //2.然后创建一个字符串数组,接收MD5的值
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    
    //3.计算MD5的值, 这是官方封装好的加密方法:把我们输入的字符串转换成16进制的32位数,然后存储到result中
    CC_MD5(fooData, (CC_LONG)strlen(fooData), result);
    /**
     第一个参数:要加密的字符串
     第二个参数: 获取要加密字符串的长度
     第三个参数: 接收结果的数组
     */
    
    //4.创建一个字符串保存加密结果
    NSMutableString *saveResult = [NSMutableString string];
    
    //5.从result 数组中获取加密结果并放到 saveResult中
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [saveResult appendFormat:@"%02x", result[i]];
    }
    /*
     x表示十六进制，%02X  意思是不足两位将用0补齐，如果多余两位则不影响
     NSLog("%02X", 0x888);  //888
     NSLog("%02X", 0x4); //04
     */
    return saveResult;
}
//执行扣费
-(void)doCoast{
    [YBToolClass postNetworkWithUrl:@"Live.roomCharge" andParameter:@{@"liveuid":minstr([selectedDic valueForKey:@"uid"]),@"stream":minstr([selectedDic valueForKey:@"stream"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            NSDictionary *infos = [info firstObject];
            LiveUser *user = [Config myProfile];
            user.coin = [NSString stringWithFormat:@"%@",[infos valueForKey:@"coin"]];
            user.level = [NSString stringWithFormat:@"%@",[infos valueForKey:@"level"]];
            [Config updateProfile:user];

            [self pushMovieVC];
            //计时扣费
            
        }else{
            [MBProgressHUD showError:msg];
        }
        
    } fail:^{
        [self.collectionView.mj_header endRefreshing];
    }];

}


@end
