//
//  HotChatCell.m
//  YBLive
//
//  Created by ybRRR on 2022/1/21.
//  Copyright © 2022 cat. All rights reserved.
//

#import "HotChatCell.h"

@implementation HotChatCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    _tjImg.image = [UIImage imageNamed:getImagename(@"语音-推荐")];

    _animationView = [[YYAnimatedImageView alloc]init];
    NSURL *url = [[NSBundle mainBundle] URLForResource:@"首页-语音直播" withExtension:@"gif"];
    _animationView.yy_imageURL = url;
    [self.contentView addSubview:_animationView];
    [_animationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(8);
        make.top.equalTo(self.contentView.mas_top).offset(7);
        make.width.height.mas_equalTo(13);
    }];
}
- (void)setModel:(hotModel *)model{
    _model = model;
    if (_model.zhuboImage) {
        [_thumbImg sd_setImageWithURL:[NSURL URLWithString:_model.zhuboImage]];
        [_headImg sd_setImageWithURL:[NSURL URLWithString:_model.zhuboImage]];
    }else{
        [_thumbImg sd_setImageWithURL:[NSURL URLWithString:_model.avatar_thumb]];
        [_headImg sd_setImageWithURL:[NSURL URLWithString:_model.avatar_thumb]];
    }
    _nameLb.text = _model.zhuboName;
    _lookLb.text  =_model.onlineCount;
    _numLb.text =YZMsg(@"人在看");
    if ([model.isrecommend isEqual:@"1"]) {
        _tjImg.hidden = NO;
    }else{
        _tjImg.hidden = YES;
    }
}


@end
