<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14313.18" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14283.14"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="MusicHeaderCell">
            <rect key="frame" x="0.0" y="0.0" width="112" height="109"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="112" height="109"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="FtM-ZX-9ar" userLabel="图片">
                        <rect key="frame" x="26" y="3" width="60" height="60"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="FtM-ZX-9ar" secondAttribute="height" multiplier="1:1" id="ixN-53-ZV2"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Ke-Jn-6gm" userLabel="标题">
                        <rect key="frame" x="0.0" y="63" width="112" height="49"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.58431372549019611" green="0.58823529411764708" blue="0.59215686274509804" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="FtM-ZX-9ar" firstAttribute="height" secondItem="gTV-IL-0wX" secondAttribute="height" multiplier="0.55" id="18M-iD-vci"/>
                <constraint firstItem="FtM-ZX-9ar" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="3" id="2hk-iv-Fp2"/>
                <constraint firstItem="8Ke-Jn-6gm" firstAttribute="height" secondItem="gTV-IL-0wX" secondAttribute="height" multiplier="0.45" id="98I-Uz-T5c"/>
                <constraint firstItem="8Ke-Jn-6gm" firstAttribute="width" secondItem="gTV-IL-0wX" secondAttribute="width" id="E7x-Ig-5MH"/>
                <constraint firstItem="8Ke-Jn-6gm" firstAttribute="top" secondItem="FtM-ZX-9ar" secondAttribute="bottom" id="YGm-9g-2ag"/>
                <constraint firstItem="FtM-ZX-9ar" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="lsa-eH-fCr"/>
                <constraint firstItem="8Ke-Jn-6gm" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="pXF-en-tLf"/>
            </constraints>
            <size key="customSize" width="112" height="109"/>
            <connections>
                <outlet property="coverIV" destination="FtM-ZX-9ar" id="LgU-xT-rAO"/>
                <outlet property="titleL" destination="8Ke-Jn-6gm" id="rRj-0L-lc1"/>
            </connections>
            <point key="canvasLocation" x="65" y="16.5"/>
        </collectionViewCell>
    </objects>
</document>
