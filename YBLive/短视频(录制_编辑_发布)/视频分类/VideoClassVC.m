//
//  VideoClassVC.m
//  YBLive
//
//  Created by ybRRR on 2019/11/9.
//  Copyright © 2019 cat. All rights reserved.
//

#import "VideoClassVC.h"
#import "VideoClassCell.h"
#import "HXSearchBar.h"

@interface VideoClassVC ()<UITableViewDelegate, UITableViewDataSource,UISearchBarDelegate>
{
    UITableView *classTable;
    NSArray *classArray;
    HXSearchBar *searchBars;

}
@end

@implementation VideoClassVC
//添加搜索条
- (void)addSearchBar {
    //加上 搜索栏
    searchBars = [[HXSearchBar alloc] initWithFrame:CGRectMake(10,10 + statusbarHeight, self.view.frame.size.width -20,60)];
    searchBars.backgroundColor = [UIColor clearColor];
    searchBars.delegate = self;
    //输入框提示
    searchBars.placeholder = YZMsg(@"搜索分类");
    //光标颜色
    searchBars.cursorColor = normalColors;
    //TextField
    searchBars.searchBarTextField.layer.cornerRadius = 16;
    searchBars.searchBarTextField.layer.masksToBounds = YES;
//    searchBars.searchBarTextField.layer.borderColor = [UIColor grayColor].CGColor;
//    searchBars.searchBarTextField.layer.borderWidth = 1.0;
    searchBars.searchBarTextField.backgroundColor = RGB(241, 241, 241);
    //清除按钮图标
    //searchBar.clearButtonImage = [UIImage imageNamed:@"demand_delete"];
    //去掉取消按钮灰色背景
    searchBars.hideSearchBarBackgroundImage = YES;
    searchBars.searchBarTextField.font = [UIFont systemFontOfSize:14];
    [self.view addSubview:searchBars];
}
//已经开始编辑时的回调
- (void)searchBarTextDidBeginEditing:(UISearchBar *)searchBar {
    HXSearchBar *sear = (HXSearchBar *)searchBar;
    //取消按钮
    sear.cancleButton.backgroundColor = [UIColor clearColor];
    [sear.cancleButton setTitle:YZMsg(@"取消") forState:UIControlStateNormal];
    [sear.cancleButton setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
    sear.cancleButton.titleLabel.font = [UIFont systemFontOfSize:14];
}
//搜索按钮
- (void)searchBarSearchButtonClicked:(UISearchBar *)searchBar {
    
//    [self.allArray removeAllObjects];
//    [self.tableView reloadData];
//    page = 1;
//    [self getIN];
    [searchBar resignFirstResponder];

}
-(void)searchBar:(UISearchBar *)searchBar textDidChange:(NSString *)searchText{
    
//    [self.allArray removeAllObjects];
//    [self.tableView reloadData];
//    page = 1;
//    [self getIN];
}
//取消按钮点击的回调
- (void)searchBarCancelButtonClicked:(UISearchBar *)searchBar {
    searchBar.showsCancelButton = NO;
    searchBar.text = nil;
    [self.view endEditing:YES];
    [self.navigationController popViewControllerAnimated:YES];
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = [UIColor whiteColor];
//    [self addSearchBar];
    self.titleL.text = YZMsg(@"选择视频分类");
    classArray = [common videoclass];

    classTable = [[UITableView alloc]initWithFrame:CGRectMake(0, 64+statusbarHeight, _window_width, _window_height-64-statusbarHeight) style:UITableViewStylePlain];
    classTable.delegate = self;
    classTable.dataSource = self;
    classTable.separatorStyle = 0;
    [self.view addSubview:classTable];

}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return classArray.count;
}
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    VideoClassCell *cell = [tableView dequeueReusableCellWithIdentifier:@"VideoClassCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"VideoClassCell" owner:nil options:nil] lastObject];
    }
    NSDictionary *dic = classArray[indexPath.row];
    if ([minstr([dic valueForKey:@"id"])isEqual:_classID]) {
        cell.selectImg.hidden = NO;
    }else{
        cell.selectImg.hidden = YES;
    }
    cell.titleLb.text = minstr([dic valueForKey:@"name"]);
    return cell;
}
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 50)];
    view.backgroundColor = [UIColor whiteColor];
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(12, 5, _window_width-20, 40)];
    label.font = [UIFont systemFontOfSize:13];
    label.textColor = RGB_COLOR(@"#646464", 1);
    label.text = YZMsg(@"所有分类");
    [view addSubview:label];
    return view;
}
- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 50;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 50;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSDictionary *dic = classArray[indexPath.row];
    if (![minstr([dic valueForKey:@"id"])isEqual:_classID]) {
        self.block(dic);
        [self dismissViewControllerAnimated:YES completion:nil];
    }
    
}


@end
