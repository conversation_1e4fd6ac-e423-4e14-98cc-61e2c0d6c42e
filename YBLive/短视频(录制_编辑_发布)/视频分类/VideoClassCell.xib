<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="15400" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="15404"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="KGk-i7-Jjw" customClass="VideoClassCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MXb-KZ-K2I">
                        <rect key="frame" x="12" y="11.5" width="233" height="21"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="21" id="zKV-kM-Tjq"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="preClassS.png" translatesAutoresizingMaskIntoConstraints="NO" id="3uW-2v-T0Q">
                        <rect key="frame" x="284" y="12" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="SxD-vV-tlC"/>
                            <constraint firstAttribute="height" constant="20" id="a5o-fC-bGj"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fpK-rK-RqS">
                        <rect key="frame" x="10" y="42" width="300" height="1"/>
                        <color key="backgroundColor" cocoaTouchSystemColor="groupTableViewBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="szE-BW-U8B"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="fpK-rK-RqS" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="5pA-Oz-l8Y"/>
                    <constraint firstAttribute="trailing" secondItem="MXb-KZ-K2I" secondAttribute="trailing" constant="75" id="CDI-hX-2Ha"/>
                    <constraint firstAttribute="trailing" secondItem="fpK-rK-RqS" secondAttribute="trailing" constant="10" id="MSz-Gx-Cke"/>
                    <constraint firstAttribute="bottom" secondItem="fpK-rK-RqS" secondAttribute="bottom" constant="1" id="YrX-HB-SpQ"/>
                    <constraint firstItem="3uW-2v-T0Q" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="irT-Cy-Lr0"/>
                    <constraint firstItem="MXb-KZ-K2I" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="12" id="jq7-Gs-A8a"/>
                    <constraint firstItem="MXb-KZ-K2I" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="sdd-4Q-wQg"/>
                    <constraint firstAttribute="trailing" secondItem="3uW-2v-T0Q" secondAttribute="trailing" constant="16" id="tvX-sR-ken"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="selectImg" destination="3uW-2v-T0Q" id="QFR-7U-mhB"/>
                <outlet property="titleLb" destination="MXb-KZ-K2I" id="ObE-rD-oVC"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="93.75"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="preClassS.png" width="60" height="60"/>
    </resources>
</document>
