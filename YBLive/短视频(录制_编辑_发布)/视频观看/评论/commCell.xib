<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="18122" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="18093"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="commCELL" rowHeight="79" id="KGk-i7-Jjw" customClass="commCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="79"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="79"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="xjr-hL-b5Z">
                        <rect key="frame" x="10" y="10" width="30" height="30"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="30" id="7NP-uK-Ygh"/>
                            <constraint firstAttribute="width" constant="30" id="ZPB-MY-Ov9"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="15"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NC6-Io-P6a">
                        <rect key="frame" x="45" y="10" width="35.5" height="35"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1fs-uN-kjt">
                        <rect key="frame" x="45" y="53" width="35.5" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cMm-cD-gVn">
                        <rect key="frame" x="275" y="12.5" width="30" height="30"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="30" id="7FH-H4-CNx"/>
                            <constraint firstAttribute="height" constant="30" id="9GI-Fi-46Q"/>
                        </constraints>
                        <state key="normal" image="likecomment.png"/>
                        <connections>
                            <action selector="zanBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="r1A-dS-FYB"/>
                        </connections>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1234" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gWP-3x-fYL">
                        <rect key="frame" x="277" y="45" width="26" height="13.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <color key="textColor" red="0.50980392156862742" green="0.50980392156862742" blue="0.50980392156862742" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <tableView contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" contentViewInsetsToSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7PD-Wd-n1v">
                        <rect key="frame" x="55" y="78" width="230" height="0.0"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" id="VGt-k5-Gkq"/>
                        </constraints>
                        <connections>
                            <outlet property="dataSource" destination="KGk-i7-Jjw" id="G2f-M4-Mzm"/>
                            <outlet property="delegate" destination="KGk-i7-Jjw" id="ByJ-GI-vhN"/>
                        </connections>
                    </tableView>
                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kxf-jE-O1Z">
                        <rect key="frame" x="10" y="78" width="295" height="1"/>
                        <color key="backgroundColor" red="0.95686274509803915" green="0.96078431372549022" blue="0.96470588235294119" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="xnc-8J-7QU"/>
                        </constraints>
                    </view>
                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="comm_zzbg.png" translatesAutoresizingMaskIntoConstraints="NO" id="dkP-2L-cJh">
                        <rect key="frame" x="85.5" y="21" width="25" height="13"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="13" id="DsM-9u-mI2"/>
                            <constraint firstAttribute="width" constant="25" id="hEq-WB-KUt"/>
                        </constraints>
                    </imageView>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" misplaced="YES" text="作者" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mLu-64-xKq">
                        <rect key="frame" x="85" y="21" width="26" height="13"/>
                        <fontDescription key="fontDescription" type="system" pointSize="9"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="1fs-uN-kjt" firstAttribute="leading" secondItem="NC6-Io-P6a" secondAttribute="leading" id="1rN-ba-vAx"/>
                    <constraint firstItem="cMm-cD-gVn" firstAttribute="centerY" secondItem="NC6-Io-P6a" secondAttribute="centerY" id="AwV-2I-vHs"/>
                    <constraint firstAttribute="trailing" secondItem="7PD-Wd-n1v" secondAttribute="trailing" constant="35" id="BbO-PQ-dbc"/>
                    <constraint firstItem="mLu-64-xKq" firstAttribute="top" secondItem="dkP-2L-cJh" secondAttribute="top" id="DzF-m3-sgw"/>
                    <constraint firstItem="dkP-2L-cJh" firstAttribute="leading" secondItem="NC6-Io-P6a" secondAttribute="trailing" constant="5" id="Gqy-eS-742"/>
                    <constraint firstItem="kxf-jE-O1Z" firstAttribute="top" secondItem="7PD-Wd-n1v" secondAttribute="bottom" id="L05-hK-oej"/>
                    <constraint firstItem="mLu-64-xKq" firstAttribute="trailing" secondItem="dkP-2L-cJh" secondAttribute="trailing" id="NNY-dl-Od7"/>
                    <constraint firstItem="gWP-3x-fYL" firstAttribute="top" secondItem="cMm-cD-gVn" secondAttribute="bottom" constant="2.5" id="RQy-QO-clF"/>
                    <constraint firstAttribute="trailing" secondItem="cMm-cD-gVn" secondAttribute="trailing" constant="15" id="WqX-CQ-iqz"/>
                    <constraint firstItem="kxf-jE-O1Z" firstAttribute="trailing" secondItem="cMm-cD-gVn" secondAttribute="trailing" id="XYO-ib-RhE"/>
                    <constraint firstItem="7PD-Wd-n1v" firstAttribute="top" secondItem="1fs-uN-kjt" secondAttribute="bottom" constant="8" id="ZLj-2a-8EC"/>
                    <constraint firstAttribute="bottom" secondItem="kxf-jE-O1Z" secondAttribute="bottom" id="hpM-PA-QgE"/>
                    <constraint firstItem="1fs-uN-kjt" firstAttribute="top" secondItem="NC6-Io-P6a" secondAttribute="bottom" constant="8" id="i0x-vg-Koi"/>
                    <constraint firstItem="mLu-64-xKq" firstAttribute="bottom" secondItem="dkP-2L-cJh" secondAttribute="bottom" id="j1g-Di-Tc3"/>
                    <constraint firstItem="xjr-hL-b5Z" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="jJM-7F-DfT"/>
                    <constraint firstItem="mLu-64-xKq" firstAttribute="leading" secondItem="dkP-2L-cJh" secondAttribute="leading" id="lXV-vO-OUS"/>
                    <constraint firstItem="NC6-Io-P6a" firstAttribute="top" secondItem="xjr-hL-b5Z" secondAttribute="top" id="ln1-0R-dfN"/>
                    <constraint firstItem="NC6-Io-P6a" firstAttribute="leading" secondItem="xjr-hL-b5Z" secondAttribute="trailing" constant="5" id="loi-wa-Zpy"/>
                    <constraint firstItem="gWP-3x-fYL" firstAttribute="centerX" secondItem="cMm-cD-gVn" secondAttribute="centerX" id="mFQ-dY-hlP"/>
                    <constraint firstItem="cMm-cD-gVn" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="1fs-uN-kjt" secondAttribute="trailing" constant="15" id="qI8-va-Ubx"/>
                    <constraint firstItem="kxf-jE-O1Z" firstAttribute="leading" secondItem="xjr-hL-b5Z" secondAttribute="leading" id="utZ-d9-V4H"/>
                    <constraint firstItem="7PD-Wd-n1v" firstAttribute="leading" secondItem="1fs-uN-kjt" secondAttribute="leading" constant="10" id="wR9-mE-KHd"/>
                    <constraint firstItem="xjr-hL-b5Z" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="10" id="x0U-TC-s8d"/>
                    <constraint firstItem="dkP-2L-cJh" firstAttribute="centerY" secondItem="NC6-Io-P6a" secondAttribute="centerY" id="yE7-rN-46c"/>
                    <constraint firstItem="cMm-cD-gVn" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="NC6-Io-P6a" secondAttribute="trailing" constant="10" id="yic-Hh-V8b"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="authorImg" destination="dkP-2L-cJh" id="IXv-PJ-B6n"/>
                <outlet property="authorLb" destination="mLu-64-xKq" id="Di5-7P-7kc"/>
                <outlet property="contentL" destination="1fs-uN-kjt" id="Prz-hf-WcH"/>
                <outlet property="iconImgView" destination="xjr-hL-b5Z" id="akS-Q9-OXY"/>
                <outlet property="nameL" destination="NC6-Io-P6a" id="Sgd-aM-Z1J"/>
                <outlet property="replyTable" destination="7PD-Wd-n1v" id="IBQ-D0-dl8"/>
                <outlet property="tableHeight" destination="VGt-k5-Gkq" id="H82-GT-r7m"/>
                <outlet property="zanBtn" destination="cMm-cD-gVn" id="0wX-KD-woO"/>
                <outlet property="zanNumL" destination="gWP-3x-fYL" id="hMy-S8-f0u"/>
            </connections>
            <point key="canvasLocation" x="-243.19999999999999" y="55.322338830584712"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="comm_zzbg.png" width="24.5" height="13"/>
        <image name="likecomment.png" width="50" height="50"/>
    </resources>
</document>
