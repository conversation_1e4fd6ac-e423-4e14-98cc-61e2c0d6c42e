//
//  commDetailCell.m
//  YBLive
//
//  Created by <PERSON> on 2018/12/17.
//  Copyright © 2018年 cat. All rights reserved.
//

#import "commDetailCell.h"
#import "PersonHomeVC.h"

@implementation commDetailCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        _headImg = [[UIImageView alloc]initWithFrame:CGRectMake(5, 0, 20, 20)];
        _headImg.layer.cornerRadius = 10;
        _headImg.layer.masksToBounds = YES;
        _headImg.userInteractionEnabled = YES;
        [self.contentView addSubview:_headImg];
        
        UITapGestureRecognizer *userTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(userTapClick)];
        [_headImg addGestureRecognizer:userTap];
        
//        _nameL = [[UILabel alloc]initWithFrame:CGRectMake(_headImg.right+5, 2, self.contentView.width, 20)];
        _nameL = [[UILabel alloc]init];
        _nameL.textColor = RGB(130, 130, 130);
        _nameL.font = [UIFont systemFontOfSize:14];
        [self.contentView addSubview:_nameL];
        [_nameL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_headImg.mas_right).offset(5);
            make.top.equalTo(self.contentView).offset(2);
            make.height.mas_equalTo(20);
        }];
        
        _dAuthorImg = [[UIImageView alloc]init];
//        _dAuthorImg.frame = CGRectMake(_nameL.right+5, 0, 25, 13);
        _dAuthorImg.image = [UIImage imageNamed:@"comm_zzbg"];
        [self.contentView addSubview:_dAuthorImg];
        _dAuthorImg.centerY = _nameL.centerY;
        [_dAuthorImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(_nameL.mas_right).offset(5);
            make.width.mas_equalTo(25);
            make.height.mas_equalTo(13);
            make.centerY.equalTo(_nameL.mas_centerY);
        }];
        _dAuthorLb = [[UILabel alloc]init];//WithFrame:CGRectMake(_dAuthorImg.left, _dAuthorImg.top, _dAuthorImg.width, _dAuthorImg.height)];
        _dAuthorLb.font = [UIFont systemFontOfSize:9];
        _dAuthorLb.textColor = [UIColor whiteColor];
        _dAuthorLb.text = YZMsg(@"作者");
        _dAuthorLb.textAlignment = NSTextAlignmentCenter;
        [self.contentView addSubview:_dAuthorLb];
        [_dAuthorLb mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.bottom.equalTo(_dAuthorImg);
        }];
        
        _dAuthorImg.hidden = YES;
        _dAuthorLb.hidden = YES;
        
        _contentL = [[UILabel alloc]init];
        _contentL.textColor = RGB_COLOR(@"#333333", 1);
        _contentL.font = [UIFont systemFontOfSize:14];
        _contentL.numberOfLines = 0;
        [self.contentView addSubview:_contentL];

    }
    return self;
}
-(void)userTapClick{
    PersonHomeVC  *person = [[PersonHomeVC alloc]init];
    person.userID = _model.ID;
    [[MXBADelegate sharedAppDelegate]pushViewController:person animated:YES];

}
- (void)setModel:(detailmodel *)model{
    _model = model;
    if ([model.ID isEqual:_videoUserid]) {
        _dAuthorImg.hidden = NO;
        _dAuthorLb.hidden = NO;

    }else{
        _dAuthorImg.hidden = YES;
        _dAuthorLb.hidden = YES;

    }
    
    _nameL.text = _model.user_nicename;
    [_headImg sd_setImageWithURL:[NSURL URLWithString:model.avatar_thumb]];
    _contentL.frame = _model.contentRect;
    NSArray *resultArr  = [[YBToolClass sharedInstance] machesWithPattern:emojiPattern andStr:_model.content];
    if (!resultArr) return;
    NSUInteger lengthDetail = 0;
    NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc]initWithString:_model.content];
    //遍历所有的result 取出range
    for (NSTextCheckingResult *result in resultArr) {
        //取出图片名
        NSString *imageName =   [_model.content substringWithRange:NSMakeRange(result.range.location, result.range.length)];
        NSLog(@"--------%@",imageName);
        NSTextAttachment *attach = [[NSTextAttachment alloc] init];
        UIImage *emojiImage = [UIImage imageNamed:imageName];
        NSAttributedString *imageString;
        if (emojiImage) {
            attach.image = emojiImage;
            attach.bounds = CGRectMake(0, -2, 15, 15);
            imageString =   [NSAttributedString attributedStringWithAttachment:attach];
        }else{
            imageString =   [[NSMutableAttributedString alloc]initWithString:imageName];
        }
        //图片附件的文本长度是1
        NSLog(@"emoj===%zd===size-w:%f==size-h:%f",imageString.length,imageString.size.width,imageString.size.height);
        NSUInteger length = attStr.length;
        NSRange newRange = NSMakeRange(result.range.location - lengthDetail, result.range.length);
        [attStr replaceCharactersInRange:newRange withAttributedString:imageString];
        
        lengthDetail += length - attStr.length;
    }
    NSAttributedString *dateStr = [[NSAttributedString alloc]initWithString:[NSString stringWithFormat:@" %@",_model.datetime] attributes:@{NSForegroundColorAttributeName:RGB_COLOR(@"#959697", 1),NSFontAttributeName:[UIFont systemFontOfSize:11]}];
    [attStr appendAttributedString:dateStr];
    //更新到label上
    _contentL.attributedText = attStr;

}

@end
