//
//  YBLookVideoCell.m
//  YBLive
//
//  Created by ybRRR on 2020/9/17.
//  Copyright © 2020 cat. All rights reserved.
//

#import "YBLookVideoCell.h"
#import "PersonHomeVC.h"
#import "OutsideGoodsDetailVC.h"
#import "CommodityDetailVC.h"
#import "PayVideoDetailVC.h"
#import "RKLampView.h"

@interface YBLookVideoCell()

/** 头像 点赞 评论 分享集合 */
@property(nonatomic,strong)UIView *rightView;
@property(nonatomic,strong) UIButton *iconBtn;
@property(nonatomic,strong) UIButton *followBtn;                    //关注
@property(nonatomic,strong) UIButton *likebtn;                      //点赞
/** 名字 标题 (音乐)集合 */
@property(nonatomic,strong)UIView *botView;
@property(nonatomic,strong)UILabel *titleL;                         //视频标题
@property(nonatomic,strong)UILabel *nameL;

@property(nonatomic,strong) UIButton *shopButton;                 //商品按钮
@property(nonatomic,strong) UILabel *ad_Lb;

@property(nonatomic,strong)UIImageView *discIV;                     //唱片背景
@property(nonatomic,strong)UIImageView *musicIV;                    //歌曲背景图
@property(nonatomic,strong)UIImageView *symbolAIV;                  //音符A
@property(nonatomic,strong)UIImageView *symbolBIV;                  //音符B
@property(nonatomic,strong)RKLampView *musicL;                     //音乐
@property(nonatomic,strong)UIImageView *symIV;

@end

@implementation YBLookVideoCell


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self.contentView addSubview:self.bgImgView];
        [_bgImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.centerX.centerY.equalTo(self.contentView);
        }];
        [self setUp];
    }
    return self;
}

-(void)setUp {
    [self.contentView addSubview:self.rightView];
    [self.contentView addSubview:self.botView];
}

- (UIImageView *)bgImgView {
    if (!_bgImgView) {
        _bgImgView = [[UIImageView alloc] init];
        _bgImgView.userInteractionEnabled = YES;
        _bgImgView.backgroundColor = [UIColor blackColor];
        _bgImgView.tag =191107;
    }
    return _bgImgView;
}


#pragma mark - set/get
- (UIView *)rightView{
    if (!_rightView) {
        CGFloat rv_w = 85;
        CGFloat rv_h = 300;//头像+点赞+评论+分享
        CGFloat rv_all_h = 360;//头像+点赞+评论+分享 +唱片
        _rightView = [[UIView alloc]initWithFrame:CGRectMake(_window_width-rv_w, _window_height-rv_all_h-49-ShowDiff, rv_w, rv_all_h)];
        _rightView.backgroundColor = [UIColor clearColor];
        
        CGFloat btnW = 70;
        CGFloat btnH = 70;
        CGFloat spaceW = 15;
        CGFloat specialH = 10;//给头像和点赞之间特意多留
        CGFloat spaceH = (rv_h-specialH-btnH*4)/3;
        
        //主播头像
        _iconBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _iconBtn.frame = CGRectMake(10+spaceW, 0, 50, 50);
        _iconBtn.layer.masksToBounds = YES;
        _iconBtn.layer.borderWidth = 1;
        _iconBtn.layer.borderColor = [UIColor whiteColor].CGColor;
        _iconBtn.layer.cornerRadius = 25;
        _iconBtn.imageView.contentMode = UIViewContentModeScaleAspectFill;
        _iconBtn.imageView.clipsToBounds = YES;
        [_iconBtn addTarget:self action:@selector(zhuboMessage) forControlEvents:UIControlEventTouchUpInside];
        [_iconBtn sd_setBackgroundImageWithURL:[NSURL URLWithString:@""] forState:UIControlStateNormal placeholderImage:[UIImage imageNamed:@"default_head.png"]];
        
        //关注按钮
        _followBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _followBtn.frame = CGRectMake(_iconBtn.left+12, _iconBtn.bottom-13, 26, 26);
        [_followBtn setImage:[UIImage imageNamed:@"home_follow"] forState:0];
        [_followBtn addTarget:self action:@selector(guanzhuzhubo) forControlEvents:UIControlEventTouchUpInside];
        
        //点赞
        _likebtn = [UIButton buttonWithType:0];
        _likebtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        _likebtn.frame = CGRectMake(spaceW, _iconBtn.bottom+spaceH+specialH, btnW, btnH);
        [_likebtn addTarget:self action:@selector(dolike) forControlEvents:UIControlEventTouchUpInside];
        [_likebtn setImage:[UIImage imageNamed:@"home_zan"] forState:0];
        [_likebtn setTitle:@" 0 " forState:0];//空格占位符不要去除
        _likebtn.titleLabel.font = [UIFont systemFontOfSize:13];
        _likebtn = [PublicObj setUpImgDownText:_likebtn];
        
        //评论列表
        _commentBtn = [UIButton buttonWithType:0];
        [_commentBtn setImage:[UIImage imageNamed:@"home_comment"] forState:0];
        [_commentBtn setTitle:@" 0 " forState:0];//空格占位符不要去除
        _commentBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        _commentBtn.frame = CGRectMake(spaceW, _likebtn.bottom+spaceH, btnW,btnH);
        [_commentBtn addTarget:self action:@selector(messgaebtn) forControlEvents:UIControlEventTouchUpInside];
        _commentBtn.titleLabel.font = [UIFont systemFontOfSize:13];
        _commentBtn = [PublicObj setUpImgDownText:_commentBtn];
        
        //分享
        _enjoyBtn = [UIButton buttonWithType:0];
        [_enjoyBtn setImage:[UIImage imageNamed:@"home_share"] forState:0];
        [_enjoyBtn setTitle:@" 0 " forState:0];//空格占位符不要去除
        _enjoyBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        _enjoyBtn.frame = CGRectMake(spaceW, _commentBtn.bottom+spaceH, btnW,btnH);
        [_enjoyBtn addTarget:self action:@selector(doenjoy) forControlEvents:UIControlEventTouchUpInside];
        _enjoyBtn.titleLabel.font = [UIFont systemFontOfSize:13];
        _enjoyBtn = [PublicObj setUpImgDownText:_enjoyBtn];
               
        UIView *animationBgView = [[UIView alloc]init];
        animationBgView.backgroundColor = [UIColor clearColor];
        [_rightView addSubview:animationBgView];
        [animationBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@75);
            make.height.equalTo(@50);
            make.left.equalTo(_rightView);
            make.bottom.equalTo(_rightView).offset(-10);
        }];
        //唱片图片
        _discIV = [[UIImageView alloc]init];
        [_discIV setImage:[UIImage imageNamed:@"music_disc"]];
        [animationBgView addSubview:_discIV];
        [_discIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@25);
            make.top.equalTo(animationBgView);
            make.width.height.equalTo(@50);
        }];
        //音乐图片(或者作者头像)
        _musicIV = [[UIImageView alloc]init];
        CGFloat l_s = (50-20*50/33)/2;
        _musicIV.layer.masksToBounds = YES;
        _musicIV.layer.cornerRadius = 20*50/33/2;
        [_discIV addSubview:_musicIV];
        [_musicIV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@(l_s));
            make.top.equalTo(@(l_s));
            make.width.height.equalTo(@(20*50/33));
        }];
        //音符A+B
        _symbolAIV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"music_symbolA"]];
        _symbolAIV.frame = CGRectMake(0, _discIV.top, 12, 12);
        [animationBgView addSubview:_symbolAIV];
        _symbolAIV.hidden = YES;
        _symbolBIV = [[UIImageView alloc]initWithImage:[UIImage imageNamed:@"music_symbolB"]];
        _symbolBIV.frame = CGRectMake(0, _discIV.top, 12, 12);
        _symbolBIV.hidden = YES;
        [animationBgView addSubview:_symbolBIV];
        
        _discIV.userInteractionEnabled = YES;
        _musicIV.userInteractionEnabled = YES;

        [_rightView addSubview:_iconBtn];
        [_rightView addSubview:_followBtn];
        [_rightView addSubview:_likebtn];
        [_rightView addSubview:_commentBtn];
        [_rightView addSubview:_enjoyBtn];
    }
    return _rightView;
}
- (UIView *)botView {
    if (!_botView) {
        _botView = [[UIView alloc]initWithFrame:CGRectMake(10, _window_height-49-ShowDiff-150, _window_width*0.75, 140)];
        _botView.backgroundColor = [UIColor clearColor];
              
        //音乐
        _symIV = [[UIImageView alloc]init];
        _symIV.image = [UIImage imageNamed:@"music_symbolB"];
        [_botView addSubview:_symIV];
//        _symIV = symIV;
//        [symIV mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.width.height.mas_equalTo(20);
//            make.left.equalTo(_botView);
//            make.bottom.equalTo(_botView.mas_bottom).offset(10);
//        }];
        
        _musicL = [[RKLampView alloc]init];
        _musicL.contentStr = @"";
        [_botView addSubview:_musicL];

        //视频标题
        _titleL = [[UILabel alloc]init];
        _titleL.textAlignment = NSTextAlignmentLeft;
        _titleL.textColor = [UIColor whiteColor];
        _titleL.shadowOffset = CGSizeMake(1,1);//设置阴影
        _titleL.numberOfLines = 3;
        _titleL.font = SYS_Font(15);
        [_botView addSubview:_titleL];
        
        //视频作者名称
        _nameL = [[UILabel alloc]init];
        _nameL.textAlignment = NSTextAlignmentLeft;
        _nameL.textColor = [UIColor whiteColor];
        _nameL.shadowOffset = CGSizeMake(1,1);//设置阴影
        _nameL.font = SYS_Font(20);
        UITapGestureRecognizer *nametap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(zhuboMessage)];
        //nametap.numberOfTouchesRequired = 1;
        _nameL.userInteractionEnabled = YES;
        [_nameL addGestureRecognizer:nametap];
        [_botView addSubview:_nameL];
        
        _shopButton = [UIButton buttonWithType:0];
        _shopButton.frame = CGRectMake(5, _botView.height-35, _botView.width*0.7, 30);
        _shopButton.titleLabel.font = [UIFont systemFontOfSize:12];
        _shopButton.layer.cornerRadius = 15;
        _shopButton.layer.masksToBounds = YES;
        [_shopButton addTarget:self action:@selector(shopButtonClick) forControlEvents:UIControlEventTouchUpInside];
        [_shopButton setBackgroundImage:[UIImage imageNamed:@"startLive_back"] forState:0];
        [_shopButton setTitleColor:[UIColor whiteColor] forState:0];
        [_shopButton setTitle:YZMsg(@"查看同款商品") forState:0];

        [_botView addSubview:_shopButton];
        _shopButton.hidden = YES;

        
        _ad_Lb = [[UILabel alloc]init];
        _ad_Lb.font = [UIFont systemFontOfSize:14];
        _ad_Lb.backgroundColor = RGBA(1, 1, 1, 0.3);
        _ad_Lb.textColor = UIColor.whiteColor;
        _ad_Lb.text = YZMsg(@"广告");
        _ad_Lb.layer.cornerRadius =3;
        _ad_Lb.layer.masksToBounds = YES;
        _ad_Lb.textAlignment = NSTextAlignmentCenter;
        [_botView addSubview:_ad_Lb];
        _ad_Lb.hidden = YES;
    }
    return _botView;
}
- (void)setDataDic:(NSDictionary *)dataDic {
    _dataDic = dataDic;
    
    CGFloat coverRatio = 1.78;
    if (![PublicObj checkNull:minstr([_dataDic valueForKey:@"anyway"])]) {
        coverRatio = [minstr([_dataDic valueForKey:@"anyway"]) floatValue];
    }
    if (coverRatio > 1) {
        _bgImgView.contentMode = UIViewContentModeScaleAspectFill;
        _bgImgView.clipsToBounds = YES;
    }else {
        _bgImgView.contentMode = UIViewContentModeScaleAspectFit;

        _bgImgView.clipsToBounds = NO;
    }
//    [_bgImgView sd_setImageWithURL:[NSURL URLWithString:minstr([_dataDic valueForKey:@"thumb"])] placeholderImage:[UIImage imageNamed:@"loading_bgView"]];
    [_bgImgView sd_setImageWithURL:[NSURL URLWithString:minstr([_dataDic valueForKey:@"thumb"])]];

    id userinfo = [dataDic valueForKey:@"userinfo"];
    NSString *dataUid;
    NSString *dataIcon;
    NSString *dataUname;

    if ([userinfo isKindOfClass:[NSDictionary class]]) {
        dataUid = [NSString stringWithFormat:@"%@",[userinfo valueForKey:@"id"]];
        dataIcon = [NSString stringWithFormat:@"%@",[userinfo valueForKey:@"avatar"]];
        dataUname = [NSString stringWithFormat:@"@%@",[userinfo valueForKey:@"user_nickname"]];
    }else{
        dataUid = @"0";
        dataIcon = @"";
        dataUname = @"";
    }

    [_iconBtn sd_setBackgroundImageWithURL:[NSURL URLWithString:dataIcon] forState:UIControlStateNormal placeholderImage:[UIImage imageNamed:@"default_head.png"]];
    //音乐
    NSString *musicID = minstr([dataDic valueForKey:@"music_id"]);
    NSString *musicCover = minstr([dataDic valueForKey:@"music_img"]);
    if ([musicID isEqual:@"0"]) {
        [_musicIV sd_setImageWithURL:[NSURL URLWithString:dataIcon]];
    }else{
        [_musicIV sd_setImageWithURL:[NSURL URLWithString:musicCover]];
        [_musicIV sd_setImageWithURL:[NSURL URLWithString:musicCover]];
        _musicL.contentStr = minstr([dataDic valueForKey:@"music_title"]);

    }

//    [_musicL startLamp];

//    id musicInfo = [_dataDic valueForKey:@"music_info"];
//    if ([musicInfo isKindOfClass:[NSDictionary class]]) {
//        if([PublicObj checkNull:minstr([musicInfo valueForKey:@"title"])]){
//            _discIV.hidden = YES;
//            _musicIV.hidden = YES;
//            _symbolAIV.hidden = YES;
//            _symIV.hidden = YES;
//            _musicL.hidden = YES;
//        }else{
//            _discIV.hidden = NO;
//            _musicIV.hidden = NO;
//            _symbolAIV.hidden = NO;
//            _symIV.hidden = NO;
//            _musicL.hidden = NO;
//
//            NSString *musicCover = minstr([musicInfo valueForKey:@"img_url"]);
//            [_musicIV sd_setImageWithURL:[NSURL URLWithString:musicCover]];
//            _musicL.contentStr = minstr([musicInfo valueForKey:@"title"]);
//            [_musicL startLamp];
//        }
//    }

    NSString * _shares =[NSString stringWithFormat:@"%@",[dataDic valueForKey:@"shares"]];
    NSString * _likes = [NSString stringWithFormat:@"%@",[dataDic valueForKey:@"likes"]];
    NSString * _islike = [NSString stringWithFormat:@"%@",[dataDic valueForKey:@"islike"]];
    NSString * _comments = [NSString stringWithFormat:@"%@",[dataDic valueForKey:@"comments"]];
    NSString *isattent = [NSString stringWithFormat:@"%@",[NSString stringWithFormat:@"%@",[dataDic valueForKey:@"isattent"]]];
    YBWeakSelf;
    //点赞数 评论数 分享数
    if ([_islike isEqual:@"1"]) {
        [_likebtn setImage:[UIImage imageNamed:@"home_zan_sel"] forState:0];
        //weakSelf.likebtn.userInteractionEnabled = NO;
    } else{
        [_likebtn setImage:[UIImage imageNamed:@"home_zan"] forState:0];
        //weakSelf.likebtn.userInteractionEnabled = YES;
    }
    [_likebtn setTitle:[NSString stringWithFormat:@"%@",_likes] forState:0];
    _likebtn = [PublicObj setUpImgDownText:_likebtn];
    [_enjoyBtn setTitle:[NSString stringWithFormat:@"%@",_shares] forState:0];
    _enjoyBtn = [PublicObj setUpImgDownText:_enjoyBtn];
    [_commentBtn setTitle:[NSString stringWithFormat:@"%@",_comments] forState:0];
    _commentBtn = [PublicObj setUpImgDownText:_commentBtn];
    
    if ( [isattent isEqual:@"1"]) {
        [_followBtn setImage:[UIImage imageNamed:@"home_follow_sel"] forState:0];
        //_followBtn.hidden = YES;
    }else{
        [_followBtn setImage:[UIImage imageNamed:@"home_follow"] forState:0];
    }
    _followBtn.hidden = NO;
    [_followBtn.layer addAnimation:[PublicObj followShowTransition] forKey:nil];

    _titleL.text = [NSString stringWithFormat:@"%@",[dataDic valueForKey:@"title"]];
    _nameL.text = dataUname;
    _symIV.frame = CGRectMake(0, _botView.height-20, 20, 20);
    _musicL.frame =CGRectMake(_symIV.right+5, _botView.height-20, _botView.width/2, 20);

    //计算名称长度
    CGSize titleSize = [_titleL.text boundingRectWithSize:CGSizeMake(_window_width*0.75, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:SYS_Font(20)} context:nil].size;
    _titleL.frame = CGRectMake(0, _musicL.top-titleSize.height, titleSize.width, titleSize.height);
    _nameL.frame = CGRectMake(0, _titleL.top-25, _botView.width, 25);
    NSString *isgoods = minstr([dataDic valueForKey:@"goodsid"]);
    NSString *goodsType = minstr([dataDic valueForKey:@"type"]);
    NSString *is_ad_str = minstr([_dataDic valueForKey:@"is_ad"]);

    if (![isgoods isEqual:@"0"] || [is_ad_str isEqual:@"1"]) {
        _titleL.frame = CGRectMake(0, _botView.height-titleSize.height-40-_musicL.height, titleSize.width, titleSize.height);
    }
    _nameL.frame = CGRectMake(0, _titleL.top-25, _botView.width, 25);
    _followBtn.frame = CGRectMake(_iconBtn.left+12, _iconBtn.bottom-13, 26, 26);
    _ad_Lb.frame = CGRectMake(0, _nameL.top-20, 50, 20);
    if ([is_ad_str isEqual:@"1"]) {
        _shopButton.hidden = NO;
        _ad_Lb.hidden = NO;
        [_shopButton setTitle:YZMsg(@"查看详情") forState:0];
        _shopButton.frame = CGRectMake(5, _musicL.top-35, _botView.width*0.7, 30);
    }else{
        _ad_Lb.hidden = YES;

        if (![goodsType isEqual:@"0"]) {
            _shopButton.hidden = NO;
        }else{
            _shopButton.hidden = YES;
        }
        //0 没有。1商品。2.付费内容
        if ([goodsType isEqual:@"1"]) {
            [_shopButton setTitle:YZMsg(@"查看商品详情") forState:0];
            _shopButton.frame = CGRectMake(5, _musicL.top-35, _botView.width*0.7, 30);
        }else if([goodsType isEqual:@"2"]){
            [_shopButton setTitle:YZMsg(@"查看付费内容") forState:0];
            _shopButton.frame = CGRectMake(5, _musicL.top-35, _botView.width*0.7, 30);
        }

    }
}
-(void)guanzhuzhubo
{
    
    if ([[Config getOwnID] intValue]<0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }
    YBWeakSelf;
    NSDictionary *userDic = [_dataDic valueForKey:@"userinfo"];
    NSString *videoUserID = minstr([userDic valueForKey:@"id"]);

    NSString *url = [purl stringByAppendingFormat:@"?service=User.setAttent"];
    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"touid":videoUserID,
                             @"token":[Config getOwnToken],
                             };
    
    [YBNetworking postWithUrl:url Dic:subdic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if([code isEqual:@"0"]) {
            NSString *isattent =minstr([[[data valueForKey:@"info"] firstObject] valueForKey:@"isattent"]);
            
            NSDictionary *newDic = @{@"isattent":isattent};
            [weakSelf updateDataDic:newDic];
            if (weakSelf.videoCellEvent) {
                weakSelf.videoCellEvent(@"视频-关注", newDic);
            }

            if ([isattent isEqual:@"1"]) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [_followBtn.imageView.layer addAnimation:[PublicObj smallToBigToSmall] forKey:nil];
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.9 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [_followBtn setImage:[UIImage imageNamed:@"home_follow_sel"] forState:0];
                    });
                });
            }else{
                 [_followBtn setImage:[UIImage imageNamed:@"home_follow"] forState:0];
            }
            
        }else if ([code isEqual:@"700"]) {
            [PublicObj tokenExpired:[data valueForKey:@"msg"]];
        }else{
            [MBProgressHUD showError:[data valueForKey:@"msg"]];
        }
    } Fail:nil];

}
 //点赞
 -(void)dolike{
     if ([[Config getOwnID] intValue]<0) {
         [[YBToolClass sharedInstance]waringLogin];
         return;
     }
     YBWeakSelf;
     NSString *url = [purl stringByAppendingFormat:@"?service=Video.addLike&uid=%@&videoid=%@&type=%@&token=%@",[Config getOwnID],minstr([_dataDic valueForKey:@"id"]),@"0",[Config getOwnToken]];
     [YBNetworking postWithUrl:url Dic:nil Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
         if([code isEqual:@"0"]) {
             NSDictionary *info = [[data valueForKey:@"info"] firstObject];
             NSString *islike = [NSString stringWithFormat:@"%@",[info valueForKey:@"islike"]];
             NSString *likes  = [NSString stringWithFormat:@"%@",[info valueForKey:@"likes"]];
             [_likebtn setTitle:[NSString stringWithFormat:@"%@",likes] forState:0];
             NSDictionary *newDic = @{@"islike":islike,@"likes":likes};
             [weakSelf updateDataDic:newDic];
             
             if (weakSelf.videoCellEvent) {
                 weakSelf.videoCellEvent(@"视频-点赞", newDic);
             }

             if ([islike isEqual:@"1"]) {
                 NSMutableArray *m_sel_arr = [NSMutableArray array];
                 for (int i=1; i<=15; i++) {
                     UIImage *img = [UIImage imageNamed:[NSString stringWithFormat:@"icon_video_zan_%02d",i]];
                     [m_sel_arr addObject:img];
                 }
                 [UIView animateWithDuration:0.8 animations:^{
                     _likebtn.imageView.animationImages = m_sel_arr;
                     _likebtn.imageView.animationDuration = 0.8;
                     _likebtn.imageView.animationRepeatCount = 1;
                     [_likebtn.imageView startAnimating];
                 } completion:^(BOOL finished) {
                     [_likebtn setImage:[UIImage imageNamed:@"icon_video_zan_15"] forState:0];
                 }];
             }else{
                     [_likebtn setImage:[UIImage imageNamed:@"icon_video_zan_01"] forState:0];
             }
         }else if ([code isEqual:@"700"]) {
             [PublicObj tokenExpired:[data valueForKey:@"msg"]];
         }else{
             [MBProgressHUD showError:msg];
         }
     } Fail:nil];

 }

-(void)zhuboMessage{
    YBWeakSelf;
    NSString *_hostid = [[_dataDic valueForKey:@"userinfo"] valueForKey:@"id"];
    PersonHomeVC *center = [[PersonHomeVC alloc]init];
    if ([_hostid isEqual:[Config getOwnID]]) {
        center.userID =[Config getOwnID];
    }else{
        center.userID =_hostid;
    }
    center.videoBlock = ^(NSString *attStr) {
        NSDictionary *newDic = @{@"isattent":attStr};
        [weakSelf updateDataDic:newDic];
        if (weakSelf.videoCellEvent) {
            weakSelf.videoCellEvent(@"视频-关注", newDic);
        }

        if ([attStr isEqual:@"1"]) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [_followBtn.imageView.layer addAnimation:[PublicObj smallToBigToSmall] forKey:nil];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.9 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [_followBtn setImage:[UIImage imageNamed:@"home_follow_sel"] forState:0];
                });
            });
        }else{
             [_followBtn setImage:[UIImage imageNamed:@"home_follow"] forState:0];
        }

    };
    center.hidesBottomBarWhenPushed = YES;
    [[MXBADelegate sharedAppDelegate] pushViewController:center animated:YES];
    
}
-(void)messgaebtn{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (self.videoCellEvent) {
        self.videoCellEvent(@"评论",_dataDic);
    }

}
-(void)doenjoy{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (self.videoCellEvent) {
        self.videoCellEvent(@"分享",_dataDic);
    }

}
-(void)shopButtonClick{
    NSString *goodsid = minstr([_dataDic valueForKey:@"goodsid"]);
    NSLog(@"yyyyyyyyyyyyy----isgoods:%@ \n  dic:%@",goodsid, _dataDic);
    //0 没有。1商品。2.付费内容
    if ([minstr([_dataDic valueForKey:@"type"]) isEqual:@"1"]) {
        if ([[YBYoungManager shareInstance]isOpenYoung]) {
            [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
            return;
        }

        [PublicObj checkGoodsExistenceWithID:minstr([_dataDic valueForKey:@"goodsid"]) Existence:^(NSString *code, NSString *msg) {
            if ([code isEqual:@"0"]) {
                if ([minstr([_dataDic valueForKey:@"goods_type"]) isEqual:@"1"]) {
                    OutsideGoodsDetailVC *detail = [[OutsideGoodsDetailVC alloc]init];
                    detail.goodsID = minstr([_dataDic valueForKey:@"goodsid"]);
                    detail.liveUid =minstr([[_dataDic valueForKey:@"userinfo"] valueForKey:@"id"]);

                    [[MXBADelegate sharedAppDelegate] pushViewController:detail animated:YES];
                }else{
                    CommodityDetailVC *detail = [[CommodityDetailVC alloc]init];
                    detail.goodsID = minstr([_dataDic valueForKey:@"goodsid"]);
                    detail.liveUid =minstr([[_dataDic valueForKey:@"userinfo"] valueForKey:@"id"]);
                    [[MXBADelegate sharedAppDelegate] pushViewController:detail animated:YES];
                }
            }else{
                [MBProgressHUD showError:msg];

            }
        }];


    }else if ([minstr([_dataDic valueForKey:@"type"]) isEqual:@"2"]) {
        if ([[YBYoungManager shareInstance]isOpenYoung]) {
            [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
            return;
        }

           PayVideoDetailVC *detail = [[PayVideoDetailVC alloc]init];
           detail.object_id = goodsid;
           [[MXBADelegate sharedAppDelegate]pushViewController:detail animated:YES];
    }else if ([minstr([_dataDic valueForKey:@"is_ad"]) isEqual:@"1"]){
        NSString *ad_url_str = minstr([_dataDic valueForKey:@"ad_url"]);
        [[UIApplication sharedApplication]openURL:[NSURL URLWithString:ad_url_str] options:@{} completionHandler:^(BOOL success) {
        }];

    }
}
///点赞、评论、关注后这里也更新一下
-(void)updateDataDic:(NSDictionary *)dic {
 NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:_dataDic];
 [m_dic addEntriesFromDictionary:dic];
 _dataDic = [NSDictionary dictionaryWithDictionary:m_dic];
}
-(void)startMusicAnimation:(NSDictionary *)startDic {
    [_musicL startLamp];
    
    _symbolAIV.hidden = NO;
    [_discIV.layer addAnimation:[PublicObj rotationAnimation] forKey:@"rotation"];
    [_symbolAIV.layer addAnimation:[PublicObj caGroup] forKey:nil];
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(startBAnimation) object:nil];
    [self performSelector:@selector(startBAnimation) withObject:nil afterDelay:1.5];
}
-(void)startBAnimation {
    _symbolBIV.hidden = NO;
    [_symbolBIV.layer addAnimation:[PublicObj caGroup] forKey:nil];
}
-(void)stopMusicAnimation:(NSDictionary *)stopDic {
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(startBAnimation) object:nil];
    [_symbolAIV.layer removeAllAnimations];
    [_symbolBIV.layer removeAllAnimations];
    [_discIV.layer removeAllAnimations];
    _symbolAIV.hidden = YES;
    _symbolBIV.hidden = YES;
    [_musicL stopLamp];
    
}

@end
