//
//  YBLookVideoCell.h
//  YBLive
//
//  Created by ybRRR on 2020/9/17.
//  Copyright © 2020 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <ZFPlayer/ZFPlayer.h>
//typedef void (^FrontBlock)(NSString *type);

typedef void (^VideoCellBlock)(NSString *eventType ,NSDictionary *eventDic);

@interface YBLookVideoCell : UICollectionViewCell
@property (nonatomic, strong) ZFPlayerController *player;
@property(nonatomic,strong)NSDictionary *dataDic;
@property(nonatomic,strong) UIButton *commentBtn;
@property(nonatomic,strong) UIButton *enjoyBtn;

//@property (nonatomic, copy)FrontBlock frontEvent;
@property(nonatomic,copy)VideoCellBlock videoCellEvent;
@property (nonatomic, strong) UIImageView *bgImgView;

-(void)zhuboMessage;
-(void)startMusicAnimation:(NSDictionary *)startDic;
-(void)stopMusicAnimation:(NSDictionary *)stopDic;

@end
