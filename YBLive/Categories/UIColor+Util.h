//
//  UIColor+Util.h
//  iosapp
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON> on 14-10-18.
//  Copyright (c) 2014年 oschina. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UIColor (Util)

//+ (UIColor *)colorWithHex:(int)hexValue alpha:(CGFloat)alpha;
//+ (UIColor *)colorWithHex:(int)hexValue;

//+ (UIColor *)themeColor;
//+ (UIColor *)nameColor;
//+ (UIColor *)titleColor;
//+ (UIColor *)separatorColor;
//+ (UIColor *)cellsColor;
//+ (UIColor *)titleBarColor;
//+ (UIColor *)selectTitleBarColor;
//+ (UIColor *)navigationbarColor;
//+ (UIColor *)selectCellSColor;
//+ (UIColor *)labelTextColor;
//+ (UIColor *)teamButtonColor;

//+ (UIColor *)infosBackViewColor;
//+ (UIColor *)lineColor;

//+ (UIColor *)contentTextColor;
//+ (UIColor *)borderColor;
//+ (UIColor *)refreshControlColor;


@end
