//
//  UIImageView+YBImageView.m
//  YBSoul
//
//  Created by YB007 on 2021/3/11.
//

#import "UIImageView+YBImageView.h"

@implementation UIImageView (YBImageView)


- (void)yb_setImageWithUrlStr:(NSString *)imageStr placeholder:(UIImage *)image; {
    if ([PublicObj checkNull:imageStr] || ![imageStr isKindOfClass:[NSString class]]) {
        NSLog(@" pic erro: %@",imageStr);
        return;;
    }
//    if (imageName && ![YBTools checkNull:imageName]) {
        [self yy_setImageWithURL:[NSURL URLWithString:imageStr] placeholder:image];
//    }else {
//        [self yy_setImageWithURL:[NSURL URLWithString:imageStr] placeholder:nil];
//    }
}

- (void)yb_setImageWithUrlStr:(NSString *)imageStr placeholder:(UIImage *)image complete:(void(^)(UIImage *image))complete; {
    if ([PublicObj checkNull:imageStr] || ![imageStr isKindOfClass:[NSString class]]) {
        NSLog(@" pic erro: %@",imageStr);
        return;;
    }
    [self yy_setImageWithURL:[NSURL URLWithString:imageStr] placeholder:image options:kNilOptions completion:^(UIImage * _Nullable image, NSURL * _Nonnull url, YYWebImageFromType from, YYWebImageStage stage, NSError * _Nullable error) {
        if (complete) {
            complete(image);
        }
    }];
}

@end
