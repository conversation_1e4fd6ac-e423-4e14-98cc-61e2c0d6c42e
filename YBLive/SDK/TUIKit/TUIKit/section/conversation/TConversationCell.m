//
//  TConversationCell.m
//  UIKit
//
//  Created by kennethmia<PERSON> on 2018/9/14.
//  Copyright © 2018年 kennethmiao. All rights reserved.
//

#import "TConversationCell.h"
#import "THeader.h"
#import "Live-Swift.h"

@implementation TConversationCellData
@end

@interface TConversationCell ()
@property (nonatomic, strong) TConversationCellData *data;
@property (nonatomic, strong) UIView *backView;
@end

@implementation TConversationCell
- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    if(self){
        [self setupViews];
        [self defaultLayout];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self.backView layoutIfNeeded];
    [self.backView setViewCornerWithPosition:self.rectCorner corner:12.0];
}

+ (CGSize)getSize;
{
    return CGSizeMake(Screen_Width, TConversationCell_Height);
}

- (void)setData:(TConversationCellData *)data
{
    _data = data;
    if ([_data.convId isEqual:@"goodsorder_admin"]) {
        NSLog(@"data00000000:%@",data);
//        NSString *subtitle = _data.subTitle;
//        NSDictionary *jsondic = [self stringToDic:subtitle];
//        NSString *languageStr= [[NSUserDefaults standardUserDefaults]objectForKey:CurrentLanguage];
//        NSLog(@"ybtoo=========Str:%@", languageStr);
//        NSString *language = @"";
//        if ([languageStr isEqual:EN]) {
//            language = minstr([jsondic valueForKey:@"en"]);
//        }else{
//            language = minstr([jsondic valueForKey:@"zh-cn"]);
//        }
//        _data.subTitle = language;
    }
    if (_data.userHeader) {
        [_headImageView sd_setImageWithURL:[NSURL URLWithString:_data.userHeader]];
    }else{
        _headImageView.image = [UIImage imageNamed:_data.head];
    }
    _timeLabel.text = _data.time;
    _titleLabel.text = _data.userName;
    if(data.convType == TConv_Type_Group){
        _sixImg.hidden = YES;
        self.levelImg.hidden = YES;
        self.hostLevelImg.hidden = YES;
    }else{
        _sixImg.hidden = NO;
        self.levelImg.hidden = NO;
        self.hostLevelImg.hidden = NO;

        if ([_data.sixStr isEqual:@"1"])
       {
           _sixImg.image = [UIImage imageNamed:@"sex_man"];
       }
       else
       {
           _sixImg.image = [UIImage imageNamed:@"sex_woman"];
       }
       //级别
       NSDictionary *userLevel = [common getUserLevelMessage:_data.levStr];
       [self.levelImg sd_setImageWithURL:[NSURL URLWithString:minstr([userLevel valueForKey:@"thumb"])]];//minstr([[common getAnchorLevelMessage:_data.hostlevStr] valueForKey:@"thumb"])
//       [self.hostLevelImg sd_setImageWithURL:[NSURL URLWithString:minstr([userLevel valueForKey:@"thumb"])]];

    }
    if ([_data.convId isEqual:@"goodsorder_admin"]) {
        _sixImg.hidden = YES;
        self.levelImg.hidden = YES;
        self.hostLevelImg.hidden = YES;

    }

    
    
    _subTitleLabel.text = _data.subTitle;
    [_unReadView setNum:_data.unRead];
    [self defaultLayout];
}

- (void)setupViews
{
    self.backgroundColor = RGB_COLOR(@"#F7F8FA", 1);
    self.backView = [[UIView alloc] init];
    self.backView.backgroundColor = UIColor.whiteColor;
    [self addSubview:self.backView];
    [self.backView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.mas_leading).mas_offset(12.0);
        make.trailing.equalTo(self.mas_trailing).mas_offset(-12.0);
        make.top.bottom.equalTo(self);
    }];
    
    _headImageView = [[UIImageView alloc] init];
    _headImageView.backgroundColor = self.backgroundColor;
    _headImageView.layer.masksToBounds = YES;
    _headImageView.layer.cornerRadius = 25;
    [self addSubview:_headImageView];
    
    _timeLabel = [[UILabel alloc] init];
    _timeLabel.font = [UIFont systemFontOfSize:12];
    _timeLabel.textColor = [UIColor lightGrayColor];
    _timeLabel.backgroundColor = self.backgroundColor;
    _timeLabel.layer.masksToBounds = YES;
    [self addSubview:_timeLabel];

    _titleLabel = [[UILabel alloc] init];
    _titleLabel.font = [UIFont systemFontOfSize:16];
    _titleLabel.textColor = [UIColor blackColor];
    _titleLabel.backgroundColor = self.backgroundColor;
    _titleLabel.layer.masksToBounds = YES;
    [self addSubview:_titleLabel];
    
    _sixImg = [[UIImageView alloc]init];
    [self addSubview:_sixImg];
    
    _levelImg = [[UIImageView alloc]init];
    [self addSubview:_levelImg];
    
//    _hostLevelImg = [[UIImageView alloc]init];
//    [self addSubview:_hostLevelImg];

    
//    _vipImageView = [[UIImageView alloc] init];
//    _vipImageView.image = [UIImage imageNamed:@"vip"];
//    [self addSubview:_vipImageView];

    _unReadView = [[TUnReadView alloc] init];
    [self addSubview:_unReadView];

    _subTitleLabel = [[UILabel alloc] init];
    _subTitleLabel.backgroundColor = self.backgroundColor;
    _subTitleLabel.layer.masksToBounds = YES;
    _subTitleLabel.font = [UIFont systemFontOfSize:14];
    _subTitleLabel.textColor = [UIColor lightGrayColor];
    [self addSubview:_subTitleLabel];
    _rightImageView = [[UIImageView alloc] init];
    _rightImageView.image = [UIImage imageNamed:@"right_arrow"];
    [self addSubview:_rightImageView];

    _chatLb = [[UILabel alloc] init];
    _chatLb.backgroundColor = normalColors;
    _chatLb.layer.cornerRadius = 12;
    _chatLb.layer.masksToBounds = YES;
    _chatLb.font = [UIFont systemFontOfSize:12];
    _chatLb.textColor = [UIColor whiteColor];
    _chatLb.textAlignment = NSTextAlignmentCenter;
    _chatLb.text = YZMsg(@"私聊");
    _chatLb.hidden= YES;
    [self addSubview:_chatLb];
}

- (void)defaultLayout
{
    CGSize size = [TConversationCell getSize];
    _headImageView.frame = CGRectMake(TConversationCell_Margin + 8.0, TConversationCell_Margin, size.height - TConversationCell_Margin * 2, size.height - TConversationCell_Margin * 2);
    if ([_data.title isEqual:YZMsg(@"预约")]) {
        _rightImageView.frame = CGRectMake(size.width-25, 25, 20, 20);
    }else{
        _rightImageView.frame = CGRectZero;
    }
    [_timeLabel sizeToFit];
    _timeLabel.frame = CGRectMake(size.width - TConversationCell_Margin - _timeLabel.frame.size.width, TConversationCell_Margin_Text, _timeLabel.frame.size.width, _timeLabel.frame.size.height);

    [_titleLabel sizeToFit];
    CGFloat wwwwwww = [[YBToolClass sharedInstance] widthOfString:_data.userName andFont:SYS_Font(16) andHeight:20];
    
    _titleLabel.frame = CGRectMake(_headImageView.frame.origin.x + _headImageView.frame.size.width + TConversationCell_Margin, TConversationCell_Margin_Text, size.width - _timeLabel.frame.size.width - _headImageView.frame.size.width - 4 * TConversationCell_Margin, _titleLabel.frame.size.height);

//    _vipImageView.frame = CGRectMake(_titleLabel.x + wwwwwww + 3, TConversationCell_Margin_Text + 2, 25, 15);
    _sixImg.frame = CGRectMake(_titleLabel.x + wwwwwww + 3, TConversationCell_Margin_Text + 2, 18, 16);
    _levelImg.frame = CGRectMake(_sixImg.right+5, TConversationCell_Margin_Text + 2, 28, 14);
//    _hostLevelImg.frame =  CGRectMake(_levelImg.right+5, TConversationCell_Margin_Text + 2, 28, 14);
    _chatLb.frame = CGRectMake(size.width-50, TConversationCell_Margin_Text + 16, 40, 24);
    
    _unReadView.frame = CGRectMake(size.width - TConversationCell_Margin - _unReadView.frame.size.width, size.height - TConversationCell_Margin_Text - _unReadView.frame.size.height, _unReadView.frame.size.width, _unReadView.frame.size.height);
    
    [_subTitleLabel sizeToFit];
    _subTitleLabel.frame = CGRectMake(_titleLabel.frame.origin.x, size.height - TConversationCell_Margin_Text - _subTitleLabel.frame.size.height, size.width - _headImageView.frame.size.width - 4 * TConversationCell_Margin - _unReadView.frame.size.width, _subTitleLabel.frame.size.height);
    
    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(20 + _headImageView.width, TConversationCell_Height-1, _window_width - 20 - _headImageView.width - 28.0, 1) andColor:colorf5 andView:self];
    [self setSeparatorInset:UIEdgeInsetsMake(0, TConversationCell_Margin, 0, 0)];
}
-(NSDictionary*)stringToDic:(NSString *)jsonString{
    NSLog(@"jsonString===%@",jsonString);
    if (jsonString == nil) {
        return nil;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if(err) {
        NSLog(@"json解析失败：%@",err);
        return nil;
    }
    return dic;
}

@end
