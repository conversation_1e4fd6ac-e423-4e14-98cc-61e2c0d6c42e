//
//  TConversationCell.h
//  UIKit
//
//  Created by kennethmia<PERSON> on 2018/9/14.
//  Copyright © 2018年 kennethmiao. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "TUnReadView.h"

typedef NS_ENUM(NSUInteger, TConvType) {
    TConv_Type_C2C      = 1,
    TConv_Type_Group    = 2,
    TConv_Type_System   = 0,
//    V2TIM_UNKNOWN   = 0,  ///< 未知
//    V2TIM_C2C       = 1,  ///< 单聊
//    V2TIM_GROUP      = 2,  ///< 群聊

};

@interface TConversationCellData : NSObject
@property(nonatomic,strong)NSString *groupOwner;            // 群组的时候 是群主的id
//@property(nonatomic,strong)TIMConversation *lastConv;
@property(nonatomic,strong)V2TIMConversation *lastConv;
@property (nonatomic, strong) NSString *convId;
@property (nonatomic, assign) TConvType convType;
@property (nonatomic, strong) NSString *head;
@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) NSString *subTitle;
@property (nonatomic, strong) NSString *time;
@property (nonatomic, strong) NSString *timestamp;
@property (nonatomic, strong) NSDate *cell_timestamp;

@property (nonatomic, strong) NSString *userName;
@property (nonatomic, strong) NSString *userHeader;
@property (nonatomic, strong) NSString *isauth;
@property (nonatomic, strong) NSString *level_anchor;
@property (nonatomic, strong) NSString *isAtt;
@property (nonatomic, strong) NSString *isVIP;
@property (nonatomic, strong) NSString *isblack;
@property (nonatomic, strong) NSString *sixStr;
@property (nonatomic, strong) NSString *levStr;
@property (nonatomic, strong) NSString *hostlevStr;

@property (nonatomic, assign) int unRead;
@end

@interface TConversationCell : UITableViewCell
@property (nonatomic, strong) UIImageView *headImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, strong) UILabel *chatLb;
@property (nonatomic, strong) TUnReadView *unReadView;
@property (nonatomic, strong) UIImageView *rightImageView;
//@property (nonatomic, strong) UIImageView *vipImageView;
@property (nonatomic, strong) UIImageView *sixImg;
@property (nonatomic, strong) UIImageView *levelImg;
@property (nonatomic, strong) UIImageView *hostLevelImg;
@property (nonatomic, assign) UIRectCorner rectCorner;
+ (CGSize)getSize;
- (void)setData:(TConversationCellData *)data;
@end
