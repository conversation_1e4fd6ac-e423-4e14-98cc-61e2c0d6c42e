//
//  THelper.h
//  TUIKit
//
//  Created by kenneth<PERSON><PERSON> on 2018/11/1.
//  Copyright © 2018年 kennethmiao. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

typedef void (^TAsyncImageComplete)(NSString *path, UIImage *image);

@interface THelper : NSObject
+ (NSString *)genImageName:(NSString *)uuid;
+ (NSString *)genSnapshotName:(NSString *)uuid;
+ (NSString *)genVideoName:(NSString *)uuid;
+ (NSString *)genFileName:(NSString *)uuid;
+ (NSString *)genVoiceName:(NSString *)uuid withExtension:(NSString *)extent;
+ (BOOL)isAmr:(NSString *)path;
+ (BOOL)convertAmr:(NSString*)amrPath toWav:(NSString*)wavPath;
+ (BOOL)convertWav:(NSString*)wavPath toAmr:(NSString*)amrPath;
+ (void)asyncDecodeImage:(NSString *)path queue:(dispatch_queue_t)queue complete:(TAsyncImageComplete)complete;
@end
