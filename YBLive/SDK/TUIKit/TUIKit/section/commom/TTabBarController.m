//
//  TTabBarController.m
//  TUIKit
//
//  Created by kenneth<PERSON><PERSON> on 2018/11/13.
//  Copyright © 2018年 kennethmiao. All rights reserved.
//

#import "TTabBarController.h"


@implementation TTabBarItem
@end

@interface TTabBarController ()

@end

@implementation TTabBarController

- (void)viewDidLoad {
    [super viewDidLoad];
    //解决navigationtroller+tabbarcontroller，push是navigationbar变黑问题
    [UIApplication sharedApplication].keyWindow.backgroundColor = [UIColor whiteColor];
}

- (void)setTabBarItems:(NSMutableArray *)tabBarItems
{
    _tabBarItems = tabBarItems;
    //tab bar items
    NSMutableArray *controllers = [NSMutableArray array];
    for (TTabBarItem *item in _tabBarItems) {
        item.controller.tabBarItem = [[UITabBarItem alloc] initWithTitle:item.title image:item.normalImage selectedImage:item.selectedImage];
        [controllers addObject:item.controller];
    }
    self.viewControllers = controllers;
}
@end
