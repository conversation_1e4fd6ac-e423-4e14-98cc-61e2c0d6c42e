//
//  TLocationMessageCell.h
//  YBPlaying
//
//  Created by ybRRR on 2021/10/18.
//  Copyright © 2021 IOS1. All rights reserved.
//

#import "TMessageCell.h"
#import <QMapKit/QMapKit.h>
#import <QMapSearchKit/QMapSearchKit.h>

typedef NS_ENUM(NSInteger, TLocationType)
{
    TLocation_Type_Thumb,
    TLocation_Type_Large,
    TLocation_Type_Origin,
};
@interface TLocationItem : NSObject
@property (nonatomic, strong) NSString *luuid;
@property (nonatomic, strong) NSString *lurl;
@property (nonatomic, assign) CGSize lsize;
@property (nonatomic, assign) TLocationType ltype;
@end

@interface TLocationMessageCellData : TMessageCellData
@property (nonatomic, strong) UIImage *thumbImage;
@property (nonatomic, strong) NSString *path;
@property (nonatomic, assign) NSInteger length;
@property (nonatomic, strong) NSMutableArray *items;

@property (nonatomic, strong) NSString *latitude;
@property (nonatomic, strong) NSString *longitude;
@property(nonatomic,retain) NSString * desc;
@property (nonatomic, assign) int uploadProgress;
- (void)downloadImage:(TLocationType)type progress:(TDownloadProgress)progress response:(TDownloadResponse)response;
- (NSString *)getImagePath:(TLocationType)type isExist:(BOOL *)isExist;

@end

@interface TLocationMessageCell : TMessageCell
@property (nonatomic, strong)UIView *backView;

@property (nonatomic, strong)UILabel *titleLb;
@property (nonatomic, strong)UILabel *infoLb;
@property (nonatomic, strong) UIImageView *thumb;
@property (nonatomic, strong) UILabel *uploadProgress;
@property (nonatomic, strong)TLocationMessageCellData *celldata;
@property (nonatomic, strong)QMapView *mapView;
@end

