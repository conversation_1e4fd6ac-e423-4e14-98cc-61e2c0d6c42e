//
//  TLocationMessageCell.m
//  YBPlaying
//
//  Created by ybRRR on 2021/10/18.
//  Copyright © 2021 IOS1. All rights reserved.
//

#import "TLocationMessageCell.h"
#import "THeader.h"
#import "THelper.h"
#import <MapKit/MapKit.h>
@implementation TLocationItem
@end

@interface TLocationMessageCellData ()
@property (nonatomic, assign) BOOL isDownloading;
@property (nonatomic, strong) NSMutableArray *thumbProgressBlocks;
@property (nonatomic, strong) NSMutableArray *thumbResponseBlocks;
@property (nonatomic, strong) NSMutableArray *originProgressBlocks;
@property (nonatomic, strong) NSMutableArray *originResponseBlocks;

@end
@implementation TLocationMessageCellData
- (id)init
{
    self = [super init];
    if(self){
        _uploadProgress = 100;
        _isDownloading = NO;
        _thumbProgressBlocks = [NSMutableArray array];
        _thumbResponseBlocks = [NSMutableArray array];
        _originProgressBlocks = [NSMutableArray array];
        _originResponseBlocks = [NSMutableArray array];

    }
    return self;
}
//- (NSString *)getImagePath:(TLocationType)type isExist:(BOOL *)isExist
//{
//    NSString *path = nil;
//    BOOL isDir = NO;
//    *isExist = NO;
//    if(super.isSelf){
//        //上传方本地原图是否有效
//        path = [NSString stringWithFormat:@"%@%@", TUIKit_Image_Path, _path.lastPathComponent];
//        if([[NSFileManager defaultManager] fileExistsAtPath:path isDirectory:&isDir]){
//            if(!isDir){
//                *isExist = YES;
//            }
//        }
//    }
//    
//    if(!*isExist){
//        //查看本地是否存在
//        TLocationItem *tImageItem = [self getTImageItem:type];
//        path = [NSString stringWithFormat:@"%@%@", TUIKit_Image_Path, tImageItem.luuid];
//        if([[NSFileManager defaultManager] fileExistsAtPath:path isDirectory:&isDir]){
//            if(!isDir){
//                *isExist = YES;
//            }
//        }
//    }
//    
//    return path;
//}

//- (void)downloadImage:(TLocationType)type progress:(TDownloadProgress)progress response:(TDownloadResponse)response
//{
//    BOOL isExist = NO;
//    NSString *path = [self getImagePath:type isExist:&isExist];
//    if(isExist){
//        if(response){
//            response(0, nil, path);
//        }
//    }
//    else{
//        if(progress){
//            if(type == TLocation_Type_Thumb){
//                [_thumbProgressBlocks addObject:progress];
//            }
//            else if(type == TLocation_Type_Origin){
//                [_originProgressBlocks addObject:progress];
//            }
//        }
//        if(response){
//            if(type == TLocation_Type_Thumb){
//                [_thumbResponseBlocks addObject:response];
//            }
//            else if(type == TLocation_Type_Origin){
//                [_originResponseBlocks addObject:response];
//            }
//        }
//
//        if(_isDownloading){
//            return;
//        }
//
//        //网络下载
//        TIMImage *imImage = [self getIMImage:type];
//        _isDownloading = YES;
//        __weak typeof(self) ws = self;
//        void (^completeBlock)(int, NSString *, NSString *) = ^(int code, NSString *msg, NSString *path){
//            ws.isDownloading = NO;
//            dispatch_async(dispatch_get_main_queue(), ^{
//                NSMutableArray *array = nil;
//                if(type == TLocation_Type_Thumb){
//                    array = ws.thumbResponseBlocks;
//                }
//                else if(type == TLocation_Type_Origin){
//                    array = ws.originResponseBlocks;
//                }
//                if(array.count != 0){
//                    for (TDownloadResponse response in array) {
//                        if(response){
//                            response(code, msg, path);
//                        }
//                    }
//                }
//                [array removeAllObjects];
//            });
//        };
//
//        [imImage getImage:path progress:^(NSInteger curSize, NSInteger totalSize) {
//            dispatch_async(dispatch_get_main_queue(), ^{
//                NSMutableArray *array = nil;
//                if(type == TLocation_Type_Thumb){
//                    array = ws.thumbProgressBlocks;
//                }
//                else if(type == TLocation_Type_Origin){
//                    array = ws.originProgressBlocks;
//                }
//                if(array.count != 0){
//                    for (TDownloadProgress progress in array) {
//                        if(progress){
//                            progress(curSize, totalSize);
//                        }
//                    }
//                }
//                if(curSize == totalSize){
//                    [array removeAllObjects];
//                }
//            });
//        } succ:^{
//            completeBlock(0, nil, path);
//        } fail:^(int code, NSString *msg) {
//            completeBlock(code, msg, nil);
//        }];
//    }
//}
//
//- (TLocationItem *)getTImageItem:(TLocationType)type
//{
//    for (TLocationItem *item in self.items) {
//        if(item.ltype == type){
//            return item;
//        }
//    }
//    return nil;
//}
//
//- (TIMImage *)getIMImage:(TLocationType)type
//{
//    TIMMessage *imMsg = self.custom;
//    for (int i = 0; i < imMsg.elemCount; ++i) {
//        TIMElem *imElem = [imMsg getElem:i];
//        if([imElem isKindOfClass:[TIMImageElem class]]){
//            TIMImageElem *imImageElem = (TIMImageElem *)imElem;
//            for (TIMImage *imImage in imImageElem.imageList) {
//                if(type == TLocation_Type_Thumb && imImage.type == TIM_IMAGE_TYPE_THUMB){
//                    return imImage;
//                }
//                else if(type == TLocation_Type_Origin && imImage.type == TIM_IMAGE_TYPE_ORIGIN){
//                    return imImage;
//                }
//                else if(type == TLocation_Type_Large && imImage.type == TIM_IMAGE_TYPE_LARGE){
//                    return imImage;
//                }
//            }
//            break;
//        }
//    }
//    return nil;
//}

@end


@implementation TLocationMessageCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

-(NSDictionary*)parseJSONStringToNSDictionary:(NSString*)JSONString {
    NSData *JSONData = [JSONString dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *responseJSON = [NSJSONSerialization JSONObjectWithData:JSONData options:NSJSONReadingMutableLeaves error:nil];
    return responseJSON;
}
- (void)setData:(TLocationMessageCellData *)data;
{
    //set data
    [super setData:data];
    _celldata = data;
    _backView.frame = super.container.bounds;
    NSString *url = @"https://apis.map.qq.com/ws/staticmap/v2/";
    NSDictionary *postDic = @{@"key":TencentKey,
                              @"scale":@"1",
                              @"size":[NSString stringWithFormat:@"%.f*%.f",_window_width*0.5,_window_width*0.5],
                              @"center":[NSString stringWithFormat:@"%@,%@",data.latitude,data.longitude],
                              @"zoom":@"16"};
    url = [url stringByAppendingFormat:@"?%@",[self getStrWithDic:postDic]];
    [_thumb sd_setImageWithURL:[NSURL URLWithString:url]];
    NSDictionary *infoDic  = [self parseJSONStringToNSDictionary:data.desc];
    _titleLb.text = minstr([infoDic valueForKey:@"name"]);
    _infoLb.text = minstr([infoDic valueForKey:@"info"]);
    _titleLb.frame = CGRectMake(10, 10, super.container.width-20, 18);
    _infoLb.frame = CGRectMake(10, _titleLb.bottom, super.container.width-20, 18);
    _thumb.frame = CGRectMake(0, _infoLb.bottom, _window_width*0.5, super.container.height-18*2-10);
//    _imageViewAnntation.center = _mapIV.center;
//    _thumb.frame = super.container.bounds;
//    _uploadProgress.frame = super.container.bounds;
    //update progress
//    _uploadProgress.text = [NSString stringWithFormat:@"%d%%", data.uploadProgress];
//    if(data.uploadProgress < 100){
//        _uploadProgress.hidden = NO;
//    }
//    else{
        _uploadProgress.hidden = YES;
//    }
}
-(NSString *)getStrWithDic:(NSDictionary*)dic {
    if ([dic allKeys].count<=0) {
        return @"";
    }
    NSString *urlStr = @"";
    for (NSString *key in [dic allKeys]) {
        NSString *value = minstr([dic valueForKey:key]);
        value = [value stringByReplacingOccurrencesOfString:@" " withString:@""];
        urlStr = [urlStr stringByAppendingFormat:@"&%@=%@",key,value];
    }
    return urlStr;
}

- (void)setupViews
{
    [super setupViews];
    _backView = [[UIView alloc]init];
    _backView.backgroundColor = UIColor.whiteColor;
    _backView.layer.cornerRadius = 5;
    _backView.layer.masksToBounds = YES;
    [super.container addSubview:_backView];
    
    _titleLb = [[UILabel alloc]init];
    _titleLb.font = [UIFont boldSystemFontOfSize:12];
    _titleLb.textColor = UIColor.blackColor;
    [super.container addSubview:_titleLb];
    
    _infoLb = [[UILabel alloc]init];
    _infoLb.font = [UIFont systemFontOfSize:10];
    _infoLb.textColor = UIColor.blackColor;
    [super.container addSubview:_infoLb];
    
    _thumb = [[UIImageView alloc] init];
    _thumb.layer.cornerRadius = 5.0;
    [_thumb.layer setMasksToBounds:YES];
    _thumb.contentMode = UIViewContentModeScaleAspectFill;
    _thumb.backgroundColor = [UIColor whiteColor];
    _thumb.userInteractionEnabled = YES;
    [super.container addSubview:_thumb];
    
    _uploadProgress = [[UILabel alloc] init];
    _uploadProgress.textColor = [UIColor whiteColor];
    _uploadProgress.font = [UIFont systemFontOfSize:15];
    _uploadProgress.textAlignment = NSTextAlignmentCenter;
    _uploadProgress.layer.cornerRadius = 5.0;
    _uploadProgress.hidden = YES;
    _uploadProgress.backgroundColor = TImageMessageCell_Progress_Color;
    [_uploadProgress.layer setMasksToBounds:YES];
    [super.container addSubview:_uploadProgress];

    
    UITapGestureRecognizer *topClick =[[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(tipClickEvent)];
    [_thumb addGestureRecognizer:topClick];
}

- (CGSize)getContainerSize:(TLocationMessageCellData *)data
{
//    CGSize size = CGSizeZero;
//    BOOL isDir = NO;
//    if(![data.path isEqualToString:@""] &&
//       [[NSFileManager defaultManager] fileExistsAtPath:data.path isDirectory:&isDir]){
//        if(!isDir){
//            size = [UIImage imageWithContentsOfFile:data.path].size;
//        }
//    }
//    else{
//        for (TLocationItem *item in data.items) {
//            if(item.ltype == TLocation_Type_Thumb){
//                size = item.lsize;
//            }
//        }
//    }
//    if(CGSizeEqualToSize(size, CGSizeZero)){
//        return size;
//    }
//    if(size.height > size.width){
//        size.width = size.width / size.height * TImageMessageCell_Image_Height_Max;
//        size.height = TImageMessageCell_Image_Height_Max;
//    }
//    else{
//        size.height = size.height / size.width * TImageMessageCell_Image_Width_Max;
//        size.width = TImageMessageCell_Image_Width_Max;
//    }
    CGSize size = CGSizeMake(_window_width*0.5, _window_width *0.5);

    return size;
}
-(void)tipClickEvent{
        NSData *mix_data = [_celldata.desc dataUsingEncoding:NSUTF8StringEncoding];
        NSDictionary *mix_dic = [NSJSONSerialization JSONObjectWithData:mix_data options:NSJSONReadingAllowFragments error:nil];
        NSString *endName = YZMsg(@"终点位置");
        if ([mix_dic isKindOfClass:[NSDictionary class]]) {
            endName = [NSString stringWithFormat:@"%@",[mix_dic valueForKey:@"name"]];
        }
        [[RKLBSManager shareManager]showNavigationsWithLat:@([_celldata.latitude longLongValue])  lng:@([_celldata.longitude longLongValue]) endName:endName];
}
@end
