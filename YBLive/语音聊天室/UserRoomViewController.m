//
//  UserRoomViewController.m
//  YBLive
//
//  Created by ybRRR on 2021/1/5.
//  Copyright © 2021 cat. All rights reserved.
//

#import "UserRoomViewController.h"
#import "MessageListModel.h"
#import <ReplayKit/ReplayKit.h>

/***********************  腾讯SDK start ********************/
//腾讯
#import <TXLiteAVSDK_Professional/V2TXLivePlayer.h>
#import <TXLiteAVSDK_Professional/TXVodPlayer.h>
#import "YBLiveRTCManager.h"
#import <mach/mach.h>
/***********************  腾讯SDK end **********************/
#import <MHBeautySDK/MHSDK.h>
#import "OrderMessageVC.h"
#import "TChatC2CController.h"
#import "THeader.h"
#import "OnlineUserView.h"
#import "IYZPlayFucView.h"
#import "ChatVideoUserLinkView.h"
/********************  MHSDK添加 开始 ********************/
#import "MHMeiyanMenusView.h"
#import <MHBeautySDK/MHBeautyManager.h>
#import "MHBeautyParams.h"
/********************  MHSDK添加 结束 ********************/

//新礼物结束
#define iPhone6Plus ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(1242,2208), [[UIScreen mainScreen] currentMode].size) : NO)

#define upViewW     _window_width*0.8

@interface UserRoomViewController ()<
UITextViewDelegate,UITableViewDataSource,UITableViewDelegate,chatdianjishijian,UIScrollViewDelegate,UIActionSheetDelegate,UIGestureRecognizerDelegate,UITextFieldDelegate ,catSwitchDelegate,UIAlertViewDelegate,socketDelegate,frontviewDelegate,sendGiftDelegate,haohuadelegate,listDelegate,tx_play_linkmic,shouhuViewDelegate,guardShowDelegate,redListViewDelegate,anchorPKViewDelegate,TXLivePlayListener,JackpotViewDelegate,UserBulletWindowDelegate,platDelgate,gameDelegate,WPFRotateViewDelegate,shangzhuangdelegate,turntableViewDelegate,SDCycleScrollViewDelegate,LiveGoodViewDelegate,ybUserLinkBtnDelegate,TXVideoCustomProcessDelegate,V2TIMConversationListener,V2TXLivePlayerObserver,YBLiveRTCDelegate,ybUserVideoLinkBtnDelegate>

@property (nonatomic, strong)V2TXLivePlayer *txLivePlayer;
@property (nonatomic,strong)OnlineUserView *onlineView;
@property (nonatomic,strong)UIView *playVideoChatV;
/********************  MHSDK添加 开始 ********************/
@property (nonatomic, strong) MHMeiyanMenusView *menusView;
@property (nonatomic, strong) MHBeautyManager *beautyManager;
/******************** MHSDK添加 结束 ********************/

@end

@implementation UserRoomViewController
{
    NSMutableArray *msgList;
    UIAlertController  *Feedeductionalertc;//扣费alert
    UIAlertController *md5AlertController;
    BOOL isSuperAdmin;
    //发起连麦进行11S的倒计时
    NSTimer *startLinkTimer;
    int startLinkTime;
    /***********************  腾讯SDK start ********************/
    CWStatusBarNotification *_notification;
    BOOL _canChange;
    BOOL _isCancleLink;
    /***********************  腾讯SDK end **********************/
    NSString * urlss;
    BOOL isHaveUpMic;  //是否已经上麦
    BOOL isJingMai;  //是否静音
    BOOL isMicUser;
    UIAlertController *smallShowAlert;
    NSString *unReadCount;
    NSString *game_xqtb_switch;
    NSString *game_xydzp_switch;
    int upMicIndex;
    BOOL isLoadWebSprout;
    BOOL isGameToGift;
}

-(void)changeBtnFrame:(CGFloat)hhh{
    hhh = hhh - ShowDiff;
    CGFloat  wwwssss = 30;
    keyBTN.frame = CGRectMake(_window_width + 15,hhh, 110, www);
    if ([[YBYoungManager shareInstance]isOpenYoung]) {
        moreBTN.frame = CGRectMake(_window_width*2 - wwwssss-20,hhh,wwwssss,wwwssss);
//        _messageBTN.frame = CGRectMake(_window_width*2 - wwwssss*2-30,hhh,wwwssss,wwwssss);
    }else{
        moreBTN.frame = CGRectMake(_window_width*2 - wwwssss*2-20,hhh,wwwssss,wwwssss);
//        _messageBTN.frame = CGRectMake(_window_width*2 - wwwssss*3-30,hhh,wwwssss,wwwssss);
    }
    _liwuBTN.frame = CGRectMake(_window_width*2-wwwssss-10,hhh,wwwssss,wwwssss);
    
    if (isHaveUpMic) {
//        emoticonBtn.frame = CGRectMake(_window_width*2 - wwwssss*4-40,hhh, wwwssss, wwwssss);
        closeMicBtn.frame = CGRectMake(_window_width*2 - wwwssss*3-30,hhh, wwwssss, wwwssss);
        waitUpMicBtn.frame = CGRectMake(_window_width*2 - wwwssss*4-40,hhh, wwwssss, wwwssss);
        _firstChargeBtn.frame = CGRectMake(_window_width*2 - wwwssss*5-50,hhh, wwwssss, wwwssss);
        _gameBtn.frame = CGRectMake(_window_width*2 - wwwssss*6-60,hhh, wwwssss, wwwssss);

    }else{
        waitUpMicBtn.frame =CGRectMake(_window_width*2 - wwwssss*3-30,hhh, wwwssss,wwwssss);
        _firstChargeBtn.frame =CGRectMake(_window_width*2 - wwwssss*4-40,hhh, wwwssss,wwwssss);
        _gameBtn.frame = CGRectMake(_window_width*2 - wwwssss*5-50,hhh, wwwssss, wwwssss);

    }
}
-(void)superAdmin:(NSString *)state{
    [socketDelegate superStopRoom];
    haohualiwuV.expensiveGiftCount = nil;
    platliwuV.expensiveGiftCount = nil;
    [self releaseObservers];
    [self lastView];
}
-(void)roomCloseByAdmin{
    [self lastView];
}
-(void)addZombieByArray:(NSArray *)array{
    if (!listView) {
        listView = [[ListCollection alloc]initWithListArray:_listArray andID:[self.playDoc valueForKey:@"uid"]andStream:[NSString stringWithFormat:@"%@",[self.playDoc valueForKey:@"stream"]]andFixWidth:50];
        listView.delegate = self;
        listView.frame = CGRectMake(_window_width+110, 20 + statusbarHeight, _window_width-200-60, 40);
        listView.listCollectionview.frame = CGRectMake(0, 0, _window_width-200-60, 40);
        [backScrollView addSubview:listView];
    }
    [listView listarrayAddArray:array];
    userCount += array.count;
//    [setFrontV.onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    if(userCount > 99){
        [setFrontV.onlineBtn setTitle:@"99+" forState:0];
    }else{
        [setFrontV.onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    }

}
-(void)light:(NSDictionary *)chats{
    chats = [YBToolClass roomChatInsertTime:chats];
    [msgList addObject:chats];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast];
}
-(void)messageListen:(NSDictionary *)chats{
    chats = [YBToolClass roomChatInsertTime:chats];
        [msgList addObject:chats];
        titleColor = @"0";
        if(msgList.count>30)
        {
            [msgList removeObjectAtIndex:0];
        }
        [self.tableView reloadData];
        [self jumpLast];
}
-(void)UserLeave:(NSDictionary *)msg{
    userCount -= 1;
//    setFrontV.onlineLabel.text = [NSString stringWithFormat:@"%lld",userCount];
//    [setFrontV.onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    if(userCount > 99){
        [setFrontV.onlineBtn setTitle:@"99+" forState:0];
    }else{
        [setFrontV.onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    }

    if (listView) {
        [listView userLive:msg];
    }
}
//********************************用户进入动画********************************************//
-(void)UserAccess:(NSDictionary *)msg{
    //用户进入
    userCount += 1;
    if (listView) {
        [listView userAccess:msg];
    }
//    setFrontV.onlineLabel.text = [NSString stringWithFormat:@"%lld",userCount];
//    [setFrontV.onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    if(userCount > 99){
        [setFrontV.onlineBtn setTitle:@"99+" forState:0];
    }else{
        [setFrontV.onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    }

    NSString *vipType = [NSString stringWithFormat:@"%@",[[msg valueForKey:@"ct"] valueForKey:@"vip_type"]];
    NSString *guard_type = [NSString stringWithFormat:@"%@",[[msg valueForKey:@"ct"] valueForKey:@"guard_type"]];

    if ([vipType isEqual:@"1"] || [guard_type isEqual:@"1"] || [guard_type isEqual:@"2"]) {
        [useraimation addUserMove:msg];
        useraimation.frame = CGRectMake(_window_width + 10,self.tableView.top - 40,_window_width,20);
    }
    NSString *car_id = minstr([[msg valueForKey:@"ct"] valueForKey:@"car_id"]);
    if (![car_id isEqual:@"0"]) {
        [_viploginArray addObject:msg];
        [self addVipLogin:msg];
    }
    [self userLoginSendMSG:msg];
}
-(void)addVipLogin:(NSDictionary *)msg{
      YBWeakSelf;
      if (!vipanimation) {
          vipanimation = [[viplogin alloc]initWithFrame:CGRectMake(_window_width,80,_window_width,_window_width*0.8) andBlock:^{
              [weakSelf.viploginArray removeObjectAtIndex:0];
              [vipanimation removeFromSuperview];
              vipanimation = nil;
              if (weakSelf.viploginArray.count > 0) {
                  [weakSelf addVipLogin:weakSelf.viploginArray[0]];
              }
          }];
          vipanimation.frame =CGRectMake(_window_width,80,_window_width,_window_width*0.8);
          [backScrollView insertSubview:vipanimation atIndex:10];
          UITapGestureRecognizer  *tapvip = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(starok)];
          [vipanimation addGestureRecognizer:tapvip];
          [vipanimation addUserMove:msg];
    }
}
//用户进入直播间发送XXX进入了直播间
-(void)userLoginSendMSG:(NSDictionary *)dic {
    titleColor = @"userLogin";
    NSString *uname = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"user_nickname"]];
    NSString *levell = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"level"]];
    NSString *ID = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"id"]];
    NSString *vip_type = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"vip_type"]];
    NSString *liangname = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"liangname"]];
    NSString *usertype = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"usertype"]];
    NSString *guard_type = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"guard_type"]];

    NSString *conttt = YZMsg(@" 进入了直播间");
    NSString *isadminn;
    if ([[NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"usertype"]] isEqual:@"40"]) {
        isadminn = @"1";
    }else{
        isadminn = @"0";
    }
    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",conttt,@"contentChat",levell,@"levelI",ID,@"id",titleColor,@"titleColor",vip_type,@"vip_type",liangname,@"liangname",usertype,@"usertype",guard_type,@"guard_type",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast];
}
////////////////////////////////////////////////////
-(void)LiveOff{
    [self lastView];
}
-(void)sendLight{
   [self staredMove];
}
-(void)setSystemNot:(NSDictionary *)msg{
    titleColor = @"firstlogin";
    NSString *ct;
    NSString *languageStr= [PublicObj getCurrentLanguage];
    if ([languageStr isEqual:@"en"]) {
        ct = minstr([msg valueForKey:@"ct_en"]);
    }else{
        ct =minstr([msg valueForKey:@"ct"]);
    }
    NSString *uname = YZMsg(@"直播间消息");
    NSString *ID = @"";
    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",ID,@"id",titleColor,@"titleColor",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast];
}

-(void)setAdmin:(NSDictionary *)msg{
    NSString *touid = [NSString stringWithFormat:@"%@",[msg valueForKey:@"touid"]];

    if ([touid isEqual:[Config getOwnID]]) {
        if ([minstr([msg valueForKey:@"action"]) isEqual:@"0"]) {
            usertype = @"0";
        }else{
            usertype = @"40";
        }
    }
    titleColor = @"firstlogin";
    NSString *ct = [msg valueForKey:@"ct"];
    NSString *uname = YZMsg(@"直播间消息");
    NSString *ID = @"";
    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",ID,@"id",titleColor,@"titleColor",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast];
}
//全站礼物
-(void)sendAllStationsGift:(NSDictionary *)msg{
    NSLog(@"liveplay======msg:%@",msg);
//    [self expensiveGift:msg andIsPlat:YES];
    [self platGift:msg];

}
-(void)sendGift:(NSDictionary *)chats andLiansong:(NSString *)liansong andTotalCoin:(NSString *)votestotal andGiftInfo:(NSDictionary *)giftInfo andCt:(NSDictionary *)ctDic{
    NSMutableDictionary *replaceDic = [NSMutableDictionary dictionaryWithDictionary:chats];
    NSString *color =@"#FF6131";
    [replaceDic setValue:color forKey:@"titleColor"];
    replaceDic = [YBToolClass roomChatInsertTime:replaceDic];
    [msgList addObject:replaceDic];
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast];

    NSString *userName = minstr([giftInfo valueForKey:@"nicename"]);
    NSString *touserName =minstr([ctDic valueForKey:@"to_username"]);
    
    NSMutableDictionary *giftDic = [NSMutableDictionary dictionaryWithDictionary:giftInfo];
    [giftDic setObject:[ctDic valueForKey:@"touid"] forKey:@"uid"];
    [giftDic setObject:[ctDic valueForKey:@"to_username"] forKey:@"nicename"];
    giftInfo = giftDic;
//      [self addCoin:totalcoin];//添加映票
    votesTatal = votestotal;
    [setFrontV changeState:votestotal andID:nil];

    if (![[ctDic valueForKey:@"sticker_id"] isEqual:@"0"]) {
        if ([minstr([giftInfo valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
//            NSString *languageStr = [PublicObj getCurrentLanguage];
//            NSString *language = @"";
//            if ([languageStr isEqual:@"en"]) {
//                language = minstr([ctDic valueForKey:@"giftname_en"]);
//            }else{
//                language =minstr([ctDic valueForKey:@"giftname"]);
//            }
//
//            [socketDelegate sendmessage:[NSString stringWithFormat:@"%@%@%@",YZMsg(@"发送了"),language,YZMsg(@"道具礼物")] andLevel:level andUsertype:usertype andGuardType:minstr([guardInfo valueForKey:@"type"])];

//
//            [socketDelegate sendmessage:[NSString stringWithFormat:@"发送了%@道具礼物",language] andLevel:level andUsertype:usertype andGuardType:minstr([guardInfo valueForKey:@"type"])];
        }
    }else{
            NSString *type = minstr([giftInfo valueForKey:@"type"]);

            if (!continueGifts) {
                continueGifts = [[continueGift alloc]init];
                [liansongliwubottomview addSubview:continueGifts];
                //初始化礼物空位
                [continueGifts initGift];
                YBWeakSelf;
                continueGifts.rkPaintedEvent = ^(NSDictionary *giftDic) {
                    [weakSelf showPaintedGift:giftDic];
                };
            }
            if ([type isEqual:@"1"]) {
                [self expensiveGift:giftInfo andIsPlat:NO];
            }
            else{
                [continueGifts GiftPopView:giftInfo andLianSong:haohualiwu ischat:YES andUser:userName andTouser:touserName];
            }
    }
}
-(void)showPaintedGift:(NSDictionary *)giftDic {
    //手绘显示动画
    _paintedShowRegion.giftPathStr = minstr([giftDic valueForKey:@"gifticon"]);
    _paintedShowRegion.paintedWidth = [minstr([giftDic valueForKey:@"paintedWidth"]) floatValue];
    _paintedShowRegion.paintedHeight = [minstr([giftDic valueForKey:@"paintedHeight"]) floatValue];
    _paintedShowRegion.paintedPointArray = [NSArray arrayWithArray:[giftDic valueForKey:@"paintedPath"]];
}
-(void)SendBarrage:(NSDictionary *)msg{
    NSString *text = [NSString stringWithFormat:@"%@",[[msg valueForKey:@"ct"] valueForKey:@"content"]];
    NSString *name = [msg valueForKey:@"uname"];
    NSString *icon = [msg valueForKey:@"uhead"];
    
    NSDictionary *levelDic = [common getUserLevelMessage:[msg valueForKey:@"level"]];
    NSString *colorStr = minstr([levelDic valueForKey:@"colour"]);
    
    NSDictionary *userinfo = [[NSDictionary alloc] initWithObjectsAndKeys:text,@"title",name,@"name",icon,@"icon",colorStr,@"nameColor", nil];
    [_danmuView setModel:userinfo];
    long totalcoin = [self.danmuprice intValue];
    [self addCoin:totalcoin];
}
-(void)StartEndLive{
    [self lastView];
}
-(void)UserDisconnect:(NSDictionary *)msg{
    userCount -= 1;
    if (listView) {
        [listView userLive:msg];
    }
}
//直播间小窗口
-(void)showLiveGoodsWithDic:(NSDictionary *)msg
{
    if (![[YBYoungManager shareInstance]isOpenYoung]) {
        if ([[msg valueForKey:@"action"]isEqual:@"0"]) {
            if (_liveGoodsView) {
                [_liveGoodsView removeFromSuperview];
                _liveGoodsView  = nil;
            }
            [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 50 - ShowDiff];
        }else{
            [self showLiveGoods:msg];
        }
    }
}
-(void)KickUser:(NSDictionary *)chats{
    chats = [YBToolClass roomChatInsertTime:chats];
    [msgList addObject:chats];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast];
}
-(void)kickOK{
    [self dissmissVC];
    [MBProgressHUD showError:YZMsg(@"你已被踢出房间")];
}
#pragma frontview 信息页面
-(void)gongxianbang{
    //跳往魅力值界面
    NSString *language= [PublicObj getCurrentLanguage];

    YBWebViewController *jumpC = [[YBWebViewController alloc]init];
    jumpC.urls = [NSString stringWithFormat:@"%@/appapi/contribute/index?uid=%@&language=%@",h5url,minstr([self.playDoc valueForKey:@"uid"]),language];
    [[MXBADelegate sharedAppDelegate]pushViewController:jumpC animated:YES];

}
//加载信息页面
-(void)zhubomessage{
    isMicUser = NO;
    [self showButtleView:minstr([self.playDoc valueForKey:@"uid"])];
}
//改变tableview高度
-(void)tableviewheight:(CGFloat)h{
    if (_liveGoodsView) {
        _liveGoodsView.frame =CGRectMake(_window_width + 10,h+40+ShowDiff,tableWidth-30,90);
        self.tableView.frame = CGRectMake(_window_width + 10,h-100,tableWidth,_window_height*0.2);
        useraimation.frame = CGRectMake(_window_width + 10,self.tableView.top - 40,_window_width,20);

    }else{
        self.tableView.frame = CGRectMake(_window_width + 10,h,tableWidth,_window_height*0.2);
        useraimation.frame = CGRectMake(_window_width + 10,self.tableView.top - 40,_window_width,20);
    }
}
//点击礼物ye消失
-(void)zhezhaoBTNdelegate{
    giftViewShow = NO;
    setFrontV.ZheZhaoBTN.hidden = YES;
    fenxiangV.hidden = YES;
    if (gameVC || rotationV) {
        if (gameVC) {
            gameVC.frame = CGRectMake(_window_width, _window_height - 260, _window_width,260);
             [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 265];
        }
        if (rotationV) {
            rotationV.frame = CGRectMake(_window_width, _window_height - _window_width/1.5, _window_width, _window_width);
            [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - _window_width+_window_width/5];
        }
        [UIView animateWithDuration:0.5 animations:^{
            [self changeGiftViewFrameY:_window_height*3];
        }];
    }else{
        [UIView animateWithDuration:0.5 animations:^{
            [self changeGiftViewFrameY:_window_height *3];
            [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 50 - ShowDiff];
        }];
    }
    keyBTN.hidden = NO;
    //wangminxinliwu
    [self changecontinuegiftframe];
    [self showBTN];
    if (huanxinviews) {
        if (sysView || tChatsamall) {
            return;
        }
        [huanxinviews.view removeFromSuperview];
        huanxinviews = nil;
        huanxinviews.view = nil;
    }
}
//页面退出
-(void)returnCancless{
    smallShowAlert = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];
    UIAlertAction *defaultActionss = [UIAlertAction actionWithTitle:YZMsg(@"退出直播间") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self dissmissVC];
        [self signOutWatchLive];

    }];
    UIAlertAction *smallAction;
    if (!isHaveUpMic) {
        smallAction = [UIAlertAction actionWithTitle:YZMsg(@"聊天室最小化") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            [[NSNotificationCenter defaultCenter]postNotificationName:@"SHOWCHATLIVE" object:nil userInfo:self.playDoc];
            [self dissmissVC];

        }];
    }
    UIAlertAction*cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
    }];

    [defaultActionss setValue:RGBA(252,43,43,1) forKey:@"_titleTextColor"];
    [smallAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    [cancelAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    
    [smallShowAlert addAction:smallAction];
    [smallShowAlert addAction:defaultActionss];
    [smallShowAlert addAction:cancelAction];
    [self presentViewController:smallShowAlert animated:YES completion:nil];
}
//每日任务-用户离开直播间
-(void)signOutWatchLive{
    if ([[Config getOwnID] intValue] <= 0) {
        return;
    }
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.signOutWatchLive"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken]
    };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
    }];
}
-(void)doFenxiang{
    if (!fenxiangV) {
        //分享弹窗
        fenxiangV = [[fenXiangView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height)];
        [fenxiangV GetDIc:self.playDoc];
        fenxiangV.roomType = @"voice";
        [self.view addSubview:fenxiangV];
    }else{
        [fenxiangV show];
    }
}
-(void)changeGiftViewFrameY:(CGFloat)Y{
    if (Y >= _window_height) {
        liansongliwubottomview.frame = CGRectMake(_window_width, self.tableView.top-150,300,140);
        giftview.frame = CGRectMake(0,Y, _window_width, _window_height);
    }else{
        giftview.frame = CGRectMake(0, 0, _window_width, _window_height);
    }
}
//礼物按钮
-(void)doLiwu{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }
    if (gameVC) {
        [self changeBtnFrame:_window_height - 260];
        [UIView animateWithDuration:0.5 animations:^{
            gameVC.frame = CGRectMake(_window_width, _window_height+60, _window_width,260);
        }];
    }
    if (rotationV) {
        [self changeBtnFrame:_window_height - 45 - _window_width/1.5];
        [UIView animateWithDuration:0.5 animations:^{
            rotationV.frame = CGRectMake(_window_width, _window_height+60, _window_width, _window_width);
        }];
    }
    if (giftview) {
        [giftview removeFromSuperview];
        giftview = nil;
    }

    NSMutableArray *muatArr = [NSMutableArray array];
    [muatArr addObject:@{@"uid":minstr([_playDoc valueForKey:@"uid"]),@"num":YZMsg(@"主持"),@"avatar":minstr([_playDoc valueForKey:@"avatar"]),@"sitid":@"0"}];
    if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
        for (YBUserLinkButton *btn in sitButtonArray) {
            if (btn.userDic && btn.userDic.count > 0) {
                NSString *numStr =[NSString stringWithFormat:@"%@%@",[self getChinaWord:[btn.sitid intValue]],YZMsg(@"麦")];
                NSString *languageStr= [PublicObj getCurrentLanguage];
                if([languageStr containsString:EN]){
                    numStr =[NSString stringWithFormat:@"%@%@",YZMsg(@"麦"),[self getChinaWord:[btn.sitid intValue]]];
                }
                [muatArr addObject:@{@"uid":minstr([btn.userDic valueForKey:@"uid"]),@"num":numStr,@"avatar":minstr([btn.userDic valueForKey:@"avatar"]),@"sitid":btn.sitid}];
            }
        }
    }else{
        for (ChatVideoUserLinkView *btn in sitVideoButtonArray) {
            if (btn.userDic && btn.userDic.count > 0) {
                NSLog(@"rk===>[用户信息]:%@",btn.userDic);
                NSString *zbUid = minstr([btn.userDic valueForKey:@"uid"]);
                if (![zbUid isEqual:@"0"] && ![zbUid isEqual:minstr([self.playDoc valueForKey:@"uid"])]) {
                    NSString *numStr =[NSString stringWithFormat:@"%@%@",[self getChinaWord:[btn.sitid intValue]],YZMsg(@"麦")];
                    NSString *languageStr= [PublicObj getCurrentLanguage];
                    if([languageStr containsString:EN]){
                        numStr =[NSString stringWithFormat:@"%@%@",YZMsg(@"麦"),[self getChinaWord:[btn.sitid intValue]]];
                    }
                    [muatArr addObject:@{@"uid":minstr([btn.userDic valueForKey:@"uid"]),@"num":numStr,@"avatar":minstr([btn.userDic valueForKey:@"avatar"]),@"sitid":btn.sitid}];
                }
            }
        }
    }
    if (giftViewShow == NO) {
        giftViewShow = YES;
        if (!giftview) {
            //礼物弹窗
            giftview = [[liwuview alloc]initWithDic:self.playDoc andMyDic:nil andAlluser:muatArr IsChatRoom:YES];
            giftview.giftDelegate = self;
             [self changeGiftViewFrameY:_window_height*3];
            [self.view addSubview:giftview];
        }else{
            [giftview chongzhiV:[Config getcoin]];
        }
        giftview.guradType = minstr([guardInfo valueForKey:@"type"]);

        [self.view bringSubviewToFront:giftview];

        backScrollView.userInteractionEnabled = YES;
        setFrontV.ZheZhaoBTN.hidden = NO;
        setFrontV.backgroundColor = [UIColor clearColor];
        LiveUser *user = [Config myProfile];
        [giftview chongzhiV:user.coin];
        [UIView animateWithDuration:0.1 animations:^{
            [self changeGiftViewFrameY:_window_height - (_window_width/2+100+ShowDiff)];
        }];
        [self changecontinuegiftframeIndoliwu];
        [self showBTN];
    }
    if(isGameToGift){
        [giftview.pageView scrollToViewAtIndex:1 animate:NO];
    }
}
- (NSString *)getChinaWord:(int)sitid{
    NSArray *array = @[@"一",@"二",@"三",@"四",@"五",@"六",@"七",@"八"];
    NSString *languageStr= [PublicObj getCurrentLanguage];
    if ([languageStr containsString:EN]) {
        array = @[@"1",@"2",@"3",@"4",@"5",@"6",@"7",@"8"];
    }
    NSString *string = array[sitid-1];
    NSLog(@"str = %@", string);
    return string;
}

#pragma gift delegate
//发送礼物
-(void)sendGift:(NSDictionary *)myDic andPlayDic:(NSDictionary *)playDic andData:(NSDictionary *)datas andLianFa:(NSString *)lianfa{
    haohualiwu = lianfa;
    NSString *info = [datas valueForKey:@"gifttoken"];
    level = [datas valueForKey:@"level"];
    LiveUser *users = [Config myProfile];
    users.level = level;
    [Config updateProfile:users];
    [socketDelegate sendGift:level andINfo:info andlianfa:lianfa andGiftInfo:datas];
    if (rotationV) {
        [rotationV reloadcoins];
    }
    if (gameVC) {
        [gameVC reloadcoins];
    }
}
-(NSMutableArray *)chatModels{
    NSMutableArray *array = [NSMutableArray array];
    for (NSDictionary *dic in msgList) {
        chatModel *model = [chatModel modelWithDic:dic];
        [model setChatFrame:[_chatModels lastObject]];
        [array addObject:model];
    }
    _chatModels = array;
    return _chatModels;
}
-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    self.navigationController.navigationBarHidden = YES;
    self.navigationController.interactivePopGestureRecognizer.delegate =nil;

    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    [backScrollView setContentOffset:CGPointMake(_window_width,0) animated:YES];
    [self labeiHid];
    if(_txLivePlayer){
        [_txLivePlayer setPlayoutVolume:100];
    }
}
-(void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    if(_txLivePlayer){
        [_txLivePlayer setPlayoutVolume:0];
    }
}
-(void)shajincheng{
}
-(void)initArray{
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"isPlaying"];
    haslianmai = NO;//本人是否连麦
    _canScrollToBottom  = YES;
    haohualiwuV.expensiveGiftCount = [NSMutableArray array];
    platliwuV.expensiveGiftCount = [NSMutableArray array];
    msgList = [[NSMutableArray alloc] init];
    level = (NSString *)[Config getLevel];
    self.content = [NSString stringWithFormat:@" "];
    _chatModels = [NSMutableArray array];
    _chatWordArr = [NSArray array];
    _liveGoodTipArr = [NSMutableArray array];
    _hostdic = [NSDictionary dictionary];
    _lastHostDic = [NSDictionary dictionary];
    _nextHostDic = [NSDictionary dictionary];
    _viploginArray = [NSMutableArray array];
    sitButtonArray = [NSMutableArray array];
    userCount = 0;
    starisok = 0;
    heartNum = @1;
    firstStar = 0;//点亮
    titleColor = @"0";
    unReadCount = @"0";
}
//更新最新配置
-(void)buildUpdate{
    // 在这里加载后台配置文件
    [YBToolClass postNetworkWithUrl:@"Home.getConfig" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            NSDictionary *subdic = [info firstObject];
            if (![subdic isEqual:[NSNull null]]) {
                liveCommon *commons = [[liveCommon alloc]initWithDic:subdic];
                [common saveProfile:commons];
            }
        }
    } fail:^{
    }];
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.automaticallyAdjustsScrollViewInsets = NO;
    [self buildUpdate];
    _canChange = NO;
    ksynotconnected = NO;
    ksyclosed = NO;
    isshow = 0;
    giftViewShow = NO;
    isSuperAdmin = NO;
    isHaveUpMic = NO;
    [self initArray];
    myUser = [Config myProfile];
    _listArray = [NSMutableArray array];
    [self playerPlayVideo];//播放视频
    [self setBottomScroll];
    [self setView];//加载信息页面
    [self Registnsnotifition];
    [self getNodeJSInfo];//初始化nodejs信息

//    //计时扣费
    if ([_livetype isEqual:@"3"]) {
        if (!timecoast) {
            timecoast = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(timecoastmoney) userInfo:nil repeats:YES];
        }
    }
    coasttime = 60;
    //显示进场标题
    [self showTitle];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidEnterBackground:) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillEnterForeground:) name:UIApplicationWillEnterForegroundNotification object:nil];
}
//显示进场标题
- (void)showTitle{
    if (minstr([self.playDoc valueForKey:@"title"]).length > 0) {
        CGFloat titleWidth = [[YBToolClass sharedInstance] widthOfString:minstr([self.playDoc valueForKey:@"title"]) andFont:[UIFont systemFontOfSize:14] andHeight:30];
        titleBackImgView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width, 110, 35+titleWidth+20, 30)];
        titleBackImgView.image = [UIImage imageNamed:@"moviePlay_title"];
        titleBackImgView.alpha = 0.5;
        titleBackImgView.layer.cornerRadius = 15;
        titleBackImgView.layer.masksToBounds = YES;
        [self.view addSubview:titleBackImgView];
        UIImageView *laba = [[UIImageView alloc]initWithFrame:CGRectMake(10, 7.5, 15, 15)];
        laba.image = [UIImage imageNamed:@"moviePlay_laba"];
        [titleBackImgView addSubview:laba];
        UILabel *titL = [[UILabel alloc]initWithFrame:CGRectMake(laba.right+10, 0, titleWidth+20, 30)];
        titL.text = minstr([self.playDoc valueForKey:@"title"]);
        titL.textColor = [UIColor whiteColor];
        titL.font = [UIFont systemFontOfSize:14];
        [titleBackImgView addSubview:titL];
        [UIView animateWithDuration:3 animations:^{
            titleBackImgView.alpha = 1;
            titleBackImgView.x = 10;
        }];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(6 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:2 animations:^{
                titleBackImgView.alpha = 0;
                titleBackImgView.x = -_window_width;
            } completion:^(BOOL finished) {
                if (titleBackImgView) {
                    [titleBackImgView removeFromSuperview];
                    titleBackImgView = nil;
                }
            }];
        });
    }
}
- (BOOL)textFieldShouldReturn:(UITextField *)textField;
{
    if (textField == keyField) {
        [self pushMessage:nil];
    }
    return YES;
}
//加载底部滑动scrollview
//-(void)backscroll{
//
//    backScrollView = [[UIScrollView alloc]initWithFrame:CGRectMake(0,_window_height, _window_width, _window_height)];
//    backScrollView.delegate = self;
//    backScrollView.contentSize = CGSizeMake(_window_width*2,0);
//    [backScrollView setContentOffset:CGPointMake(_window_width,0) animated:YES];
//    backScrollView.pagingEnabled = YES;
//    backScrollView.backgroundColor = [UIColor clearColor];
//    backScrollView.showsHorizontalScrollIndicator = NO;
//    backScrollView.bounces = NO;
//    backScrollView.userInteractionEnabled = YES;
//    [self.view addSubview:backScrollView];
//
//      fangKeng = !fangKeng;//全部加载完毕了再释放滑动
//}
-(void)socketShutUp:(NSString *)name andID:(NSString *)ID andType:(NSString *)type{
    [socketDelegate shutUp:name andID:ID andType:type];
}
-(void)socketkickuser:(NSString *)name andID:(NSString *)ID{
    [socketDelegate kickuser:name andID:ID];
}
-(void)GetInformessage:(NSDictionary *)subdic{
    isMicUser = NO;
    [self showButtleView:minstr([subdic valueForKey:@"id"])];
}
//几秒后隐藏消失
-(void)doAlpha{
    [UIView animateWithDuration:3.0 animations:^{
        starImage.alpha = 0;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [starImage removeFromSuperview];
        });
    }];
}
//点亮星星
-(void)starok{

    if (gameVC) {
    }
    else if (rotationV){
    }

    //wangminxinliwu
    [self changecontinuegiftframe];
    toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
    [keyField resignFirstResponder];
    [self showBTN];
    keyBTN.hidden = NO;
    [self staredMove];
    if ([[Config getOwnID] intValue] <= 0) {
          return;
    }
    //♥点亮
    if (firstStar == 0) {
        firstStar = 1;
        [socketDelegate starlight:level :heartNum andUsertype:usertype andGuardType:minstr([guardInfo valueForKey:@"type"])];
        titleColor = @"0";
    }
    [self getweidulabel];
    [self zhezhaoBTNdelegate];
}
-(void)staredMove{
    CGFloat starX;
    CGFloat starY;
    starX = _returnCancle.frame.origin.x - 10;
    starY = _liwuBTN.frame.origin.y - 20;
    NSInteger random = arc4random()%5;
    starImage = [[UIImageView alloc]initWithFrame:CGRectMake(starX+random,starY-random,30,30)];
    starImage.alpha = 0;
    
    NSMutableArray *array = [NSMutableArray arrayWithObjects:@"plane_heart_no1.png",@"plane_heart_pink.png",@"plane_heart_red.png",@"plane_heart_no2.png",@"plane_heart_heart.png", nil];
    
    srand((unsigned)time(0));
    
    starImage.image = [UIImage imageNamed:[array objectAtIndex:random]];
    
    heartNum = [NSNumber numberWithInteger:random];
    
    [UIView animateWithDuration:0.2 animations:^{
            starImage.alpha = 1.0;
            starImage.frame = CGRectMake(starX+random - 10, starY-random - 30, 30, 30);
            CGAffineTransform transfrom = CGAffineTransformMakeScale(1.3, 1.3);
            starImage.transform = CGAffineTransformScale(transfrom, 1, 1);
        }];
    [backScrollView insertSubview:starImage atIndex:10];
    
    CGFloat finishX = _window_width*2 - round(arc4random() % 200);
    //  动画结束点的Y值
    CGFloat finishY = 200;
    //  imageView在运动过程中的缩放比例
    CGFloat scale = round(arc4random() % 2) + 0.7;
    // 生成一个作为速度参数的随机数
    CGFloat speed = 1 / round(arc4random() % 900) + 0.6;
    //  动画执行时间
    NSTimeInterval duration = 4 * speed;
    //  如果得到的时间是无穷大，就重新附一个值（这里要特别注意，请看下面的特别提醒）
    if (duration == INFINITY) duration = 2.412346;
    //  开始动画
    [UIView beginAnimations:nil context:(__bridge void *_Nullable)(starImage)];
    //  设置动画时间
    [UIView setAnimationDuration:duration];
    //  设置imageView的结束frame
    starImage.frame = CGRectMake( finishX, finishY, 30 * scale, 30 * scale);
    //  设置渐渐消失的效果，这里的时间最好和动画时间一致
    [UIView animateWithDuration:duration animations:^{
        starImage.alpha = 0;
    }];
    //  结束动画，调用onAnimationComplete:finished:context:函数
    [UIView setAnimationDidStopSelector:@selector(onAnimationComplete:finished:context:)];
    //  设置动画代理
    [UIView setAnimationDelegate:self];
    [UIView commitAnimations];
    if (starisok == 0) {
        starisok = 1;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            starisok = 0;
        });
//    [socketDelegate starlight];
        
    }
}
/// 动画完后销毁iamgeView
- (void)onAnimationComplete:(NSString *)animationID finished:(NSNumber *)finished context:(void *)context{
    if ([finished isEqual:[NSNumber numberWithBool:YES]]) {
        UIImageView *imageViewsss = (__bridge UIImageView *)(context);
        [imageViewsss removeFromSuperview];
        imageViewsss = nil;

    }
}
-(void)setBottomScroll{
    buttomscrollview = [[UIScrollView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    buttomscrollview.delegate = self;
    buttomscrollview.contentSize = CGSizeMake(_window_width, _window_height*_scrollarray.count);
    [buttomscrollview setContentOffset:CGPointMake(0, _window_height*_scrollindex)];
    buttomscrollview.pagingEnabled = YES;
    buttomscrollview.showsVerticalScrollIndicator = NO;
    buttomscrollview.showsHorizontalScrollIndicator =NO;
    buttomscrollview.scrollsToTop = NO;
    buttomscrollview.bounces = NO;
    buttomscrollview.userInteractionEnabled = YES;
    buttomscrollview.backgroundColor = [UIColor clearColor];
    if (@available(iOS 11.0, *)) {
        buttomscrollview.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }

    [self.view insertSubview:buttomscrollview atIndex:5];
    scrollImgArr = [NSMutableArray array];
    CGFloat X = 0;
    for (int i=0; i<_scrollarray.count; i++) {
        UIImageView *imagevB = [[UIImageView alloc]initWithFrame:CGRectMake(0, X, _window_width, _window_height)];
        imagevB.userInteractionEnabled = YES;
        imagevB.backgroundColor = [UIColor clearColor];
        imagevB.contentMode = UIViewContentModeScaleAspectFill;
        imagevB.clipsToBounds = YES;
        [buttomscrollview addSubview:imagevB];
        UIBlurEffect *blur = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
        UIVisualEffectView *effectview = [[UIVisualEffectView alloc] initWithEffect:blur];
        effectview.frame = CGRectMake(0, 0,_window_width,_window_height);
        [imagevB addSubview:effectview];

        if (i == _scrollindex) {
            buttomimageviews = imagevB;
            [buttomimageviews sd_setImageWithURL:[NSURL URLWithString:[self.playDoc valueForKey:@"avatar"]] placeholderImage:[UIImage imageNamed:@"icon_avatar_placeholder"]];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                buttomimageviews.hidden = YES;
            });
        }
        if (i == _scrollindex-1) {
            [imagevB sd_setImageWithURL:[NSURL URLWithString:[_scrollarray[i] valueForKey:@"avatar"]] placeholderImage:[UIImage imageNamed:@"icon_avatar_placeholder"]];
        }
        if (i == _scrollindex+1) {
            [imagevB sd_setImageWithURL:[NSURL URLWithString:[_scrollarray[i] valueForKey:@"avatar"]] placeholderImage:[UIImage imageNamed:@"icon_avatar_placeholder"]];
        }

        [scrollImgArr addObject:imagevB];
        X+=_window_height;

    }
}
/*==================  以上是点亮  ================*/
-(void)setView{
    backScrollView = [[UIScrollView alloc]initWithFrame:CGRectMake(0,_window_height*_scrollindex, _window_width, _window_height)];
    backScrollView.delegate = self;
    backScrollView.contentSize = CGSizeMake(_window_width*2,0);
    [backScrollView setContentOffset:CGPointMake(_window_width,0) animated:YES];
    backScrollView.pagingEnabled = YES;
    backScrollView.scrollEnabled = NO;
    backScrollView.backgroundColor =[[UIColor blackColor] colorWithAlphaComponent:0.01];// [UIColor clearColor];
    backScrollView.showsHorizontalScrollIndicator = NO;
    backScrollView.userInteractionEnabled = YES;
    [buttomscrollview addSubview:backScrollView];
    
    if (@available(iOS 11.0,*)) {
        backScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    setFrontV = [[setViewM alloc] initWithDic:self.playDoc];
    setFrontV.frame = CGRectMake(_window_width,0,_window_width,_window_height);
    setFrontV.frontviewDelegate = self;
    setFrontV.clipsToBounds = YES;
    [backScrollView addSubview:setFrontV];
    setFrontV.bigAvatarImageView.hidden = YES;

    UIImageView *backImg = [[UIImageView alloc]init];
    backImg.frame = CGRectMake(0, 0, _window_width, _window_height);
    backImg.image = [UIImage imageNamed:@"chatbackgroundImg"];
    backImg.contentMode = UIViewContentModeScaleAspectFill;
    [setFrontV addSubview:backImg];

    
    if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
        CGFloat sitTop = statusbarHeight +150;
        //上麦用户
        CGFloat bWidth = (_window_width - 80)/4;
        NSArray *sits =@[@{},@{},@{},@{},@{},@{},@{},@{}];
        sitUserArray = sits.mutableCopy;
        livePlayerArray = [NSMutableArray array];
        for (int i = 0; i < sitUserArray.count; i ++) {
            YBUserLinkButton *userButton = [[YBUserLinkButton alloc] initWithFrame:CGRectMake(10+(i%4)*(bWidth+20), sitTop+(i/4)*(bWidth + 20)+ShowDiff, bWidth, (bWidth + 20)) andIsBoos:NO andRoomMessage:@{}];;
            userButton.roomDic = @{};
//          userButton.skillID = currentSkillID;
            NSMutableDictionary *muDic = [sitUserArray[i] mutableCopy];
            if ([muDic valueForKey:@"id"]) {
                [muDic setObject:minstr([muDic valueForKey:@"id"]) forKey:@"uid"];
            }
            userButton.sitid = [NSString stringWithFormat:@"%d",i+1];
            [setFrontV addSubview:userButton];
            [sitButtonArray addObject:userButton];
            userButton.userDic = muDic;
            userButton.delegate = self;
            V2TXLivePlayer *_txLivePlayer = [[V2TXLivePlayer alloc] init];
            [_txLivePlayer setObserver:self];
            [_txLivePlayer enableObserveAudioFrame:YES];
            [livePlayerArray addObject:_txLivePlayer];
        }
    }else{
        CGFloat sitTop = statusbarHeight +150;
        CGFloat bWidth = (_window_width )/3;
        sitUserArray = @[@{},@{},@{},@{},@{},@{}].mutableCopy;
        sitVideoButtonArray = [NSMutableArray array];
        livePlayerArray = [NSMutableArray array];

        _playVideoChatV = [[UIView alloc]init];
        _playVideoChatV.frame = CGRectMake(0, sitTop+ShowDiff, _window_width, bWidth*2);
        _playVideoChatV.backgroundColor = RGBA(0, 0, 0, 0.2);
        _playVideoChatV.contentMode = UIViewContentModeScaleToFill;
        _playVideoChatV.hidden = YES;
        [setFrontV addSubview:_playVideoChatV];
        
        for (int i = 0; i < sitUserArray.count; i ++) {
            ChatVideoUserLinkView *userVideoView = [[ChatVideoUserLinkView alloc] initWithFrame:CGRectMake((i%3)*(bWidth), sitTop+(i/3)*(bWidth )+ShowDiff, bWidth, bWidth) andIsBoss:NO andRoomMessage:@{}];
            userVideoView.sitid = [NSString stringWithFormat:@"%d",i+1];
            userVideoView.delegate = self;
            [setFrontV addSubview:userVideoView];
            [sitVideoButtonArray addObject:userVideoView];
            if(i == 0){
                userVideoView.isBoss = YES;
                if ([_sdkType isEqual:@"1"]) {
                    [self.txLivePlayer setRenderView:userVideoView];
                    [self.txLivePlayer setRenderFillMode:V2TXLiveFillModeFill];
                    NSString *playUrl = [self.playDoc valueForKey:@"pull"];
                    [_txLivePlayer startLivePlay:playUrl];
                    V2TXLiveCode liveCode = [_txLivePlayer startLivePlay:playUrl];
                    NSLog(@"gethostlowurl---:%ld",liveCode);
                }
            }else{
                if ([_sdkType isEqual:@"1"]) {
                    V2TXLivePlayer *_txLivePlayer = [[V2TXLivePlayer alloc] init];
                    [_txLivePlayer setObserver:self];
                    [_txLivePlayer enableObserveAudioFrame:YES];
                    [livePlayerArray addObject:_txLivePlayer];
                }
            }
        }
    }
    self.tableView = [[UITableView alloc]initWithFrame:CGRectMake(_window_width + 10,setFrontV.frame.size.height - _window_height*0.25 - 50 - ShowDiff,tableWidth,_window_height*0.2) style:UITableViewStylePlain];
    [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 50 - ShowDiff];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.estimatedRowHeight = 80.0;
    [backScrollView insertSubview:self.tableView atIndex:4];
    self.tableView.clipsToBounds = YES;
    
    useraimation = [[userLoginAnimation alloc]init];
    useraimation.frame = CGRectMake(_window_width + 10,self.tableView.top - 40,_window_width,20);
    [backScrollView insertSubview:useraimation atIndex:4];
    
    _danmuView = [[GrounderSuperView alloc] initWithFrame:CGRectMake(_window_width, 100, self.view.frame.size.width, 140)];
    [backScrollView insertSubview:_danmuView atIndex:5];//添加弹幕
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(starok)];
    [_danmuView addGestureRecognizer:tap];
    cs = [[catSwitch alloc] initWithFrame:CGRectMake(6,11,44,22)];
    cs.delegate = self;
    //输入框
    if ([[YBYoungManager shareInstance]isOpenYoung]) {
        cs.hidden = YES;
        keyField = [[UITextField alloc]initWithFrame:CGRectMake(10,7,_window_width-40 - 30, 30)];
    }else{
        //输入框
        keyField = [[UITextField alloc]initWithFrame:CGRectMake(cs.right+10,7,_window_width-90 - 30, 30)];
    }

    keyField.returnKeyType = UIReturnKeySend;
    keyField.delegate = self;
    keyField.textColor = [UIColor blackColor];
    keyField.borderStyle = UITextBorderStyleNone;
    keyField.placeholder = YZMsg(@"和大家说些什么");
    keyField.backgroundColor = [UIColor whiteColor];
    keyField.layer.cornerRadius = 15.0;
    keyField.layer.masksToBounds = YES;
    UIView *fieldLeft = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 15, 30)];
    fieldLeft.backgroundColor = [UIColor whiteColor];
    keyField.leftView = fieldLeft;
    keyField.leftViewMode = UITextFieldViewModeAlways;
    keyField.font = [UIFont systemFontOfSize:15];
    #pragma mark -- 绑定键盘
    www = 30;
    //点击弹出键盘
    keyBTN = [UIButton buttonWithType:UIButtonTypeCustom];
//    [keyBTN setBackgroundImage:[UIImage imageNamed:@"live_聊天"] forState:UIControlStateNormal];
    [keyBTN addTarget:self action:@selector(showkeyboard:) forControlEvents:UIControlEventTouchUpInside];
    [keyBTN setTitle:YZMsg(@"  说点什么...") forState:0];
    keyBTN.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    keyBTN.titleLabel.font= [UIFont systemFontOfSize:13];
    keyBTN.titleLabel.textAlignment = NSTextAlignmentLeft;
    [keyBTN setBackgroundColor:RGBA(1, 1, 1, 0.4)];
    keyBTN.layer.cornerRadius = 15;
    keyBTN.layer.masksToBounds = YES;
    keyBTN.frame = CGRectMake(_window_width + 15,_window_height - 45 - ShowDiff, 110, www);
    UIImageView *faceIcon =[[UIImageView alloc]init];
    faceIcon.frame = CGRectMake(keyBTN.width-26, 4, 22, 22);
    faceIcon.image = [UIImage imageNamed:@"msg_face"];
    [keyBTN addSubview:faceIcon];

    //发送按钮
    pushBTN = [UIButton buttonWithType:UIButtonTypeCustom];
//    [pushBTN setTitle:YZMsg(@"发送") forState:UIControlStateNormal];
    [pushBTN setImage:[UIImage imageNamed:@"chat_send_gray"] forState:UIControlStateNormal];
    [pushBTN setImage:[UIImage imageNamed:@"chat_send_yellow"] forState:UIControlStateSelected];
    pushBTN.imageView.contentMode = UIViewContentModeScaleAspectFit;
    pushBTN.layer.masksToBounds = YES;
    pushBTN.layer.cornerRadius = 5;
//    [pushBTN setTitleColor:RGB(255, 204, 0) forState:0];
    pushBTN.selected = NO;
    [pushBTN addTarget:self action:@selector(pushMessage:) forControlEvents:UIControlEventTouchUpInside];
    pushBTN.frame = CGRectMake(_window_width-55,7,50,30);
    
    //tool绑定键盘
    toolBar = [[UIView alloc]initWithFrame:CGRectMake(0,_window_height+10, _window_width, 44)];
    toolBar.backgroundColor = [UIColor clearColor];
    UIView *tooBgv = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 44)];
    tooBgv.backgroundColor = [UIColor whiteColor];
    tooBgv.alpha = 0.7;
    [toolBar addSubview:tooBgv];
    [toolBar addSubview:pushBTN];
    [toolBar addSubview:keyField];
    [toolBar addSubview:cs];
    
    [self.view addSubview:toolBar];
    //增加监听，当键盘出现或改变时收出消息
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ChangePushBtnState) name:UITextFieldTextDidChangeNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(denglushixiao) name:@"denglushixiao" object:nil];

    starTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(starok)];
    starTap.delegate = (id<UIGestureRecognizerDelegate>)self;
    starTap.numberOfTapsRequired = 1;
    starTap.numberOfTouchesRequired = 1;
    [setFrontV addGestureRecognizer:starTap];
    //手绘礼物
    [backScrollView addSubview:self.paintedShowRegion];

    liansongliwubottomview = [[UIView alloc]init];
    [backScrollView insertSubview:liansongliwubottomview atIndex:8];
    liansongliwubottomview.frame = CGRectMake(_window_width, self.tableView.top-150,300,140);
    
    UITapGestureRecognizer *gifttaps = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(starok)];
    [liansongliwubottomview addGestureRecognizer:gifttaps];
    
    //添加底部按钮
    _returnCancle = [UIButton buttonWithType:UIButtonTypeCustom];
    _returnCancle.frame = CGRectMake(_window_width*2-40,statusbarHeight+30,30,30);
    _returnCancle.tintColor = [UIColor whiteColor];
    [_returnCancle setImage:[UIImage imageNamed:@"live_关闭"] forState:UIControlStateNormal];//直播间观众—关闭
    _returnCancle.backgroundColor = [UIColor clearColor];
    [_returnCancle addTarget:self action:@selector(returnCancless) forControlEvents:UIControlEventTouchUpInside];
    //消息按钮
    _messageBTN =[UIButton buttonWithType:UIButtonTypeCustom];
//     self.unReadLabel = [[UILabel alloc]initWithFrame:CGRectMake(13, -5, 16, 16)];
//     self.unReadLabel.hidden = YES;
//     self.unReadLabel.textAlignment = NSTextAlignmentCenter;
//     self.unReadLabel.textColor = [UIColor whiteColor];
//     self.unReadLabel.layer.masksToBounds = YES;
//     self.unReadLabel.layer.cornerRadius = 8;
//     self.unReadLabel.font = [UIFont systemFontOfSize:9];
//     self.unReadLabel.backgroundColor = [UIColor redColor];
//    [_messageBTN addSubview: self.unReadLabel];
    [_messageBTN setImage:[UIImage imageNamed:@"live_私信"] forState:UIControlStateNormal];//直播间观众—私信
    _messageBTN.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [_messageBTN addTarget:self action:@selector(doMessage) forControlEvents:UIControlEventTouchUpInside];
    //更多功能
    moreBTN = [UIButton buttonWithType:UIButtonTypeSystem];
    moreBTN.tintColor = [UIColor whiteColor];
    [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能"] forState:UIControlStateNormal];
    [moreBTN addTarget:self action:@selector(showmoreview) forControlEvents:UIControlEventTouchUpInside];
//    keyField.frame = CGRectMake(cs.right+10,7,_window_width-90 - 30, 30);
    
    //上麦按钮
    waitUpMicBtn = [UIButton buttonWithType:0];
    waitUpMicBtn.tintColor = [UIColor whiteColor];
    [waitUpMicBtn setBackgroundImage:[UIImage imageNamed:@"chatwaitup"] forState:UIControlStateNormal];
    [waitUpMicBtn addTarget:self action:@selector(upMicBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    //闭麦按钮
    closeMicBtn = [UIButton buttonWithType:0];
    closeMicBtn.tintColor = [UIColor whiteColor];
    [closeMicBtn setBackgroundImage:[UIImage imageNamed:@"openmicBtn"] forState:UIControlStateNormal];
    [closeMicBtn addTarget:self action:@selector(closeMicBtnClick) forControlEvents:UIControlEventTouchUpInside];
    closeMicBtn.hidden = YES;
//    //大表情按钮
//    emoticonBtn = [UIButton buttonWithType:0];
//    emoticonBtn.tintColor = [UIColor whiteColor];
//    [emoticonBtn setBackgroundImage:[UIImage imageNamed:@"bigemotion"] forState:UIControlStateNormal];
//    [emoticonBtn addTarget:self action:@selector(showEmotionView) forControlEvents:UIControlEventTouchUpInside];
//    emoticonBtn.hidden = YES;
    //礼物
    _liwuBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    _liwuBTN.tintColor = [UIColor whiteColor];
    [_liwuBTN setBackgroundImage:[UIImage imageNamed:@"live_礼物"] forState:UIControlStateNormal];
    [_liwuBTN addTarget:self action:@selector(doLiwu) forControlEvents:UIControlEventTouchUpInside];
    
    //首充
    _firstChargeBtn = [UIButton buttonWithType:0];
    _firstChargeBtn.tintColor = [UIColor whiteColor];
    [_firstChargeBtn setBackgroundImage:[UIImage imageNamed:getImagename(@"live_首充")] forState:UIControlStateNormal];
    [_firstChargeBtn addTarget:self action:@selector(doFirstCharge) forControlEvents:UIControlEventTouchUpInside];
    
    _gameBtn =[UIButton buttonWithType:UIButtonTypeCustom];
    [_gameBtn addTarget:self action:@selector(clikcGameBtn) forControlEvents:UIControlEventTouchUpInside];
    [_gameBtn setImage:[UIImage imageNamed:@"live_游戏"] forState:0];

    //礼物
    turntableBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    turntableBtn.frame = CGRectMake(_window_width*2-55, 190+statusbarHeight, 50, 50);
    [turntableBtn setBackgroundImage:[UIImage imageNamed:@"幸运大转盘"] forState:UIControlStateNormal];
    turntableBtn.hidden = YES;
    [turntableBtn addTarget:self action:@selector(doTurntable) forControlEvents:UIControlEventTouchUpInside];

    [self setbtnframe];
    
    [backScrollView insertSubview:keyBTN atIndex:5];
    [backScrollView insertSubview:_returnCancle atIndex:5];
    [backScrollView  insertSubview:moreBTN atIndex:5];
//    [backScrollView insertSubview:_messageBTN atIndex:5];
    [backScrollView insertSubview:closeMicBtn atIndex:5];

//    [backScrollView insertSubview:emoticonBtn atIndex:5];
    if (![[YBYoungManager shareInstance]isOpenYoung]) {
        [backScrollView insertSubview:_liwuBTN atIndex:5];
        [backScrollView insertSubview:_firstChargeBtn atIndex:5];
        [backScrollView insertSubview:waitUpMicBtn atIndex:5];
        [backScrollView insertSubview:_gameBtn atIndex:5];

    }
}
-(void)setbtnframe{
    
    CGFloat  wwwwww = 30;
    CGFloat hhh = _window_height - wwwwww - 15 - ShowDiff;
    CGFloat  wwwssss = 30;
    CGFloat btnSpeace = 0;
    if(!IS_BIG_SCREEN){
        btnSpeace = 5;
    }

    keyBTN.frame = CGRectMake(_window_width + 15,hhh, 110, www);
    if ([[YBYoungManager shareInstance]isOpenYoung]) {
        moreBTN.frame = CGRectMake(_window_width*2 - wwwssss+btnSpeace-20,hhh,wwwssss,wwwssss);
//        _messageBTN.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*2-30,hhh,wwwssss,wwwssss);
    }else{
        moreBTN.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*2-20,hhh,wwwssss,wwwssss);
//        _messageBTN.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*3-30,hhh,wwwssss,wwwssss);
    }
    _liwuBTN.frame = CGRectMake(_window_width*2-wwwssss-10+btnSpeace,hhh,wwwssss,wwwssss);
    
    if (isHaveUpMic) {
        closeMicBtn.hidden = NO;
//        emoticonBtn.hidden = NO;
//        emoticonBtn.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*4-40,hhh, wwwssss, wwwssss);
        closeMicBtn.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*3-30,hhh, wwwssss, wwwssss);
        waitUpMicBtn.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*4-40,hhh, wwwssss, wwwssss);
        [waitUpMicBtn setBackgroundImage:[UIImage imageNamed:@"chatdownmic"] forState:UIControlStateNormal];
        _firstChargeBtn.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*5-50,hhh, wwwssss, wwwssss);
        _gameBtn.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*6-60,hhh, wwwssss, wwwssss);
    }else{
        closeMicBtn.hidden = YES;
//        emoticonBtn.hidden = YES;

        waitUpMicBtn.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*3-30,hhh, wwwssss, wwwssss);
        [waitUpMicBtn setBackgroundImage:[UIImage imageNamed:@"chatwaitup"] forState:UIControlStateNormal];
        _firstChargeBtn.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*4-40,hhh, wwwssss, wwwssss);
        _gameBtn.frame = CGRectMake(_window_width*2 - (wwwssss-btnSpeace)*5-50,hhh, wwwssss, wwwssss);

    }
}
-(void)doFirstCharge{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }
    YBWeakSelf;
    _chargeView = [[FirstChargeView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    _chargeView.btnEvent = ^{
        [weakSelf.chargeView removeFromSuperview];
        weakSelf.chargeView = nil;
    };
    [self.view addSubview:_chargeView];
}
-(void)clikcGameBtn{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }
    [IYZPlayFucView showPlayFucViewWithRes:_gameBtn withRoomInfo:_playDoc xqtb_switch:game_xqtb_switch xydzp_switch:game_xydzp_switch];
}

-(void)showUserInfo:(NSString *)uidStr{
    isMicUser = YES;
    [self showButtleView:uidStr];
}

-(void)closeMicBtnClick{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }
    NSString *currentIndex;
    if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
        for (int i = 0; i < sitButtonArray.count; i ++) {
            YBUserLinkButton *btn = sitButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:[Config getOwnID]]) {
                currentIndex = [NSString stringWithFormat:@"%d",i];
            }
        }
    }else{
        for (int i = 0; i < sitVideoButtonArray.count; i ++) {
            ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:[Config getOwnID]]) {
                currentIndex = [NSString stringWithFormat:@"%d",i];
            }
        }

    }
    NSString *status;
    if (isJingMai) {
        status = @"1";
    }else{
        status = @"0";
    }
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.changeVoiceMicStatus"];
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":minstr([_playDoc valueForKey:@"stream"]),
                          @"position":currentIndex,
                          @"status":status
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
//        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            isJingMai = !isJingMai;
            if (isJingMai) {
                [closeMicBtn setBackgroundImage:[UIImage imageNamed:@"closemicBtn"] forState:UIControlStateNormal];
            }else{
                [closeMicBtn setBackgroundImage:[UIImage imageNamed:@"openmicBtn"] forState:UIControlStateNormal];
            }
            NSString *micStatus;
            if ([status isEqual:@"1"]) {
                micStatus = @"1";
            }else{
                micStatus = @"-1";

            }
            NSDictionary *statusDic = @{@"status":micStatus,@"position":currentIndex,@"uid":[Config
                                                                                           getOwnID]};
            [socketDelegate changeMicStatus:statusDic];
        }else{
            [MBProgressHUD showError:msg];
        }
        } Fail:^(id fail) {
    }];

}
/*----------------大表情界面---------------*/
-(void)showEmotionView{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (_emtionView) {
        [_emtionView removeFromSuperview];
        _emtionView = nil;
    }
    YBWeakSelf;
    _emtionView = [[BigEmoticonView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    _emtionView.emotionHide = ^(NSString *type, NSString *index) {
        if ([type isEqual:@"0"]) {
            [weakSelf.emtionView removeFromSuperview];
            weakSelf.emtionView = nil;
        }else{
            [socketDelegate sendGiftFace:index];
        }
    };
    [self.view addSubview:_emtionView];
}
-(void)showBigFaceWithDic:(NSDictionary *)dic{
    NSString *uidStr = [dic valueForKey:@"uid"];
    NSString *index = [dic valueForKey:@"face"];
    NSString *imgUrl = [NSString stringWithFormat:@"chatemoticon%@",index];
    if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]) {
        for (int i = 0; i < sitButtonArray.count; i ++) {
            YBUserLinkButton *btn = sitButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:uidStr]) {
                btn.imgUrl = imgUrl;
            }
        }
    }else {
        for (int i = 0; i<sitVideoButtonArray.count; i++) {
            ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:uidStr]) {
                btn.imgUrl = imgUrl;
            }
        }
    }
}
/*-----------------获取上麦状态-------------*/
-(void)getMicStateData{
    YBWeakSelf;
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.getVoiceMicApplyList"];
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":minstr([_playDoc valueForKey:@"stream"])
                          };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
            NSLog(@"userRoom-------:%@",data);
            if ([code isEqual:@"0"]) {
                NSDictionary *infos = [[data valueForKey:@"info"] firstObject];
                if ([minstr([infos valueForKey:@"position"]) isEqual:@"-1"]) {
                    [weakSelf showUpMicUserView];
                }else{
                    [self showMicListWithDic:infos];
                }
            }
        } Fail:^(id fail) {
    }];
}
//语音聊天室中用户主动下麦
-(void)closeMic{
    YBWeakSelf;
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.userCloseVoiceMic"];
    
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":minstr([_playDoc valueForKey:@"stream"])
                          };

    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
//            [_txLivePush setMute:NO];
            isJingMai = NO;
            [closeMicBtn setBackgroundImage:[UIImage imageNamed:@"openmicBtn"] forState:UIControlStateNormal];
            [socketDelegate closeMicBySelf:@"0" andToUid:[Config getOwnID]];
        }
        } Fail:^(id fail) {
    }];
}
/*----------------点击上麦按钮--------------*/
-(void)upMicBtnClick{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }
    if (isHaveUpMic) {
        [self closeMic];
    }else{
        [self getMicStateData];
    }
}

-(void)showUpMicUserView{
    YBWeakSelf;

    if (_upMicUserView) {
        [_upMicUserView removeFromSuperview];
        _upMicUserView = nil;
    }
    _upMicUserView = [[ApplyUpMicView alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, _window_height) ApplyClick:^{
        [weakSelf applyVoiceLiveMic];
    } HideClick:^{
        [UIView animateWithDuration:0.6 animations:^{
            weakSelf.upMicUserView.frame = CGRectMake(0, _window_height, _window_width, _window_height);
                        } completion:^(BOOL finished) {
                            [weakSelf.upMicUserView removeFromSuperview];
                            weakSelf.upMicUserView = nil;
                        }];
    }];
    [self.view addSubview:_upMicUserView];
    [UIView animateWithDuration:0.6 animations:^{
        weakSelf.upMicUserView.frame = CGRectMake(0, 0, _window_width, _window_height);
    }];

}
/*----------------上麦申请接口--------------*/
-(void)applyVoiceLiveMic{
    YBWeakSelf;
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.applyVoiceLiveMic"];
    
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":minstr([_playDoc valueForKey:@"stream"])
                          };

    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            if (_upMicUserView) {
                [UIView animateWithDuration:0.6 animations:^{
                    weakSelf.upMicUserView.frame = CGRectMake(0, _window_height, _window_width, _window_height);
                                } completion:^(BOOL finished) {
                                    [weakSelf.upMicUserView removeFromSuperview];
                                    weakSelf.upMicUserView = nil;
                                }];
            }
            [socketDelegate applyVoiceRoom];
        }
        } Fail:^(id fail) {
            
    }];
}
/*----------------显示麦序界面--------------*/
-(void)showMicListWithDic:(NSDictionary *)dic{
    YBWeakSelf;
    if (_micListView) {
        [_micListView removeFromSuperview];
        _micListView = nil;
    }
    _micListView = [[MicListView alloc]initWithFrame:CGRectMake(0, _window_height, _window_width, _window_height)];
    _micListView.dataDic = dic;
    _micListView.hideEvent = ^{
        [UIView animateWithDuration:0.6 animations:^{
            weakSelf.micListView.frame = CGRectMake(0, _window_height, _window_width, _window_height);
                        } completion:^(BOOL finished) {
                            [weakSelf.micListView removeFromSuperview];
                            weakSelf.micListView = nil;

                        }];

    };
    _micListView.cancelEvent = ^{
        [weakSelf cancelVoiceLiveMicApply];
    };
    [self.view addSubview:_micListView];
    [UIView animateWithDuration:0.6 animations:^{
        weakSelf.micListView.frame = CGRectMake(0, 0, _window_width, _window_height);
    }];

}
/*----------------取消上麦--------------*/
-(void)cancelVoiceLiveMicApply{
    YBWeakSelf;
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.cancelVoiceLiveMicApply"];
    
    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":minstr([_playDoc valueForKey:@"stream"])
                          };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            [UIView animateWithDuration:0.6 animations:^{
                weakSelf.micListView.frame = CGRectMake(0, _window_height, _window_width, _window_height);
                            } completion:^(BOOL finished) {
                                [weakSelf.micListView removeFromSuperview];
                                weakSelf.micListView = nil;

                            }];
        }
        } Fail:^(id fail) {
        }];
}

#pragma mark--同意用户上麦
-(void)chatRoomAgreeUserUp:(NSDictionary *)dic
{
    NSDictionary *usseDic = @{@"uid":minstr([dic valueForKey:@"touid"]),@"user_nickname":minstr([dic valueForKey:@"toname"]),@"avatar":minstr([dic valueForKey:@"avatar"])};
    upMicIndex = [minstr([dic valueForKey:@"position"]) intValue];
    
    ChatVideoUserLinkView *userVideoView;
    if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
        YBUserLinkButton *btn = sitButtonArray [upMicIndex];
        btn.userDic = usseDic;
    }else{
        userVideoView =sitVideoButtonArray[upMicIndex];
        userVideoView.userDic = usseDic;
        [sitVideoButtonArray replaceObjectAtIndex:upMicIndex withObject:userVideoView];
    }
    if ([minstr([dic valueForKey:@"touid"]) isEqual:[Config getOwnID]]) {
        [MBProgressHUD showError:YZMsg(@"上麦成功")];
        //改变底部按钮
        isHaveUpMic = YES;
        [self setbtnframe];
        [self startPushStream];
        if([_sdkType isEqual:@"1"]){
            [self getVoiceLivePullStreams];
        }
    }else{
        if (isHaveUpMic) {
            if (![_sdkType isEqual:@"1"]) {
//                for (int i = 0; i < sitVideoButtonArray.count; i ++) {
//                    ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
//                    if (minstr([btn.userDic valueForKey:@"avatar"]).length > 0 &&![minstr([btn.userDic valueForKey:@"avatar"]) isEqual:@"0"]) {
//                        [[YBAgoraManager shareInstance]showWithCanvasView:btn.livePushView andUserId:minstr([btn.userDic valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience];
//                    }
//                }
                
                [[YBAgoraManager shareInstance]showWithCanvasView:userVideoView.livePushView andUserId:minstr([userVideoView.userDic valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
            }
        }else{
            if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
            }else{
                if ([_sdkType isEqual:@"1"]) {
                    _playVideoChatV.hidden = NO;
                    [self.txLivePlayer setRenderView:_playVideoChatV];
                    [self.txLivePlayer setRenderFillMode:V2TXLiveFillModeFill];
                }else{
//                    for (int i = 0; i < sitVideoButtonArray.count; i ++) {
//                        ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
//                        if (minstr([btn.userDic valueForKey:@"avatar"]).length > 0 &&![minstr([btn.userDic valueForKey:@"avatar"]) isEqual:@"0"]) {//
////                            btn.hidden = YES;
//                            [[YBAgoraManager shareInstance]showWithCanvasView:btn.livePushView andUserId:minstr([btn.userDic valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience];
//                        }
//                    }
                    [[YBAgoraManager shareInstance]showWithCanvasView:userVideoView.livePushView andUserId:minstr([userVideoView.userDic valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];

                }
            }
        }
    }
}
-(void)chatRoomCloseUserMic:(NSDictionary *)dic
{
    [self removeUserLinkMic:minstr([dic valueForKey:@"uid"])];
    if ([minstr([dic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
        if ([minstr([dic valueForKey:@"type"]) isEqual:@"1"]) {
            [MBProgressHUD showError:YZMsg(@"您已被下麦")];
        }
    }
}
//改变麦的状态
-(void)chatRoomUserMicBtnStatus:(NSDictionary *)dic
{
    int index = [minstr([dic valueForKey:@"position"]) intValue];
    NSString *status = minstr([dic valueForKey:@"status"]);
    
    if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
        YBUserLinkButton *btn = sitButtonArray [index];
        [btn changeLinkBtnStatus:status];
        if ([minstr([dic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
            if ([status isEqual:@"1"]){
    //            [_txLivePush setMute:NO];
                if ([_sdkType isEqual:@"1"]) {
                    [[YBLiveRTCManager shareInstance]resumeAudio];

                }else{
                    [[YBAgoraManager shareInstance]muteLocalAudioStream:NO];
                }
                isJingMai = NO;
                [closeMicBtn setBackgroundImage:[UIImage imageNamed:@"openmicBtn"] forState:UIControlStateNormal];
                [MBProgressHUD showError:YZMsg(@"已取消静音")];

            }else if ([status isEqual:@"-1"]){
                if ([_sdkType isEqual:@"1"]) {
                    [[YBLiveRTCManager shareInstance]pauseAudio];
                }else{
                    [[YBAgoraManager shareInstance]muteLocalAudioStream:YES];
                }
                isJingMai = YES;
                [closeMicBtn setBackgroundImage:[UIImage imageNamed:@"closemicBtn"] forState:UIControlStateNormal];
                [MBProgressHUD showError:YZMsg(@"已设置为静音")];

            }
        }

    }else{
        ChatVideoUserLinkView *btn = sitVideoButtonArray [index];
        [btn changeLinkBtnStatus:status];
        if ([minstr([dic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
            if ([status isEqual:@"1"]){
                if ([_sdkType isEqual:@"1"]) {
                    [[YBLiveRTCManager shareInstance]resumeAudio];
                }else{
                    [[YBAgoraManager shareInstance]muteLocalAudioStream:NO];
                }
                isJingMai = NO;
                [closeMicBtn setBackgroundImage:[UIImage imageNamed:@"openmicBtn"] forState:UIControlStateNormal];
                [MBProgressHUD showError:YZMsg(@"已取消静音")];

            }else if ([status isEqual:@"-1"]){
                if ([_sdkType isEqual:@"1"]) {
                    [[YBLiveRTCManager shareInstance]pauseAudio];
                }else{
                    [[YBAgoraManager shareInstance]muteLocalAudioStream:YES];
                }
                isJingMai = YES;
                [closeMicBtn setBackgroundImage:[UIImage imageNamed:@"closemicBtn"] forState:UIControlStateNormal];
                [MBProgressHUD showError:YZMsg(@"已设置为静音")];
            }
        }
    }
}
//用户被下麦
-(void)removeUserLinkMic:(NSString *)uid{

    if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
        for (int i = 0; i < sitButtonArray.count; i ++) {
            YBUserLinkButton *btn = sitButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:uid]) {
                btn.userDic = @{};
                [btn changeLinkBtnStatus:@"1"];
                if ([uid isEqual:[Config getOwnID]]) {
                    if ([_sdkType isEqual:@"1"]) {
                        [self stopPush];

                    }else{
                        AgoraRtcChannelMediaOptions *mediaOption = [[AgoraRtcChannelMediaOptions alloc]init];
                        mediaOption.clientRoleType = AgoraClientRoleAudience;
                        mediaOption.publishCameraTrack = NO;
                        mediaOption.publishMicrophoneTrack = NO;
                        [[YBAgoraManager shareInstance]updateChannel:minstr([_playDoc valueForKey:@"stream"]) options:mediaOption];
                    }
                }
            }
        }
    }else{
        for (int i = 0; i < sitVideoButtonArray.count; i ++) {
            ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:uid]) {
                NSMutableDictionary *tempDic = [NSMutableDictionary dictionaryWithDictionary:btn.userDic];
                [tempDic setValue:@"0" forKey:@"avatar"];
                [tempDic setValue:@"0" forKey:@"id"];
                [tempDic setValue:@"0" forKey:@"uid"];
                btn.userDic = tempDic;
                btn.hidden = NO;
                if ([uid isEqual:[Config getOwnID]]) {
                    [self stopPush];
                }else{
                    if (![_sdkType isEqual:@"1"]) {
                        [[YBAgoraManager shareInstance]removePlayView:btn WithUserId:uid];
                    }
                }
            }
        }
        int count = 1;
        for (int j = 0; j  < sitVideoButtonArray.count; j  ++) {
            ChatVideoUserLinkView *btn = sitVideoButtonArray [j];
            if ([[btn.userDic valueForKey:@"avatar"] isEqual:@"0"] && minstr([btn.userDic valueForKey:@"avatar"]).length > 1) {
                count++;
            }
        }
        if (count == 1) {
            ChatVideoUserLinkView *btn = sitVideoButtonArray [0];
            btn.hidden = NO;
            if ([_sdkType isEqual:@"1"]) {
                [self.txLivePlayer setRenderView:btn];
                NSString *playUrl = [self.playDoc valueForKey:@"pull"];
                [_txLivePlayer startLivePlay:playUrl];
                V2TXLiveCode liveCode = [_txLivePlayer startLivePlay:playUrl];
                NSLog(@"gethostlowurl---:%ld",liveCode);
            }else{
                //声网
            }

        }
    }
    if ([uid isEqual:[Config getOwnID]]) {
        isHaveUpMic = NO;
        [self setbtnframe];
    }
}
-(void)getVoiceLivePullStreams{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.getVoiceLivePullStreams"];
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":minstr([_playDoc valueForKey:@"stream"]),
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        
        if ([code isEqual:@"0"]) {
            NSArray *infoArr = [data valueForKey:@"info"];
            for (int i = 0; i < infoArr.count; i++) {
                [self playRtmpUrl:infoArr[i]];
            }
        }
        } Fail:^(id fail) {
    }];
}
/*播上麦用户流*/
-(void)playRtmpUrl:(NSDictionary *)dic
{
    if ([minstr([dic valueForKey:@"isanchor"]) isEqual:@"1"]) {
        NSString *liveUrl = minstr([dic valueForKey:@"pull"]);
        NSString * mainStreamId = [self getStreamIDByStreamUrl:liveUrl];

        NSString *url = [purl stringByAppendingFormat:@"?service=Live.getMicPullUrl"];
        NSDictionary *pardic = @{
                              @"uid":[Config getOwnID],
                              @"token":[Config getOwnToken],
                              @"stream":mainStreamId
                              };
        [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
            if ([code isEqual:@"0"]) {
                [self.txLivePlayer stopPlay];
                NSDictionary *pullDic =[[data valueForKey:@"info"]firstObject];
                NSString *playUrl = minstr([pullDic valueForKey:@"play_url"]);
                if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
                }else{
                    ChatVideoUserLinkView *userVideoView =sitVideoButtonArray[0];
                    userVideoView.hidden = NO;
                    [self.txLivePlayer setRenderView:userVideoView];
                }

                V2TXLiveCode result = [self.txLivePlayer startLivePlay:playUrl];
                NSLog(@"wangminxin%ld",result);
                if (result == -1)
                {
                    
                }
                if( result != 0)
                {
                    [_notification displayNotificationWithMessage:@"视频流播放失败" forDuration:5];
                    [self lastView];
                }
                if( result == 0){
                    NSLog(@"播放视频");
                }

            }else{
                [MBProgressHUD showError:msg];
            }
        } Fail:^(id fail) {
            [MBProgressHUD hideHUD];
        }];
    }else{
        NSString *uidStr = minstr([dic valueForKey:@"uid"]);
        if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
            for (int i = 0; i < sitButtonArray.count; i ++) {
                YBUserLinkButton *btn = sitButtonArray [i];
                if ([[btn.userDic valueForKey:@"uid"] isEqual:uidStr]) {
                    [self playRtmpDic:dic andIndex:i];
                }
            }
        }else{
            if([uidStr isEqual:[Config getOwnID]])
            {
            }else{
                if(isHaveUpMic){
                    for (int i = 0; i < sitVideoButtonArray.count; i ++) {
                        ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
                        if ([[btn.userDic valueForKey:@"uid"] isEqual:uidStr]) {
                            btn.hidden = NO;
                            [self playRtmpDic:dic andIndex:i];
                        }
                    }
                }

            }
        }
    }

}
-(NSString*) getStreamIDByStreamUrl:(NSString*) strStreamUrl {
    if (strStreamUrl == nil || strStreamUrl.length == 0) {
        return nil;
    }
    strStreamUrl = [strStreamUrl lowercaseString];
    NSString * strLive = @"/play/";
    NSRange range = [strStreamUrl rangeOfString:strLive];
    if (range.location == NSNotFound) {
        return nil;
    }
    NSString * strSubString = [strStreamUrl substringFromIndex:range.location + range.length];
    NSArray * array = [strSubString componentsSeparatedByCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"?."]];
    if ([array count] > 0) {
        return [array objectAtIndex:0];
    }
    return @"";
}

-(void)playRtmpDic:(NSDictionary *)dic andIndex:(int)index{
    V2TXLivePlayer *txLivePlayer =livePlayerArray[index];
    if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
        
    }else{
        for (int i = 0; i < sitVideoButtonArray.count; i ++) {
            ChatVideoUserLinkView *userVideoView =sitVideoButtonArray[i];
            if([[dic valueForKey:@"uid"]isEqual:minstr([userVideoView.userDic valueForKey:@"uid"])]){
//                ChatVideoUserLinkView *userVideoView =sitVideoButtonArray[index];
                userVideoView.hidden = NO;
                [txLivePlayer setRenderView:userVideoView];

            }
        }

    }
    NSString *rtmpUrl =minstr([dic valueForKey:@"pull"]);
    NSString *playUrl = rtmpUrl;
    NSInteger _playType = 0;
    if ([playUrl hasPrefix:@"rtmp:"]) {
        _playType = PLAY_TYPE_LIVE_RTMP;
    } else if (([playUrl hasPrefix:@"https:"] || [playUrl hasPrefix:@"http:"]) && [playUrl rangeOfString:@".flv"].length > 0) {
        _playType = PLAY_TYPE_LIVE_FLV;
        
    } else{
        [_notification displayNotificationWithMessage:@"播放地址不合法，直播目前仅支持rtmp,flv播放方式!" forDuration:5];
    }
    NSLog(@"============:%@",playUrl);
    //int result = [_txLivePlayer startPlay:playUrl type:PLAY_TYPE_LIVE_RTMP_ACC];//RTMP直播加速播放PLAY_TYPE_LIVE_RTMP_ACC
//    int result = [_txLivePlayer startLivePlay:playUrl type:PLAY_TYPE_LIVE_RTMP_ACC];
    V2TXLiveCode result = [txLivePlayer startLivePlay:playUrl];

    NSLog(@"play_linkMicwangminxin%ld",result);
    if (result == -1)
    {
        
    }
    if( result != 0)
    {
        [_notification displayNotificationWithMessage:@"视频流播放失败" forDuration:5];
    }
    if( result == 0){
        
        
    }

}
- (void)stopPlayRtmpIndex:(int)index{
    V2TXLivePlayer *_txLivePlayer = livePlayerArray[index];
    if(_txLivePlayer != nil)
    {
        [_txLivePlayer stopPlay];
    }
}

//播放上麦用户的流
-(void)startPlayPullStream:(NSDictionary *)dic
{
    if ([minstr([dic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
        return;
    }
    [self playRtmpUrl:dic];

}

//语音聊天室上麦用户获取推拉流地址
-(void)startPushStream{
    YBWeakSelf;
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.getVoiceMicStream"];
    
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":minstr([_playDoc valueForKey:@"stream"]),
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        
        if ([code isEqual:@"0"]) {
            NSDictionary *dic = [[data valueForKey:@"info"] firstObject];
            [weakSelf rtcPush:dic];
        }
        } Fail:^(id fail) {
    }];
}
//停止推流
-(void)stopPush{
//    [_txLivePush stopPreview];
//    [_txLivePush stopPush];
    if([_sdkType isEqual:@"1"]){
        [[YBLiveRTCManager shareInstance]stopPushIsGameLive:NO];
    }else{
        [[YBAgoraManager shareInstance]stopPushView];
        AgoraRtcChannelMediaOptions *mediaOption = [[AgoraRtcChannelMediaOptions alloc]init];
        mediaOption.clientRoleType = AgoraClientRoleAudience;
        mediaOption.publishCameraTrack = NO;
        mediaOption.publishMicrophoneTrack = NO;
        [[YBAgoraManager shareInstance]updateChannel:minstr([self.playDoc valueForKey:@"user_stream"]) options:mediaOption];

    }

}
-(void)setBgAndPreview {
    if (_pushPreview) {
        [_pushPreview removeFromSuperview];
        _pushPreview = nil;
    }
    _pushPreview = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    _pushPreview.backgroundColor = [UIColor clearColor];
    [self.view addSubview:_pushPreview];
    [self.view sendSubviewToBack:_pushPreview];
}
-(void)rtcPush:(NSDictionary*)dic{
    [self setBgAndPreview];
    _playurl = [NSString stringWithFormat:@"%@",[dic valueForKey:@"pull"]];
    _pushurl = [NSString stringWithFormat:@"%@",[dic valueForKey:@"push"]];
    _streamUrl = [NSString stringWithFormat:@"%@",[dic valueForKey:@"user_stream"]];
    //0 ---语音   1----语音视频
    if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
        if ([_sdkType isEqual:@"1"]) {
            [[YBLiveRTCManager shareInstance]initWithChatLiveMode:V2TXLiveMode_RTC];
            [[YBLiveRTCManager shareInstance]setPushView:_pushPreview];
            [YBLiveRTCManager shareInstance].delegate = self;
            [[YBLiveRTCManager shareInstance]startPush:_pushurl isGameLive:NO];
            [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
        }else{
            AgoraRtcChannelMediaOptions *mediaOption = [[AgoraRtcChannelMediaOptions alloc]init];
            mediaOption.clientRoleType = AgoraClientRoleBroadcaster;
            mediaOption.publishCameraTrack = YES;
            mediaOption.publishMicrophoneTrack = YES;
            [[YBAgoraManager shareInstance]updateChannel:minstr([dic valueForKey:@"user_stream"]) options:mediaOption];
        }
    }else{
        if ([_sdkType isEqual:@"1"]) {
            ChatVideoUserLinkView *userVideoView =sitVideoButtonArray[upMicIndex];
            [[YBLiveRTCManager shareInstance]initWithChatVideoLiveModel:V2TXLiveMode_RTC andPushData:@{}];
            [[YBLiveRTCManager shareInstance]setPushView:userVideoView];
            [YBLiveRTCManager shareInstance].delegate = self;
            [[YBLiveRTCManager shareInstance]startPush:_pushurl isGameLive:NO];
            [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
        }else{
            ChatVideoUserLinkView *userVideoView =sitVideoButtonArray[upMicIndex];
            [[YBAgoraManager shareInstance]showWithCanvasView:userVideoView.livePushView andUserId:[Config getOwnID] isBroadcaster:AgoraClientRoleBroadcaster RenderMode:AgoraVideoRenderModeHidden];
            AgoraRtcChannelMediaOptions *mediaOption = [[AgoraRtcChannelMediaOptions alloc]init];
            mediaOption.clientRoleType = AgoraClientRoleBroadcaster;
            mediaOption.publishCameraTrack = YES;
            mediaOption.publishMicrophoneTrack = YES;
            [[YBAgoraManager shareInstance]updateChannel:minstr([dic valueForKey:@"user_stream"]) options:mediaOption];

        }
    }
}
#pragma mark  --RTC推流回调
/**
 * 推流器连接状态回调通知
 *
 * @param status    推流器连接状态 {@link V2TXLivePushStatus}。
 * @param msg       连接状态信息。
 * @param extraInfo 扩展信息。
 */
-(void)ybRTCPushStatusUpdate:(V2TXLivePushStatus)status message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (status == V2TXLivePushStatusDisconnected) {
            /// 与服务器断开连接
            [_notification displayNotificationWithMessage:@"推流失败，结束连麦" forDuration:5];
//            [self hostStopRoom];

        }else if(status == V2TXLivePushStatusConnecting){
            /// 正在连接服务器
//             if (isPUSH_WARNING_RECONNECT) {
//                isPUSH_WARNING_RECONNECT = NO;
//                [self checkLiveingStatus];
//             }

        }else if(status == V2TXLivePushStatusConnectSuccess){
            /// 连接服务器成功
//            [self changePlayState:1];
            //3.拉取其它正在和大主播连麦的小主播的视频流
            [socketDelegate startSendStreamWithUid:[Config getOwnID] andUrl:_playurl andStream:_streamUrl];


        }else if(status == V2TXLivePushStatusConnectSuccess){
            ///  重连服务器中
            [_notification displayNotificationWithMessage:@"网络断连, 已启动自动重连" forDuration:5];
        }
    });
}
-(void)ybPushLiveStatus:(V2TXLiveCode)pushStatus
{
    if (pushStatus == V2TXLIVE_OK) {
        NSLog(@"LIVEBROADCAST --:推流成功、停止推流");
    }else if (pushStatus == V2TXLIVE_ERROR_INVALID_PARAMETER){
        [_notification displayNotificationWithMessage:@"操作失败，url 不合法" forDuration:5];
        NSLog(@"推流器启动失败");
    }else if (pushStatus == V2TXLIVE_ERROR_INVALID_LICENSE){
        [_notification displayNotificationWithMessage:@"操作失败，license 不合法，鉴权失败" forDuration:5];
        NSLog(@"推流器启动失败");
    }else if (pushStatus == V2TXLIVE_ERROR_REFUSED){
        [_notification displayNotificationWithMessage:@"操作失败，RTC 不支持同一设备上同时推拉同一个 StreamId" forDuration:5];
        NSLog(@"推流器启动失败");
    }else if (pushStatus == V2TXLIVE_WARNING_NETWORK_BUSY){
        [_notification displayNotificationWithMessage:
            @"您当前的网络环境不佳，请尽快更换网络保证正常直播" forDuration:5];
    }
}

//更多按钮
-(void)showmoreview{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (!_bottomView) {
        [self showmoreviews];
    }
    
    if (_bottomView.hidden == YES) {
        _bottomView.hidden = NO;
        [UIView animateWithDuration:0.4 animations:^{
            _bottomView.frame = CGRectMake(0,0, _window_width, _window_height);
        }];
        
    }else{
        [UIView animateWithDuration:0.4 animations:^{
            _bottomView.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
        }];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            _bottomView.hidden = YES;
        });
    }
}
-(void)showmoreviews{
    NSArray *moreArr = [NSArray array];
    if ([[YBYoungManager shareInstance]isOpenYoung]) {
        moreArr = @[@"分享"];
    }else{
//        if ([_dailytask_switch isEqual:@"0"]) {
//            moreArr =@[@"分享",@"幸运转盘",@"红包"];
//        }else{
//            moreArr = @[@"分享",@"幸运转盘",@"红包",@"每日任务"];
//        }
        moreArr = @[@"分享"];
        NSMutableArray *m_array = [NSMutableArray arrayWithArray:moreArr];
        if ([turntable_switch isEqual:@"1"]) {
            [m_array addObject:@"幸运抽奖"];
        }
        [m_array addObject:@"红包"];
        if ([_dailytask_switch isEqual:@"1"]) {
            [m_array addObject:@"每日任务"];
        }
        if (isHaveUpMic) {
            [m_array addObject:@"大表情"];
            [m_array addObject:@"私信"];
        }else{
            [m_array addObject:@"私信"];
        }
        [m_array addObject:@"举报"];
        moreArr = [NSArray arrayWithArray:m_array];
    }

    YBWeakSelf;
    _bottomView = [[ChatBottomView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) Music:^(NSString *type) {
        //因为和主播端公用 这个方法返回的是幸运大转盘
        [weakSelf doTurntable];
    } Share:^(NSString *type) {
        //分享
        [weakSelf doFenxiang];
    } Redbag:^(NSString *type) {
        //红包
        [weakSelf showRedView];

    } Task:^(NSString *type) {
        if (!_taskView) {
            _taskView = [[DayTaskView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)andLiveUid:[self.playDoc valueForKey:@"uid"]];
//            _taskView.liveUid =[self.playDoc valueForKey:@"uid"];
            _taskView.closeEvent = ^{
                [weakSelf.taskView removeFromSuperview];
                weakSelf.taskView = nil;
            };
            [self.view addSubview:_taskView];
        }
    } Jackpot:^(NSString *type) {
        //奖池
        [weakSelf showJackpotView];

    }BigEmo:^(NSString *type) {
        [weakSelf showEmotionView];
    } SMsg:^(NSString *type) {
        [weakSelf doMessage];
    } MsgCount:unReadCount UserLink:^(NSString *type) {
        
    }Hideself:^(NSString *type) {
        //隐藏
        [UIView animateWithDuration:0.4 animations:^{
            _bottomView.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
        }];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能"] forState:UIControlStateNormal];
            [_bottomView removeFromSuperview];
            _bottomView = nil;
        });

    }andDataArray:moreArr];//,@"幸运奖池"
    // 举报
    _bottomView.reportEvent = ^(NSString *type) {
        [weakSelf doReportAnchor:minstr([weakSelf.playDoc valueForKey:@"uid"])];
    };
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    [window addSubview:_bottomView];
    _bottomView.hidden = YES;
//    [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能_S"] forState:UIControlStateNormal];
    [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能"] forState:UIControlStateNormal];


}
-(void)toolbarHidden
{
    toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
    [UIView animateWithDuration:0.5 animations:^{
        tChatsamall.view.frame = CGRectMake(0, _window_height*3, _window_width, _window_height*0.4);
    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (tChatsamall) {
            [tChatsamall.view removeFromSuperview];
            tChatsamall.view = nil;
            tChatsamall = nil;
        }
    });
}
-(void)toolbarClick:(id)sender
{
    [keyField resignFirstResponder];
    toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
}
-(void)guanzhuZhuBolela{
    dispatch_async(dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:0.3 animations:^{
            setFrontV.leftView.frame = CGRectMake(10, 25+statusbarHeight, 95, leftW);
            listcollectionviewx = _window_width+105;
            listView.frame = CGRectMake(listcollectionviewx, 20+statusbarHeight, _window_width-150-60,40);
            listView.listCollectionview.frame = CGRectMake(0, 0, _window_width-150-60, 40);
        }];
        setFrontV.newattention.hidden = YES;
        [socketDelegate attentionLive:level];

    });
}
//ray-声网
//- (void) addObservers {
//    //播放器播放完成
//    [[NSNotificationCenter defaultCenter]addObserver:self
//                                            selector:@selector(handlePlayerNotify:)
//                                                name:(MPMoviePlayerPlaybackDidFinishNotification)
//                                              object:nil];
//    
//}
#pragma mark - 连麦鉴权信息
-(void)reloadChongzhi:(NSString *)coin{
    if (giftview) {
    [giftview chongzhiV:coin];
    }
}
#pragma mark ---- 私信方法
-(void)getUnreadCount{
    [self labeiHid];
}
- (void)onTotalUnreadMessageCountChanged:(UInt64)totalUnreadCount {
    [self labeiHid];
}
-(void)labeiHid{
    __block NSInteger unRead = 0;
    [[YBImManager shareInstance]getAllUnredNumExceptUser:@[@"dsp_fans",@"dsp_like",@"dsp_at",@"dsp_comment"] complete:^(int allUnread) {
        unRead = allUnread;
        dispatch_async(dispatch_get_main_queue(), ^{
            unReadCount= [NSString stringWithFormat:@"%ld",unRead];
            if(_bottomView){
                [_bottomView reloadUnreadCount:unReadCount];
            }

//            if ([self.unReadLabel.text isEqual:@"0"] || unRead <= 0) {
//                self.unReadLabel.hidden =YES;
//            }else {
//                self.unReadLabel.hidden = NO;
//            }
        });

    }];
}
-(void)Registnsnotifition{
    //点击用户聊天
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(changePlayRoom:) name:@"changePlayRoom" object:nil];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(forsixin:) name:@"sixinok" object:nil];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(getweidulabel) name:@"gengxinweidu" object:nil];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(shajincheng) name:@"shajincheng" object:nil];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(reloadLiveplayAttion:) name:@"reloadLiveplayAttion" object:nil];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(loginEnterRoom) name:@"loginEnter" object:nil];
    //获取所有未读消息
    [[V2TIMManager sharedInstance] addConversationListener:self];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(sendXQTBResult:) name:@"sendXQTBResult" object:nil];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(sendXYDZPResult:) name:@"sendXYDZPResult" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(gameDoLiwu) name:@"iyz_show_bag_noti" object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(getChatRefresh:) name:ybChatRefresh object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(userLoginDestroyRoom) name:ybUserDestroyRoom object:nil];
}
-(void)userLoginDestroyRoom {
    [self dissmissVC];
}
-(void)getChatRefresh:(NSNotification*)noti {
    NSDictionary *notiDic = noti.userInfo;
    NSString *add_time = minstr([notiDic valueForKey:@"add_time"]);
    NSArray *curArray = [NSArray arrayWithArray:msgList];
    BOOL have = NO;
    for (int i = 0; i<curArray.count; i++) {
        NSDictionary *subDic = curArray[i];
        if ([minstr([subDic valueForKey:@"add_time"]) isEqual:add_time]) {
            have = YES;
            NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:subDic];
            [m_dic setObject:@"0" forKey:@"is_first"];
            [msgList replaceObjectAtIndex:i withObject:m_dic.mutableCopy];
        }
    }
    if (have) {
        NSLog(@"======图片加载......refresh");
        [_tableView reloadData];
    }
}
-(void)gameDoLiwu{
    isGameToGift = YES;
    [self doLiwu];
    isGameToGift = NO;
}
-(void)loginEnterRoom{
    if(self.txLivePlayer){
        [self.txLivePlayer stopPlay];
        self.txLivePlayer = nil;
    }
}

-(void)getweidulabel{
    [self labeiHid];
}
//跳往消息列表
-(void)doMessage{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    [tChatsamall.view removeFromSuperview];
    tChatsamall = nil;
    tChatsamall.view = nil;
    [huanxinviews.view removeFromSuperview];
    huanxinviews = nil;
    huanxinviews.view = nil;
    if (!huanxinviews) {
        huanxinviews = [[huanxinsixinview alloc]init];
        huanxinviews.zhuboID = minstr([_playDoc valueForKey:@"uid"]);
        [huanxinviews forMessage];
        huanxinviews.view.frame = CGRectMake(0, _window_height*3, _window_width, _window_height*0.4);
        [self.view insertSubview:huanxinviews.view atIndex:9];
        if (liansongliwubottomview) {
            [backScrollView insertSubview:liansongliwubottomview atIndex:8];
        }
    }
    [UIView animateWithDuration:0.2 animations:^{
        huanxinviews.view.frame = CGRectMake(0, _window_height - _window_height*0.4,_window_width, _window_height*0.4);
    }];
}
//点击用户聊天
-(void)forsixin:(NSNotification *)ns{
    NSMutableDictionary *dic = [[ns userInfo] mutableCopy];
    if (sysView.view) {
        [sysView.view removeFromSuperview];
        sysView = nil;
        sysView.view = nil;

    }
    __weak UserRoomViewController *wSelf = self;

    if ([[dic valueForKey:@"id"] isEqual:@"1"]) {
        sysView = [[MsgSysVC alloc]init];
        sysView.view.frame = CGRectMake(_window_width, _window_height-_window_height*0.4, _window_width, _window_height*0.4);
        sysView.block = ^(int type) {
            if (type == 0) {
                [wSelf hideSysTemView];
            }
        };
        [sysView reloadSystemView];
        
        [self.view insertSubview:sysView.view atIndex:10];
        if (liansongliwubottomview) {
            [backScrollView insertSubview:liansongliwubottomview atIndex:8];
        }
        [UIView animateWithDuration:0.5 animations:^{
            sysView.view.frame = CGRectMake(0, _window_height-_window_height*0.4, _window_width, _window_height*0.4);
        }];
        return;
    }
    if([[dic valueForKey:@"id"] isEqual:@"goodsorder_admin"]){
        if ([[YBYoungManager shareInstance]isOpenYoung]) {
            [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
            return;
        }

        OrderMessageVC *order = [[OrderMessageVC alloc]init];
        [[MXBADelegate sharedAppDelegate]pushViewController:order animated:YES];
        return;

    }
    [tChatsamall.view removeFromSuperview];
    tChatsamall = nil;
    tChatsamall.view = nil;
    
    if (!tChatsamall) {
        tChatsamall = [[TChatC2CController alloc]init];
        [dic setObject:minstr([dic valueForKey:@"name"]) forKey:@"user_nickname"];
        TConversationCellData *conv =[dic valueForKey:@"conversation"];
        tChatsamall.conversation = conv;
        tChatsamall.block = ^(int type) {
            if (type == 0) {
                [wSelf hideChatMall];
            }
            if (type == 1) {
                if ([conv.convId isEqual:minstr([wSelf.playDoc valueForKey:@"uid"])]) {
                    [wSelf isAttentionLive:@"1"];
                }
            }

        };
        [tChatsamall reloadSamllChtaView:@"0"];

        [self.view insertSubview:tChatsamall.view atIndex:10];
        if (liansongliwubottomview) {
            [backScrollView insertSubview:liansongliwubottomview atIndex:8];
        }
    }
    tChatsamall.view.hidden = NO;
}
- (void)hideSysTemView{
    [sysView.view removeFromSuperview];
    sysView = nil;
    sysView.view = nil;

}
-(void)siXin:(NSString *)icon andName:(NSString *)name andID:(NSString *)ID andIsatt:(NSString *)isatt{
    [tChatsamall.view removeFromSuperview];
    tChatsamall = nil;
    tChatsamall.view = nil;
    [huanxinviews.view removeFromSuperview];
    huanxinviews = nil;
    huanxinviews.view = nil;
    YBWeakSelf;
    if (!tChatsamall) {
        tChatsamall = [[TChatC2CController alloc]init];
        TConversationCellData *data = [[TConversationCellData alloc] init];
        data.convId = minstr(ID);
        data.convType = TConv_Type_C2C;
        data.userHeader = minstr(icon);
        data.userName = minstr(name);
        tChatsamall.conversation = data;

        tChatsamall.block = ^(int type) {
            if (type == 0) {
                [weakSelf hideChatMall];
            }
//            if (type == 1) {
//                if ([data.convId isEqual:minstr([wSelf.playDoc valueForKey:@"uid"])]) {
//                    [weakSelf isAttentionLive:@"1"];
//                }
//            }
        };
        [tChatsamall reloadSamllChtaView:@"0"];

        [self.view insertSubview:tChatsamall.view atIndex:10];
        if (liansongliwubottomview) {
            [backScrollView insertSubview:liansongliwubottomview atIndex:8];
        }
    }
    tChatsamall.view.hidden = NO;
}
- (void)hideChatMall{
    if (huanxinviews) {
        [huanxinviews forMessage];
        CATransition *transition = [CATransition animation];    //创建动画效果类
        transition.duration = 0.3; //设置动画时长
        transition.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];  //设置动画淡入淡出的效果
        transition.type = kCATransitionPush;//{kCATransitionMoveIn, kCATransitionPush, kCATransitionReveal, kCATransitionFade};设置动画类型，移入，推出等
        //更多私有{@"cube",@"suckEffect",@"oglFlip",@"rippleEffect",@"pageCurl",@"pageUnCurl",@"cameraIrisHollowOpen",@"cameraIrisHollowClose"};
        transition.subtype = kCATransitionFromLeft;//{kCATransitionFromLeft, kCATransitionFromRight, kCATransitionFromTop, kCATransitionFromBottom};
        [tChatsamall.view.layer addAnimation:transition forKey:nil];       //在图层增加动画效果
        [tChatsamall.view removeFromSuperview];
        tChatsamall.view = nil;
        tChatsamall = nil;

    }else{
        [UIView animateWithDuration:0.3 animations:^{
            tChatsamall.view.frame = CGRectMake(0, _window_height, _window_width, _window_height*0.4);
        } completion:^(BOOL finished) {
            [tChatsamall.view removeFromSuperview];
            tChatsamall.view = nil;
            tChatsamall = nil;
        }];
    }
}
-(void)pushZhuYe:(NSString *)IDS{
    PersonHomeVC  *person = [[PersonHomeVC alloc]init];
     person.userID = IDS;
    [[MXBADelegate sharedAppDelegate]pushViewController:person animated:YES];

}
-(void)sendAtMsgClick:(NSString *)nameStr
{
    if (tChatsamall) {
        tChatsamall.view.hidden = YES;
        [tChatsamall.view removeFromSuperview];
        tChatsamall.view = nil;
        tChatsamall = nil;
    }
    [keyField becomeFirstResponder];
    keyField.text = [NSString stringWithFormat:@"@%@ ",nameStr];
}
#pragma mark -- 获取键盘高度
- (void)keyboardWillShow:(NSNotification *)aNotification
{
    if (redBview) {
        return;
    }
    if ([md5AlertController.textFields.firstObject becomeFirstResponder]) {
        return;
    }
    if (tChatsamall) {
        return;
    }
    if (gameVC) {
        gameVC.hidden = YES;
    }
    if (rotationV) {
        rotationV.hidden = YES;
    }

    [self hideBTN];
    
    keyBTN.hidden = YES;
    //获取键盘的高度
    NSDictionary *userInfo = [aNotification userInfo];
    NSValue *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardRect = [aValue CGRectValue];
    CGFloat height = keyboardRect.origin.y;
    CGFloat heightw = keyboardRect.size.height;
    int newHeight = _window_height - height -44;
//    [UIView animateWithDuration:0.3 animations:^{
        [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 40 - heightw];
        toolBar.frame = CGRectMake(0,height-44,_window_width,44);
        listView.frame = CGRectMake(listcollectionviewx,-height,_window_width-200-60,40);
        listView.listCollectionview.frame = CGRectMake(0, 0, _window_width-200-60, 40);
        setFrontV.frame = CGRectMake(_window_width,-newHeight,_window_width,_window_height);
        [self changeGiftViewFrameY:_window_height*10];
        //wangminxinliwu
        [self changecontinuegiftframe];
        if (zhuangVC) {
            zhuangVC.frame =  CGRectMake(_window_width + 10,20, _window_width/4, _window_width/4 + 20 + _window_width/8);
        }

//    }];
}
- (void)keyboardWillHide:(NSNotification *)aNotification
{
    if (gameVC) {
        gameVC.hidden = NO;
    }
    if (rotationV) {
        rotationV.hidden = NO;
    }

    
    [UIView animateWithDuration:0.1 animations:^{
        setFrontV.frame = CGRectMake(_window_width,0,_window_width,_window_height);
        listView.frame = CGRectMake(listcollectionviewx, 20+statusbarHeight, _window_width-200-60,40);
        listView.listCollectionview.frame = CGRectMake(0, 0, _window_width-200-60, 40);
        if (giftViewShow) {
            [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2- 265];
        }
        else if (rotationV){
            [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - _window_width + _window_width/5];
        }
        else{
            [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 50 - ShowDiff];
        }

        //wangminxinliwu
        [self changecontinuegiftframe];
        toolBar.frame = CGRectMake(0,_window_height + 10,_window_width,44);
        [self changeGiftViewFrameY:_window_height*3];
    }];
    if (zhuangVC) {
        zhuangVC.frame =  CGRectMake(_window_width + 10,90, _window_width/4, _window_width/4 + 20 + _window_width/8);
    }
    [self showBTN];
    keyBTN.hidden = NO;
}
-(void)hideBTN{
//    _returnCancle.hidden = YES;
    _liwuBTN.hidden = YES;
//    _fenxiangBTN.hidden = YES;
    waitUpMicBtn.hidden = YES;
    if (isHaveUpMic) {
//        emoticonBtn.hidden = YES;
        closeMicBtn.hidden = YES;
    }
    _firstChargeBtn.hidden = YES;
    _gameBtn.hidden = YES;
    moreBTN.hidden = YES;
    _messageBTN.hidden = YES;
    keyBTN.hidden = YES;
    redBagBtn.hidden = YES;
//    goodsShowBtn.hidden = YES;

    _cycleScroll.hidden = YES;
    sysPageControl.hidden = YES;
}
//按钮出现
-(void)showBTN{
//    _returnCancle.hidden = NO;
    _liwuBTN.hidden = NO;
//    _fenxiangBTN.hidden = NO;
    waitUpMicBtn.hidden = NO;
    if (isHaveUpMic) {
//        emoticonBtn.hidden = NO;
        closeMicBtn.hidden = NO;
    }
    _firstChargeBtn.hidden = NO;
    _gameBtn.hidden = NO;
    moreBTN.hidden = NO;
    _messageBTN.hidden = NO;
    keyBTN.hidden = NO;
    redBagBtn.hidden = NO;
//    goodsShowBtn.hidden = NO;
    
    _cycleScroll.hidden = NO;
    sysPageControl.hidden = NO;

}
//发送消息
-(void)sendBarrage
{
    //屏蔽词过滤
    NSString *sendMsg = keyField.text;
    for (NSString *str in self.chatWordArr) {
        if ([sendMsg containsString:str]) {
           sendMsg =  [sendMsg  stringByReplacingOccurrencesOfString:str withString:@"***"];
        }
    }

    [socketDelegate sendBarrageID:[self.playDoc valueForKey:@"uid"] andTEst:sendMsg andDic:self.playDoc and:^(id arrays) {
        NSArray *data = [arrays valueForKey:@"data"];
        NSNumber *code = [data valueForKey:@"code"];
        if([code isEqualToNumber:[NSNumber numberWithInt:0]])
        {
            level = [[[data valueForKey:@"info"] firstObject] valueForKey:@"level"];
            [socketDelegate sendBarrage:level andmessage:[[[data valueForKey:@"info"] firstObject] valueForKey:@"barragetoken"]];
            //刷新本地魅力值
            LiveUser *liveUser = [Config myProfile];
            keyField.text = @"";
            liveUser.coin = [NSString stringWithFormat:@"%@",[[[data valueForKey:@"info"] firstObject] valueForKey:@"coin"]];
            liveUser.level = level;
            [Config updateProfile:liveUser];
            if (gameVC) {
                [gameVC reloadcoins];
            }
            if (rotationV) {
                [rotationV reloadcoins];
            }

            if (giftview) {
                [giftview chongzhiV:[NSString stringWithFormat:@"%@",liveUser.coin]];
            }
        }
        else
        {
            [MBProgressHUD showError:[data valueForKey:@"msg"]];
//            giftview.continuBTN.hidden = YES;
        }
    }];
}
-(void)pushMessage:(UITextField *)sender{
    if (keyField.text.length >50) {
        [MBProgressHUD showError:YZMsg(@"字数最多50字")];
        return;
    }
    pushBTN.enabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        pushBTN.enabled = YES;
    });
    NSCharacterSet *set = [NSCharacterSet whitespaceAndNewlineCharacterSet];
    NSString *trimedString = [keyField.text stringByTrimmingCharactersInSet:set];
    if ([trimedString length] == 0) {
        return ;
    }
    NSString *userLevel = [Config getLevel];
    if(cs.state == YES)//发送
    {
        if (keyField.text.length <=0) {
            return;
        }
        if ([[YBYoungManager shareInstance]isOpenYoung]) {
            [MBProgressHUD showError:YZMsg(@"青少年模式下不支持该功能")];
            return;
        }

        if ([userLevel intValue] < [barrage_limit intValue]) {
            [MBProgressHUD showError:[NSString stringWithFormat:@"发送弹幕,需要到达%@级",barrage_limit]];
            return;
        }
        [self sendBarrage];
        keyField.text =nil;
        pushBTN.selected = NO;
        return;
    }
    else{
        if ([userLevel intValue] < [speak_limit intValue]) {
            [MBProgressHUD showError:[NSString stringWithFormat:@"发言,需要到达%@级",speak_limit]];
            return;
        }

        titleColor = @"0";
        self.content = keyField.text;
        
        //屏蔽词过滤
        for (NSString *str in self.chatWordArr) {
            if ([self.content containsString:str]) {
               self.content =  [self.content  stringByReplacingOccurrencesOfString:str withString:@"***"];
            }
        }

        [socketDelegate sendmessage:self.content andENText:self.content andLevel:[Config getLevel] andUsertype:usertype andGuardType:minstr([guardInfo valueForKey:@"type"])];
        keyField.text =nil;
        pushBTN.selected = NO;
    }
}
//聊天输入框
-(void)showkeyboard:(UIButton *)sender{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    if (tChatsamall) {
        tChatsamall.view.hidden = YES;
        [tChatsamall.view removeFromSuperview];
        tChatsamall.view = nil;
        tChatsamall = nil;
    }
    [keyField becomeFirstResponder];
}
// 以下是 tableview的方法
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return UITableViewAutomaticDimension;
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return self.chatModels.count;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return 1;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    chatMsgCell *cell = [tableView dequeueReusableCellWithIdentifier:@"chatMsgCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"chatMsgCell" owner:nil options:nil] lastObject];
    }
    
    chatModel *models =self.chatModels[indexPath.section];
    for (NSString *str in self.chatWordArr) {
        if ([models.contentChat containsString:str]&& ![models.titleColor isEqual:@"firstlogin"]) {
           models.contentChat =  [models.contentChat  stringByReplacingOccurrencesOfString:str withString:@"***"];
        }
    }

    cell.model =models;
    
    return cell;
}
- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 5;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 5)];
    view.backgroundColor = [UIColor clearColor];
    return view;
}
-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    chatModel *model = self.chatModels[indexPath.section];
    [keyField resignFirstResponder];
    NSString *IsUser = [NSString stringWithFormat:@"%@",model.userID];
    if (IsUser.length > 1) {
//        if (![model.userID isEqual:[Config getOwnID]]) {
//            [self sendAtMsgClick:model.userName];
            NSDictionary *subdic = @{@"id":model.userID};
            [self GetInformessage:subdic];

//        }
    }
//    toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
}

#pragma mark ---scrollviewDelegate-------
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView{
    _canScrollToBottom = NO;
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    if (scrollView == buttomscrollview) {
        static float newX;
        newX = buttomscrollview.contentOffset.y;
        NSLog(@"currentIndex=====%ld-------------------%.2f",_scrollindex,newX);
        if (newX<0) {
            return;
        }
        
        if (_scrollindex == newX/_window_height) {
            //        [wmPlayer play];
            return;
        }else{
            _scrollindex = newX/_window_height;
        }
        buttomimageviews = scrollImgArr[_scrollindex];
        [self showAndHideImgView];
        _playDoc = _scrollarray[_scrollindex];
        [self changeRoom:_playDoc];

    }

}
- (void)showAndHideImgView{
    for (UIImageView *imgV in scrollImgArr) {
        if (imgV != buttomimageviews) {
            imgV.hidden = NO;
        }
    }
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    if (scrollView == backScrollView) {
        if (backScrollView.contentOffset.x == 0) {
            _danmuView.hidden = YES;
        }
        else{
            _danmuView.hidden = NO;
        }
        [keyField resignFirstResponder];
        toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
        [self showBTN];
        keyBTN.hidden = NO;
    }
    if (scrollView == buttomscrollview) {
//        videoviewbottom.frame = CGRectMake(0, 0, _window_width, _window_height);
        static float newY;
        newY = buttomscrollview.contentOffset.y;
        if (_scrollindex > 0) {
            if (newY > _scrollindex*_window_height) {
                if (_scrollindex+1<scrollImgArr.count) {
                    UIImageView *imageView = scrollImgArr[_scrollindex+1];
                    if (!imageView.image) {
                        [imageView sd_setImageWithURL:[NSURL URLWithString:minstr([_scrollarray[_scrollindex+1] valueForKey:@"avatar"])] placeholderImage:[UIImage imageNamed:@"icon_avatar_placeholder"]];
                    }
                }
            }else{
                if (_scrollindex-1<scrollImgArr.count) {
                    UIImageView *imageView = scrollImgArr[_scrollindex-1];
                    if (!imageView.image) {
                        [imageView sd_setImageWithURL:[NSURL URLWithString:minstr([_scrollarray[_scrollindex-1] valueForKey:@"avatar"])] placeholderImage:[UIImage imageNamed:@"icon_avatar_placeholder"]];
                    }
                }
            }
        }
        NSLog(@"currentIndex=====%.2f-------------------%.2f",scrollView.contentSize.height,newY);
//        videoviewbottom.frame = CGRectMake(0, _window_height*_scrollindex-newY, _window_width, _window_height);

    }

}
/*************   以上socket.io 监听  *********/
//直播结束跳到此页面
-(void)lastView{
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"isPlaying"];

    [timecoast invalidate];
    timecoast = nil;
    [Feedeductionalertc dismissViewControllerAnimated:YES completion:nil];
    if (smallShowAlert) {
        [smallShowAlert dismissViewControllerAnimated:YES completion:nil];
    }
    [self removetimer];

    [buttleView removeFromSuperview];
    buttleView = nil;
    [self releaseall];
    if (haslianmai) {
        if ([_sdkType isEqual:@"1"]) {
            [self txStopLinkMic];
        }else{
            [socketDelegate closeConnect];
        }
    }
    [haohualiwuV stopHaoHUaLiwu];
    [platliwuV stopHaoHUaLiwu];
    [self onStopVideo];
    haohualiwuV.expensiveGiftCount = nil;
    platliwuV.expensiveGiftCount = nil;
    [continueGifts stopTimerAndArray];
    continueGifts = nil;
    if (_paintedShowRegion) {
        [_paintedShowRegion destroyPaitend];
    }

    if(_bottomView){
        [_bottomView removeFromSuperview];
        _bottomView = nil;
    }

    [haohualiwuV removeFromSuperview];
    [platliwuV removeFromSuperview];
    [tChatsamall.view removeFromSuperview];
    [huanxinviews.view removeFromSuperview];
    [setFrontV removeFromSuperview];
    [listView removeFromSuperview];
    listView = nil;
    
    [self requestLiveAllTimeandVotes];
//    lastv = [[lastview alloc]initWithFrame:self.view.bounds block:^(NSString *nulls) {
//        [self dismissViewControllerAnimated:YES completion:nil];
//        [self.navigationController popViewControllerAnimated:YES];
//    } andavatar:[NSString stringWithFormat:@"%@",[self.playDoc valueForKey:@"avatar"]]];
//    [self.view addSubview:lastv];
}
#pragma mark ================ 直播结束 ===============
- (void)requestLiveAllTimeandVotes{
    [socketDelegate socketStop];
    NSString *url = [NSString stringWithFormat:@"Live.stopInfo&stream=%@",minstr([_playDoc valueForKey:@"stream"])];
    [YBToolClass postNetworkWithUrl:url andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *subdic = [info firstObject];
            [self lastview:subdic];
        }else{
            [self lastview:nil];
        }
    } fail:^{
        [self lastview:nil];
    }];
    
}
-(void)lastview:(NSDictionary *)dic{
    //无数据都显示0
    if (!dic) {
        dic = @{@"votes":@"0",@"nums":@"0",@"length":@"0"};
    }
    UIImageView *lastView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    lastView.userInteractionEnabled = YES;
    [lastView sd_setImageWithURL:[NSURL URLWithString:[Config getavatar]]];
    UIBlurEffect *blur = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
    UIVisualEffectView *effectview = [[UIVisualEffectView alloc] initWithEffect:blur];
    effectview.frame = CGRectMake(0, 0,_window_width,_window_height);
    [lastView addSubview:effectview];
    
    
    UILabel *labell= [[UILabel alloc]initWithFrame:CGRectMake(0,24+statusbarHeight, _window_width, _window_height*0.17)];
    labell.textColor = normalColors;
    labell.text = YZMsg(@"直播已结束");
    labell.textAlignment = NSTextAlignmentCenter;
    labell.font = [UIFont fontWithName:@"Helvetica-Bold" size:20];
    [lastView addSubview:labell];
    
    UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(_window_width*0.1, labell.bottom+50, _window_width*0.8, _window_width*0.8*8/13)];
    backView.backgroundColor = RGB_COLOR(@"#000000", 0.2);
    backView.layer.cornerRadius = 5.0;
    backView.layer.masksToBounds = YES;
    [lastView addSubview:backView];
    
    UIImageView *headerImgView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width/2-50, labell.bottom, 100, 100)];
    [headerImgView sd_setImageWithURL:[NSURL URLWithString:minstr([_playDoc valueForKey:@"avatar"])] placeholderImage:[UIImage imageNamed:@"icon_avatar_placeholder"]];
    headerImgView.layer.masksToBounds = YES;
    headerImgView.layer.cornerRadius = 50;
    [lastView addSubview:headerImgView];
    
    
    UILabel *nameL= [[UILabel alloc]initWithFrame:CGRectMake(0,50, backView.width, backView.height*0.55-50)];
    nameL.textColor = [UIColor whiteColor];
    nameL.text = minstr([_playDoc valueForKey:@"user_nickname"]);
    nameL.textAlignment = NSTextAlignmentCenter;
    nameL.font = [UIFont fontWithName:@"Helvetica-Bold" size:18];
    [backView addSubview:nameL];
    
    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(10, nameL.bottom, backView.width-20, 1) andColor:RGB_COLOR(@"#585452", 1) andView:backView];
    
    NSArray *labelArray = @[YZMsg(@"直播时长"),[NSString stringWithFormat:@"%@",YZMsg(@"收获")],YZMsg(@"观看人数")];
    for (int i = 0; i < labelArray.count; i++) {
        UILabel *topLabel = [[UILabel alloc]initWithFrame:CGRectMake(i*backView.width/3, nameL.bottom, backView.width/3, backView.height/4)];
        topLabel.font = [UIFont boldSystemFontOfSize:18];
        topLabel.textColor = [UIColor whiteColor];
        topLabel.textAlignment = NSTextAlignmentCenter;
        topLabel.adjustsFontSizeToFitWidth = YES;
        topLabel.numberOfLines = 0;
        if (i == 0) {
            topLabel.text = minstr([dic valueForKey:@"length"]);
        }
        if (i == 1) {
            topLabel.text = minstr([dic valueForKey:@"votes"]);
        }
        if (i == 2) {
            topLabel.text = minstr([dic valueForKey:@"nums"]);
        }
        [backView addSubview:topLabel];
        UILabel *footLabel = [[UILabel alloc]initWithFrame:CGRectMake(topLabel.left, topLabel.bottom, topLabel.width, 14)];
        footLabel.font = [UIFont systemFontOfSize:13];
        footLabel.textColor = RGB_COLOR(@"#cacbcc", 1);
        footLabel.textAlignment = NSTextAlignmentCenter;
        footLabel.text = labelArray[i];
        [backView addSubview:footLabel];
    }
    
    
    
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.frame = CGRectMake(_window_width*0.1,_window_height *0.75, _window_width*0.8,50);
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [button addTarget:self action:@selector(docancle) forControlEvents:UIControlEventTouchUpInside];
//    [button setBackgroundColor:normalColors];
    [button setBackgroundImage:[UIImage imageNamed:@"startLive_back"]];
    [button setTitle:YZMsg(@"返回首页") forState:0];
    button.titleLabel.font = [UIFont systemFontOfSize:15];
    button.layer.cornerRadius = 25;
    button.layer.masksToBounds  =YES;
    [lastView addSubview:button];
    [self.view addSubview:lastView];
    
    
    
}
- (void)docancle{
    if (self.endEvent) {
        self.endEvent();
    }
    [self dismissViewControllerAnimated:YES completion:nil];
    [self.navigationController popViewControllerAnimated:YES];
}

//注销计时器
-(void)removetimer{
    
    [starMove invalidate];
    starMove = nil;
    [listTimer invalidate];
    listTimer = nil;
    [lianmaitimer invalidate];
    lianmaitimer = nil;
    [timecoast invalidate];
    timecoast = nil;
    if (startLinkTimer) {
        [startLinkTimer invalidate];
        startLinkTimer = nil;
    }

    
}
-(void)releaseall{
    if([_sdkType isEqual:@"1"]){
        for (int i = 0; i < livePlayerArray.count; i ++) {
            V2TXLivePlayer *_txLivePlayer = livePlayerArray[i];
            if(_txLivePlayer != nil)
            {
                [_txLivePlayer stopPlay];
                _txLivePlayer = nil;
            }
        }
    }else{
        if (isHaveUpMic) {
            [[YBAgoraManager shareInstance] stopPushView];
        }
        [[YBAgoraManager shareInstance]leaveChannel:nil];
    }
        
    [Feedeductionalertc dismissViewControllerAnimated:YES completion:nil];
    [self removetimer];
    if (haslianmai == YES) {
        if ([_sdkType isEqual:@"1"]) {
            [self txStopLinkMic];
        }else{
            [socketDelegate closeConnect];
        }
    }
    if (zhuangVC) {
        [zhuangVC dismissroom];
        [zhuangVC removeall];
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (gameVC) {
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
    }
    if (rotationV) {
        [rotationV stopRotatipnGameInt];
         [rotationV stoplasttimer];
        [rotationV removeFromSuperview];
        [rotationV removeall];
        rotationV = nil;
    }

    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    haohualiwuV.expensiveGiftCount = nil;
    platliwuV.expensiveGiftCount = nil;
    if (continueGifts) {
        [continueGifts stopTimerAndArray];
        continueGifts = nil;
    }
    if (_paintedShowRegion) {
        [_paintedShowRegion destroyPaitend];
    }

    [self onStopVideo];
    [self releaseObservers];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [socketDelegate socketStop];
        socketDelegate = nil;
    });
}
//直播结束时退出房间
-(void)dissmissVC{
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"isPlaying"];

    [self removetimer];
    [buttleView removeFromSuperview];
    buttleView = nil;
    self.tableView.hidden = YES;
    [self releaseall];
    
    [self dismissViewControllerAnimated:YES completion:nil];
    if ([_isJpush isEqual:@"1"]) {
        [self.navigationController popToRootViewControllerAnimated:YES];
    }else{
        [self.navigationController popViewControllerAnimated:YES];
    }
}
-(void)changeVideoUserHidde:(BOOL)isHidden{
    for (int i = 0; i < sitVideoButtonArray.count; i ++) {
        ChatVideoUserLinkView *userVideoView =sitVideoButtonArray[i];
        if(i != 0){
            userVideoView.hidden = isHidden;
        }
    }

}
//获取进入直播间所需要的所有信息全都在这个enterroom这个接口返回
-(void)getNodeJSInfo
{
    socketDelegate = [[socketMovieplay alloc]init];
    socketDelegate.socketDelegate = self;
    [socketDelegate setnodejszhuboDic:self.playDoc Handler:^(id arrays) {
        
        NSMutableArray *info = [[arrays valueForKey:@"info"] firstObject];
        guardInfo = [info valueForKey:@"guard"];
        speak_limit = minstr([info valueForKey:@"speak_limit"]);
        barrage_limit = minstr([info valueForKey:@"barrage_limit"]);
        jackpot_level = minstr([info valueForKey:@"jackpot_level"]);
        turntable_switch = minstr([info valueForKey:@"turntable_switch"]);
        _dailytask_switch = minstr([info valueForKey:@"dailytask_switch"]);
        game_xqtb_switch =minstr([info valueForKey:@"game_xqtb_switch"]);
        game_xydzp_switch = minstr([info valueForKey:@"game_xydzp_switch"]);
        if([game_xqtb_switch  isEqual:@"0"]&&[game_xydzp_switch  isEqual:@"0"]){
            _gameBtn.hidden = YES;
        }else{
            _gameBtn.hidden = NO;
        }
        NSString *user_sw_token = minstr([info valueForKey:@"user_sw_token"]);

        if (![_sdkType isEqual:@"1"]) {
            if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
                [[YBAgoraManager shareInstance]joinChannelWithChannelId:minstr([self.playDoc valueForKey:@"stream"]) andUserToken:user_sw_token WithModel:RtcMode_Chat isBroadcaster:AgoraClientRoleAudience isGameLive:NO];
                [[YBAgoraManager shareInstance]showWithCanvasView:_videoPlayView andUserId:minstr([self.playDoc valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
            }else{
                //加载主播播放画面
                [[YBAgoraManager shareInstance]joinChannelWithChannelId:minstr([_playDoc valueForKey:@"stream"]) andUserToken:minstr([info valueForKey:@"user_sw_token"])  WithModel:RtcMode_Living isBroadcaster:AgoraClientRoleAudience isGameLive:NO];
                ChatVideoUserLinkView *btn = sitVideoButtonArray [0];
                btn.livePushView.hidden = NO;
                [[YBAgoraManager shareInstance]showWithCanvasView:btn.livePushView andUserId:minstr([self.playDoc valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
            }
        }
        //mark---showGoodsDic 直播间商品展示小窗口 判断goodsid  为0时没有否则有商品
        showGoodsDic = [info valueForKey:@"show_goods"];
            if(![minstr([showGoodsDic valueForKey:@"goodsid"]) isEqual:@"0"]){
            [self showLiveGoods:showGoodsDic];
        }
        if ([turntable_switch isEqual:@"1"]) {
            turntableBtn.hidden = NO;
        }
        self.chatWordArr = [info valueForKey:@"sensitive_words"];
//        if (![jackpot_level isEqual:@"-1"]) {
//            [self JackpotLevelUp:@{@"uplevel":jackpot_level}];
//        }
        [common saveagorakitid:minstr([info valueForKey:@"agorakitid"])];//保存声网ID
        if ([minstr([info valueForKey:@"issuper"]) isEqual:@"1"]) {
            isSuperAdmin = YES;
        }else{
            isSuperAdmin = NO;
        }
        usertype = minstr([info valueForKey:@"usertype"]);
        //保存靓号和vip信息
        NSDictionary *liang = [info valueForKey:@"liang"];
        NSString *liangnum = minstr([liang valueForKey:@"name"]);
        NSDictionary *vip = [info valueForKey:@"vip"];
        NSString *type = minstr([vip valueForKey:@"type"]);
        
        NSDictionary *subdic = [NSDictionary dictionaryWithObjects:@[type,liangnum] forKeys:@[@"vip_type",@"liang"]];
        [Config saveVipandliang:subdic];
        
        self.danmuprice = [info valueForKey:@"barrage_fee"];
        _listArray = [info valueForKey:@"userlists"];
        LiveUser *users = [Config myProfile];
        users.coin = [NSString stringWithFormat:@"%@",[info valueForKey:@"coin"]];
        [Config updateProfile:users];
        
        NSString *isattention = [NSString stringWithFormat:@"%@",[info valueForKey:@"isattention"]];
        userCount = [[info valueForKey:@"nums"] intValue];
        dispatch_async(dispatch_get_main_queue(), ^{
            votesTatal = minstr([info valueForKey:@"votestotal"]);
           [setFrontV changeState:votesTatal andID:nil];
            setFrontV.onlineBtn.frame = CGRectMake(_window_width-90, 25+statusbarHeight, 40, 40);

            if (![minstr([info valueForKey:@"guard_nums"]) isEqual:@"0"]) {
                [setFrontV changeGuardButtonFrame:minstr([info valueForKey:@"guard_nums"])];
            }
            if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
                micListArr = [info valueForKey:@"mic_list"];
                for (int i = 0; i < micListArr.count; i++) {
                    NSDictionary *infos =micListArr[i];
                      if (minstr([infos valueForKey:@"avatar"]).length > 0 &&![minstr([infos valueForKey:@"avatar"]) isEqual:@"0"]) {
                        YBUserLinkButton *btn = sitButtonArray [i];
                        btn.userDic = infos;
                    }
                    YBUserLinkButton *btn2 = sitButtonArray [i];
                    [btn2 changeLinkBtnStatus:minstr([infos valueForKey:@"mic_status"])];
                }
                NSLog(@"micList--------:%@",micListArr);
            }else{
                //1有人 0没人
                NSArray *mic_list = [info valueForKey:@"mic_list"];
                for (int i = 0; i < mic_list.count; i ++) {
                    ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
                    btn.userDic = mic_list[i];
                }
                if([minstr([info valueForKey:@"is_mic_list"]) isEqual:@"1"]){
                    if ([_sdkType isEqual:@"1"]) {
                        _playVideoChatV.hidden = NO;
                        [self.txLivePlayer setRenderView:_playVideoChatV];
                    }
                    for (int i = 0; i < mic_list.count; i++) {
                        NSDictionary *infos =mic_list[i];
                        ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
                        if (minstr([infos valueForKey:@"avatar"]).length > 0 &&![minstr([infos valueForKey:@"avatar"]) isEqual:@"0"]) {//
                              btn.userDic = infos;
                            if ([_sdkType isEqual:@"1"]) {}else{
                                [[YBAgoraManager shareInstance]showWithCanvasView:btn.livePushView andUserId:minstr([infos valueForKey:@"id"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
                            }
                          }else{
                              btn.hidden = NO;
                        }
                    }
                }
            }
            if(userCount > 99){
                [setFrontV.onlineBtn setTitle:@"99+" forState:0];
            }else{
                [setFrontV.onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
            }

           //userlist_time 间隔时间
               //获取用户列表
           listView = [[ListCollection alloc]initWithListArray:[info valueForKey:@"userlists"] andID:[self.playDoc valueForKey:@"uid"] andStream:[NSString stringWithFormat:@"%@",[self.playDoc valueForKey:@"stream"]]andFixWidth:50];
           listView.delegate = self;
               userlist_time = [[info valueForKey:@"userlist_time"] intValue];
               if (!listTimer) {
                   listTimer = [NSTimer scheduledTimerWithTimeInterval:userlist_time target:self selector:@selector(reloadUserList) userInfo:nil repeats:YES];
               }
           [backScrollView addSubview:listView];
           [self isAttentionLive:isattention];
       });
        //游戏******************************************
        //获取庄家信息
        NSString *coin = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_coin"]];
        
        NSString *game_banker_limit = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_limit"]];
        NSString *uname = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_name"]];
        NSString *uhead = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_avatar"]];
        NSString *uid = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_bankerid"]];
        NSDictionary *zhuangdic = @{
                                    @"coin":coin,
                                    @"game_banker_limit":game_banker_limit,
                                    @"uname":uname,
                                    @"uhead":uhead,
                                    @"id":uid
                                    };
        [gameState savezhuanglimit:game_banker_limit];//缓存上庄钱数限制
        zhuangstartdic = zhuangdic;
        NSString *gametime = [NSString stringWithFormat:@"%@",[info valueForKey:@"gametime"]];
        NSString *gameaction = [NSString stringWithFormat:@"%@",[info valueForKey:@"gameaction"]];
        if (!gametime || [gametime isEqual:[NSNull null]] || [gametime isEqual:@"<null>"] || [gametime isEqual:@"null"] || [gametime isEqual:@"0"]) {
            //没有游戏
        }
        else{
            //有游戏 1炸金花  2海盗  3转盘  4牛牛  5二八贝
            if ([gameaction isEqual:@"1"] || [gameaction isEqual:@"4"] || [gameaction isEqual:@"2"]) {

                if ([gameaction isEqual:@"2"]) {
                    gameVC = [[gameBottomVC alloc]initWIthDic:_playDoc andIsHost:NO andMethod:@"startLodumaniGame" andMsgtype:@"18"];
                }
                if ([gameaction isEqual:@"1"]) {
                    gameVC = [[gameBottomVC alloc]initWIthDic:_playDoc andIsHost:NO andMethod:@"startGame" andMsgtype:@"15"];
                }
                else if ([gameaction isEqual:@"4"]){
                    gameVC = [[gameBottomVC alloc]initWIthDic:_playDoc andIsHost:NO andMethod:@"startCattleGame" andMsgtype:@"17"];
                }
                gameVC.delagate = self;
                gameVC.frame = CGRectMake(_window_width, _window_height - 260, _window_width,260);
                [self changeBtnFrame:_window_height - 260];
                [backScrollView insertSubview:gameVC atIndex:4];
                [backScrollView insertSubview:_liwuBTN atIndex:5];
                [self tableviewheight:_window_height - _window_height*0.2 - 260];
                [gameVC continueUI];
                [gameVC movieplayStartCut:gametime andGameid:[info valueForKey:@"gameid"]];
                NSArray *arrays = [info valueForKey:@"game"];
                if (arrays) {
                    [gameVC getNewCOins:[info valueForKey:@"game"]];
                }
                NSArray *arraysbet = [info valueForKey:@"gamebet"];
                if (arraysbet) {
                    [gameVC getmyCOIns:[info valueForKey:@"gamebet"]];
                }
                //上庄
                if ([gameaction isEqual:@"4"]) {
                    if (!zhuangVC) {
                        zhuangVC = [[shangzhuang alloc]initWithFrame:CGRectMake(_window_width + 10,90, _window_width/4, _window_width/4 + 20 + _window_width/8) ishost:NO withstreame:[self.playDoc valueForKey:@"stream"]];
                        zhuangVC.deleagte = self;
                        [backScrollView insertSubview:zhuangVC atIndex:10];
                        [backScrollView bringSubviewToFront:zhuangVC];
                        [zhuangVC getbanksCoin:zhuangstartdic];
                        [zhuangVC setpoker];
                        [zhuangVC addtableview];
                    }
                }
            }
            //转盘
            else if ([gameaction isEqual:@"3"]){
                [self getRotation];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [rotationV continueGame:gametime andgameId:[info valueForKey:@"gameid"] andMoney:[info valueForKey:@"game"] andmycoin:[info valueForKey:@"gamebet"]];
                });
            }
        }
        [self changecontinuegiftframe];
        //进入房间的时候checklive返回的收费金额
        if ([_livetype isEqual:@"3"] || [_livetype isEqual:@"2"]) {
            //此处用于计时收费
            //刷新所有人的影票
            [self addCoin:[_type_val longLongValue]];
            [socketDelegate addvotesenterroom:minstr(_type_val)];
        }
        if (![minstr([info valueForKey:@"linkmic_uid"]) isEqual:@"0"] && ![_sdkType isEqual:@"1"]) {
            //金山连麦(用户--主播)
            [self playLinkUserUrl:minstr([info valueForKey:@"linkmic_pull"]) andUid:minstr([info valueForKey:@"linkmic_uid"])];
        }
        
        if ([minstr([info valueForKey:@"isred"]) isEqual:@"1"]) {
            if (redBagBtn) {
                [redBagBtn removeFromSuperview];
                redBagBtn = nil;
            }
                redBagBtn = [UIButton buttonWithType:0];
                [redBagBtn setBackgroundImage:[UIImage imageNamed:@"红包-右上角"] forState:UIControlStateNormal];
                [redBagBtn addTarget:self action:@selector(redBagBtnClick) forControlEvents:UIControlEventTouchUpInside];
                redBagBtn.frame = CGRectMake(_window_width*2-50, 80+statusbarHeight, 40, 40);
                [backScrollView addSubview:redBagBtn];
        }
        NSDictionary *pkinfo = [info valueForKey:@"pkinfo"];
        if (![minstr([pkinfo valueForKey:@"pkuid"]) isEqual:@"0"]) {
            [self anchor_agreeLink:pkinfo];
            if ([minstr([pkinfo valueForKey:@"ifpk"]) isEqual:@"1"]) {
                [self showPKView:pkinfo];
                NSMutableDictionary *pkDic = [NSMutableDictionary dictionary];
                [pkDic setObject:minstr([_playDoc valueForKey:@"uid"]) forKey:@"pkuid1"];
                [pkDic setObject:minstr([pkinfo valueForKey:@"pk_gift_liveuid"]) forKey:@"pktotal1"];
                [pkDic setObject:minstr([pkinfo valueForKey:@"pk_gift_pkuid"]) forKey:@"pktotal2"];
                [self changePkProgressViewValue:pkDic];
            }
        }
        
    }andlivetype:_livetype];
}
//改变连送礼物的frame
-(void)changecontinuegiftframe{
    
    liansongliwubottomview.frame = CGRectMake(_window_width, self.tableView.top - 150,300,140);
}
//改变连送礼物的frame
-(void)changecontinuegiftframeIndoliwu{
    liansongliwubottomview.frame = CGRectMake(_window_width, _window_height - (_window_width/2+100+ShowDiff)-140,_window_width/2,140);
}
-(void)reloadUserList{
    [listView listReloadNoew];
}
- (void)reloadLiveplayAttion:(NSNotification *)not{
    NSDictionary *dic = [not object];
    if ([dic isKindOfClass:[NSDictionary class]] && [minstr([dic valueForKey:@"touid"]) isEqual:minstr([_playDoc valueForKey:@"uid"])]) {
        [self isAttentionLive:minstr([dic valueForKey:@"isattent"])];
    }
}
-(void)isAttentionLive:(NSString *)isattention{
    if ([isattention isEqual:@"0"]) {
        //未关注
        setFrontV.newattention.hidden = NO;
        setFrontV.leftView.frame = CGRectMake(10,25+statusbarHeight,140,leftW);
        listcollectionviewx = _window_width+150;
        listView.frame = CGRectMake(listcollectionviewx, 20+statusbarHeight, _window_width-200-60,40);
        listView.listCollectionview.frame = CGRectMake(0, 0, _window_width-200-60, 40);
    }
    else{
        //关注
        setFrontV.newattention.hidden = YES;
        setFrontV.leftView.frame = CGRectMake(10,25+statusbarHeight,95,leftW);
        listcollectionviewx = _window_width+105;
        listView.frame = CGRectMake(listcollectionviewx, 20+statusbarHeight, _window_width-150-60,40);
        listView.listCollectionview.frame = CGRectMake(0, 0, _window_width-150-60, 40);
        [socketDelegate attentionLive:level];
    }
}
#pragma mark----/*************** 以下视频播放 ***************/
-(void)playerPlayVideo {

    _videoPlayView = [[UIView alloc] init];
    _videoPlayView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:_videoPlayView];
    [self.view sendSubviewToBack:_videoPlayView];
    UIImageView *pkBackImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0,0,_window_width,_window_height)];
    pkBackImgView.userInteractionEnabled = YES;
    pkBackImgView.image = [UIImage imageNamed:@"pk背景"];
    [self.view addSubview:pkBackImgView];
    [self.view sendSubviewToBack:pkBackImgView];
    _videoPlayView.frame = CGRectMake(0,0, _window_width, _window_height);
    _url = [NSURL URLWithString:[minstr([self.playDoc valueForKey:@"pull"]) stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]
]];
    [self setupObservers];
    NSLog(@"======播流地址%@",_url);
    [self onPlayVideo];
}
- (void)setupObservers {
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(toolbarHidden) name:@"toolbarHidden" object:nil];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(qiehuanfangjian:) name:@"qiehuanfangjian" object:nil];
}
- (void)releaseObservers {
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"toolbarHidden" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"gengxinweidu" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"sixinok" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"qiehuanfangjian" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"changePlayRoom" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"reloadLiveplayAttion" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"denglushixiao" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:ybChatRefresh object:nil];
}
- (void)onPlayVideo {
    if ([_sdkType isEqual:@"1"]) {
        [self txPlayer];
    }

}
-(void)txPlayer {
    [self.txLivePlayer setRenderView:_videoPlayView];
    NSString *playUrl = [self.playDoc valueForKey:@"pull"];
    V2TXLiveCode result = [self.txLivePlayer startLivePlay:playUrl];
    NSLog(@"wangminxin%ld",result);
    if( result != 0)
    {
        [_notification displayNotificationWithMessage:@"视频流播放失败" forDuration:5];
        [self lastView];
    }
    if( result == 0){
        NSLog(@"播放视频");
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            buttomimageviews.hidden = YES;
        });
    }
    if ([_livetype isEqual:@"6"]) {
        [_txLivePlayer setPlayoutVolume:0];
    }
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
}
- (BOOL)isNotchScreen {
    
    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        return NO;
    }
    
    CGSize size = [UIScreen mainScreen].bounds.size;
    NSInteger notchValue = size.width / size.height * 100;
    
    if (216 == notchValue || 46 == notchValue) {
        return YES;
    }
    return NO;
}
- (void)onStopVideo{
    //tx
    if(self.txLivePlayer != nil) {
        [self.txLivePlayer stopPlay];
    }
    if (_liveGoodsView) {
        [_liveGoodsView removeFromSuperview];
        _liveGoodsView = nil;
    }
    if (titleBackImgView) {
        [titleBackImgView removeFromSuperview];
        titleBackImgView = nil;
    }
    [[UIApplication sharedApplication] setIdleTimerDisabled:NO];
    for (TXPlayLinkMic *playv in self.view.subviews) {
        if ([playv isKindOfClass:[TXPlayLinkMic class]]) {
            [playv stopConnect];
            [playv stopPush];
            [playv removeFromSuperview];
        }
    }
}
/*************** 以上视频播放 ***************/
//礼物效果
/************ 礼物弹出及队列显示开始 *************/
-(void)expensiveGiftdelegate:(NSDictionary *)giftData{
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]initWithIsPlat:NO];
        haohualiwuV.delegate = self;
        [backScrollView addSubview:haohualiwuV];
        [backScrollView insertSubview:haohualiwuV atIndex:8];
        CGAffineTransform t = CGAffineTransformMakeTranslation(_window_width, 0);
        haohualiwuV.transform = t;
    }
    if (giftData == nil) {
        
    }else{
        [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive:NO];
    }
}
-(void)platGiftdelegate:(NSDictionary *)giftData
{
    
    if (!platliwuV) {
        platliwuV = [[PlatGiftView alloc]initWithIsPlat:YES];
        platliwuV.delegate = self;
        [backScrollView addSubview:platliwuV];
        [backScrollView insertSubview:platliwuV atIndex:8];
        CGAffineTransform t = CGAffineTransformMakeTranslation(_window_width, 0);
        platliwuV.transform = t;
    }
    if (giftData == nil) {
    }
    else
    {
        [platliwuV addArrayCount:giftData];
    }
    if(platliwuV.haohuaCount == 0){
        [platliwuV enGiftEspensive:YES];
    }

}
-(void)platGift:(NSDictionary *)giftData{
    if (!platliwuV) {
        platliwuV = [[PlatGiftView alloc]initWithIsPlat:YES];
        platliwuV.delegate = self;
        [backScrollView addSubview:platliwuV];
        CGAffineTransform t = CGAffineTransformMakeTranslation(_window_width, 0);
        platliwuV.transform = t;
    }
    if (giftData == nil) {
        
    }else
    {
        [platliwuV addArrayCount:giftData];
    }
    if(platliwuV.haohuaCount == 0){
        [platliwuV enGiftEspensive:YES];
    }

}
-(void)expensiveGift:(NSDictionary *)giftData andIsPlat:(BOOL)isPlat{
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]initWithIsPlat:isPlat];
        haohualiwuV.delegate = self;
        [backScrollView addSubview:haohualiwuV];
        CGAffineTransform t = CGAffineTransformMakeTranslation(_window_width, 0);
        haohualiwuV.transform = t;
    }
    if (giftData == nil) {
        
    }else{
         [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive:isPlat];
    }
}
/*
 *添加魅力值数
 */
-(void)addCoin:(long)coin
{
    long long ordDate = [votesTatal longLongValue];
    votesTatal = [NSString stringWithFormat:@"%lld",ordDate + coin];
    [setFrontV changeState: votesTatal andID:nil];
}
-(void)addvotesdelegate:(NSString *)votes{
    [self addCoin:[votes longLongValue]];
}
/************  杨志刚 礼物弹出及队列显示结束 *************/
//跳转充值
-(void)pushCoinV{
    [self zhezhaoBTNdelegate];
    [self requestPayList];
}
-(void)luckyBtnClickdelegate:(BOOL)isLuckGift{
    YBWebViewController *web = [[YBWebViewController alloc]init];
    if (isLuckGift) {
        web.urls = [NSString stringWithFormat:@"%@/portal/page/index?id=26",h5url];
    }else{
        web.urls = [NSString stringWithFormat:@"%@",[common stricker_url]];
    }
    [[MXBADelegate sharedAppDelegate]pushViewController:web animated:YES];

}

//聊天自动上滚
-(void)jumpLast
{
    if (_canScrollToBottom) {
    NSUInteger sectionCount = [self.tableView numberOfSections];
    if (sectionCount) {
        NSUInteger rowCount = [self.tableView numberOfRowsInSection:0];
        if (rowCount) {
            NSUInteger ii[2] = {sectionCount-1, 0};
            NSIndexPath* indexPath = [NSIndexPath indexPathWithIndexes:ii length:2];
            [self.tableView scrollToRowAtIndexPath:indexPath
                                  atScrollPosition:UITableViewScrollPositionBottom animated:YES];
        }
    }
     }
}
//切换聊天和弹幕
-(void)switchState:(BOOL)state
{
    if(!state)
    {
        keyField.placeholder = YZMsg(@"和大家说些什么");
    }
    else
    {
        keyField.frame  = CGRectMake(70,7,_window_width-90 - 50, 30);//CGRectMake(50,5,_window_width-60 - 50 , 30);
        keyField.placeholder = [NSString stringWithFormat:@"%@，%@%@/%@",YZMsg(@"开启弹幕"),self.danmuprice,[common name_coin],YZMsg(@"条")];
    }
}
- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self.view endEditing:YES];
}
//执行扣费
-(void)timecoastmoney{
    coasttime -= 1;
    if (coasttime == 0) {
        [timecoast invalidate];
        timecoast = nil;
        coasttime = 60;
        [self docoast:@"Live.timeCharge"];
    }
}
//切换房间类型
-(void)changeLive:(NSString *)type_val{
    if (isSuperAdmin) {
        return;
    }
    _type_val = type_val;
    _videoPlayView.hidden = YES;
    setFrontV.bigAvatarImageView.hidden = NO;
    if (timecoast) {
        [timecoast invalidate];
        timecoast = nil;
    }
    coasttime = 0;
    Feedeductionalertc = [UIAlertController alertControllerWithTitle:[NSString stringWithFormat:@"%@%@%@/%@",YZMsg(@"当前房间收费"),type_val,[common name_coin],YZMsg(@"分钟")] message:@"" preferredStyle:UIAlertControllerStyleAlert];
    //修改按钮的颜色，同上可以使用同样的方法修改内容，样式
    UIAlertAction *cancelActionss = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        if (timecoast) {
            [timecoast invalidate];
            timecoast = nil;
        }
        [self dissmissVC];
    }];
    UIAlertAction *surelActionss = [UIAlertAction actionWithTitle:@"付费" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self docoast:@"Live.roomCharge"];
    }];
    NSString *version = [UIDevice currentDevice].systemVersion;
    if (version.doubleValue < 9.0) {
        
    }
    else{
        [surelActionss setValue:normalColors forKey:@"_titleTextColor"];
        [cancelActionss setValue:normalColors forKey:@"_titleTextColor"];
    }
    [Feedeductionalertc addAction:cancelActionss];
    [Feedeductionalertc addAction:surelActionss];
    [self presentViewController:Feedeductionalertc animated:YES completion:nil];
    
}
-(void)docoast:(NSString *)type{
    NSString *url = [purl stringByAppendingFormat:@"?service=%@",type];
    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             @"liveuid":[self.playDoc valueForKey:@"uid"],
                             @"stream":[self.playDoc valueForKey:@"stream"]
                             };
    AFHTTPSessionManager *session = [AFHTTPSessionManager manager];
    [session POST:url parameters:subdic headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSNumber *number = [responseObject valueForKey:@"ret"];
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [responseObject valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
                _videoPlayView.hidden = NO;
                coasttime = 60;
                if (!timecoast) {
                    timecoast = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(timecoastmoney) userInfo:nil repeats:YES];
                }
                [socketDelegate addvotes:_type_val isfirst:@"0"];
                [self addCoin:[_type_val longLongValue]];
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                LiveUser *user = [Config myProfile];
                user.coin = minstr([info valueForKey:@"coin"]);
                [Config updateProfile:user];
                _videoPlayView.hidden = NO;
                setFrontV.bigAvatarImageView.hidden = YES;
            }
            else{
                UIAlertController  *alertlianmaiVC = [UIAlertController alertControllerWithTitle:YZMsg(@"您当前余额不足无法观看") message:@"" preferredStyle:UIAlertControllerStyleAlert];
                //修改按钮的颜色，同上可以使用同样的方法修改内容，样式
                UIAlertAction *cancelActionss = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
                    
                    [self dissmissVC];
                }];
                if (timecoast) {
                    [timecoast invalidate];
                    timecoast = nil;
                }
                
                _videoPlayView.hidden = YES;
                setFrontV.bigAvatarImageView.hidden = NO;
                
                NSString *version = [UIDevice currentDevice].systemVersion;
                if (version.doubleValue < 9.0) {
                    
                }
                else{
                    [cancelActionss setValue:normalColors forKey:@"_titleTextColor"];
                }
                [alertlianmaiVC addAction:cancelActionss];
                [self presentViewController:alertlianmaiVC animated:YES completion:nil];
  
                
            }
        }
        else{
            
            [MBProgressHUD showError:[responseObject valueForKey:@"msg"]];
            if (timecoast) {
                [timecoast invalidate];
                timecoast = nil;
            }
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self dissmissVC];
            });
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (timecoast) {
            [timecoast invalidate];
            timecoast = nil;
        }
        [self dissmissVC];
    }];
}
-(void)addvideoswipe{
    if (!videopan) {
        videopan = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(handlepanss:)];
        [videopan setDelegate:self];
        [_videoPlayView addGestureRecognizer:videopan];
    }
    if (!videotap) {
        videotap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(videotap)];
        [_videoPlayView addGestureRecognizer:videotap];
    }
}
-(void)videotap{
    [_videoPlayView removeGestureRecognizer:videopan];
    [_videoPlayView removeGestureRecognizer:videotap];
    videopan = nil;
    videotap = nil;
    
    
    [UIView animateWithDuration:0.5 animations:^{
        _videoPlayView.frame = CGRectMake(0, 0, _window_width, _window_height);
    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.tableView.hidden = NO;
        setFrontV.hidden      = NO;
        _danmuView.hidden     = NO;
        listView.hidden       = NO;
        backScrollView.hidden = NO;
    });
}
- (void) handlepanss: (UIPanGestureRecognizer *)rec{
    CGPoint point = [rec translationInView:_videoPlayView];
    NSLog(@"%f,%f",point.x,point.y);
    rec.view.center = CGPointMake(rec.view.center.x + point.x, rec.view.center.y + point.y);
    [rec setTranslation:CGPointMake(0, 0) inView:_videoPlayView];
}
//执行扣费
-(void)doCoastRoomCharge:(NSDictionary *)infos{
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.roomCharge"];
    NSDictionary *subdic = @{
                             @"uid":[Config getOwnID],
                             @"token":[Config getOwnToken],
                             @"liveuid":minstr([self.playDoc valueForKey:@"uid"]),
                             @"stream":minstr([self.playDoc valueForKey:@"stream"])
                             };
    AFHTTPSessionManager *session = [AFHTTPSessionManager manager];
    [session POST:url parameters:subdic headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSNumber *number = [responseObject valueForKey:@"ret"];
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [responseObject valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
//                [self onPlayVideo];
                [self playLiveRoom:infos];
                //计时扣费
                if ([_livetype isEqual:@"3"]) {
                    coasttime = 60;

                    if (!timecoast) {
                        timecoast = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(timecoastmoney) userInfo:nil repeats:YES];
                    }
                }

            }
            else{
                [MBProgressHUD showError:[data valueForKey:@"msg"]];
                [self.navigationController popViewControllerAnimated:YES];
                [self dismissViewControllerAnimated:NO completion:nil];
            }
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        [MBProgressHUD showError:@"无网络"];
    }];
}
//#pragma mark ================ 切换房间通知 ===============
//- (void)qiehuanfangjian:(NSNotification *)not{
//    self.playDoc = [not object];
//    [self changeRoom:self.playDoc];
//}


-(void)playLiveRoom:(NSDictionary *)info{
    [self releasePlayLinkView];

    [self.txLivePlayer stopPlay];
    [backScrollView removeFromSuperview];
    backScrollView = nil;
    _isJpush = @"1";
    [socketDelegate socketStop];
    socketDelegate = nil;
    [self initArray];
        
        
    [continueGifts stopTimerAndArray];
    [continueGifts initGift];
    [continueGifts removeFromSuperview];
    continueGifts = nil;
    
    if (_paintedShowRegion) {
        [_paintedShowRegion destroyPaitend];
    }

    [setFrontV removeFromSuperview];
    setFrontV = nil;

    haohualiwuV.expensiveGiftCount = nil;
    haohualiwuV.expensiveGiftCount = [NSMutableArray array];
    [haohualiwuV stopHaoHUaLiwu];
    if (haohualiwuV) {
        [haohualiwuV removeFromSuperview];
        haohualiwuV = nil;
    }

    platliwuV.expensiveGiftCount = nil;
    platliwuV.expensiveGiftCount = [NSMutableArray array];
    [platliwuV stopHaoHUaLiwu];
    msgList = nil;
    msgList = [NSMutableArray array];
    [self.tableView removeFromSuperview];
    self.tableView = nil;
    userCount = 0;
    [listView initArray];
    listView = nil;
    [listView removeFromSuperview];
    [fenxiangV removeFromSuperview];
    fenxiangV = nil;
    socketDelegate = nil;
    [self removetimer];
    if (haslianmai == YES) {
        if ([_sdkType isEqual:@"1"]) {
            [self txStopLinkMic];
        }else{
            [socketDelegate closeConnect];
        }
    }
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    platliwuV.expensiveGiftCount = nil;
    if (continueGifts) {
        [continueGifts stopTimerAndArray];
        continueGifts = nil;
    }
    if (giftview) {
        [giftview removeFromSuperview];
        giftview = nil;
    }
    if (buttleView) {
        [buttleView removeFromSuperview];
        buttleView = nil;
    }
    if (payView) {
        [payView removeFromSuperview];
        payView = nil;
    }
    if (zhuangVC) {
        [zhuangVC dismissroom];
        [zhuangVC removeall];
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (gameVC) {
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
    }
    if (rotationV) {
        [rotationV stopRotatipnGameInt];
        [rotationV stoplasttimer];
        [rotationV removeFromSuperview];
        [rotationV removeall];
        rotationV = nil;
    }

    dispatch_async(dispatch_get_main_queue(), ^{
        _url = [NSURL URLWithString:[minstr([self.playDoc valueForKey:@"pull"]) stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]]];
//        [self playerPlayVideo];
        [self onPlayVideo];
        [self setView];

        [huanxinviews.view removeFromSuperview];
        huanxinviews = nil;
        [tChatsamall.view removeFromSuperview];
        tChatsamall = nil;
    });
    
    [self removePKView];
    if ([_sdkType isEqual:@"1"]) {
        _videoPlayView.frame = CGRectMake(0, 0, _window_width, _window_height);
//        [_zbtxLivePlayer setupVideoWidget:_videoPlayView.bounds containView:_videoPlayView insertIndex:0];
        [self.txLivePlayer setRenderView:_videoPlayView];
    }
    
    [self showBTN];
    [self getNodeJSInfo]; //初始化nodejs信息
    giftViewShow = NO;

    //显示进场标题
    [self showTitle];
}

- (void)changeRoom:(NSDictionary *)info{
    
    NSLog(@"-=-=-=-=liveplay-=-=-=-::%@",info);
    [self checkliveInfo:info];
    
}
- (void)qiehuanfangjian:(NSNotification *)not{
    NSDictionary *dic = [not object];
    _isJpush = @"1";
    [self dissmissVC];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"jinruzhibojiantongzhi" object:dic];
}
#pragma mark ================ 切换房间 ===============
- (void)changePlayRoom:(NSNotification *)not{
    NSDictionary *dic = [not userInfo];
    _livetype = minstr([dic valueForKey:@"livetype"]);
    _type_val = minstr([dic valueForKey:@"type_val"]);
    _sdkType = minstr([dic valueForKey:@"sdkType"]);

    self.playDoc = [dic valueForKey:@"dic"];
    [self changeRoom:self.playDoc];
}

#pragma mark ================ 连麦 ===============
- (void)playLinkUserUrl:(NSString *)playurl andUid:(NSString *)userid{
    [self releasePlayLinkView];
}
//连麦
-(void)connectVideos{
// ===== user link
    if (haslianmai) {
        [self faqilaimai];
    }else{
        [YBToolClass postNetworkWithUrl:@"Linkmic.isMic" andParameter:@{@"liveuid":minstr([_playDoc valueForKey:@"uid"])} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            if (code == 0) {
                [self faqilaimai];
            }else{
                [MBProgressHUD showError:msg];
            }
            
        } fail:^{
            
        }];
    }
}
- (void)faqilaimai{
    if (zhuangVC || gameVC || rotationV) {
        [MBProgressHUD showError:YZMsg(@"游戏状态下不能进行连麦哦")];
        return;
    }

    if (!haslianmai) {
//        if (_js_playrtmp) {
//            [MBProgressHUD showError:YZMsg(@"主播连麦中，请等会儿再试哦")];
//            return;
//        }
    }
    if (haslianmai == NO) {
        haslianmai = YES;
        //发送连麦socket
        [MBProgressHUD showError:YZMsg(@"连麦请求已发送")];
        [socketDelegate connectvideoToHost];
        if (!startLinkTimer) {
            startLinkTime = 11;
            startLinkTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(startLinkTimeDaoJiShi) userInfo:nil repeats:YES];
        }
        
    }
    else{
        if (startLinkTimer) {
            [MBProgressHUD showError:YZMsg(@"您已申请，请稍等")];
            return;
        }
        UIAlertController  *alertlianmaiVC = [UIAlertController alertControllerWithTitle:YZMsg(@"是否断开连麦") message:@"" preferredStyle:UIAlertControllerStyleAlert];
        //修改按钮的颜色，同上可以使用同样的方法修改内容，样式
        UIAlertAction *defaultActionss = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            //关闭连麦
            haslianmai = NO;
            NSLog(@"关闭连麦");
            [self closeconnect];
            [socketDelegate closeConnect];
        }];
        UIAlertAction *cancelActionss = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            
            
        }];
        NSString *version = [UIDevice currentDevice].systemVersion;
        if (version.doubleValue < 9.0) {
            
        }
        else{
            [defaultActionss setValue:normalColors forKey:@"_titleTextColor"];
            [cancelActionss setValue:normalColors forKey:@"_titleTextColor"];
        }
        [alertlianmaiVC addAction:defaultActionss];
        [alertlianmaiVC addAction:cancelActionss];
        [self presentViewController:alertlianmaiVC animated:YES completion:nil];
    }

}
- (void)startLinkTimeDaoJiShi{
    startLinkTime -- ;
    if (startLinkTime <= 0) {
        [startLinkTimer invalidate];
        startLinkTimer = nil;
    }
}
-(void)closeconnect{
    dispatch_async(dispatch_get_main_queue(), ^{
        [self releasePlayLinkView];
        [MBProgressHUD hideHUD];
        haslianmai = NO;
    });
}
//得到主播同意开始连麦
-(void)startConnectvideo{
    if (startLinkTimer) {
        [startLinkTimer invalidate];
        startLinkTimer = nil;
    }
    [YBToolClass postNetworkWithUrl:@"Linkmic.RequestLVBAddrForLinkMic" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if(code == 0)
        {
            NSDictionary *infoDic = [info firstObject];
            _myplayurl = [infoDic valueForKey:@"playurl"];
            NSString *_mypushurl = [infoDic valueForKey:@"pushurl"];
            NSDictionary *subdic = @{
                                     @"userid":[Config getOwnID],
                                     @"playurl":[NSString stringWithFormat:@"%@",_myplayurl],
                                     @"pushurl":[NSString stringWithFormat:@"%@",_mypushurl]
                                     };
            [self releasePlayLinkView];
            //腾讯
            _tx_playrtmp = [[TXPlayLinkMic alloc]initWithRTMPURL:subdic andFrame:CGRectMake(_window_width - 100, _window_height - 110 - ShowDiff - 150 , 100, 150) andisHOST:NO andAnToAn:NO];
            _tx_playrtmp.delegate = self;
            _tx_playrtmp.tag = 1500 + [[Config getOwnID] intValue];
            [self.view addSubview:_tx_playrtmp];
            [self.view insertSubview:_tx_playrtmp aboveSubview:self.tableView];
        }
        else{
            [MBProgressHUD showError:msg];
        }
    } fail:^{
    }];
}
#pragma mark -  腾讯连麦start
//停止从CDN拉流，开始推流
-(void)tx_startConnectRtmpForLink_mic{
    //1.结束从CDN拉流
    [self.txLivePlayer stopPlay];
    //2.拉取主播的低时延流
    [self gethostlowurl];
    //3.通知主播和其他小主播拉取自己的流
    [socketDelegate sendSmallURL:_myplayurl andID:[Config getOwnID]];
}
//1.拉取主播的低时延流
-(void)gethostlowurl{
    NSString *url = [purl stringByAppendingFormat:@"?service=Linkmic.RequestPlayUrlWithSignForLinkMic&uid=%@&originStreamUrl=%@",[Config getOwnID],[self.playDoc valueForKey:@"pull"]];
    NSString *newUrlStr = [url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    [YBNetworking postWithUrl:newUrlStr Dic:nil Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            NSDictionary *info = [[data valueForKey:@"info"] firstObject];
            NSString *streamUrlWithSignature = [info valueForKey:@"streamUrlWithSignature"];
            if([minstr([self.playDoc valueForKey:@"voice_type"]) isEqual:@"0"]){
                [self.txLivePlayer setRenderView:_playVideoChatV];
                [self.txLivePlayer startLivePlay:streamUrlWithSignature];

            }else{
                [self.txLivePlayer startLivePlay:streamUrlWithSignature];
            }
           
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:nil];
}
-(void)tx_stoppushlink{
    [self txStopLinkMic];
}

-(void)tx_closeUserbyVideo:(NSDictionary *)subdic{
    [MBProgressHUD showError:@"播放失败"];
}
-(void)txStopLinkMic{
    [socketDelegate closeConnect];//关闭连麦通知
    //清除其他连麦信息
    for (TXPlayLinkMic *playv in self.view.subviews) {
        if ([playv isKindOfClass:[TXPlayLinkMic class]]) {
            [playv stopConnect];
            [playv stopPush];
            [playv removeFromSuperview];
        }
    }
    [self.txLivePlayer stopPlay];

}
#pragma mark -  腾讯连麦end
//开始连麦推流
-(void)js_startConnectRtmpForLink_mic{
    [socketDelegate sendSmallURL:_myplayurl andID:[Config getOwnID]];
}
- (void)js_stoppushlink{
    [MBProgressHUD showError:YZMsg(@"推流失败")];
    [socketDelegate closeConnect];
}
- (void)hostoutMsg:(NSDictionary *)msgDic{
    if (startLinkTimer) {
        [startLinkTimer invalidate];
        startLinkTimer = nil;
    }
    [self releasePlayLinkView];
    [self enabledlianmaibtn];
}
- (void)releasePlayLinkView{
    if (startLinkTimer) {
        [startLinkTimer invalidate];
        startLinkTimer = nil;
    }
    if (_tx_playrtmp) {
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }

}
- (void)enabledlianmaibtn{
    haslianmai = NO;
}
#pragma mark ================ s添加印象 ===============
- (void)setLabel:(NSString *)touid{
    impressVC *vc = [[impressVC alloc]init];
    vc.isAdd = YES;
    vc.touid = touid;
    [[MXBADelegate sharedAppDelegate]pushViewController:vc animated:YES];
}
#pragma mark ================ 改变发送按钮图片 ===============
- (void)ChangePushBtnState{
    if (keyField.text.length > 0) {
        pushBTN.selected = YES;
    }else{
        pushBTN.selected = NO;
    }
}
#pragma mark ================ 守护功能 ===============
-(void)showGuardView{
    if (giftview) {
        [self changeGiftViewFrameY:_window_height*10];
    }
    gShowView = [[guardShowView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andUserGuardMsg:guardInfo andLiveUid:minstr([_playDoc valueForKey:@"uid"])];
    gShowView.delegate = self;
    [self.view addSubview:gShowView];
    [gShowView show];
}
- (void)buyOrRenewGuard{
    [self removeShouhuView];
    if (!guardView) {
        guardView = [[shouhuView alloc]init];
        guardView.liveUid = minstr([_playDoc valueForKey:@"uid"]);
        guardView.stream = minstr([_playDoc valueForKey:@"stream"]);
        guardView.delegate = self;
        guardView.guardType = minstr([guardInfo valueForKey:@"type"]);
        [self.view addSubview:guardView];
    }
    [guardView show];
}
- (void)removeShouhuView{
    if (guardView) {
        [guardView removeFromSuperview];
        guardView = nil;
    }
    if (gShowView) {
        [gShowView removeFromSuperview];
        gShowView = nil;
    }
    if (redList) {
        [redList removeFromSuperview];
        redList = nil;
    }
}
- (void)buyShouhuSuccess:(NSDictionary *)dic{
    guardInfo = dic;
    [socketDelegate buyGuardSuccess:dic];
}
- (void)updateGuardMsg:(NSDictionary *)dic{
    [setFrontV changeState:minstr([dic valueForKey:@"votestotal"]) andID:nil];
    [setFrontV changeGuardButtonFrame:minstr([dic valueForKey:@"guard_nums"])];
    if (listView) {
        [listView listReloadNoew];
    }
    
}
#pragma mark ================ 红包 ===============
- (void)showRedView{
    redBview = [[redBagView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    __weak UserRoomViewController *wSelf = self;
    redBview.block = ^(NSString *type) {
        [wSelf sendRedBagSuccess:type];
    };
    redBview.zhuboDic = _playDoc;
    [self.view addSubview:redBview];
}
- (void)sendRedBagSuccess:(NSString *)type{
    [redBview removeFromSuperview];
    redBview = nil;
    if ([type isEqual:@"909"]) {
        return;
    }
    [socketDelegate fahongbaola];
}
- (void)showRedbag:(NSDictionary *)dic{
    if (!redBagBtn) {
        //PK按钮
        redBagBtn = [UIButton buttonWithType:0];
        [redBagBtn setBackgroundImage:[UIImage imageNamed:@"红包-右上角"] forState:UIControlStateNormal];
        [redBagBtn addTarget:self action:@selector(redBagBtnClick) forControlEvents:UIControlEventTouchUpInside];
        redBagBtn.frame = CGRectMake(_window_width*2-50, 80+statusbarHeight, 40, 40);
        [backScrollView addSubview:redBagBtn];
    }
    NSString *uname;
    if ([minstr([dic valueForKey:@"uid"]) isEqual:minstr([_playDoc valueForKey:@"uid"])]) {
        uname = YZMsg(@"主播");
    }else{
        uname = minstr([dic valueForKey:@"uname"]);
    }
    NSString *levell = @" ";
    NSString *ID = @" ";
    NSString *vip_type = @"0";
    NSString *liangname = @"0";
    NSString *language = [PublicObj getCurrentLanguage];
    NSString *l_ct;
    if ([language isEqual:@"en"]) {
        l_ct = minstr([dic valueForKey:@"ct_en"]);
    }else{
        l_ct =minstr([dic valueForKey:@"ct"]);
    }

    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",l_ct,@"contentChat",levell,@"levelI",ID,@"id",@"redbag",@"titleColor",vip_type,@"vip_type",liangname,@"liangname",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast];
    
}
- (void)redBagBtnClick{
    if ([[Config getOwnID] intValue] <= 0) {
        [[YBToolClass sharedInstance]waringLogin];
        return;
    }

    redList = [[redListView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) withZHuboMsg:_playDoc];
    redList.delegate =self;
    [self.view addSubview:redList];
}
#pragma mark ================ 主播连麦 ===============
- (void)anchor_agreeLink:(NSDictionary *)dic{
    _videoPlayView.frame = CGRectMake(0, 130+statusbarHeight, _window_width, _window_width*2/3);
    [self.txLivePlayer setRenderView:_videoPlayView];
}
- (void)anchor_stopLink:(NSDictionary *)dic{
    if (pkView) {
        [pkView removeTimer];
        [pkView removeFromSuperview];
        pkView = nil;
    }
    if (_tx_playrtmp) {
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:0.3 animations:^{
                _videoPlayView.frame = CGRectMake(0, 0, _window_width, _window_height);
                [self.txLivePlayer setRenderView:_videoPlayView];
            }];
        });
}
#pragma mark ================ PK ===============
-(void)removePKView{
    if (pkView) {
        [pkView removeTimer];
        [pkView removeFromSuperview];
        pkView = nil;
    }
}
- (void)showPKView:(NSDictionary *)dic{
    if (pkView) {
        [pkView removeTimer];
        [pkView removeFromSuperview];
        pkView = nil;
    }
    NSString *time;
    if (dic) {
        time = minstr([dic valueForKey:@"pk_time"]);
    }else{
        time = @"300";
    }
    CGFloat startY = 130+statusbarHeight;
    if ([_sdkType isEqual:@"1"]) {
        startY = 0;
    }
    pkView = [[anchorPKView alloc]initWithFrame:CGRectMake(0, startY, _window_width, _window_width*2/3+20) andTime:time];
    pkView.delegate = self;
    [_videoPlayView addSubview:pkView];
}
- (void)showPKResult:(NSDictionary *)dic{
    int win;
    if ([minstr([dic valueForKey:@"win_uid"]) isEqual:@"0"]) {
        win = 0;
    }else if ([minstr([dic valueForKey:@"win_uid"]) isEqual:minstr([_playDoc valueForKey:@"uid"])]) {
        win = 1;
    }else{
        win = 2;
    }
    
    [pkView showPkResult:dic andWin:win];
}
- (void)changePkProgressViewValue:(NSDictionary *)dic{
    NSString *blueNum;
    NSString *redNum;
    CGFloat progress = 0.0;
    if ([minstr([dic valueForKey:@"pkuid1"]) isEqual:minstr([_playDoc valueForKey:@"uid"])]) {
        blueNum = minstr([dic valueForKey:@"pktotal1"]);
        redNum = minstr([dic valueForKey:@"pktotal2"]);
    }else{
        redNum = minstr([dic valueForKey:@"pktotal1"]);
        blueNum = minstr([dic valueForKey:@"pktotal2"]);
    }
    if ([blueNum isEqual:@"0"] && [redNum isEqual:@"0"]) {
        progress = 0.5;
    }else{
        if ([blueNum isEqual:@"0"]) {
            progress = 0.2;
        }else if ([redNum isEqual:@"0"]) {
            progress = 0.8;
        }else{
            CGFloat ppp = [blueNum floatValue]/([blueNum floatValue] + [redNum floatValue]);
            if (ppp < 0.2) {
                progress = 0.2;
            }else if (ppp > 0.8){
                progress = 0.8;
            }else{
                progress = ppp;
            }
        }
    }
    
    [pkView updateProgress:progress withBlueNum:blueNum withRedNum:redNum];
}
#pragma mark ============举报=============

-(void)doReportAnchor:(NSString *)touid{
    jubaoVC *vc = [[jubaoVC alloc]init];
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    vc.dongtaiId = touid;
    vc.isLive = YES;
    [self presentViewController:vc animated:YES completion:nil];
}


#pragma mark ===========================   腾讯播放start   =======================================

////播放监听事件
//-(void) onPlayEvent:(int)EvtID withParam:(NSDictionary*)param {
////    NSLog(@"eventID:%d===%@",EvtID,param);
//    dispatch_async(dispatch_get_main_queue(), ^{
//        if (EvtID == PLAY_EVT_CONNECT_SUCC) {
//            NSLog(@"moviplay不连麦已经连接服务器");
//        }
//        else if (EvtID == PLAY_EVT_RTMP_STREAM_BEGIN){
//            NSLog(@"moviplay不连麦已经连接服务器，开始拉流");
//        }
//        else if (EvtID == PLAY_EVT_PLAY_BEGIN){
//            NSLog(@"moviplay不连麦视频播放开始");
//            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                    setFrontV.bigAvatarImageView.hidden = YES;
////                [buttomimageviews removeFromSuperview];
//                backScrollView.userInteractionEnabled = YES;
//                backScrollView.contentSize = CGSizeMake(_window_width*2,0);
//            });
//        }
//        else if (EvtID== PLAY_WARNING_VIDEO_PLAY_LAG){
//            NSLog(@"moviplay不连麦当前视频播放出现卡顿（用户直观感受）");
//        }
//        else if (EvtID == PLAY_EVT_PLAY_END){
//            NSLog(@"moviplay不连麦视频播放结束");
//            [_zbtxLivePlayer resume];
//
//        }
//        else if (EvtID == PLAY_ERR_NET_DISCONNECT) {
//            //视频播放结束
//            NSLog(@"moviplay不连麦网络断连,且经多次重连抢救无效,可以放弃治疗,更多重试请自行重启播放");
//        }else if (EvtID == PLAY_EVT_CHANGE_RESOLUTION) {
//            NSLog(@"主播连麦分辨率改变");
//            /*
//            if (_canChange) {
//                _canChange = NO;
//                if (_isCancleLink) {
//                     _videoPlayView.frame = CGRectMake(0, 0, _window_width, _window_height);
//                }else{
//                    _videoPlayView.frame = CGRectMake(0, 130+statusbarHeight, _window_width, _window_width*2/3);
//                }
//                dispatch_async(dispatch_get_main_queue(), ^{
//                    [UIView animateWithDuration:0.3 animations:^{
//                        [_txLivePlayer setupVideoWidget:_videoPlayView.bounds containView:_videoPlayView insertIndex:0];
//                    }];
//                });
//            }
//            */
//        }
//    });
//}
//-(void)onNetStatus:(NSDictionary *)param{
//
//
//}

#pragma mark ===========================   腾讯播放end   =======================================

-(void)addCycleScroll:(NSArray *)titleArr{

    if (_cycleScroll) {
        [_cycleScroll removeFromSuperview];
        _cycleScroll  = nil;
    }
    NSArray * sliderMuArr;
    if ([jackpot_level isEqual:@"-1"]) {
        if ([turntable_switch isEqual:@"1"]) {
            sliderMuArr = @[@"每日任务",@"幸运大转盘"];
        }else{
            sliderMuArr = @[@"每日任务"];
        }

    }else{
        if ([turntable_switch isEqual:@"1"]) {
            sliderMuArr = @[@"每日任务",@"Jackpot_btnBack",@"幸运大转盘"];
        }else{
            sliderMuArr = @[@"每日任务",@"Jackpot_btnBack"];
        }

    }

    _cycleScroll = [[SDCycleScrollView alloc]initWithFrame: CGRectMake(_window_width + 10, statusbarHeight + 135, 80, 60)];
    _cycleScroll.backgroundColor = [UIColor clearColor];
    _cycleScroll.bannerImageViewContentMode = UIViewContentModeScaleAspectFit;
    _cycleScroll.delegate = self;
    _cycleScroll.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
    [backScrollView addSubview:_cycleScroll];
    _cycleScroll.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    _cycleScroll.autoScrollTimeInterval = 3.0;//轮播时间间隔，默认1.0秒，可自定义
    _cycleScroll.currentPageDotColor = [UIColor whiteColor];
    _cycleScroll.pageDotColor = [[UIColor whiteColor] colorWithAlphaComponent:0.4];
    _cycleScroll.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
    _cycleScroll.imageURLStringsGroup = sliderMuArr;
    _cycleScroll.titlesGroup = titleArr;
    _cycleScroll.titleLabelBackgroundColor = [UIColor clearColor];
    _cycleScroll.titleLabelHeight = 40;
    _cycleScroll.titleLabelTextFont = SYS_Font(12);
    
    if (sysPageControl) {
        [sysPageControl removeFromSuperview];
        sysPageControl = nil;
    }
    sysPageControl  = [[UIPageControl alloc]initWithFrame:CGRectMake(_cycleScroll.left, _cycleScroll.bottom+2, 20, 5)];
    sysPageControl.pageIndicatorTintColor = [UIColor grayColor];
    sysPageControl.currentPageIndicatorTintColor = [UIColor whiteColor];
    sysPageControl.numberOfPages = sliderMuArr.count;
    sysPageControl.transform=CGAffineTransformScale(CGAffineTransformIdentity, 0.7, 0.7);
    [backScrollView addSubview:sysPageControl];
    sysPageControl.centerX = _cycleScroll.centerX;

}
/** 点击图片回调 */
- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index;
{
    NSLog(@"点击轮播----------index:%ld",index);
    if (index == 0) {
        YBWeakSelf;
        if (!_taskView) {
            _taskView = [[DayTaskView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)andLiveUid:[self.playDoc valueForKey:@"uid"]];
//            _taskView.liveUid =[self.playDoc valueForKey:@"uid"];

            _taskView.closeEvent = ^{
                [weakSelf.taskView removeFromSuperview];
                weakSelf.taskView = nil;
            };
            [self.view addSubview:_taskView];
        }
    }else if (index == 1) {
        [self showJackpotView];
    }else{
        [self doTurntable];
    }

}
/** 图片滚动回调 */
- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didScrollToIndex:(NSInteger)index;
{
    sysPageControl.currentPage = index;

}
#pragma mark ============奖池View=============
- (void)JackpotLevelUp:(NSDictionary *)dic{
}
- (void)WinningPrize:(NSDictionary *)dic{
//    if (winningView) {
//        [winningView removeFromSuperview];
//        winningView = nil;
//    }
//    winningView = [[WinningPrizeView alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight, _window_width, _window_width) andMsg:dic];
//    [self.view addSubview:winningView];
//    [self.view bringSubviewToFront:winningView];

}
- (void)showJackpotView{
    JackpotBtn.userInteractionEnabled = NO;
    if (jackV) {
        [jackV removeFromSuperview];
        jackV = nil;
    }
    [YBToolClass postNetworkWithUrl:@"Jackpot.getJackpot" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        JackpotBtn.userInteractionEnabled = YES;

        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            jackV = [[[NSBundle mainBundle]loadNibNamed:NSStringFromClass([JackpotView class]) owner:nil options:nil] lastObject];
            jackV.delegate = self;
            jackV.levelL.text = [NSString stringWithFormat:@"Lv.%@",minstr([infoDic valueForKey:@"level"])];
            jackV.coinL.text = minstr([infoDic valueForKey:@"total"]);
            jackV.frame = CGRectMake(_window_width*0.2, 135+statusbarHeight, _window_width*0.6+20, _window_width*0.6);
            [self.view addSubview:jackV];
        }else{
            [MBProgressHUD  showError:msg];
        }
    } fail:^{
        JackpotBtn.userInteractionEnabled = YES;
    }];
}
-(void)jackpotViewClose{
    [jackV removeFromSuperview];
    jackV = nil;
}
#pragma mark ============幸运礼物全站效果=============
- (void)showAllLuckygift:(NSDictionary *)dic{
    [setFrontV.luckyGift addLuckyGiftMove:dic];
}
#pragma mark ============显示用户弹窗选项卡=============
- (void)showButtleView:(NSString *)touid{
    for (YBUserLinkButton *btn in sitButtonArray) {
        if (btn.userDic && btn.userDic.count > 0) {
            if ([[btn.userDic valueForKey:@"uid"]isEqual:touid]) {
                isMicUser = YES;
            }
        }
    }

    buttleView = [[UserBulletWindow alloc]initWithUserID:touid andIsAnchor:NO andAnchorID:minstr([_playDoc valueForKey:@"uid"])];
    buttleView.delegate  = self;
    buttleView.isChatLive = @"1";
    buttleView.micUserBool = isMicUser;
    [self.view addSubview:buttleView];
}
//管理员把用户下麦
-(void)closeUpMic:(NSString *)touid
{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.closeUserVoiceMic"];
    
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":[self.playDoc valueForKey:@"stream"],
                          @"touid":touid,
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            [socketDelegate closeMicBySelf:@"2" andToUid:touid];
        }
        } Fail:^(id fail) {
            
    }];


}
- (void)removeButtleView{
    [buttleView removeFromSuperview];
    buttleView = nil;
}
#pragma mark ============充值=============
- (void)requestPayList{
    if (!payView) {
        [YBToolClass postNetworkWithUrl:@"User.GetBalance" andParameter:@{@"type":@"1"} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            if (code == 0) {
                NSDictionary *infoDic = [info firstObject];
                if (!payView) {
                    payView = [[roomPayView alloc]initWithMsg:infoDic andFrome:1];
                    [self.view addSubview:payView];
                }
                [payView show];
                [self.view bringSubviewToFront:payView];
            }
        } fail:^{
            
        }];
    }else{
        [payView show];
        [self.view bringSubviewToFront:payView];
        
    }
    
}
#pragma mark ============登录失效=============
- (void)denglushixiao{
    dispatch_async(dispatch_get_main_queue(), ^{
        [self lastView];
    });
}
#pragma mark ============转盘=============
- (void)doTurntable{
    if (!turntableV) {
        turntableV = [[turntableView alloc]init];
        turntableV.zhuboMsg = _playDoc;
        turntableV.delegate = self;
        [self.view addSubview:turntableV];
    }else{
        turntableV.hidden = NO;
        [turntableV show];
    }
}
- (void)turntableZhongjianla{
    if (giftview) {
        [giftview reloadBagView];
    }
}
#pragma mark ============店铺相关=============
- (void)showGoodsBtnAnimaition{
    CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
    //速度控制函数，控制动画运行的节奏
    animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    animation.duration = 0.7;       //执行时间
    animation.repeatCount = 99999999;      //执行次数
    animation.autoreverses = YES;    //完成动画后会回到执行动画之前的状态
    animation.fromValue = [NSNumber numberWithFloat:0.7];   //初始伸缩倍数
    animation.toValue = [NSNumber numberWithFloat:1.1];     //结束伸缩倍数
    [goodsShowBtn.imageView.layer addAnimation:animation forKey:nil];

}
- (void)showgoodsShowView{
    if (roomGoodsV) {
        [roomGoodsV removeFromSuperview];
        roomGoodsV = nil;
    }
    roomGoodsV = [[roomShowGoodsView alloc]initWithFrom:NO andZhuboMsg:_playDoc];
    [self.view addSubview:roomGoodsV];
    [socketDelegate showGoodsLiveFloat];
}

//点击购物车展示
-(void)showLiveGooodTips:(NSDictionary *)msg{
    NSString *userName = minstr([msg valueForKey:@"uname"]);
    NSDictionary *userDic = @{@"uname":userName};
    [_liveGoodTipArr addObject:userDic];
    [self beginShowLiveGoodTips];
    
}
-(void)beginShowLiveGoodTips{
    YBWeakSelf;
    if (_liveGoodTipArr.count > 0) {
        NSDictionary *infoDic = [_liveGoodTipArr firstObject];
        NSString *userName = minstr([infoDic valueForKey:@"uname"]);
        NSString*newStr = [userName substringWithRange:NSMakeRange(0,1)];
        NSString *ssss = [NSString stringWithFormat:@"***%@",YZMsg(@"前去购买")];
        newStr = [newStr stringByAppendingString:ssss];
        CGSize sizee = [PublicObj sizeWithString:newStr andFont:SYS_Font(14)];
        
        if (!_liveTipLabel) {
            _liveTipLabel = [[UILabel alloc]init];
            _liveTipLabel.frame = CGRectMake(_window_width, _tableView.frame.origin.y-50, sizee.width+20, 32);
            _liveTipLabel.font = [UIFont systemFontOfSize:14];
            _liveTipLabel.textColor = [UIColor whiteColor];
            _liveTipLabel.textAlignment = NSTextAlignmentCenter;
            _liveTipLabel.text = newStr;
            _liveTipLabel.layer.cornerRadius = 16;
            _liveTipLabel.layer.masksToBounds = YES;
            _liveTipLabel.backgroundColor = RGBA(248,152,38, 0.55);
            [setFrontV addSubview:_liveTipLabel];
            
            [UIView animateWithDuration:1.5 animations:^{
                _liveTipLabel.frame = CGRectMake(20, _tableView.frame.origin.y-50, sizee.width+20, 32);
            } completion:^(BOOL finished) {
                
                [UIView animateWithDuration:1 animations:^{
                    _liveTipLabel.frame = CGRectMake(0, _tableView.frame.origin.y-50, sizee.width+20, 32);

                } completion:^(BOOL finished) {
                    
                    [UIView animateWithDuration:1 animations:^{
                        _liveTipLabel.frame = CGRectMake(-_window_width, _tableView.frame.origin.y-50, sizee.width+20, 32);

                    } completion:^(BOOL finished) {
                        [_liveTipLabel removeFromSuperview];
                        _liveTipLabel = nil;
                        [_liveGoodTipArr removeObjectAtIndex:0];
                        [weakSelf beginShowLiveGoodTips];
                    }];
                }];
                
            }];
        }
    }

}

#pragma mark-------添加商品小窗口------------------

-(void)showLiveGoods:(NSDictionary *)liveGoods{
    YBWeakSelf;
     if (!_liveGoodsView) {
        _liveGoodsView = [[LiveGoodView alloc]initWithFrame:CGRectMake(_window_width + 10,0,tableWidth,90)];
         _liveGoodsView.delegate = weakSelf;
         _liveGoodsView.liveId = [self.playDoc valueForKey:@"uid"];
         [backScrollView insertSubview:_liveGoodsView atIndex:4];

    }
    [_liveGoodsView setDataInfo:liveGoods];
    [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 50 - ShowDiff];

}
-(void)showLiveGoodtapEvent
{
    [socketDelegate showGoodsLiveFloat];

}
#pragma mark ================ 检查房间类型 ===============
-(void)checkliveInfo:(NSDictionary *)liveinfo{
    [self onStopVideo];
    NSString *language = [PublicObj getCurrentLanguage];

    NSString *url = [purl stringByAppendingFormat:@"?service=Live.checkLive"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.timeoutInterval = 5.0;
    request.HTTPMethod = @"post";
    NSString *param = [NSString stringWithFormat:@"uid=%@&token=%@&liveuid=%@&stream=%@&language=%@",[Config getOwnID],[Config getOwnToken],minstr([liveinfo valueForKey:@"uid"]),minstr([liveinfo valueForKey:@"stream"]),language];
    request.HTTPBody = [param dataUsingEncoding:NSUTF8StringEncoding];
    NSURLResponse *response;
    NSError *error;
    NSData *backData = [NSURLConnection sendSynchronousRequest:request returningResponse:&response error:&error];
    if (error) {
        [MBProgressHUD showError:@"无网络"];
    }
    else{
        
        
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:backData options:NSJSONReadingMutableContainers error:nil];
        NSNumber *number = [dic valueForKey:@"ret"];
        
        if([number isEqualToNumber:[NSNumber numberWithInt:200]])
        {
            NSArray *data = [dic valueForKey:@"data"];
            NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
            if([code isEqual:@"0"])
            {
                NSDictionary *info = [[data valueForKey:@"info"] firstObject];
                NSString *type = [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];
                _type_val =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type_val"]];
                _livetype =  [NSString stringWithFormat:@"%@",[info valueForKey:@"type"]];

                _sdkType = minstr([info valueForKey:@"live_sdk"]);
                if ([type isEqual:@"0"]) {
                    //普通房间
//                    [self pushMovieVC];
                    [self playLiveRoom:liveinfo];
                }
                else if ([type isEqual:@"1"]){
                    YBWeakSelf;
                    NSString *_MD5 = [NSString stringWithFormat:@"%@",[info valueForKey:@"type_msg"]];

                    _alert = [[YBAlertView alloc]initWithTitle:YZMsg(@"提示") Msg:YZMsg(@"本房间为密码房间，请输入密码") LeftMsg:@"" RightMsg:@"" PlaceHodler:YZMsg(@"") Style:YBAlertPassWord];
                    if (_scrollindex == _scrollarray.count-1) {
                        [_alert.cancleBtn setTitleColor:[UIColor grayColor] forState:0];
                        _alert.cancleBtn.userInteractionEnabled = NO;
                    }else{
                        [_alert.cancleBtn setTitleColor:[UIColor grayColor] forState:0];
                        _alert.cancleBtn.userInteractionEnabled = YES;

                    }

                    _alert.actionEvent = ^(NSString * _Nonnull type, NSString * _Nonnull tipstr) {
                        //type 0下一个 2关闭。 1确定。 3 密码房间确定
                        if ([type isEqual:@"3"]) {
                            if (tipstr.length < 1) {
                                [MBProgressHUD showError:YZMsg(@"请输入密码")];
                                return ;
                            }else{
                                NSLog(@"你输入的文本%@",tipstr);
                                if ([_MD5 isEqualToString:[weakSelf stringToMD5:tipstr]]) {
                                    [weakSelf.alert removeFromSuperview];
                                    weakSelf.alert = nil;
                                    //进入密码房间
                                    [weakSelf playLiveRoom:liveinfo];
                                }else{
                                    [MBProgressHUD showError:YZMsg(@"密码错误")];
                                    return ;
                                }

                            }
                        }else if ([type isEqual:@"0"]){
                            [weakSelf.alert removeFromSuperview];
                            weakSelf.alert = nil;

                            _scrollindex+=1;
                            buttomimageviews = scrollImgArr[_scrollindex];
                            [weakSelf showAndHideImgView];

                            buttomscrollview.contentOffset = CGPointMake(0, _scrollindex*_window_height);
                            _playDoc = _scrollarray[_scrollindex];
                            [weakSelf changeRoom:_playDoc];
                            

                        }else if([type isEqual:@"2"]){
                            [weakSelf.alert removeFromSuperview];
                            weakSelf.alert = nil;
                            [weakSelf.navigationController popViewControllerAnimated:YES];
                            [weakSelf dismissViewControllerAnimated:NO completion:nil];

                        }
                    };
                    [self.view addSubview:_alert];
                }
                else if ([type isEqual:@"2"] || [type isEqual:@"3"]){

                    YBWeakSelf;
                    _alert = [[YBAlertView alloc]initWithTitle:YZMsg(@"提示") Msg:minstr([info valueForKey:@"type_msg"]) LeftMsg:@"" RightMsg:@"" PlaceHodler:@"" Style:YBAlertNormal];
                    if (_scrollindex == _scrollarray.count-1) {
                        [_alert.cancleBtn setTitleColor:[UIColor grayColor] forState:0];
                        _alert.cancleBtn.userInteractionEnabled = NO;
                    }else{
                        [_alert.cancleBtn setTitleColor:[UIColor grayColor] forState:0];
                        _alert.cancleBtn.userInteractionEnabled = YES;

                    }
                    _alert.actionEvent = ^(NSString * _Nonnull type, NSString * _Nonnull tipstr) {
                        //type 0下一个 2关闭。 1确定。 3 密码房间确定
                        if ([type isEqual:@"0"]) {
                            [weakSelf.alert removeFromSuperview];
                            weakSelf.alert = nil;

                            _scrollindex+=1;
                            buttomimageviews = scrollImgArr[_scrollindex];
                            [weakSelf showAndHideImgView];

                            buttomscrollview.contentOffset = CGPointMake(0, _scrollindex*_window_height);
                            _playDoc = _scrollarray[_scrollindex];
                            [weakSelf changeRoom:_playDoc];

                        }else if([type isEqual:@"1"]){
                            if ([[Config getOwnID] intValue] <= 0) {
                                [[YBToolClass sharedInstance]waringLogin];
                            }else{
                                [weakSelf doCoastRoomCharge:liveinfo];
                            }
                            [weakSelf.alert removeFromSuperview];
                            weakSelf.alert = nil;
                        }else if([type isEqual:@"2"]){
                            [weakSelf.alert removeFromSuperview];
                            weakSelf.alert = nil;
                            [weakSelf.navigationController popViewControllerAnimated:YES];
                            [weakSelf dismissViewControllerAnimated:NO completion:nil];

                        }
                    };
                    [self.view addSubview:_alert];
//                    //收费
//                    UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:minstr([info valueForKey:@"type_msg"]) preferredStyle:UIAlertControllerStyleAlert];
//                    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//                        [self.navigationController popViewControllerAnimated:YES];
//                        [self dismissViewControllerAnimated:NO completion:nil];
//
//                    }];
//                    [alertContro addAction:cancleAction];
//                    UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//                        [self doCoastRoomCharge:liveinfo];
//                    }];
//                    [alertContro addAction:sureAction];
//                        [self presentViewController:alertContro animated:YES completion:nil];
                }
                
            }
            else{
                NSString *msg = [NSString stringWithFormat:@"%@",[data valueForKey:@"msg"]];
                [MBProgressHUD showError:msg];
            }
        }
        
    }
    
}
- (NSString *)stringToMD5:(NSString *)str
{
    
    //1.首先将字符串转换成UTF-8编码, 因为MD5加密是基于C语言的,所以要先把字符串转化成C语言的字符串
    const char *fooData = [str UTF8String];
    
    //2.然后创建一个字符串数组,接收MD5的值
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    
    //3.计算MD5的值, 这是官方封装好的加密方法:把我们输入的字符串转换成16进制的32位数,然后存储到result中
    CC_MD5(fooData, (CC_LONG)strlen(fooData), result);
    /**
     第一个参数:要加密的字符串
     第二个参数: 获取要加密字符串的长度
     第三个参数: 接收结果的数组
     */
    
    //4.创建一个字符串保存加密结果
    NSMutableString *saveResult = [NSMutableString string];
    
    //5.从result 数组中获取加密结果并放到 saveResult中
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [saveResult appendFormat:@"%02x", result[i]];
    }
    /*
     x表示十六进制，%02X  意思是不足两位将用0补齐，如果多余两位则不影响
     NSLog("%02X", 0x888);  //888
     NSLog("%02X", 0x4); //04
     */
    return saveResult;
}



#pragma socketDelegate---------游戏-----------
//准备炸金花游戏
//*********************************************************************** 炸金花********************************//
-(void)stopGamendMethod:(NSString *)method andMsgtype:(NSString *)msgtype{
    
    
    
}
-(void)prepGameandMethod:(NSString *)method andMsgtype:(NSString *)msgtype{
    if (gameVC) {
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
    }
    gameVC = [[gameBottomVC alloc]initWIthDic:_playDoc andIsHost:NO andMethod:method andMsgtype:msgtype];
    gameVC.delagate = self;
    gameVC.frame = CGRectMake(_window_width, _window_height - 260, _window_width,260);
    [self changeBtnFrame:_window_height - 260];
    [backScrollView insertSubview:gameVC atIndex:4];
    [backScrollView insertSubview:_liwuBTN atIndex:5];
    [self tableviewheight:_window_height - _window_height*0.2 -260];
    [self changecontinuegiftframe];
    //上庄
    if ([method isEqual:@"startCattleGame"]) {
        if (!zhuangVC) {
            zhuangVC = [[shangzhuang alloc]initWithFrame:CGRectMake(_window_width + 10,90, _window_width/4, _window_width/4 + 20 + _window_width/8) ishost:NO withstreame:[self.playDoc valueForKey:@"stream"]];
            zhuangVC.deleagte = self;
            [backScrollView insertSubview:zhuangVC atIndex:10];
            [backScrollView bringSubviewToFront:zhuangVC];
            [zhuangVC addtableview];
            [zhuangVC getbanksCoin:zhuangstartdic];
        }
    }
}
//**************************************************上庄操作***********************

-(void)changeBank:(NSDictionary *)bankdic{
       [gameVC changebankid:[bankdic valueForKey:@"id"]];
       [zhuangVC getNewZhuang:bankdic];
}
-(void)getzhuangjianewmessagedelegatem:(NSDictionary *)subdic{
    
    [zhuangVC getNewZhuang:subdic];
    
}
-(void)takePoker:(NSString *)gameid Method:(NSString *)method andMsgtype:(NSString *)msgtype{
    if (!gameVC) {
        gameVC = [[gameBottomVC alloc]initWIthDic:_playDoc andIsHost:NO andMethod:method andMsgtype:msgtype];
        gameVC.delagate = self;
        gameVC.frame = CGRectMake(_window_width, _window_height - 260, _window_width,260);
        [self changeBtnFrame:_window_height - 260];
        [backScrollView insertSubview:gameVC atIndex:4];
        [backScrollView insertSubview:_liwuBTN atIndex:5];
    }
    //上庄
    if ([method isEqual:@"startCattleGame"]) {
        if (!zhuangVC) {
            zhuangVC = [[shangzhuang alloc]initWithFrame:CGRectMake(_window_width + 10,90, _window_width/4, _window_width/4 + 20 + _window_width/8) ishost:NO withstreame:[self.playDoc valueForKey:@"stream"]];
            zhuangVC.deleagte = self;
            [backScrollView insertSubview:zhuangVC atIndex:10];
            [zhuangVC getbanksCoin:zhuangstartdic];
            [zhuangVC addtableview];
        }
        [zhuangVC addPoker];
    }
    [self tableviewheight:_window_height - _window_height*0.2 -260 - www ];
    //wangminxinliwu
    [self changecontinuegiftframe];
    [gameVC createUI];
}
-(void)startGame:(NSString *)time andGameID:(NSString *)gameid{
    [self removeAllGames];
    [gameVC movieplayStartCut:time andGameid:gameid];
}
- (void)removeAllGames{
//    if (zhuangVC) {
//        [zhuangVC dismissroom];
//        [zhuangVC removeall];
//        [zhuangVC remopokers];
//        [zhuangVC removeFromSuperview];
//        zhuangVC = nil;
//    }
//    if (shell) {
//        [shell stopGame];
//        [shell releaseAll];
//        [shell removeFromSuperview];
//        shell = nil;
//    }
//    if (gameVC) {
//        [gameVC releaseAll];
//        [gameVC removeFromSuperview];
//        gameVC = nil;
//    }
//    if (rotationV) {
//        [rotationV stopRotatipnGameInt];
//        [rotationV stoplasttimer];
//        [rotationV removeFromSuperview];
//        [rotationV removeall];
//        rotationV = nil;
//    }
}
//得到游戏结果
-(void)getResult:(NSArray *)array{
    [gameVC getResult:array];
    if (zhuangVC) {
        [zhuangVC getresult:array];
    }
    
}
-(void)reloadcoinsdelegate{
    if (gameVC) {
        [gameVC reloadcoins];
    }
}
-(void)stopGame{
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    [gameVC removeFromSuperview];
    gameVC = nil;
    [self changeBtnFrame:_window_height - 45];
    [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 50 - ShowDiff];
    //wangminxinliwu
    [self changecontinuegiftframe];
}
//用户投注
-(void)skate:(NSString *)type andMoney:(NSString *)money andMethod:(NSString *)method andMsgtype:(NSString *)msgtype{
    [socketDelegate stakePoke:type andMoney:money andMethod:method andMsgtype:msgtype];
}
-(void)getCoin:(NSString *)type andMoney:(NSString *)money{
    [gameVC getCoinType:type andMoney:money];
}

//**********************************************************************转盘游戏
//关闭游戏
-(void)stopRotationGame{
    [self setbtnframe];
     [rotationV stopRotatipnGameInt];
     [rotationV stoplasttimer];
    [rotationV removeFromSuperview];
    [rotationV removeall];
    rotationV = nil;
    [self changeBtnFrame:_window_height - 45];
    [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 50 - ShowDiff];
    //wangminxinliwu
    [self changecontinuegiftframe];
}
//出现游戏界面
-(void)prepRotationGame{
    [self removeAllGames];
    [self getRotation];
}
-(void)getRotation{
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (!rotationV) {
        rotationV = [WPFRotateView rotateView];
        [rotationV setlayoutview];
        rotationV.delegate = self;
        [rotationV isHost:NO andHostDic:[_playDoc valueForKey:@"stream"]];
        rotationV.frame = CGRectMake(_window_width, _window_height - _window_width/1.5, _window_width, _window_width);
        [backScrollView insertSubview:rotationV atIndex:6];
        [backScrollView insertSubview:_liwuBTN atIndex:7];
        [rotationV addtableview];
    }
    rotationV.frame = CGRectMake(_window_width, _window_height - _window_width/1.5, _window_width, _window_width);
    [self changeBtnFrame:_window_height - 45 - _window_width/1.5];
    [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2- _window_width/1.5-50];
    
}
//开始倒计时
-(void)startRotationGame:(NSString *)time andGameID:(NSString *)gameid{
    [self getRotation];
    [rotationV movieplayStartCut:time andGameid:gameid];
}
//获取游戏结果
-(void)getRotationResult:(NSArray *)array{
    [rotationV getRotationResult:array];
}
//用户押注
-(void)skateRotaton:(NSString *)type andMoney:(NSString *)money{
    [socketDelegate stakeRotationPoke:type andMoney:money];
}
//更新押注数量
-(void)getRotationCoin:(NSString *)type andMoney:(NSString *)money{
    [rotationV getRotationCoinType:type andMoney:money];
}
//*****************************************************************************

//进入后台-----进入前台
- (void)appDidEnterBackground:(NSNotification *)not{
    if(_txLivePlayer){
        [_txLivePlayer setPlayoutVolume:0];
    }
}
- (void)appWillEnterForeground:(NSNotification*)note
{
    if(_txLivePlayer){
        [_txLivePlayer setPlayoutVolume:100];
    }
}

- (RKShowPaintedView *)paintedShowRegion {
    if (!_paintedShowRegion) {
        _paintedShowRegion = [[RKShowPaintedView alloc]init];
        _paintedShowRegion.frame = CGRectMake(_window_width, 0, _window_width, _window_height*0.6);
        //_paintedShowRegion.backgroundColor = UIColor.redColor;
    }
    return _paintedShowRegion;
}
-(V2TXLivePlayer *)txLivePlayer{
    if(!_txLivePlayer){
        _txLivePlayer = [[V2TXLivePlayer alloc] init];
        [_txLivePlayer setObserver:self];
        [_txLivePlayer enableObserveAudioFrame:YES];
        [_txLivePlayer setRenderFillMode:V2TXLiveFillModeFit];
    }
    return _txLivePlayer;
}
#pragma mark---liveplayObserver
- (void)onError:(id<V2TXLivePlayer>)player code:(V2TXLiveCode)code message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo;
{
    NSLog(@"liveplay-error");
}
- (void)onWarning:(id<V2TXLivePlayer>)player code:(V2TXLiveCode)code message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo;
{
    NSLog(@"liveplay-onWarning");
}
/**
 * 已经成功连接到服务器
 *
 * @param player    回调该通知的播放器对象。
 * @param extraInfo 扩展信息。
 */

- (void)onVideoPlaying:(id<V2TXLivePlayer>)player firstPlay:(BOOL)firstPlay extraInfo:(NSDictionary *)extraInfo;
{
    NSLog(@"liveplay-VideoPlaying");
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            setFrontV.bigAvatarImageView.hidden = YES;
            backScrollView.userInteractionEnabled = YES;
            backScrollView.contentSize = CGSizeMake(_window_width*2,0);
        });

}
/*
* 直播播放器分辨率变化通知
*
* @param player    回调该通知的播放器对象。
* @param width     视频宽。
* @param height    视频高。
*/
- (void)onVideoResolutionChanged:(id<V2TXLivePlayer>)player width:(NSInteger)width height:(NSInteger)height;
{
    
}
#pragma mark -------------在线人数--------------
-(void)onlineBtnClick{
    YBWeakSelf;
    _onlineView  =  [[OnlineUserView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)withInfoDic:self.playDoc];
    _onlineView.btnEvent = ^(NSString *status, NSDictionary *userDic) {
        if([status isEqual:@"关闭"]){
            [weakSelf.onlineView removeFromSuperview];
            weakSelf.onlineView = nil;
        }else{
            [weakSelf showButtleView:minstr([userDic valueForKey:@"id"])];
        }
    };

    [self.view addSubview:_onlineView];
}
#pragma mark ----游戏 星球探宝
-(void)sendXQTBResult:(NSNotification *)noti{
    NSArray *result_list = noti.object;
    [socketDelegate sendXQTBResult:result_list];
}
#pragma mark -游戏 大转盘
-(void)sendXYDZPResult:(NSNotification *)noti{
    NSArray *result_list = noti.object;
    [socketDelegate sendXYDZPResult:result_list];
}
-(void)showXQTBMsgList:(NSDictionary *)msg;
{
    NSArray *getList = [msg valueForKey:@"list"];
    for (int i = 0; i <getList.count;i ++ ){
        titleColor = @"firstlogin";
        NSString *ct;
        NSString *languageStr= [PublicObj getCurrentLanguage];
        if ([languageStr isEqual:@"en"]) {
            ct = minstr([getList[i] valueForKey:@"title_en"]);
        }else{
            ct = minstr([getList[i] valueForKey:@"title"]);
        }
        NSString *uname = YZMsg(@"直播间消息");
        NSString *ID = @"";
        NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",ID,@"id",titleColor,@"titleColor",nil];
        chat = [YBToolClass roomChatInsertTime:chat];
        [msgList addObject:chat];
        titleColor = @"0";
        if(msgList.count>30)
        {
            [msgList removeObjectAtIndex:0];
        }
        [self.tableView reloadData];
        [self jumpLast];

    }

}
-(void)showXYDZPMsgList:(NSDictionary *)msg
{
    NSArray *getList = [msg valueForKey:@"list"];
    for (int i = 0; i <getList.count;i ++ ){
        titleColor = @"firstlogin";
        NSString *ct;
        NSString *languageStr= [PublicObj getCurrentLanguage];
        if ([languageStr isEqual:@"en"]) {
            ct = minstr([getList[i] valueForKey:@"title_en"]);
        }else{
            ct = minstr([getList[i] valueForKey:@"title"]);
        }
        NSString *uname = YZMsg(@"直播间消息");
        NSString *ID = @"";
        NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",ID,@"id",titleColor,@"titleColor",nil];
        chat = [YBToolClass roomChatInsertTime:chat];
        [msgList addObject:chat];
        titleColor = @"0";
        if(msgList.count>30)
        {
            [msgList removeObjectAtIndex:0];
        }
        [self.tableView reloadData];
        [self jumpLast];

    }

}





#pragma mark -美狐回调
//-(void)MHBeautyBlock:(V2TXLiveVideoFrame *)srcFrame dstFrame:(V2TXLiveVideoFrame *)dstFrame
//{
//    dstFrame.textureId= [self.beautyManager getTextureProcessWithTexture:srcFrame.textureId width:(GLint)srcFrame.width height:(GLint)srcFrame.height mirror:YES];
//    dispatch_async(dispatch_get_main_queue(), ^{
//        if (self.menusView) {
//            if (!isLoadWebSprout) {
//                isLoadWebSprout = YES;
//                [self.menusView setupDefaultBeautyAndFaceValue];
//            }
//        }
//    });
//}
#pragma mark ================ TXVideoProcessDelegate ===============
- (MHBeautyManager *)beautyManager {
    if (!_beautyManager) {
        _beautyManager = [[MHBeautyManager alloc] init];
        [_beautyManager setWatermarkRect:CGRectMake(0.2, 0.1, 0.1, 0)];
    }
    return _beautyManager;
}
#pragma mark - MHMenuViewDelegate
- (void)beautyEffectWithLevel:(NSInteger)beauty whitenessLevel:(NSInteger)white ruddinessLevel:(NSInteger)ruddiness {
    //暂时用腾讯的美颜
//    _tx_beauty_level = 9;
//    _tx_whitening_level = 3;
    
//    NSString *mopi =  [sproutCommon getYBskin_smooth];//磨皮数值
//    NSString *white = [sproutCommon getYBskin_whiting];//美白数值
//    NSString *hongrun = [sproutCommon getYBskin_tenderness];//红润数值

//    [_txLivePublisher setBeautyStyle:0 beautyLevel:beauty whitenessLevel:white ruddinessLevel:ruddiness];
}
#pragma mark -美狐回调
-(void)MHBeautyBlock:(V2TXLiveVideoFrame *)srcFrame dstFrame:(V2TXLiveVideoFrame *)dstFrame;
{
    //暂时用腾讯的美颜
    float _tx_beauty_level = 9;
    float _tx_whitening_level = 3;
    [[YBLiveRTCManager shareInstance]setBeautyLevel:_tx_beauty_level WhitenessLevel:_tx_whitening_level];
//    GLuint dstTextureId = renderItemWithTexture(srcFrame.textureId, srcFrame.width, srcFrame.height);
    dstFrame.textureId =srcFrame.textureId;// dstTextureId;
}
#pragma mark -初始化美颜UI
- (void)initMeiyanFaceUI{
    _menusView = [[MHMeiyanMenusView alloc] initWithFrame:CGRectMake(0, window_height - MHMeiyanMenuHeight - BottomIndicatorHeight, window_width, MHMeiyanMenuHeight) superView:self.view  beautyManager:self.beautyManager];
}


- (void)onTextureDestoryed{
    NSLog(@"[self.tiSDKManager destroy];");
}


@end
