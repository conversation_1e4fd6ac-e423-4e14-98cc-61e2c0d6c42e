//
//  YBUserLinkButton.h
//  YBPlaying
//
//  Created by IOS1 on 2019/12/14.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import <UIKit/UIKit.h>
//#import "UserWindowsView.h"
#import <YYWebImage/YYWebImage.h>

@protocol ybUserLinkBtnDelegate <NSObject>

-(void)showUserInfo:(NSString *)uidStr;

@end

NS_ASSUME_NONNULL_BEGIN
typedef void(^singerShangmai)(NSString *sitid);
@interface YBUserLinkButton : UIView

- (instancetype)initWithFrame:(CGRect)frame andIsBoos:(BOOL)isBoos andRoomMessage:(NSDictionary *)dic;
@property (nonatomic,strong) NSDictionary *userDic;
@property (nonatomic,strong) NSDictionary *roomDic;
@property (nonatomic,strong) NSString *skillID;
@property (nonatomic,strong) NSString *streamStr;

@property (nonatomic,assign) BOOL isMute;
@property (nonatomic,strong) NSString *sitid;
@property (nonatomic,copy) singer<PERSON>hang<PERSON><PERSON> block;
@property (nonatomic, assign) id<ybUserLinkBtnDelegate>delegate;
@property (nonatomic,assign) BOOL isHeardSelected;
@property (strong, nonatomic)YYAnimatedImageView *animationImageView;
@property (strong, nonatomic)NSString *imgUrl;
- (void)showXindongA;
- (void)hideXindongA;
- (void)showAnimation;
- (void)hideAnimation;
- (void)removeUserMessage;
-(void)changeLinkBtnStatus:(NSString *)type;

@end

NS_ASSUME_NONNULL_END
