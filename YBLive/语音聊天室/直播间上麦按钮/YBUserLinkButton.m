//
//  YBUserLinkButton.m
//  YBPlaying
//
//  Created by IOS1 on 2019/12/14.
//  Copyright © 2019 IOS1. All rights reserved.
//

#import "YBUserLinkButton.h"
#import "UserBulletWindow.h"
@interface YBUserLinkButton ()<UserBulletWindowDelegate>{
    BOOL _isBoos;
    UserBulletWindow *buttleView;
}
@property (nonatomic,strong) UIImageView *animationImgView;
@property (nonatomic,strong) UIImageView *iconImgView;
@property (nonatomic,strong) UIImageView *jingyinImageView;
@property (nonatomic,strong) UILabel *nameL;
@property (nonatomic,strong) UIImageView *hostImageView;
@property(nonatomic,strong)UIView *nameBgView;
@property (nonatomic,strong) UIImageView *sexImgV;
@property (nonatomic,strong) UILabel *numL;
@property (nonatomic,strong) UIImageView *xindongImgView;
@end

@implementation YBUserLinkButton

- (instancetype)initWithFrame:(CGRect)frame andIsBoos:(BOOL)isBoos andRoomMessage:(NSDictionary *)dic{
    if (self = [super initWithFrame:frame]) {
        _isBoos = isBoos;
        _isMute = YES;
        _roomDic = dic;
        [self creatUI];
    }
    return self;
}
- (void)creatUI{
    UIImageView *animationImgView = [[UIImageView alloc]init];
    [self addSubview:animationImgView];
    [animationImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.width.left.equalTo(self);
        make.height.equalTo(animationImgView.mas_width);
    }];
    animationImgView.animationImages = @[[UIImage imageNamed:@"voice_ani0"],[UIImage imageNamed:@"voice_ani1"],[UIImage imageNamed:@"voice_ani2"]];
    animationImgView.animationDuration = 1;
    _animationImgView = animationImgView;
    
    UIImageView *iconImgView = [[UIImageView alloc]init];
    [self addSubview:iconImgView];
    [iconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(animationImgView);
        make.width.equalTo(animationImgView.mas_width).offset(-20);
        make.height.equalTo(iconImgView.mas_width);
    }];
    _iconImgView = iconImgView;
    
    UIImageView *hostImageView = [[UIImageView alloc]init];
    hostImageView.hidden = YES;
    hostImageView.image = [UIImage imageNamed:getImagename(@"room_主持")];
    [self addSubview:hostImageView];
    [hostImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(iconImgView.mas_bottom);
        make.centerX.equalTo(iconImgView);
        make.width.mas_equalTo(44);
        make.height.mas_equalTo(16);
    }];
    _hostImageView = hostImageView;
    CGFloat gggggg = [[YBToolClass sharedInstance] widthOfString:YZMsg(@"男神位") andFont:SYS_Font(10) andHeight:14];
    _jingyinImageView =[[UIImageView alloc]init];
    _jingyinImageView.image = [UIImage imageNamed:@"chatjingyin"];
    [self addSubview:_jingyinImageView];
    [_jingyinImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-10);
        make.centerY.equalTo(iconImgView).offset(20);
        make.width.height.mas_equalTo(16);
    }];
    _jingyinImageView.hidden = YES;
    
    _sexImgV = [[UIImageView alloc]init];
    _sexImgV.contentMode = UIViewContentModeScaleAspectFill;
    _sexImgV.clipsToBounds = YES;
    _sexImgV.layer.cornerRadius = 7.0;
    _sexImgV.layer.masksToBounds = YES;
    [self addSubview:_sexImgV];
    [_sexImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(animationImgView.mas_bottom).offset(-1);
        make.centerX.equalTo(self);
        make.height.mas_equalTo(14);
        make.width.mas_equalTo(gggggg + 23);
    }];
    
    UILabel *label = [[UILabel alloc]init];
    label.font = SYS_Font(10);
    [self addSubview:label];
    _nameL = label;
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(animationImgView.mas_bottom).offset(-1);
        make.centerX.equalTo(self);
        make.width.lessThanOrEqualTo(self);
    }];
    label.text = YZMsg(@"等待上麦");
    label.textColor = RGB_COLOR(@"#c8c8c8", 1);
    _iconImgView.image = [UIImage imageNamed:@"room_等待上麦"];
    _animationImageView = [[YYAnimatedImageView alloc]init];
    _animationImageView.hidden = YES;
    [self addSubview:_animationImageView];
    [_animationImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(animationImgView);
        make.width.equalTo(self.mas_width);//.offset(-20);
        make.height.equalTo(self.mas_width);
    }];

    UIButton *btn = [UIButton buttonWithType:0];
    [btn addTarget:self action:@selector(doUserWindows) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.width.height.equalTo(self);
    }];
}
-(void)setImgUrl:(NSString *)imgUrl
{
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(animationEnd) object:nil];
    NSURL *url =[[NSBundle mainBundle] URLForResource:imgUrl withExtension:@"gif"];
    _animationImageView.hidden = NO;
    _animationImageView.yy_imageURL = url;
    [self performSelector:@selector(animationEnd) withObject:nil afterDelay:4];
}
-(void)animationEnd{
    _animationImageView.hidden = YES;
}
- (void)setUserDic:(NSDictionary *)userDic{
    _userDic = userDic;
    [self layoutIfNeeded];
    _iconImgView.layer.cornerRadius = _iconImgView.width/2;
    _iconImgView.layer.masksToBounds = YES;
    _iconImgView.layer.borderWidth = 1;
    _nameL.textColor = [UIColor whiteColor];
    _iconImgView.layer.borderColor = [UIColor whiteColor].CGColor;
    if (userDic.count > 0) {
        [_iconImgView sd_setImageWithURL:[NSURL URLWithString:minstr([userDic valueForKey:@"avatar"])]];
        _nameL.text = minstr([userDic valueForKey:@"user_nickname"]);
    }else{
        _iconImgView.image = [UIImage imageNamed:@"room_等待上麦"];
        _nameL.text = YZMsg(@"等待上麦");
    }
}

-(void)changeLinkBtnStatus:(NSString *)type{
    [self layoutIfNeeded];
    if ([type isEqual:@"2"]) {
        _iconImgView.image = [UIImage imageNamed:@"suomai"];
    }else if ([type isEqual:@"0"]){
        _iconImgView.image = [UIImage imageNamed:@"room_等待上麦"];

    }else if ([type isEqual:@"1"]){
        _jingyinImageView.hidden = YES;

    }else if ([type isEqual:@"-1"]){
        _jingyinImageView.hidden = NO;

    }
}

- (void)setSitid:(NSString *)sitid{
    _sitid = sitid;
    if ([minstr([_roomDic valueForKey:@"type"]) isEqual:@"2"]) {
        _numL.text = sitid;
        if ([sitid intValue] == 3 || [sitid intValue] == 4 || [sitid intValue] == 7 || [sitid intValue] == 8) {
            _numL.textColor = RGB_COLOR(@"#FF78FF", 1);
            _sexImgV.image = [UIImage imageNamed:@"room_女神位"];
            _nameL.text = YZMsg(@"女神位");
        }else{
            _numL.textColor = RGB_COLOR(@"#53B6EA", 1);
            _sexImgV.image = [UIImage imageNamed:@"room_男神位"];
            _nameL.text = YZMsg(@"男神位");
        }
    }
}
- (void)removeUserMessage{
    _userDic = nil;
    if ([minstr([_roomDic valueForKey:@"type"]) isEqual:@"2"]) {
        _iconImgView.image = [UIImage imageNamed:@"room_等待上麦"];
        _numL.backgroundColor = [UIColor whiteColor];
        if ([_sitid intValue] == 3 || [_sitid intValue] == 4 || [_sitid intValue] == 7 || [_sitid intValue] == 8) {
            _numL.textColor = RGB_COLOR(@"#FF78FF", 1);
            _nameL.text = YZMsg(@"女神位");
        }else{
            _nameL.text = YZMsg(@"男神位");
            _numL.textColor = RGB_COLOR(@"#53B6EA", 1);
        }
        _sexImgV.hidden = NO;
    }else{
        if (_isBoos) {
            _nameL.text = YZMsg(@"老板位");
            _nameL.textColor = RGB_COLOR(@"#FFEA00", 1);
            _iconImgView.image = [UIImage imageNamed:@"room_老板位"];
        }else{
            _nameL.text = YZMsg(@"等待上麦");
            _nameL.textColor = RGB_COLOR(@"#c8c8c8", 1);
            _iconImgView.image = [UIImage imageNamed:@"room_等待上麦"];
        }
    }
}
- (void)doUserWindows{
    if (_userDic && [_userDic count] > 0) {
        if ([self.delegate respondsToSelector:@selector(showUserInfo:)]) {
            [self.delegate showUserInfo:minstr([_userDic valueForKey:@"uid"])];
        }
    }
}
- (void)showXindongA{
    if (_xindongImgView) {
        _xindongImgView.hidden = NO;
        [_xindongImgView startAnimating];
    }
}
- (void)hideXindongA{
    if (_xindongImgView) {
        _xindongImgView.hidden = YES;
        [_xindongImgView stopAnimating];
    }
}

- (void)showAnimation{
    [_animationImgView startAnimating];
}
- (void)hideAnimation{
    [_animationImgView stopAnimating];
    if (_xindongImgView) {
        [self hideXindongA];
    }
}

@end
