//
//  ChatVideoUserLinkView.m
//  YBLive
//
//  Created by y<PERSON><PERSON><PERSON> on 2024/1/24.
//  Copyright © 2024 cat. All rights reserved.
//

#import "ChatVideoUserLinkView.h"

@interface ChatVideoUserLinkView ()
{
    UIButton *isMuteBtn;
    UIButton *nameBtn;
    UIButton *bgBtn;
    UILabel *subLb;
    UIImageView *waitImg;
    UIImageView *isBossImg;
    UIView *frontView;
}
@end

@implementation ChatVideoUserLinkView

-(instancetype)initWithFrame:(CGRect)frame andIsBoss:(BOOL)isBoss andRoomMessage:(NSDictionary *)dic{
    self = [super initWithFrame:frame];
    if (self) {
        _roomDic = dic;
        _isBoss = isBoss;
        self.layer.borderWidth = 1;
        self.layer.borderColor = RGBA(245, 245, 245, 0.5).CGColor;
        self.layer.masksToBounds = YES;
        [self creatUI];

    }
    return self;
}
-(void)creatUI{
    bgBtn = [UIButton buttonWithType:0];
    [bgBtn setBackgroundColor:RGBA(1, 1, 1, 0.3)];
    [self addSubview:bgBtn];
    [bgBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.right.equalTo(self);
    }];
    waitImg = [[UIImageView alloc]init];
    waitImg.image = [UIImage imageNamed:@"chatvideowait"];
    [bgBtn addSubview:waitImg];
    [waitImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(bgBtn);
        make.centerY.equalTo(bgBtn).offset(-14);
        make.width.height.mas_equalTo(30);
    }];
    subLb = [[UILabel alloc]init];
    subLb.text = YZMsg(@"等待上麦");
    subLb.font = [UIFont systemFontOfSize:14];
    subLb.textColor = UIColor.whiteColor;
    [bgBtn addSubview:subLb];
    [subLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(bgBtn);
        make.top.equalTo(waitImg.mas_bottom).offset(5);
    }];
    
    _livePushView = [[UIView alloc]init];
    _livePushView.backgroundColor =RGBA(1, 1, 1, 0.3);
    _livePushView.hidden = YES;
    [self addSubview:_livePushView];
    [_livePushView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.right.equalTo(self);
    }];
    frontView =[[UIView alloc]init];
    frontView.backgroundColor = UIColor.clearColor;
    frontView.hidden = YES;
    [self addSubview:frontView];
    [frontView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.right.equalTo(self);
    }];

    
    isBossImg = [[UIImageView alloc]init];
    isBossImg.contentMode = UIViewContentModeScaleAspectFit;
    isBossImg.image = [UIImage imageNamed:@"livevideo_fz"];
    [frontView addSubview:isBossImg];
    [isBossImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(_livePushView).offset(7);
        make.width.height.mas_equalTo(16);
    }];
    isBossImg.hidden = !_isBoss;
    
    isMuteBtn = [UIButton buttonWithType:0];
    [isMuteBtn setImage:[UIImage imageNamed:@"chatopen"] forState:0];
    [isMuteBtn setImage:[UIImage imageNamed:@"chatclose"] forState:UIControlStateSelected];
    [frontView addSubview:isMuteBtn];
    [isMuteBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mas_right).offset(-7);
        make.top.equalTo(self).offset(4);
        make.width.height.mas_equalTo(24);
    }];
    
    nameBtn = [UIButton buttonWithType:0];
    [nameBtn setBackgroundColor:RGBA(255, 255, 255, 0.7)];
    nameBtn.titleLabel.font =[UIFont systemFontOfSize:11];
    nameBtn.layer.cornerRadius = 9;
    nameBtn.layer.masksToBounds = YES;
    [nameBtn setTitle: [Config getOwnNicename] forState:0];
    [nameBtn setTitleColor:UIColor.darkGrayColor forState:0];
    [nameBtn setContentEdgeInsets:UIEdgeInsetsMake(0, 10, 0, 10)];
    [frontView addSubview:nameBtn];
    [nameBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(5);
        make.bottom.equalTo(self.mas_bottom).offset(-5);
        make.height.mas_equalTo(18);
    }];
    UIButton *btn = [UIButton buttonWithType:0];
    [btn addTarget:self action:@selector(doUserWindows) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.width.height.equalTo(self);
    }];
    
    _animationImageView = [[YYAnimatedImageView alloc]init];
    _animationImageView.hidden = YES;
    [self addSubview:_animationImageView];
    [_animationImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.width.height.equalTo(self);
    }];
}
-(void)setImgUrl:(NSString *)imgUrl
{
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(animationEnd) object:nil];
    NSURL *url =[[NSBundle mainBundle] URLForResource:imgUrl withExtension:@"gif"];
    _animationImageView.hidden = NO;
    _animationImageView.yy_imageURL = url;
    [self performSelector:@selector(animationEnd) withObject:nil afterDelay:4];
}
-(void)animationEnd{
    _animationImageView.hidden = YES;
}
- (void)setUserDic:(NSDictionary *)userDic{
    _userDic = userDic;
    [self layoutIfNeeded];
    if (minstr([userDic valueForKey:@"avatar"]).length > 0 &&![minstr([userDic valueForKey:@"avatar"]) isEqual:@"0"]) {
        bgBtn.hidden = YES;
        _livePushView.hidden = NO;
        frontView.hidden = NO;
        [nameBtn setTitle: minstr([userDic valueForKey:@"user_nickname"]) forState:0];

    }else{
        bgBtn.hidden = NO;
        _livePushView.hidden = YES;
        frontView.hidden = YES;

    }
}
-(void)setIsBoss:(BOOL)isBoss
{
    _isBoss = isBoss;
    isBossImg.hidden = !_isBoss;

}
- (void)removeUserMessage{
    _userDic = nil;
    bgBtn.hidden = NO;
    _livePushView.hidden = YES;
}
-(void)changeLinkBtnStatus:(NSString *)type{
    [self layoutIfNeeded];
    if ([type isEqual:@"2"]) {
        subLb.text = YZMsg(@"禁止上麦");
    }else if ([type isEqual:@"0"]){
        subLb.text = YZMsg(@"等待上麦");

    }else if ([type isEqual:@"1"]){
        isMuteBtn.selected  = NO;

    }else if ([type isEqual:@"-1"]){
        isMuteBtn.selected  = YES;
    }
}
- (void)doUserWindows{
    if (_userDic && [_userDic count] > 0) {
        if ([minstr([_userDic valueForKey:@"id"]) isEqual:@"0"]) {
            return;
        }
        if ([self.delegate respondsToSelector:@selector(showUserInfo:)]) {
            [self.delegate showUserInfo:minstr([_userDic valueForKey:@"uid"])];
        }
    }
}

@end
