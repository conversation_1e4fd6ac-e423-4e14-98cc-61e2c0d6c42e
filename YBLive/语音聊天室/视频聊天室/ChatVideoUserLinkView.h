//
//  ChatVideoUserLinkView.h
//  YBLive
//
//  Created by y<PERSON><PERSON><PERSON> on 2024/1/24.
//  Copyright © 2024 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol ybUserVideoLinkBtnDelegate <NSObject>

-(void)showUserInfo:(NSString *)uidStr;

@end


@interface ChatVideoUserLinkView : UIView
@property (nonatomic,strong) NSDictionary *userDic;
@property (nonatomic,strong) NSDictionary *roomDic;
@property (nonatomic,strong) UIView *livePushView;
@property (nonatomic,strong) NSString *streamStr;
@property (nonatomic,strong) NSString *pull;
@property (nonatomic,strong) NSString *sitid;
@property (nonatomic,assign) BOOL isBoss;

// 大表情
@property (strong, nonatomic)YYAnimatedImageView *animationImageView;
@property (strong, nonatomic)NSString *imgUrl;

@property (nonatomic, assign) id<ybUserVideoLinkBtnDelegate>delegate;


-(instancetype)initWithFrame:(CGRect)frame andIsBoss:(BOOL)isBoss andRoomMessage:(NSDictionary *)dic;
-(void)changeLinkBtnStatus:(NSString *)type;

@end


