//
//  ChatApplyView.h
//  YBLive
//
//  Created by ybRRR on 2021/1/4.
//  Copyright © 2021 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol chatAppViewDelegate <NSObject>

-(void)agreeApplyMicWithDic:(NSDictionary *)dic andPosition:(NSString *)position;
-(void)rejectApplyMicWithDic:(NSDictionary *)dic;

@end

typedef void(^ApplyEvent)();

@interface ChatApplyView : UIView

@property (nonatomic, copy)ApplyEvent hideEvent;
@property (nonatomic, assign)id<chatAppViewDelegate>delegate;
-(instancetype)initWithFrame:(CGRect)frame andStream:(NSString *)streamStr;
@end

