<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="KGk-i7-Jjw" customClass="ChatApplyCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eSK-X2-duO">
                        <rect key="frame" x="12" y="21.5" width="15" height="17"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="15" id="0Zj-Ec-7RR"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Pqf-VG-p5M">
                        <rect key="frame" x="32" y="10" width="40" height="40"/>
                        <color key="backgroundColor" systemColor="groupTableViewBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="40" id="G4f-hF-Iyp"/>
                            <constraint firstAttribute="width" constant="40" id="bWv-wd-Zjh"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="20"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gRH-18-fg6">
                        <rect key="frame" x="75" y="10" width="35.5" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8og-0r-kQq">
                        <rect key="frame" x="258" y="20" width="50" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="50" id="Kob-fI-h6V"/>
                            <constraint firstAttribute="height" constant="20" id="Qgb-Kp-NBv"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <state key="normal" title="拒绝">
                            <color key="titleColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </state>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" red="0.58823529409999997" green="0.58823529409999997" blue="0.58823529409999997" alpha="1" colorSpace="calibratedRGB"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="1"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                        <connections>
                            <action selector="rejectBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="gLe-pf-6vW"/>
                        </connections>
                    </button>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TLz-bB-JYp">
                        <rect key="frame" x="198" y="20" width="50" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="50" id="GGn-Wr-Gxo"/>
                            <constraint firstAttribute="height" constant="20" id="UUJ-GB-4Uo"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <state key="normal" title="上麦"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" systemColor="linkColor"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="1"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                        <connections>
                            <action selector="upMicBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="p8G-lG-OlL"/>
                        </connections>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="giH-WR-94b">
                        <rect key="frame" x="8" y="58" width="304" height="1"/>
                        <color key="backgroundColor" systemColor="groupTableViewBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="k1c-dF-y4d"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hnP-M3-1Io">
                        <rect key="frame" x="75" y="34" width="18" height="16"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="18" id="EWW-PS-lXc"/>
                            <constraint firstAttribute="height" constant="16" id="wku-cS-out"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="swP-cF-7FH">
                        <rect key="frame" x="101" y="34.5" width="24" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="I6t-Gz-hvq"/>
                            <constraint firstAttribute="width" constant="24" id="ew2-Bj-UGE"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="Pqf-VG-p5M" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="51Q-uC-mkb"/>
                    <constraint firstItem="Pqf-VG-p5M" firstAttribute="leading" secondItem="eSK-X2-duO" secondAttribute="trailing" constant="5" id="Aee-cw-0dP"/>
                    <constraint firstAttribute="trailing" secondItem="giH-WR-94b" secondAttribute="trailing" constant="8" id="Awv-4P-dMh"/>
                    <constraint firstItem="8og-0r-kQq" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="MMx-he-xS7"/>
                    <constraint firstItem="gRH-18-fg6" firstAttribute="top" secondItem="Pqf-VG-p5M" secondAttribute="top" id="Ner-UD-Cd1"/>
                    <constraint firstItem="eSK-X2-duO" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="12" id="Nfd-L3-pWm"/>
                    <constraint firstItem="gRH-18-fg6" firstAttribute="leading" secondItem="Pqf-VG-p5M" secondAttribute="trailing" constant="3" id="Q2R-Ww-krW"/>
                    <constraint firstItem="TLz-bB-JYp" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="QI7-eW-lPm"/>
                    <constraint firstItem="giH-WR-94b" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="UX6-Ku-NHr"/>
                    <constraint firstItem="8og-0r-kQq" firstAttribute="leading" secondItem="TLz-bB-JYp" secondAttribute="trailing" constant="10" id="VUZ-HN-r4w"/>
                    <constraint firstAttribute="bottom" secondItem="giH-WR-94b" secondAttribute="bottom" constant="1" id="dao-9X-VpV"/>
                    <constraint firstItem="swP-cF-7FH" firstAttribute="leading" secondItem="hnP-M3-1Io" secondAttribute="trailing" constant="8" id="fmh-NE-wGb"/>
                    <constraint firstItem="eSK-X2-duO" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="ijR-iW-Gs8"/>
                    <constraint firstAttribute="trailing" secondItem="8og-0r-kQq" secondAttribute="trailing" constant="12" id="j5K-27-XV0"/>
                    <constraint firstItem="hnP-M3-1Io" firstAttribute="bottom" secondItem="Pqf-VG-p5M" secondAttribute="bottom" id="kj3-vt-bxm"/>
                    <constraint firstItem="swP-cF-7FH" firstAttribute="centerY" secondItem="hnP-M3-1Io" secondAttribute="centerY" id="rU9-8q-qlx"/>
                    <constraint firstItem="hnP-M3-1Io" firstAttribute="leading" secondItem="Pqf-VG-p5M" secondAttribute="trailing" constant="3" id="uMS-ee-dmp"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="indexLb" destination="eSK-X2-duO" id="p3n-Sh-yIw"/>
                <outlet property="levelImg" destination="swP-cF-7FH" id="sSR-R7-0Ww"/>
                <outlet property="nameLb" destination="gRH-18-fg6" id="6pk-X9-YUb"/>
                <outlet property="rejectBtn" destination="8og-0r-kQq" id="RWR-xY-NfQ"/>
                <outlet property="sexImg" destination="hnP-M3-1Io" id="qR2-Ga-8TP"/>
                <outlet property="thumbImg" destination="Pqf-VG-p5M" id="tyk-WV-qYQ"/>
                <outlet property="upMicBtn" destination="TLz-bB-JYp" id="w0R-Cx-Lf4"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="103.79464285714285"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="groupTableViewBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="groupTableViewBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="linkColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
