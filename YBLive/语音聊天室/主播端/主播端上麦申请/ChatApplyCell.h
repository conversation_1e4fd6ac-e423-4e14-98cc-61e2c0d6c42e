//
//  ChatApplyCell.h
//  YBLive
//
//  Created by ybRRR on 2021/1/4.
//  Copyright © 2021 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^cellBtnEvent)(NSDictionary *currentDic, NSString *state);

@interface ChatApplyCell : UITableViewCell

@property (nonatomic, strong)NSDictionary *cellData;
@property (nonatomic, strong)NSString *roomStream;
@property (nonatomic, copy)cellBtnEvent cellEvent;

@property (weak, nonatomic) IBOutlet UILabel *indexLb;
@property (weak, nonatomic) IBOutlet UIImageView *thumbImg;
@property (weak, nonatomic) IBOutlet UILabel *nameLb;
@property (weak, nonatomic) IBOutlet UIImageView *sexImg;
@property (weak, nonatomic) IBOutlet UIImageView *levelImg;
@property (weak, nonatomic) IBOutlet UIButton *rejectBtn;
@property (weak, nonatomic) IBOutlet UIButton *upMicBtn;

+(ChatApplyCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;
@end

