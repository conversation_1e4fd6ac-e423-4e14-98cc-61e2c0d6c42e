//
//  ChatApplyCell.m
//  YBLive
//
//  Created by ybRRR on 2021/1/4.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ChatApplyCell.h"

@implementation ChatApplyCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [_upMicBtn setTitle:YZMsg(@"上麦") forState:0];
    [_rejectBtn setTitle:YZMsg(@"拒绝") forState:0];

}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(ChatApplyCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath{
   ChatApplyCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ChatApplyCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"ChatApplyCell" owner:nil options:nil]objectAtIndex:0];
    }
    return cell;
}
-(void)setCellData:(NSDictionary *)cellData
{
    _cellData = cellData;
    [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr([cellData valueForKey:@"avatar"])]];
    self.nameLb.text = minstr([cellData valueForKey:@"user_nickname"]);
    //性别 1男
     if ([[cellData valueForKey:@"sex"] isEqual:@"1"])
    {
        self.sexImg.image = [UIImage imageNamed:@"sex_man"];
    }
    else
    {
        self.sexImg.image = [UIImage imageNamed:@"sex_woman"];
    }
    //级别
    NSDictionary *userLevel = [common getUserLevelMessage:minstr([cellData valueForKey:@"level"])];
    [self.levelImg sd_setImageWithURL:[NSURL URLWithString:minstr([userLevel valueForKey:@"thumb"])]];

}
- (IBAction)upMicBtnClick:(UIButton *)sender {
    
    if (self.cellEvent) {
        self.cellEvent(_cellData, @"1");
    }
    

    
}
- (IBAction)rejectBtnClick:(UIButton *)sender {
    if (self.cellEvent) {
        self.cellEvent(_cellData, @"0");
    }

}
@end
