//
//  ChatApplyView.m
//  YBLive
//
//  Created by ybRRR on 2021/1/4.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ChatApplyView.h"
#import "ChatApplyCell.h"
@interface ChatApplyView ()<UITableViewDelegate, UITableViewDataSource,UIGestureRecognizerDelegate>
{
    UILabel *titleLb;
    UIView *backview ;
    
    NSString *stream;
}
@property (nonatomic, strong)UITableView *tableview;
@property (nonatomic, strong)NSArray *listArr;
@end

@implementation ChatApplyView

-(instancetype)initWithFrame:(CGRect)frame andStream:(NSString *)streamStr
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        _listArr = [NSArray array];
        stream = streamStr;
        UITapGestureRecognizer *hideTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideSelf)];
        hideTap.delegate = self;
        [self addGestureRecognizer:hideTap];
        [self createUI];
        [self getApplyList];
    }
    return self;
}
-(void)hideSelf{
    if (self.hideEvent) {
        self.hideEvent();
    }
}
-(void)getApplyList{
        YBWeakSelf;
        NSString *url = [purl stringByAppendingFormat:@"?service=Live.getVoiceMicApplyList"];
        
        NSDictionary *dic = @{
                              @"uid":[Config getOwnID],
                              @"token":[Config getOwnToken],
                              @"stream":stream
                              };

        [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
                NSLog(@"userRoom-------:%@",data);
                if ([code isEqual:@"0"]) {
                    NSDictionary *infos = [[data valueForKey:@"info"] firstObject];
                    NSArray *arr = [infos valueForKey:@"apply_list"];
                    weakSelf.listArr = arr;

                    titleLb.text = [NSString stringWithFormat:@"%@(%d)",YZMsg(@"当前上麦申请"),arr.count];

                    [weakSelf.tableview reloadData];
                }
            } Fail:^(id fail) {
                
        }];
}


-(void)createUI{
    backview = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height *0.6, _window_width, _window_height *0.4)];
    backview.backgroundColor = [UIColor whiteColor];
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:backview.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)];
           CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
           maskLayer.frame = backview.bounds;
           maskLayer.path = maskPath.CGPath;
           backview.layer.mask = maskLayer;
    [self addSubview:backview];
    titleLb = [[UILabel alloc]init];
    titleLb.frame = CGRectMake(10, 10, _window_width-20, 20);
    titleLb.font = [UIFont systemFontOfSize:14];
    titleLb.textColor = [UIColor blackColor];
    titleLb.textAlignment = NSTextAlignmentCenter;
    titleLb.text = YZMsg(@"当前上麦申请()");
    [backview addSubview:titleLb];
    
    [[YBToolClass sharedInstance]lineViewWithFrame:CGRectMake(0, titleLb.bottom +9, _window_width, 1) andColor:RGB(238, 238, 238) andView:backview];
    
    [backview addSubview:self.tableview];
}

-(UITableView *)tableview
{
    if (!_tableview) {
        _tableview = [[UITableView alloc]initWithFrame:CGRectMake(0, titleLb.bottom+10, _window_width, backview.height-40) style:UITableViewStylePlain];
        _tableview.delegate = self;
        _tableview.dataSource = self;
        _tableview.separatorStyle = UITableViewCellSeparatorStyleNone;
        
    }
    return _tableview;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.listArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 60;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    YBWeakSelf;
    ChatApplyCell *cell = [ChatApplyCell cellWithTab:tableView andIndexPath:indexPath];
    cell.indexLb.text = [NSString stringWithFormat:@"%ld",indexPath.row+1];
    cell.cellData = self.listArr[indexPath.row];
    cell.cellEvent = ^(NSDictionary *currentDic, NSString *state) {
        [weakSelf handleVoiceMicApply:currentDic andStatus:state];
    };
    return cell;
}
-(void)handleVoiceMicApply:(NSDictionary *)dic andStatus:(NSString *)Status{
    //status（0 拒绝 1 同意）
    YBWeakSelf;
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.handleVoiceMicApply"];
    
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":stream,
                          @"apply_uid":minstr([dic valueForKey:@"id"]),
                          @"status":Status
                          };

    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            NSLog(@"chatappplyview----:%@",data);
            [self hideSelf];
            if ([Status isEqual:@"0"]) {
                if ([self.delegate respondsToSelector:@selector(rejectApplyMicWithDic:)]) {
                    [self.delegate rejectApplyMicWithDic:dic];
                }
            }else{
                NSDictionary *infos = [data valueForKey:@"info"];
                if ([self.delegate respondsToSelector:@selector(agreeApplyMicWithDic:andPosition:)]) {
                    [self.delegate agreeApplyMicWithDic:dic andPosition:minstr([infos valueForKey:@"position"])];
                }
            }
        }
        
        } Fail:^(id fail) {
            
    }];

}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch{
    NSLog(@"s0000000000:%@",NSStringFromClass([touch.view class]));
    if ([NSStringFromClass([touch.view class]) isEqual:@"UIView"] || [NSStringFromClass([touch.view class]) isEqual:@"UITableViewCellContentView"]) {
        return NO;
    }
    return YES;
}

@end
