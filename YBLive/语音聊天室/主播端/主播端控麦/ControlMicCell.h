//
//  ControlMicCell.h
//  YBLive
//
//  Created by ybRRR on 2021/1/11.
//  Copyright © 2021 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^cellBtnStautChange)(NSString *status, NSDictionary *cellDic);

@interface ControlMicCell : UITableViewCell
@property (weak, nonatomic) IBOutlet UILabel *indeLb;
@property (weak, nonatomic) IBOutlet UIImageView *thumbImg;
@property (weak, nonatomic) IBOutlet UILabel *nameLb;
@property (weak, nonatomic) IBOutlet UIImageView *sexImg;
@property (weak, nonatomic) IBOutlet UIImageView *levelImg;
@property (weak, nonatomic) IBOutlet UIButton *downMicBtn;
@property (weak, nonatomic) IBOutlet UIButton *leftBtn;

@property (nonatomic, strong)NSDictionary *cellData;
@property (nonatomic, strong)NSString *liveStream;
@property (nonatomic, copy) cellBtnStautChange statusChange;

+(ControlMicCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath;
@end
