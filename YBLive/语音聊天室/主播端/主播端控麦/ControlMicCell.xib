<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="KGk-i7-Jjw" customClass="ControlMicCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kEm-tg-Olv">
                        <rect key="frame" x="16" y="21.5" width="30" height="17"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="30" id="BSM-x2-8e6"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <color key="highlightedColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chatuserdefault.png" translatesAutoresizingMaskIntoConstraints="NO" id="G2w-9H-0ta">
                        <rect key="frame" x="46" y="10" width="40" height="40"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="40" id="4Zr-RH-wSM"/>
                            <constraint firstAttribute="width" constant="40" id="ynd-py-tXp"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="20"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SKp-WB-9BD">
                        <rect key="frame" x="94" y="10" width="0.0" height="0.0"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="90m-z4-B6Z">
                        <rect key="frame" x="94" y="34" width="18" height="16"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="18" id="JnG-VT-1xf"/>
                            <constraint firstAttribute="height" constant="16" id="Xbm-Bf-1IU"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="IYP-gf-34H">
                        <rect key="frame" x="115" y="34" width="21" height="16"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="16" id="3wr-8u-KAr"/>
                            <constraint firstAttribute="width" constant="21" id="i8k-pD-vKM"/>
                        </constraints>
                    </imageView>
                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NnB-jY-jym">
                        <rect key="frame" x="258" y="20" width="50" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="NuD-bX-LIc"/>
                            <constraint firstAttribute="width" constant="50" id="UjX-1I-fLa"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <state key="normal" title="下麦">
                            <color key="titleColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </state>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="1"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                        <connections>
                            <action selector="rightBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="lPM-Na-yZk"/>
                        </connections>
                    </button>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="anB-Vz-Xnx">
                        <rect key="frame" x="200" y="20" width="50" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="Yv0-jk-ZMt"/>
                            <constraint firstAttribute="width" constant="50" id="paA-fu-Yag"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <state key="normal" title="禁麦">
                            <color key="titleColor" red="0.29411764705882354" green="0.59607843137254901" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        </state>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="1"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" red="0.29411764705882354" green="0.59607843137254901" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                        <connections>
                            <action selector="leftBtnClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="RnP-l0-ZHj"/>
                        </connections>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3jJ-LA-H1T">
                        <rect key="frame" x="6" y="58" width="308" height="1"/>
                        <color key="backgroundColor" systemColor="systemGray5Color"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="wyZ-tT-MLR"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="3jJ-LA-H1T" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="6" id="2iU-fW-HRu"/>
                    <constraint firstAttribute="trailing" secondItem="3jJ-LA-H1T" secondAttribute="trailing" constant="6" id="2no-Wn-kbW"/>
                    <constraint firstItem="IYP-gf-34H" firstAttribute="centerY" secondItem="90m-z4-B6Z" secondAttribute="centerY" id="5hY-g4-FQd"/>
                    <constraint firstItem="NnB-jY-jym" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="9va-4S-0qW"/>
                    <constraint firstItem="IYP-gf-34H" firstAttribute="leading" secondItem="90m-z4-B6Z" secondAttribute="trailing" constant="3" id="EkS-Mg-ta4"/>
                    <constraint firstItem="SKp-WB-9BD" firstAttribute="top" secondItem="G2w-9H-0ta" secondAttribute="top" id="ItW-nt-GVp"/>
                    <constraint firstAttribute="trailing" secondItem="NnB-jY-jym" secondAttribute="trailing" constant="12" id="KBL-Zt-SaK"/>
                    <constraint firstItem="90m-z4-B6Z" firstAttribute="bottom" secondItem="G2w-9H-0ta" secondAttribute="bottom" id="Mwl-OK-SBJ"/>
                    <constraint firstItem="SKp-WB-9BD" firstAttribute="leading" secondItem="G2w-9H-0ta" secondAttribute="trailing" constant="8" id="Nsu-eg-F7T"/>
                    <constraint firstItem="NnB-jY-jym" firstAttribute="leading" secondItem="anB-Vz-Xnx" secondAttribute="trailing" constant="8" id="U7u-VJ-9xe"/>
                    <constraint firstAttribute="bottom" secondItem="3jJ-LA-H1T" secondAttribute="bottom" constant="1" id="Uaf-Br-SdA"/>
                    <constraint firstItem="anB-Vz-Xnx" firstAttribute="centerY" secondItem="NnB-jY-jym" secondAttribute="centerY" id="dCD-b4-gY3"/>
                    <constraint firstItem="kEm-tg-Olv" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="16" id="e1b-dM-CJF"/>
                    <constraint firstItem="90m-z4-B6Z" firstAttribute="leading" secondItem="SKp-WB-9BD" secondAttribute="leading" id="iZx-5f-F86"/>
                    <constraint firstItem="kEm-tg-Olv" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="nY4-11-pAT"/>
                    <constraint firstItem="G2w-9H-0ta" firstAttribute="leading" secondItem="kEm-tg-Olv" secondAttribute="trailing" id="tam-qD-ml7"/>
                    <constraint firstItem="G2w-9H-0ta" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="yeG-bI-pSw"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="downMicBtn" destination="NnB-jY-jym" id="2G5-OE-ToH"/>
                <outlet property="indeLb" destination="kEm-tg-Olv" id="nqI-Qx-E8c"/>
                <outlet property="leftBtn" destination="anB-Vz-Xnx" id="xWS-Qn-5oD"/>
                <outlet property="levelImg" destination="IYP-gf-34H" id="tvC-yM-23o"/>
                <outlet property="nameLb" destination="SKp-WB-9BD" id="3RC-SC-TJf"/>
                <outlet property="sexImg" destination="90m-z4-B6Z" id="Bi7-su-6fb"/>
                <outlet property="thumbImg" destination="G2w-9H-0ta" id="Asc-MJ-mR4"/>
            </connections>
            <point key="canvasLocation" x="128.98550724637681" y="75"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="chatuserdefault.png" width="40" height="40"/>
        <systemColor name="systemGray5Color">
            <color red="0.89803921568627454" green="0.89803921568627454" blue="0.91764705882352937" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
