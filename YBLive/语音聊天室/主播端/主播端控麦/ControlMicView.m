//
//  ControlMicView.m
//  YBLive
//
//  Created by ybRRR on 2021/1/4.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ControlMicView.h"
#import "ChatApplyCell.h"
#import "ControlMicCell.h"
@interface ControlMicView ()<UITableViewDelegate, UITableViewDataSource,UIGestureRecognizerDelegate>
{
    UILabel *titleLb;
    UIView *backview ;
    NSString *stramStr;
    BOOL _isVoiceRoom;
    NSString *_sdkTypeStr;
}
@property (nonatomic, strong)UITableView *tableview;
@property (nonatomic, strong)NSArray *listArr;
@end

@implementation ControlMicView

-(void)requstAnchorGetVoiceMicList{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.anchorGetVoiceMicList"];
    
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":stramStr,
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        
        if ([code isEqual:@"0"]) {
            _listArr = [data valueForKey:@"info"];
            [self.tableview reloadData];
        }
        
        } Fail:^(id fail) {
            
    }];

}

-(instancetype)initWithFrame:(CGRect)frame withStream:(NSString *)stream isVoiceRoom:(BOOL)isVoice andSdkType:(NSString *)sdkType
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        _listArr = [NSArray array];
        stramStr = stream;
        _isVoiceRoom = isVoice;
        _sdkTypeStr = sdkType;
        UITapGestureRecognizer *hideTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideSelf)];
        hideTap.delegate = self;
        [self addGestureRecognizer:hideTap];
        [self createUI];
        [self requstAnchorGetVoiceMicList];
    }
    return self;
}
-(void)hideSelf{
    if (self.hideEvent) {
        self.hideEvent();
    }
}
-(void)createUI{
    if ([_sdkTypeStr isEqual:@"1"]) {
        backview = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height *0.2, _window_width, _window_height *0.8)];
    }else{
        if (_isVoiceRoom) {
            backview = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height *0.2, _window_width, _window_height *0.8)];
        }else{
            backview = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height *0.3, _window_width, _window_height *0.7)];
        }
    }
    backview.backgroundColor = [UIColor whiteColor];
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:backview.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)];
           CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
           maskLayer.frame = backview.bounds;
           maskLayer.path = maskPath.CGPath;
           backview.layer.mask = maskLayer;
    [self addSubview:backview];
    titleLb = [[UILabel alloc]init];
    titleLb.frame = CGRectMake(10, 10, _window_width-20, 20);
    titleLb.font = [UIFont systemFontOfSize:14];
    titleLb.textColor = [UIColor blackColor];
    titleLb.textAlignment = NSTextAlignmentCenter;
    titleLb.text = YZMsg(@"控麦");
    [backview addSubview:titleLb];
    
    [[YBToolClass sharedInstance]lineViewWithFrame:CGRectMake(0, titleLb.bottom +9, _window_width, 1) andColor:RGB(238, 238, 238) andView:backview];
    
    [backview addSubview:self.tableview];
}

-(UITableView *)tableview
{
    if (!_tableview) {
        _tableview = [[UITableView alloc]initWithFrame:CGRectMake(0, titleLb.bottom+10, _window_width, backview.height-40) style:UITableViewStylePlain];
        _tableview.delegate = self;
        _tableview.dataSource = self;
        _tableview.separatorStyle = UITableViewCellSeparatorStyleNone;
        
    }
    return _tableview;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return _listArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 60;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    YBWeakSelf;
    ControlMicCell *cell = [ControlMicCell cellWithTab:tableView andIndexPath:indexPath];
    cell.indeLb.text = [NSString stringWithFormat:@"%ld",indexPath.row+1];
    NSDictionary *micCellData = _listArr[indexPath.row];
    cell.cellData = micCellData;
    cell.liveStream = stramStr;
    if(!_isVoiceRoom && [minstr([micCellData valueForKey:@"uid"]) isEqual:[Config getOwnID]]){
        cell.downMicBtn.hidden = YES;
    }
    if ([[micCellData valueForKey:@"mic_status"] isEqual:@"0"]) {
        cell.leftBtn.hidden = YES;
    }
    cell.statusChange = ^(NSString *status, NSDictionary *cellDic) {
        if([status isEqual:@"0"]){
            if ([minstr([cellDic valueForKey:@"mic_status"])isEqual:@"2"]) {
                status = @"0";
            }else{
                status = @"2";
            }

            NSDictionary *statusDic = @{@"status":status,@"position":minstr([cellDic valueForKey:@"position"]),@"uid":@"0"};
            if ([self.delegate respondsToSelector:@selector(changeMicStatus:)]) {
                [self.delegate changeMicStatus:statusDic];
            }

        } else if ([status isEqual:@"1"]) {
            //下麦
            if ([self.delegate respondsToSelector:@selector(closeUserMic:)]) {
                [self.delegate closeUserMic:cellDic];
            }
            
        }else if ([status isEqual:@"2"]){
            //开麦、闭麦
            if ([minstr([cellDic valueForKey:@"mic_status"])isEqual:@"1"]) {
                status = @"-1";
            }else{
                status = @"1";
            }

            NSDictionary *statusDic = @{@"status":status,@"position":minstr([cellDic valueForKey:@"position"]),@"uid":minstr([cellDic valueForKey:@"id"])};
            if ([self.delegate respondsToSelector:@selector(changeMicStatus:)]) {
                [self.delegate changeMicStatus:statusDic];
            }
        }
        [weakSelf requstAnchorGetVoiceMicList];

    };
    return cell;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch{
    NSLog(@"s0000000000:%@",NSStringFromClass([touch.view class]));
    if ([NSStringFromClass([touch.view class]) isEqual:@"UIView"]) {
        return NO;
    }
    return YES;
}

@end
