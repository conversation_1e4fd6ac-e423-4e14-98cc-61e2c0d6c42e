//
//  ControlMicCell.m
//  YBLive
//
//  Created by ybRRR on 2021/1/11.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ControlMicCell.h"

@implementation ControlMicCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    [_downMicBtn setTitle:YZMsg(@"下麦") forState:0];
    [_leftBtn setTitle:YZMsg(@"禁麦") forState:0];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(ControlMicCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath{
    ControlMicCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ControlMicCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"ControlMicCell" owner:nil options:nil]objectAtIndex:0];
    }
    return cell;
}
-(void)setCellData:(NSDictionary *)cellData
{
    //mic_status   麦位的状态 -1 关麦；  0无人； 1开麦 ； 2 禁麦；
    //position  麦位 0表示第一位
    _cellData = cellData;
    if ([minstr([_cellData valueForKey:@"mic_status"])isEqual:@"2"]) {
        [self.leftBtn setTitle:YZMsg(@"取消禁麦") forState:0];
        [self.leftBtn setBackgroundColor:RGBA(73,152,247,1)];
//        self.leftBtn.layer.borderColor =RGBA(73,152,247,1).CGColor;
        [self.leftBtn setTitleColor:[UIColor whiteColor] forState:0];
    }else if ([minstr([_cellData valueForKey:@"mic_status"])isEqual:@"-1"]){
        [self.leftBtn setTitle:YZMsg(@"开麦") forState:0];
    }else if ([minstr([_cellData valueForKey:@"mic_status"])isEqual:@"1"]){
        [self.leftBtn setTitle:YZMsg(@"闭麦") forState:0];
        [self.leftBtn setTitleColor:[UIColor whiteColor] forState:0];
        [self.leftBtn setBackgroundColor:RGBA(73,152,247,1)];

    }
    if ([minstr([_cellData valueForKey:@"mic_status"])isEqual:@"0"] || [minstr([_cellData valueForKey:@"mic_status"])isEqual:@"2"]) {
        self.downMicBtn.hidden = YES;
    }else{
        [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr([_cellData valueForKey:@"avatar"])]];
        self.nameLb.text = minstr([_cellData valueForKey:@"user_nickname"]);
        //性别 1男
         if ([[cellData valueForKey:@"sex"] isEqual:@"1"])
        {
            self.sexImg.image = [UIImage imageNamed:@"sex_man"];
        }
        else
        {
            self.sexImg.image = [UIImage imageNamed:@"sex_woman"];
        }
        //级别
        NSDictionary *userLevel = [common getUserLevelMessage:minstr([cellData valueForKey:@"level"])];
        [self.levelImg sd_setImageWithURL:[NSURL URLWithString:minstr([userLevel valueForKey:@"thumb"])]];

        self.downMicBtn.hidden = NO;
   }
}
- (IBAction)rightBtnClick:(UIButton *)sender {
    [self closeUserVoiceMic];
}
- (IBAction)leftBtnClick:(UIButton *)sender {
    if ([minstr([_cellData valueForKey:@"mic_status"])isEqual:@"0"] || [minstr([_cellData valueForKey:@"mic_status"])isEqual:@"2"]) {
        [self changeVoiceEmptyMicStatus];
    }else if ([minstr([_cellData valueForKey:@"mic_status"])isEqual:@"1"] || [minstr([_cellData valueForKey:@"mic_status"])isEqual:@"-1"]){
        [self changeVoiceMicStatus];
    }
}
//语音聊天室主播对空麦位设置禁麦或取消禁麦
-(void)changeVoiceEmptyMicStatus{
    NSString *status;
    if ([minstr([_cellData valueForKey:@"mic_status"])isEqual:@"2"]) {
        status = @"1";
    }else{
        status = @"0";
    }
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.changeVoiceEmptyMicStatus"];
    
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":self.liveStream,
                          @"position":minstr([_cellData valueForKey:@"position"]),
                          @"status":status
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            if (self.statusChange) {
                self.statusChange(@"0",_cellData);
            }
        }
        
        } Fail:^(id fail) {
            
    }];

}
//语音聊天室主播或管理员将连麦用户下麦
-(void)closeUserVoiceMic{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.closeUserVoiceMic"];
    
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":self.liveStream,
                          @"touid":minstr([_cellData valueForKey:@"id"]),
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            if (self.statusChange) {
                self.statusChange(@"1",_cellData);
            }
        }
        
        } Fail:^(id fail) {
            
    }];

}
//主播对麦位上的用户开麦/闭麦
-(void)changeVoiceMicStatus{
    NSString *status;
    if ([minstr([_cellData valueForKey:@"mic_status"])isEqual:@"1"]) {
        status = @"0";
    }else{
        status = @"1";
    }
    
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.changeVoiceMicStatus"];
    
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":self.liveStream,
                          @"position":minstr([_cellData valueForKey:@"position"]),
                          @"status":status
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            if (self.statusChange) {
                self.statusChange(@"2",_cellData);
            }
        }
        
        } Fail:^(id fail) {
            
    }];

}
@end
