//
//  ControlMicView.h
//  YBLive
//
//  Created by ybRR<PERSON> on 2021/1/4.
//  Copyright © 2021 cat. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol ControllMicViewDelegate <NSObject>

-(void)closeUserMic:(NSDictionary *)userDic;
-(void)changeMicStatus:(NSDictionary *)micDic;
@end

typedef void(^ApplyEvent)();

@interface ControlMicView : UIView
@property (nonatomic, copy)ApplyEvent hideEvent;
@property (nonatomic, assign)id<ControllMicViewDelegate>delegate;
-(instancetype)initWithFrame:(CGRect)frame withStream:(NSString *)stream  isVoiceRoom:(BOOL)isVoice andSdkType:(NSString *)sdkType;

@end

