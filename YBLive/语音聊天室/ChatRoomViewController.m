//
//  ChatRoomViewController.m
//  YBLive
//
//  Created by ybRRR on 2020/12/24.
//  Copyright © 2020 cat. All rights reserved.
//

#import "ChatRoomViewController.h"
#import "startLiveClassVC.h"

/***********************  腾讯SDK start ********************/
#import <TXLiteAVSDK_Professional/V2TXLivePlayer.h>
#import <TXLiteAVSDK_Professional/TXLivePush.h>
#import <TXLiteAVSDK_Professional/TXLiveBase.h>
#import <CWStatusBarNotification/CWStatusBarNotification.h>
#import "V8HorizontalPickerView.h"

#import <CoreTelephony/CTCallCenter.h>
#import <CoreTelephony/CTCall.h>
#import <MHBeautySDK/MHBeautyManager.h>
#import <SSZipArchive/SSZipArchive.h>
#import "sproutCommon.h"
#import "OrderMessageVC.h"
#import "ChatBottomView.h"
#import "ChatApplyView.h"
#import "ControlMicView.h"
#import "YBUserLinkButton.h"
#import "roomPayView.h"
#import "TChatC2CController.h"
#import "THeader.h"
#import "YBLiveRTCManager.h"
#import "OnlineUserView.h"
#import "ChatVideoUserLinkView.h"
/********************  MHSDK添加 开始 ********************/
#import "MHMeiyanMenusView.h"
#import <MHBeautySDK/MHBeautyManager.h>
#import "MHBeautyParams.h"
/********************  MHSDK添加 结束 ********************/

#define upViewW  _window_width*0.8
@import CoreTelephony;

@interface ChatRoomViewController ()<UITextViewDelegate,UITableViewDataSource,UITableViewDelegate,UIActionSheetDelegate,UITextFieldDelegate,catSwitchDelegate,haohuadelegate,socketLiveDelegate,listDelegate,tx_play_linkmic,UIImagePickerControllerDelegate,UINavigationControllerDelegate,UITextViewDelegate,adminDelegate,guardShowDelegate,anchorOnlineDelegate,redListViewDelegate,anchorPKViewDelegate,anchorPKAlertDelegate,TXVideoCustomProcessDelegate,TXLivePushListener,V8HorizontalPickerViewDelegate,V8HorizontalPickerViewDataSource,JackpotViewDelegate,UserBulletWindowDelegate,NSURLSessionDelegate,SSZipArchiveDelegate,platDelgate,gameDelegate,WPFRotateViewDelegate,shangzhuangdelegate,gameselected,SDCycleScrollViewDelegate,chatAppViewDelegate,ControllMicViewDelegate,TXLivePlayListener,ybUserLinkBtnDelegate,sendGiftDelegate,V2TIMConversationListener,YBLiveRTCDelegate,V2TXLivePlayerObserver,ybUserVideoLinkBtnDelegate,YbAgoraDelegate>

@property (nonatomic,strong)UIView *preFrontView;
//@property(nonatomic, strong) GPUImageView *gpuPreviewView;
//@property (nonatomic, strong) GPUImageStillCamera *videoCamera;
//@property (nonatomic, strong) CIImage *outputImage;
//@property (nonatomic, assign) size_t outputWidth;
//@property (nonatomic, assign) size_t outputheight;

/***********************  腾讯SDK start **********************/
//@property TXLivePushConfig* txLivePushonfig;
//@property TXLivePush*       txLivePublisher;

//@property (nonatomic,strong)UIView     *vBeauty;
@property (nonatomic,strong)CTCallCenter     *callCenter;

/***********************  腾讯SDK end **********************/
@property (nonatomic,strong)OnlineUserView *onlineView;

@property (nonatomic, strong)ChatBottomView *bottomView;
@property (nonatomic, strong)ChatApplyView *chatapplyView;
@property (nonatomic, strong)ControlMicView *controlmicView;
@property (nonatomic, strong)ChatVideoUserLinkView *anchorLinkView;

/********************  MHSDK添加 开始 ********************/
@property (nonatomic, strong) MHMeiyanMenusView *menusView;
@property (nonatomic, strong) MHBeautyManager *beautyManager;
/******************** MHSDK添加 结束 ********************/
@property (nonatomic,strong)UIView     *vBeauty;

@end

@implementation ChatRoomViewController

{
    NSMutableArray *msgList;
    CGSize roomsize;
    UILabel *roomID;
    //预览的视图
    UIButton *preThumbBtn;//上传封面按钮
    UILabel *thumbLabel;//上传封面状态的label
    UIImage *thumbImage;
    UILabel *liveClassLabel;
    UITextView *liveTitleTextView;
    UILabel *textPlaceholdLabel;
    
    NSMutableArray *preShareBtnArray;//分享按钮数组
    NSString *selectShareName;//选择分享的名称
    
//    UIScrollView *roomTypeView;//选择房间类型
//    NSMutableArray *roomTypeBtnArray;//房间类型按钮
//    NSString *roomType;//房间类型
//    NSString *roomTypeValue;//房间价值
    
    NSString *liveClassID;
    //预览的视图结束
    //直播时长
    UIView *liveTimeBGView;
    UILabel *liveTimeLabel;
    int liveTime;
    NSTimer *liveTimer;
    BOOL isTorch;
    
    /***********************  腾讯SDK start **********************/
    float  _tx_beauty_level;
    float  _tx_whitening_level;
    float  _tx_eye_level;
    float  _tx_face_level;
    UIButton              *_beautyBtn;
    UIButton              *_filterBtn;
    UILabel               *_beautyLabel;
    UILabel               *_whiteLabel;
    UILabel               *_bigEyeLabel;
    UILabel               *_slimFaceLabel;
    UISlider              *_sdBeauty;
    UISlider              *_sdWhitening;
    UISlider              *_sdBigEye;
    UISlider              *_sdSlimFace;

    V8HorizontalPickerView  *_filterPickerView;
    NSInteger    _filterType;

    /***********************  腾讯SDK end **********************/
    UIImageView *loactionImgView;
    UILabel *locationLabel;
    BOOL locationSwitch;

    UIButton *openGoodsCar;
    
    BOOL isMirror;
    OrderMessageVC *orderChat;
    
    NSString *live_thumb;
    BOOL _needScale;
    
    
    NSMutableArray *sitUserArray;
    NSMutableArray <YBUserLinkButton *>*sitButtonArray;
    NSMutableArray <ChatVideoUserLinkView *>*sitVideoButtonArray;

    NSMutableArray *livePlayerArray;
    int linkCount;
    
    roomPayView *payView;
    BOOL isMicUser;
    NSString *thumbUrlStr;

    UIButton *voiceChatBtn;
    UIButton *videoChatBtn;
    
    BOOL isVoiceChat;
}
#pragma mark ================ tiuivew代理 ===============
-(void)showPreFrontView{
    if (_preFrontView) {
        _preFrontView.hidden = NO;
    }
}
#pragma mark ============定位开关=============
- (void)locationSwitchBtnClick{
    if ([locationLabel.text isEqual:YZMsg(@"开定位")]) {
        loactionImgView.image = [UIImage imageNamed:@"pre_location"];
        locationLabel.text = [cityDefault getMyCity];
        locationSwitch = YES;
    }else{
        UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"关闭定位，直播不会被附近的人看到，直播间人数可能会减少，确认关闭吗？") preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            locationSwitch = YES;
        }];
        [cancleAction setValue:[UIColor blackColor] forKey:@"_titleTextColor"];

        [alertContro addAction:cancleAction];
        UIAlertAction *sureAction = [UIAlertAction actionWithTitle:YZMsg(@"坚决关闭") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            loactionImgView.image = [UIImage imageNamed:@"pre_location_off"];
            locationLabel.text = YZMsg(@"开定位");
            locationSwitch = NO;
         }];
        [sureAction setValue:normalColors forKey:@"_titleTextColor"];
        [alertContro addAction:sureAction];
        [self presentViewController:alertContro animated:YES completion:nil];

    }
}
#pragma mark ================ 直播开始之前的预览 ===============
- (void)creatPreFrontView{
    _preFrontView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    _preFrontView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:_preFrontView];
    
    UIImageView *backImg = [[UIImageView alloc]init];
    backImg.frame = CGRectMake(0, 0, _window_width, _window_height);
    backImg.image = [UIImage imageNamed:@"chatbackgroundImg"];
    backImg.contentMode = UIViewContentModeScaleAspectFill;
    [_preFrontView addSubview:backImg];

    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hidePreTextView)];
    [_preFrontView addGestureRecognizer:tap];
    
    loactionImgView = [[UIImageView alloc]initWithFrame:CGRectMake(10, 42+statusbarHeight, 16, 16)];
    loactionImgView.image = [UIImage imageNamed:@"pre_location"];
    [_preFrontView addSubview:loactionImgView];
    UIView *locationLabelView = [[UIView alloc]init];
    locationLabelView.backgroundColor = RGB_COLOR(@"#000000", 0.3);
    locationLabelView.layer.cornerRadius = 8;
    locationLabelView.layer.masksToBounds = YES;
    [_preFrontView addSubview:locationLabelView];
    [locationLabelView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(loactionImgView);
        make.left.equalTo(loactionImgView.mas_right).offset(-8);
        make.height.mas_equalTo(16);
    }];

    locationLabel = [[UILabel alloc]init];
    locationLabel.font = [UIFont systemFontOfSize:11];
    locationLabel.textColor = [UIColor whiteColor];
    [locationLabelView addSubview:locationLabel];
    [locationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(locationLabelView);
        make.height.mas_equalTo(16);
        make.left.equalTo(locationLabelView).offset(10);
        make.right.equalTo(locationLabelView).offset(-10);
    }];
    [[RKLBSManager shareManager]startLocation];
    [[RKLBSManager shareManager]locationUpdae:^(NSString *Province, NSString *City, NSString *District) {
        locationLabel.text = [cityDefault getMyCity];
    }];

    [_preFrontView insertSubview:locationLabelView belowSubview:loactionImgView];
    UIButton *locationSwitchBtn = [UIButton buttonWithType:0];
    [locationSwitchBtn addTarget:self action:@selector(locationSwitchBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [_preFrontView addSubview:locationSwitchBtn];
    [locationSwitchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.height.equalTo(loactionImgView);
        make.right.equalTo(locationLabelView);
    }];
    locationSwitch = YES;
    
    
    UIButton *preCloseBtn = [UIButton buttonWithType:0];
    preCloseBtn.frame = CGRectMake(_window_width-30, loactionImgView.top, loactionImgView.height, loactionImgView.height);
    [preCloseBtn setImage:[UIImage imageNamed:@"live_close"] forState:0];
    [preCloseBtn addTarget:self action:@selector(doClosePreView) forControlEvents:UIControlEventTouchUpInside];
    [_preFrontView addSubview:preCloseBtn];

    
    
    UIView *preMiddleView = [[UIView alloc]initWithFrame:CGRectMake(10, _window_height/2-(_window_width-20)*38/60, _window_width-20, (_window_width-20)*38/60)];
    preMiddleView.backgroundColor = RGB_COLOR(@"#0000000", 0.3);
    preMiddleView.layer.cornerRadius = 5;
    [_preFrontView addSubview:preMiddleView];
    
    preThumbBtn = [UIButton buttonWithType:0];
    preThumbBtn.frame = CGRectMake(10, preMiddleView.height*2/24, preMiddleView.height*10/19, preMiddleView.height*10/19);
    [preThumbBtn setImage:[UIImage imageNamed:@"pre_uploadThumb"] forState:0];
    [preThumbBtn addTarget:self action:@selector(doUploadPicture) forControlEvents:UIControlEventTouchUpInside];
    preThumbBtn.layer.cornerRadius = 5.0;
    preThumbBtn.layer.masksToBounds = YES;
    [preMiddleView addSubview:preThumbBtn];
    thumbLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, preThumbBtn.height*0.7, preThumbBtn.width, preThumbBtn.height/4)];
    thumbLabel.textColor = [UIColor whiteColor];//RGB_COLOR(@"#c8c8c8", 1);
    thumbLabel.textAlignment = NSTextAlignmentCenter;
    thumbLabel.text = YZMsg(@"直播封面");
    thumbLabel.font = [UIFont systemFontOfSize:13];
    [preThumbBtn addSubview:thumbLabel];
    
    CGFloat chatTypeW = [YBToolClass widthOfString:YZMsg(@"直播类型：") andFont:[UIFont systemFontOfSize:14] andHeight:20];
    UILabel *chatTypeLb = [[UILabel alloc]init];
    chatTypeLb.frame = CGRectMake(preThumbBtn.left+14, preThumbBtn.bottom, chatTypeW, 20);
    chatTypeLb.font = [UIFont systemFontOfSize:14];
    chatTypeLb.textColor = UIColor.whiteColor;
    chatTypeLb.text = YZMsg(@"直播类型：");
    [preMiddleView addSubview:chatTypeLb];
    
    voiceChatBtn = [UIButton buttonWithType:0];
    voiceChatBtn.frame = CGRectMake(chatTypeLb.right+10, chatTypeLb.top, 100, 20);
    [voiceChatBtn setImage:[UIImage imageNamed:@"chatroom_voicenormal"] forState:0];
    [voiceChatBtn setImage:[UIImage imageNamed:@"chatroom_voicesel"] forState:UIControlStateSelected];
    [voiceChatBtn setTitle:YZMsg(@"语音聊天室") forState:0];
    voiceChatBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    voiceChatBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
    [voiceChatBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    voiceChatBtn.layer.cornerRadius = 10;
    voiceChatBtn.layer.borderColor = UIColor.whiteColor.CGColor;
    voiceChatBtn.layer.borderWidth = 1;
    [voiceChatBtn addTarget:self action:@selector(typeBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [preMiddleView addSubview:voiceChatBtn];
    
    videoChatBtn = [UIButton buttonWithType:0];
    videoChatBtn.frame = CGRectMake(voiceChatBtn.right+10, chatTypeLb.top, 100, 20);
    [videoChatBtn setImage:[UIImage imageNamed:@"chatroom_videonormal"] forState:0];
    [videoChatBtn setImage:[UIImage imageNamed:@"chatroom_videosel"] forState:UIControlStateSelected];
    [videoChatBtn setTitle:YZMsg(@"视频聊天室") forState:0];
    videoChatBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
    videoChatBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    [videoChatBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    videoChatBtn.layer.cornerRadius = 10;
    videoChatBtn.layer.borderColor = UIColor.whiteColor.CGColor;
    videoChatBtn.layer.borderWidth = 1;
    [videoChatBtn addTarget:self action:@selector(typeBtnClick:) forControlEvents:UIControlEventTouchUpInside];

    [preMiddleView addSubview:videoChatBtn];
    
    [self changeChatTypeState];

    UILabel *preTitlelabel = [[UILabel alloc]initWithFrame:CGRectMake(preThumbBtn.right+5, preThumbBtn.top, 100, preThumbBtn.height/4)];
    preTitlelabel.font = [UIFont systemFontOfSize:13];
    preTitlelabel.textColor = [UIColor whiteColor];//RGB_COLOR(@"#c8c8c8", 1);
    preTitlelabel.text = YZMsg(@"直播标题");
    [preMiddleView addSubview:preTitlelabel];
    liveTitleTextView = [[UITextView alloc]initWithFrame:CGRectMake(preTitlelabel.left, preTitlelabel.bottom, preMiddleView.width-10-preThumbBtn.right, preThumbBtn.height*0.75)];
    liveTitleTextView.delegate = self;
    liveTitleTextView.font = [UIFont systemFontOfSize:20];
    liveTitleTextView.textColor = [UIColor whiteColor];
    liveTitleTextView.backgroundColor = [UIColor clearColor];
    [preMiddleView addSubview:liveTitleTextView];
    textPlaceholdLabel = [[UILabel alloc]initWithFrame:CGRectMake(5, 10, liveTitleTextView.width, 22)];
    textPlaceholdLabel.font = [UIFont systemFontOfSize:20];
    textPlaceholdLabel.textColor = [UIColor whiteColor];//RGB_COLOR(@"#c8c8c8", 1);
    textPlaceholdLabel.text = YZMsg(@"给直播写个标题吧");
    [liveTitleTextView addSubview:textPlaceholdLabel];

    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(10, preMiddleView.height*14/19, preMiddleView.width-20, 1) andColor:RGB_COLOR(@"#ffffff", 0.3) andView:preMiddleView];
    
    UILabel *shareLabel = [[UILabel alloc]initWithFrame:CGRectMake(10, preMiddleView.height*14/19, 50, preMiddleView.height*5/19)];
    shareLabel.font = [UIFont systemFontOfSize:13];
    shareLabel.textColor = [UIColor whiteColor];//RGB_COLOR(@"#c8c8c8", 1);
    shareLabel.text = YZMsg(@"分享至");
    [preMiddleView addSubview:shareLabel];
    NSArray *shareArray = [common share_type];
    CGFloat speace = (preMiddleView.width-70-180)/5;
    preShareBtnArray = [NSMutableArray array];
    if ([shareArray isKindOfClass:[NSArray class]]) {
        for (int i = 0; i<shareArray.count; i++) {
            UIButton *btn = [UIButton buttonWithType:0];
            [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"share%@",shareArray[i]]] forState:UIControlStateNormal];
            [btn setImage:[UIImage imageNamed:[NSString stringWithFormat:@"share_%@",shareArray[i]]] forState:UIControlStateSelected];
            [btn setTitle:shareArray[i] forState:UIControlStateNormal];
            [btn setTitle:shareArray[i] forState:UIControlStateSelected];
            [btn setTitleColor:[UIColor clearColor] forState:UIControlStateNormal|UIControlStateSelected];
            [btn addTarget:self action:@selector(share:) forControlEvents:UIControlEventTouchUpInside];
            [btn setTitle:shareArray[i] forState:UIControlStateNormal];
            [btn setTitleColor:[UIColor clearColor] forState:UIControlStateNormal];
            btn.imageView.contentMode = UIViewContentModeScaleAspectFit;
            btn.frame = CGRectMake(shareLabel.right+i*(30+speace),shareLabel.top+ preMiddleView.height*5/19/2-15, 30, 30);
            [preMiddleView addSubview:btn];
            [preShareBtnArray addObject:btn];
        }

    }
    if ([_live_isban isEqual:@"1"]) {
        UILabel *ban_titleLb = [[UILabel alloc]init];
        ban_titleLb.frame = CGRectMake(preMiddleView.left+5, preMiddleView.bottom+10, preMiddleView.width, 20);
        ban_titleLb.font = [UIFont systemFontOfSize:14];
        ban_titleLb.textColor = normalColors;
        ban_titleLb.text = _liveban_title;
        NSLog(@"-----livebroadcast-----:%@ \n %@",_live_isban, _liveban_title);
        [_preFrontView addSubview:ban_titleLb];
    }

    //开播按钮
    UIButton *startLiveBtn = [UIButton buttonWithType:0];
    startLiveBtn.size = CGSizeMake(_window_width*0.8,40);
    startLiveBtn.center = CGPointMake(_window_width*0.5, _window_height*0.8);
    startLiveBtn.layer.cornerRadius = 20.0;
    startLiveBtn.layer.masksToBounds = YES;
    [startLiveBtn setBackgroundImage:[UIImage imageNamed:@"startLive_back"]];
    [startLiveBtn addTarget:self action:@selector(doHidden:) forControlEvents:UIControlEventTouchUpInside];
    [startLiveBtn setTitle:YZMsg(@"开始直播") forState:0];
    startLiveBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [_preFrontView addSubview:startLiveBtn];
}
-(void)typeBtnClick:(UIButton *)sender{
    if(sender == voiceChatBtn){
        isVoiceChat = YES;
    }else{
        isVoiceChat = NO;
    }
    [self changeChatTypeState];
}
-(void)changeChatTypeState{
    if(isVoiceChat){
        voiceChatBtn.layer.borderColor = normalColors.CGColor;
        videoChatBtn.layer.borderColor = UIColor.whiteColor.CGColor;

        voiceChatBtn.selected = YES;
        videoChatBtn.selected = NO;
    }else{
        voiceChatBtn.layer.borderColor = UIColor.whiteColor.CGColor;
        videoChatBtn.layer.borderColor = normalColors.CGColor;

        voiceChatBtn.selected = NO;
        videoChatBtn.selected = YES;

    }
}

- (void)doOpenGoodsCar:(UIButton *)sender{
    openGoodsCar.selected = !openGoodsCar.selected;
    if (openGoodsCar.selected) {
        [openGoodsCar setBackgroundColor:[UIColor whiteColor]];
    }else{
        [openGoodsCar setBackgroundColor:[[UIColor blackColor] colorWithAlphaComponent:0.4]];
    }
}

//创建房间
-(void)createroom{
    if (thumbImage) {
        YBWeakSelf;
        [[YBStorageManage shareManage]getCOSInfo:^(int code) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (code == 0) {
                    [weakSelf startCreateRoomImage];
                }
            });
        }];

    }else{
        [self startCreateRoom];
    }
}
-(void)startCreateRoomImage{
//    [MBProgressHUD showMessage:@""];
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    YBWeakSelf;
    if (thumbImage) {
        dispatch_group_async(group, queue, ^{
            NSString *imageName = [PublicObj getNameBaseCurrentTime:@"duibinaf.png"];
            [[YBStorageManage shareManage]yb_storageImg:thumbImage andName:imageName progress:^(CGFloat percent) {
                
            }complete:^(int code, NSString *key) {
                thumbUrlStr = minstr(key);
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        });
    }
    dispatch_group_notify(group, queue, ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf startCreateRoom];
        });
        NSLog(@"任务完成执行");
    });

}

//创建房间
-(void)startCreateRoom{
    NSString *deviceinfo = [NSString stringWithFormat:@"%@_%@_%@",[YBNetworking iphoneType],[[UIDevice currentDevice] systemVersion],[YBNetworking getNetworkType]];

    NSString *title = [liveTitleTextView.text stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    AFHTTPSessionManager *session = [AFHTTPSessionManager manager];
    NSString *languageStr= [PublicObj getCurrentLanguage];
    NSLog(@"ybtoo=========Str:%@", languageStr);

    NSString *url = [purl stringByAppendingFormat:@"?service=Live.createRoom&uid=%@&token=%@&user_nicename=%@&title=%@&province=%@&city=%@&lng=%@&lat=%@&type=%@&type_val=%@&liveclassid=%@&deviceinfo=%@&isshop=%d&live_type=%@&thumb=%@&language=%@&voice_type=%@",[Config getOwnID],[Config getOwnToken],[Config getOwnNicename],title,@"",locationSwitch ? [cityDefault getMyCity] : @"",locationSwitch ? [cityDefault getMylng] : @"",locationSwitch ? [cityDefault getMylat] : @"",@"0",@"",@"0",deviceinfo,openGoodsCar.selected,@"1",thumbUrlStr,languageStr, isVoiceChat ? @"0":@"1"];
    url = [url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    NSDictionary *subDic = @{
                             @"avatar":minstr([Config getavatar]),
                             @"avatar_thumb":minstr([Config getavatarThumb])
                             };
    [session POST:url parameters:subDic headers:nil constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
//        if (thumbImage) {
//            [formData appendPartWithFileData:[Utils compressImage:thumbImage] name:@"file" fileName:@"duibinaf.png" mimeType:@"image/jpeg"];
//        }
    } progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        [MBProgressHUD hideHUD];

        NSDictionary *data = [responseObject valueForKey:@"data"];
        NSString *code = [NSString stringWithFormat:@"%@",[data valueForKey:@"code"]];
        if ([code isEqual:@"0"]) {
            NSDictionary *info = [[data valueForKey:@"info"] firstObject];
            live_thumb = minstr([info valueForKey:@"thumb"]);
            NSString *coin = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_coin"]];
            NSString *game_banker_limit = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_limit"]];
            NSString *uname = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_name"]];
            NSString *uhead = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_banker_avatar"]];
            NSString *uid = [NSString stringWithFormat:@"%@",[info valueForKey:@"game_bankerid"]];
            NSDictionary *zhuangdic = @{
                                        @"coin":coin,
                                        @"game_banker_limit":game_banker_limit,
                                        @"user_nicename":uname,
                                        @"avatar":uhead,
                                        @"id":uid
                                        };
            _zhuangDic = zhuangdic;

            NSArray *game_switch = [info valueForKey:@"game_switch"];//游戏开关
            _game_switch = game_switch;

            NSString *agorakitid = [NSString stringWithFormat:@"%@",[info valueForKey:@"agorakitid"]];
            [common saveagorakitid:agorakitid];//保存声网ID
            NSString *tx_appid = minstr([info valueForKey:@"tx_appid"]);
            [common saveTXSDKAppID:tx_appid];
            //保存靓号和vip信息
            NSDictionary *liang = [info valueForKey:@"liang"];
            NSString *liangnum = minstr([liang valueForKey:@"name"]);
            NSDictionary *vip = [info valueForKey:@"vip"];
            NSString *type = minstr([vip valueForKey:@"type"]);
            NSDictionary *subdic = [NSDictionary dictionaryWithObjects:@[type,liangnum] forKeys:@[@"vip_type",@"liang"]];
            [Config saveVipandliang:subdic];
            self.chatWordArr = [info valueForKey:@"sensitive_words"];
            _roomDic = info;
            if (_type == 0 || _type == 2) {
                _canFee = YES;
            }else{
                _canFee = NO;
            }
            _dailytask_switch = minstr([info valueForKey:@"dailytask_switch"]);

            //移除预览UI
            [_preFrontView removeFromSuperview];
            _preFrontView = nil;
            //注册通知
            [self nsnotifition];
            //开始直播
            [self startUI];
        }else if ([code isEqual:@"700"]) {
             UIAlertController *quitAlert = [UIAlertController alertControllerWithTitle:YZMsg(@"提示") message:YZMsg(@"您的登录状态失效，请重新登录！") preferredStyle:UIAlertControllerStyleAlert];
             UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
                  [[NSNotificationCenter defaultCenter] postNotificationName:@"denglushixiao" object:nil];

                 [[YBToolClass sharedInstance] quitLogin];

             }];
             [cancelAction setValue:normalColors forKey:@"_titleTextColor"];
             [quitAlert addAction:cancelAction];
             [[[MXBADelegate sharedAppDelegate] topViewController] presentViewController:quitAlert animated:YES completion:nil];
         }else{
            [MBProgressHUD showError:[data valueForKey:@"msg"]];
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        [MBProgressHUD hideHUD];
        [MBProgressHUD showError:YZMsg(@"网络错误")];
    }];

}
//分享
- (void)share:(UIButton *)sender{
    if ([selectShareName isEqual:sender.titleLabel.text]) {
        sender.selected = NO;
        selectShareName = @"";
    }else{
        sender.selected = YES;
        selectShareName = sender.titleLabel.text;
        for (UIButton *btn in preShareBtnArray) {
            if (btn != sender) {
                btn.selected = NO;
            }
        }
    }
}
-(void)doHidden:(UIButton *)sender{
    
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        NSLog(@"相机权限受限");
        UIAlertView *alert = [[UIAlertView alloc]initWithTitle:YZMsg(@"权限受阻") message:YZMsg(@"请在设置中开启相机权限") delegate:self cancelButtonTitle:YZMsg(@"确定") otherButtonTitles:nil, nil];
        [alert show];
        
        
        return;
    }
    [[AVAudioSession sharedInstance] requestRecordPermission:^(BOOL granted) {
        
        if (granted) {
            
            // 用户同意获取麦克风
            
        } else {
            
            UIAlertView *alert = [[UIAlertView alloc]initWithTitle:YZMsg(@"权限受阻") message:YZMsg(@"请在设置中开启麦克风权限") delegate:self cancelButtonTitle:YZMsg(@"确定") otherButtonTitles:nil, nil];
            
            [alert show];
            
            return ;
            
        }
        
    }];
    [MBProgressHUD showMessage:@""];
    if ([selectShareName isEqual:@""]) {
        [self createroom];
    }else if([selectShareName isEqual:@"qq"]){
        [self simplyShare:SSDKPlatformSubTypeQQFriend];
    }else if([selectShareName isEqual:@"qzone"]){
        [self simplyShare:SSDKPlatformSubTypeQZone];
    }else if([selectShareName isEqual:@"wx"]){
        [self simplyShare:SSDKPlatformSubTypeWechatSession];
    }else if([selectShareName isEqual:@"wchat"]){
        [self simplyShare:SSDKPlatformSubTypeWechatTimeline];
    }else if([selectShareName isEqual:@"facebook"]){
        [self simplyShare:SSDKPlatformTypeFacebook];
    }else if([selectShareName isEqual:@"twitter"]){
        [self simplyShare:SSDKPlatformTypeTwitter];
    }else if([selectShareName isEqual:@"weibo"]){
        [self simplyShare:SSDKPlatformTypeSinaWeibo];
    }
}

//-(void)doHidden:(UIButton *)sender{
////    if ([liveClassID isEqual:@"-99999999"]) {
////        [MBProgressHUD showError:YZMsg(@"请选择频道")];
////        return;
////    }
////
//
//
//    NSString *mediaType = AVMediaTypeVideo;
//    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
////    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
////        NSLog(@"相机权限受限");
////        UIAlertView *alert = [[UIAlertView alloc]initWithTitle:YZMsg(@"权限受阻") message:YZMsg(@"请在设置中开启相机权限") delegate:self cancelButtonTitle:YZMsg(@"确定") otherButtonTitles:nil, nil];
////        [alert show];
////
////
////        return;
////    }
//    [[AVAudioSession sharedInstance] requestRecordPermission:^(BOOL granted) {
//
//        if (granted) {
//
//            // 用户同意获取麦克风
//            [MBProgressHUD showMessage:@""];
//            if ([selectShareName isEqual:@""]) {
//                [self createroom];
//            }else if([selectShareName isEqual:@"qq"]){
//                [self simplyShare:SSDKPlatformSubTypeQQFriend];
//            }else if([selectShareName isEqual:@"qzone"]){
//                [self simplyShare:SSDKPlatformSubTypeQZone];
//            }else if([selectShareName isEqual:@"wx"]){
//                [self simplyShare:SSDKPlatformSubTypeWechatSession];
//            }else if([selectShareName isEqual:@"wchat"]){
//                [self simplyShare:SSDKPlatformSubTypeWechatTimeline];
//            }else if([selectShareName isEqual:@"facebook"]){
//                [self simplyShare:SSDKPlatformTypeFacebook];
//            }else if([selectShareName isEqual:@"twitter"]){
//                [self simplyShare:SSDKPlatformTypeTwitter];
//            }else if([selectShareName isEqual:@"weibo"]){
//                [self simplyShare:SSDKPlatformTypeSinaWeibo];
//            }
//
//        } else {
//
//            UIAlertView *alert = [[UIAlertView alloc]initWithTitle:YZMsg(@"权限受阻") message:YZMsg(@"请在设置中开启麦克风权限") delegate:self cancelButtonTitle:YZMsg(@"确定") otherButtonTitles:nil, nil];
//
//            [alert show];
//
//            return ;
//
//        }
//
//    }];
//}
- (void)simplyShare:(int)SSDKPlatformType
{
    [self shareLiveRoom];

    /**
     * 在简单分享中，只要设置共有分享参数即可分享到任意的社交平台
     **/
    
    int SSDKContentType = SSDKContentTypeAuto;
    
    NSURL *ParamsURL;
    
    
    if(SSDKPlatformType == SSDKPlatformTypeSinaWeibo)
    {
        SSDKContentType = SSDKContentTypeImage;
    }
    else if((SSDKPlatformType == SSDKPlatformSubTypeWechatSession || SSDKPlatformType == SSDKPlatformSubTypeWechatTimeline))
    {
//        NSString *strFullUrl = [[common wx_siteurl] stringByAppendingFormat:@"%@",[Config getOwnID]];
//        ParamsURL = [NSURL URLWithString:strFullUrl];
        ParamsURL = [NSURL URLWithString:[common app_ios]];

    }else{
        
        ParamsURL = [NSURL URLWithString:[common app_ios]];
    }
    //获取我的头像
    LiveUser *user = [Config myProfile];
    //创建分享参数
    NSMutableDictionary *shareParams = [NSMutableDictionary dictionary];
//    [shareParams SSDKSetupShareParamsByText:[NSString stringWithFormat:@"%@%@",[Config getOwnNicename],[common share_des]]
//                                     images:user.avatar_thumb
//                                        url:ParamsURL
//                                      title:[common share_title]
//                                       type:SSDKContentType];
    NSString *shareDes;
    if (liveTitleTextView.text.length < 1) {
        shareDes = [common share_des];
    }else{
        shareDes = liveTitleTextView.text;
    }
    NSString *shareTitle = [common share_title];
    if (shareTitle.length > 0) {
        if ([shareTitle containsString:@"username"]) {
            shareTitle = [shareTitle stringByReplacingOccurrencesOfString:@"username" withString:[Config getOwnNicename]];
            shareTitle = [shareTitle stringByReplacingOccurrencesOfString:@"{" withString:@""];
            shareTitle = [shareTitle stringByReplacingOccurrencesOfString:@"}" withString:@""];
        }
    }

    [shareParams SSDKSetupShareParamsByText:shareDes
                                     images:user.avatar_thumb
                                        url:ParamsURL
                                      title:shareTitle
                                       type:SSDKContentType];

    [MBProgressHUD hideHUD];

    [ShareSDK share:SSDKPlatformType parameters:shareParams onStateChanged:^(SSDKResponseState state, NSDictionary *userData, SSDKContentEntity *contentEntity, NSError *error) {
        
        if (state == SSDKResponseStateSuccess) {
            [MBProgressHUD showError:YZMsg(@"分享成功")];
        }
        else if (state == SSDKResponseStateFail){
            [MBProgressHUD showError:YZMsg(@"分享失败")];
        }
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self createroom];
        });
    }];
}
-(void)shareLiveRoom{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.shareLiveRoom"];

    NSDictionary *dic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken]
    };
    [YBNetworking postWithUrl:url Dic:dic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if ([code isEqual:@"0"]) {
            
        }else{
            [MBProgressHUD showError:msg];
        }
    } Fail:^(id fail) {
        
    }];

}

- (void)hidePreTextView{
    [liveTitleTextView resignFirstResponder];
}
- (void)textViewDidChange:(UITextView *)textView

{
    if (textView.text.length == 0) {
        textPlaceholdLabel.text = YZMsg(@"给直播写个标题吧");
    }else{
        textPlaceholdLabel.text = @"";
    }
    
}


//选择频道
- (void)showAllClassView{
    startLiveClassVC *vc = [[startLiveClassVC alloc]init];
    vc.classID = liveClassID;
    vc.block = ^(NSDictionary * _Nonnull dic) {
        liveClassID = minstr([dic valueForKey:@"id"]);
        liveClassLabel.text = minstr([dic valueForKey:@"name"]);
    };
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    self.animator = [[ZFModalTransitionAnimator alloc] initWithModalViewController:vc];
    self.animator.bounces = NO;
    self.animator.behindViewAlpha = 1;
    self.animator.behindViewScale = 0.5f;
    self.animator.transitionDuration = 0.4f;
    vc.transitioningDelegate = self.animator;
    self.animator.dragable = YES;
    self.animator.direction = ZFModalTransitonDirectionRight;
    [self presentViewController:vc animated:YES completion:nil];

}
//选择封面
-(void)doUploadPicture{
    UIAlertController *alertContro = [UIAlertController alertControllerWithTitle:YZMsg(@"选择上传方式") message:@"" preferredStyle:UIAlertControllerStyleActionSheet];
    UIAlertAction *picAction = [UIAlertAction actionWithTitle:YZMsg(@"相册") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self selectThumbWithType:UIImagePickerControllerSourceTypePhotoLibrary];
    }];
    [alertContro addAction:picAction];
//    UIAlertAction *photoAction = [UIAlertAction actionWithTitle:YZMsg(@"拍照") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
//        if ([_sdkType isEqual:@"1"]) {
//            [_txLivePublisher stopPreview];
//        }else{
//            [_gpuStreamer stopPreview];
//        }
//        [self selectThumbWithType:UIImagePickerControllerSourceTypeCamera];
//    }];
//    [alertContro addAction:photoAction];
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
    }];
    [alertContro addAction:cancleAction];

    [self presentViewController:alertContro animated:YES completion:nil];
}
- (void)selectThumbWithType:(UIImagePickerControllerSourceType)type{
    UIImagePickerController *imagePickerController = [UIImagePickerController new];
    imagePickerController.delegate = self;
    imagePickerController.sourceType = type;
    imagePickerController.allowsEditing = YES;
    if (type == UIImagePickerControllerSourceTypeCamera) {
        imagePickerController.showsCameraControls = YES;
        imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    }
    [self presentViewController:imagePickerController animated:YES completion:nil];
}
-(void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info{
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    NSString *type = [info objectForKey:UIImagePickerControllerMediaType];
    if ([type isEqualToString:@"public.image"])
    {
        //先把图片转成NSData
        UIImage* image = [info objectForKey:@"UIImagePickerControllerEditedImage"];
        thumbImage = image;
        [preThumbBtn setImage:image forState:UIControlStateNormal];
        thumbLabel.text = YZMsg(@"更换封面");
        thumbLabel.backgroundColor = RGB_COLOR(@"#0000000", 0.3);
    }
    [picker dismissViewControllerAnimated:YES completion:nil];
//    [_txLivePublisher startPreview:_pushPreview];
}
-(void)imagePickerControllerDidCancel:(UIImagePickerController *)picker{
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    [picker dismissViewControllerAnimated:YES completion:nil];
//    [_txLivePublisher startPreview:_pushPreview];
}
- (void)navigationController:(UINavigationController *)navigationController didShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if ([UIDevice currentDevice].systemVersion.floatValue < 11) {
        return;
    }
    if ([viewController isKindOfClass:NSClassFromString(@"PUPhotoPickerHostViewController")]) {
        [viewController.view.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.frame.size.width < 42) {
                [viewController.view sendSubviewToBack:obj];
                *stop = YES;
            }
        }];
    }
}

//退出预览
- (void)doClosePreView{
    [self txStopRTC];
    self.isRedayCloseRoom = YES;
    //ray-声网
//    if (_gpuStreamer) {
//        [_gpuStreamer stopPreview];
//        _gpuStreamer = nil;
//    }
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"isLiveing"];
    [self.navigationController popViewControllerAnimated:YES];
}
#pragma mark ================ 预览结束 ===============

#pragma mark ================ 直播结束 ===============
- (void)requestLiveAllTimeandVotes{
    NSString *url = [NSString stringWithFormat:@"Live.stopInfo&stream=%@",minstr([_roomDic valueForKey:@"stream"])];
    [YBToolClass postNetworkWithUrl:url andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *subdic = [info firstObject];
            [self lastview:subdic];
        }else{
            [self lastview:nil];
        }
    } fail:^{
        [self lastview:nil];
    }];

}
-(void)lastview:(NSDictionary *)dic{
    //无数据都显示0
    if (!dic) {
        dic = @{@"votes":@"0",@"nums":@"0",@"length":@"0"};
    }
    UIImageView *lastView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    lastView.userInteractionEnabled = YES;
    [lastView sd_setImageWithURL:[NSURL URLWithString:[Config getavatar]]];
    UIBlurEffect *blur = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
    UIVisualEffectView *effectview = [[UIVisualEffectView alloc] initWithEffect:blur];
    effectview.frame = CGRectMake(0, 0,_window_width,_window_height);
    [lastView addSubview:effectview];
    
    
    UILabel *labell= [[UILabel alloc]initWithFrame:CGRectMake(0,24+statusbarHeight, _window_width, _window_height*0.17)];
    labell.textColor = normalColors;
    labell.text = YZMsg(@"直播已结束");
    labell.textAlignment = NSTextAlignmentCenter;
    labell.font = [UIFont fontWithName:@"Helvetica-Bold" size:20];
    [lastView addSubview:labell];
    
    UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(_window_width*0.1, labell.bottom+50, _window_width*0.8, _window_width*0.8*8/13)];
    backView.backgroundColor = RGB_COLOR(@"#000000", 0.2);
    backView.layer.cornerRadius = 5.0;
    backView.layer.masksToBounds = YES;
    [lastView addSubview:backView];
    
    UIImageView *headerImgView = [[UIImageView alloc]initWithFrame:CGRectMake(_window_width/2-50, labell.bottom, 100, 100)];
    [headerImgView sd_setImageWithURL:[NSURL URLWithString:[Config getavatar]] placeholderImage:[UIImage imageNamed:@"icon_avatar_placeholder"]];
    headerImgView.layer.masksToBounds = YES;
    headerImgView.layer.cornerRadius = 50;
    [lastView addSubview:headerImgView];

    
    UILabel *nameL= [[UILabel alloc]initWithFrame:CGRectMake(0,50, backView.width, backView.height*0.55-50)];
    nameL.textColor = [UIColor whiteColor];
    nameL.text = [Config getOwnNicename];
    nameL.textAlignment = NSTextAlignmentCenter;
    nameL.font = [UIFont fontWithName:@"Helvetica-Bold" size:18];
    [backView addSubview:nameL];

    [[YBToolClass sharedInstance] lineViewWithFrame:CGRectMake(10, nameL.bottom, backView.width-20, 1) andColor:RGB_COLOR(@"#585452", 1) andView:backView];
    
    NSArray *labelArray = @[YZMsg(@"直播时长"),[NSString stringWithFormat:@"%@",YZMsg(@"收获")],YZMsg(@"观看人数")];
    for (int i = 0; i < labelArray.count; i++) {
        UILabel *topLabel = [[UILabel alloc]initWithFrame:CGRectMake(i*backView.width/3, nameL.bottom, backView.width/3, backView.height/4)];
        topLabel.font = [UIFont boldSystemFontOfSize:18];
        topLabel.textColor = [UIColor whiteColor];
        topLabel.textAlignment = NSTextAlignmentCenter;
        topLabel.adjustsFontSizeToFitWidth = YES;
        topLabel.numberOfLines = 0;
        if (i == 0) {
            topLabel.text = minstr([dic valueForKey:@"length"]);
        }
        if (i == 1) {
            topLabel.text = minstr([dic valueForKey:@"votes"]);
        }
        if (i == 2) {
            topLabel.text = minstr([dic valueForKey:@"nums"]);
        }
        [backView addSubview:topLabel];
        UILabel *footLabel = [[UILabel alloc]initWithFrame:CGRectMake(topLabel.left, topLabel.bottom, topLabel.width, 14)];
        footLabel.font = [UIFont systemFontOfSize:13];
        footLabel.textColor = RGB_COLOR(@"#cacbcc", 1);
        footLabel.textAlignment = NSTextAlignmentCenter;
        footLabel.text = labelArray[i];
        [backView addSubview:footLabel];
    }
    
    
    
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.frame = CGRectMake(_window_width*0.1,_window_height *0.75, _window_width*0.8,50);
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [button addTarget:self action:@selector(docancle) forControlEvents:UIControlEventTouchUpInside];
//    [button setBackgroundColor:normalColors];
    [button setBackgroundImage:[UIImage imageNamed:@"startLive_back"]];

    [button setTitle:YZMsg(@"返回首页") forState:0];
    button.titleLabel.font = [UIFont systemFontOfSize:15];
    button.layer.cornerRadius = 25;
    button.layer.masksToBounds  =YES;
    [lastView addSubview:button];
    [self.view addSubview:lastView];
    
}
- (void)docancle{
    self.isRedayCloseRoom = YES;
//    [self.menusView clearFaceSelectedStatus];
    [UIApplication sharedApplication].idleTimerDisabled = NO;

    [self.navigationController popViewControllerAnimated:YES];

}
#pragma mark ================ 直播结束 ===============

-(void)sendBarrage:(NSDictionary *)msg{
    NSString *text = [NSString stringWithFormat:@"%@",[[msg valueForKey:@"ct"] valueForKey:@"content"]];
    NSString *name = [msg valueForKey:@"uname"];
    NSString *icon = [msg valueForKey:@"uhead"];
    NSDictionary *levelDic = [common getUserLevelMessage:[msg valueForKey:@"level"]];
    NSString *colorStr = minstr([levelDic valueForKey:@"colour"]);

    NSDictionary *userinfo = [[NSDictionary alloc] initWithObjectsAndKeys:text,@"title",name,@"name",icon,@"icon",colorStr,@"nameColor",nil];
    [danmuview setModel:userinfo];
}
-(void)sendMessage:(NSDictionary *)dic{
    dic = [YBToolClass roomChatInsertTime:dic];
    [msgList addObject:dic];
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];
}
-(void)sendDanMu:(NSDictionary *)dic{
    NSString *text = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"content"]];
    NSString *name = [dic valueForKey:@"uname"];
    NSString *icon = [dic valueForKey:@"uhead"];
    NSDictionary *levelDic = [common getUserLevelMessage:[dic valueForKey:@"level"]];
    NSString *colorStr = minstr([levelDic valueForKey:@"colour"]);

    NSDictionary *userinfo = [[NSDictionary alloc] initWithObjectsAndKeys:text,@"title",name,@"name",icon,@"icon",colorStr,@"nameColor",nil];
    if (_tx_playrtmp) {
        [self.view insertSubview:danmuview aboveSubview:_tx_playrtmp];
    }else {
        //ray-声网
//        [self.view insertSubview:danmuview aboveSubview:_js_playrtmp];

    }
    [danmuview setModel:userinfo];
    long totalcoin = [self.danmuPrice intValue];//
    [self addCoin:totalcoin];
}
-(void)getZombieList:(NSArray *)list{
    NSArray *arrays =[list firstObject];
    userCount += arrays.count;
//    onlineLabel.text = [NSString stringWithFormat:@"%lld",userCount];
    if(userCount > 99){
        [onlineBtn setTitle:@"99+" forState:0];
    }else{
        [onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    }

    if (!listView) {
        listView = [[ListCollection alloc]initWithListArray:list andID:[Config getOwnID] andStream:[NSString stringWithFormat:@"%@",[self.roomDic valueForKey:@"stream"]]andFixWidth:50];
        listView.delegate = self;
        listView.frame = CGRectMake(130, 20+statusbarHeight, _window_width-130-50, 40);
        [frontView addSubview:listView];
    }
      [listView listarrayAddArray:[list firstObject]];
}
-(void)jumpLast:(UITableView *)tableView
{
    if (_canScrollToBottom) {
    NSUInteger sectionCount = [tableView numberOfSections];
    if (sectionCount) {
        
        NSUInteger rowCount = [tableView numberOfRowsInSection:0];
        if (rowCount) {
            
            NSUInteger ii[2] = {sectionCount-1, 0};
            NSIndexPath* indexPath = [NSIndexPath indexPathWithIndexes:ii length:2];
            [tableView scrollToRowAtIndexPath:indexPath
                             atScrollPosition:UITableViewScrollPositionBottom animated:YES];
        }
    }
    
    }
}
-(void)quickSort1:(NSMutableArray *)userlist
{
    for (int i = 0; i<userlist.count; i++)
    {
        for (int j=i+1; j<[userlist count]; j++)
        {
            int aac = [[[userlist objectAtIndex:i] valueForKey:@"level"] intValue];
            int bbc = [[[userlist objectAtIndex:j] valueForKey:@"level"] intValue];
            NSDictionary *da = [NSDictionary dictionaryWithDictionary:[userlist objectAtIndex:i]];
            NSDictionary *db = [NSDictionary dictionaryWithDictionary:[userlist objectAtIndex:j]];
            if (aac >= bbc)
            {
                [userlist replaceObjectAtIndex:i withObject:da];
                [userlist replaceObjectAtIndex:j withObject:db];
            }else{
                [userlist replaceObjectAtIndex:j withObject:da];
                [userlist replaceObjectAtIndex:i withObject:db];
            }
        }
    }
}
-(void)socketUserLive:(NSString *)ID and:(NSDictionary *)msg{
    userCount -= 1;
//    onlineLabel.text = [NSString stringWithFormat:@"%lld",userCount];
//    [onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    if(userCount > 99){
        [onlineBtn setTitle:@"99+" forState:0];
    }else{
        [onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    }

    if (listView) {
        [listView userLive:msg];
    }
    //ray-声网
//    if (_js_playrtmp) {
//        if ([ID integerValue] == _js_playrtmp.tag-1500) {
//            [_js_playrtmp stopConnect];
//            [_js_playrtmp stopPush];
//            [_js_playrtmp removeFromSuperview];
//            _js_playrtmp = nil;
//        }
//    }

}
-(void)socketUserLogin:(NSString *)ID andDic:(NSDictionary *)dic{
    userCount += 1;
    if (listView) {
        [listView userAccess:dic];
    }
//    onlineLabel.text = [NSString stringWithFormat:@"%lld",userCount];
//    [onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    if(userCount > 99){
        [onlineBtn setTitle:@"99+" forState:0];
    }else{
        [onlineBtn setTitle:[NSString stringWithFormat:@"%lld",userCount] forState:0];
    }

    //进场动画级别限制
//    NSString *levelLimit = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"level"]];
//    int levelLimits = [levelLimit intValue];
//    int levelLimitsLocal = [[common enter_tip_level] intValue];
//    if (levelLimitsLocal >levelLimits) {
//    }
//    else{
    NSString *vipType = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"vip_type"]];
    NSString *guard_type = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"guard_type"]];
    if ([vipType isEqual:@"1"] || [guard_type isEqual:@"1"] || [guard_type isEqual:@"2"]) {
        [useraimation addUserMove:dic];
        useraimation.frame = CGRectMake(10,self.tableView.top - 40,_window_width,20);
    }
    NSString *car_id = minstr([[dic valueForKey:@"ct"] valueForKey:@"car_id"]);
    if (![car_id isEqual:@"0"]) {
        [_viploginArray addObject:dic];
        [self addVipLogin:dic];
//
//        if (!vipanimation) {
//            vipanimation = [[viplogin alloc]initWithFrame:CGRectMake(0,80,_window_width,_window_width*0.8) andBlock:^(id arrays) {
//                [vipanimation removeFromSuperview];
//                vipanimation = nil;
//            }];
//            vipanimation.frame =CGRectMake(0,80,_window_width,_window_width*0.8);
//            [self.view insertSubview:vipanimation atIndex:10];
//            [self.view bringSubviewToFront:vipanimation];
//        }
//        [vipanimation addUserMove:dic];
    }
    
    [self userLoginSendMSG:dic];
    
}
-(void)addVipLogin:(NSDictionary *)msg{
      YBWeakSelf;
      if (!vipanimation) {
          vipanimation = [[viplogin alloc]initWithFrame:CGRectMake(0,80,_window_width,_window_width*0.8) andBlock:^{
              [weakSelf.viploginArray removeObjectAtIndex:0];
              [vipanimation removeFromSuperview];
              vipanimation = nil;
              if (weakSelf.viploginArray.count > 0) {
                  [weakSelf addVipLogin:weakSelf.viploginArray[0]];
              }

          }];
          vipanimation.frame =CGRectMake(0,80,_window_width,_window_width*0.8);
          [self.view insertSubview:vipanimation atIndex:10];
          [self.view bringSubviewToFront:vipanimation];
          [vipanimation addUserMove:msg];

    }

}

//用户进入直播间发送XXX进入了直播间
-(void)userLoginSendMSG:(NSDictionary *)dic {
    titleColor = @"userLogin";
    NSString *uname = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"user_nickname"]];
    NSString *levell = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"level"]];
    NSString *ID = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"id"]];
    NSString *vip_type = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"vip_type"]];
    NSString *liangname = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"liangname"]];
    NSString *usertype = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"usertype"]];
    NSString *guard_type = [NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"guard_type"]];

    NSString *conttt = YZMsg(@" 进入了直播间");
    NSString *isadmin;
    if ([[NSString stringWithFormat:@"%@",[[dic valueForKey:@"ct"] valueForKey:@"usertype"]] isEqual:@"40"]) {
        isadmin = @"1";
    }else{
        isadmin = @"0";
    }
    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",conttt,@"contentChat",levell,@"levelI",ID,@"id",titleColor,@"titleColor",vip_type,@"vip_type",liangname,@"liangname",usertype,@"usertype",guard_type,@"guard_type",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];
}
-(void)socketSystem:(NSString *)ct{
    titleColor = @"firstlogin";
    NSString *uname = YZMsg(@"直播间消息");
    NSString *levell = @" ";
    NSString *ID = @" ";
    NSString *vip_type = @"0";
    NSString *liangname = @"0";
    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",levell,@"levelI",ID,@"id",titleColor,@"titleColor",vip_type,@"vip_type",liangname,@"liangname",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];
}
-(void)socketLight{
    starX = _window_width - www- 10;//closeLiveBtn.frame.origin.x ;
    starY = _window_height - 45-30;//closeLiveBtn.frame.origin.y - 30;
    starImage = [[UIImageView alloc]initWithFrame:CGRectMake(starX, starY, 30, 30)];
    starImage.contentMode = UIViewContentModeScaleAspectFit;
    NSMutableArray *array = [NSMutableArray arrayWithObjects:@"plane_heart_cyan.png",@"plane_heart_pink.png",@"plane_heart_red.png",@"plane_heart_yellow.png",@"plane_heart_heart.png", nil];
    NSInteger random = arc4random()%array.count;
    starImage.image = [UIImage imageNamed:[array objectAtIndex:random]];
    [UIView animateWithDuration:0.2 animations:^{
        starImage.alpha = 1.0;
        starImage.frame = CGRectMake(starX+random - 10, starY-random - 30, 30, 30);
        CGAffineTransform transfrom = CGAffineTransformMakeScale(1.3, 1.3);
        starImage.transform = CGAffineTransformScale(transfrom, 1, 1);
    }];
    [self.view insertSubview:starImage atIndex:10];
    CGFloat finishX = _window_width - round(arc4random() % 200);
    //  动画结束点的Y值
    CGFloat finishY = 200;
    //  imageView在运动过程中的缩放比例
    CGFloat scale = round(arc4random() % 2) + 0.7;
    // 生成一个作为速度参数的随机数
    CGFloat speed = 1 / round(arc4random() % 900) + 0.6;
    //  动画执行时间
    NSTimeInterval duration = 4 * speed;
    //  如果得到的时间是无穷大，就重新附一个值（这里要特别注意，请看下面的特别提醒）
    if (duration == INFINITY) duration = 2.412346;
    //  开始动画
    [UIView beginAnimations:nil context:(__bridge void *_Nullable)(starImage)];
    //  设置动画时间
    [UIView setAnimationDuration:duration];
    
    
    //  设置imageView的结束frame
    starImage.frame = CGRectMake( finishX, finishY, 30 * scale, 30 * scale);
    
    //  设置渐渐消失的效果，这里的时间最好和动画时间一致
    [UIView animateWithDuration:duration animations:^{
        starImage.alpha = 0;
    }];
    
    //  结束动画，调用onAnimationComplete:finished:context:函数
    [UIView setAnimationDidStopSelector:@selector(onAnimationComplete:finished:context:)];
    //  设置动画代理
    [UIView setAnimationDelegate:self];
    [UIView commitAnimations];
}
//全站礼物
-(void)sendAllStationsGift:(NSDictionary *)msg
{
    [self platGift:msg];
}
-(void)sendGift:(NSDictionary *)msg{
    titleColor = @"2";

    NSString *haohualiwuss =  [NSString stringWithFormat:@"%@",[msg valueForKey:@"evensend"]];
    NSDictionary *ct = [msg valueForKey:@"ct"];
    NSString *stickerid = minstr([ct valueForKey:@"sticker_id"]);
    _voteNums = minstr([ct valueForKey:@"votestotal"]);
    [self changeState];
    NSString *languageStr= [PublicObj getCurrentLanguage];
    NSString *language = @"";
    if ([languageStr isEqual:@"en"]) {
        language = minstr([ct valueForKey:@"giftname_en"]);
    }else{
        language =minstr([ct valueForKey:@"giftname"]);
    }

    if (![stickerid isEqual:@"0"]) {}else{
        NSString *userName = minstr([msg valueForKey:@"uname"]);
        NSString *touserName =minstr([ct valueForKey:@"to_username"]);

        
        NSDictionary *GiftInfo = @{@"uid":[msg valueForKey:@"uid"],
                                   @"nicename":[msg valueForKey:@"uname"],
                                   @"giftname":language,//[ct valueForKey:@"giftname"],
                                   @"gifticon":[ct valueForKey:@"gifticon"],
                                   @"giftcount":[ct valueForKey:@"giftcount"],
                                   @"giftid":[ct valueForKey:@"giftid"],
                                   @"level":[msg valueForKey:@"level"],
                                   @"avatar":[msg valueForKey:@"uhead"],
                                   @"type":[ct valueForKey:@"type"],
                                   @"swf":minstr([ct valueForKey:@"swf"]),
                                   @"swftime":minstr([ct valueForKey:@"swftime"]),
                                   @"swftype":minstr([ct valueForKey:@"swftype"]),
                                   @"isluck":minstr([ct valueForKey:@"isluck"]),
                                   @"lucktimes":minstr([ct valueForKey:@"lucktimes"]),
                                   @"mark":minstr([ct valueForKey:@"mark"]),
                                   @"isplatgift":minstr([ct valueForKey:@"isplatgift"]),
                                   @"paintedWidth":minstr([msg valueForKey:@"paintedWidth"]),
                                   @"paintedHeight":minstr([msg valueForKey:@"paintedHeight"]),
                                   @"paintedPath":[msg valueForKey:@"paintedPath"],
                                   };
        NSMutableDictionary *giftDic = [NSMutableDictionary dictionaryWithDictionary:GiftInfo];
        [giftDic setObject:[ct valueForKey:@"touid"] forKey:@"uid"];
        [giftDic setObject:[ct valueForKey:@"to_username"] forKey:@"nicename"];
        GiftInfo = giftDic;

        if ([minstr([ct valueForKey:@"type"]) isEqual:@"1"]) {
            [self expensiveGift:GiftInfo isPlatGift:NO];
        }
        else{
            if (!continueGifts) {
                continueGifts = [[continueGift alloc]init];
                [liansongliwubottomview addSubview:continueGifts];
                //初始化礼物空位
                [continueGifts initGift];
                YBWeakSelf;
                continueGifts.rkPaintedEvent = ^(NSDictionary *giftDic) {
                    [weakSelf showPaintedGift:giftDic];
                };
            }
            if (isAnchorLink) {
                //ray-声网
//                [self.view insertSubview:liansongliwubottomview aboveSubview:[_sdkType isEqual:@"1"]?_tx_playrtmp:_js_playrtmp];
            }
            [self.view insertSubview:liansongliwubottomview aboveSubview:frontView];

            if (pkView) {
                [self.view insertSubview:liansongliwubottomview aboveSubview:pkView];
            }
            [continueGifts GiftPopView:GiftInfo andLianSong:haohualiwuss ischat:YES andUser:userName andTouser:touserName];
        }

    }
//聊天区域显示送礼物
    NSString *ctt = [NSString stringWithFormat:@"%@ %@%@%@%@",YZMsg(@"送给"),[ct valueForKey:@"to_username"],[ct valueForKey:@"giftcount"],YZMsg(@"个"),language];
    NSString* uname = [msg valueForKey:@"uname"];
    NSString *levell = [msg valueForKey:@"level"];
    NSString *ID = [msg valueForKey:@"uid"];
    NSString *avatar = [msg valueForKey:@"uhead"];
    NSString *vip_type =minstr([msg valueForKey:@"vip_type"]);
    NSString *liangname =minstr([msg valueForKey:@"liangname"]);

    NSDictionary *chat6 = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ctt,@"contentChat",levell,@"levelI",ID,@"id",@"#FF6131",@"titleColor",avatar,@"avatar",vip_type,@"vip_type",liangname,@"liangname",nil];
    chat6 = [YBToolClass roomChatInsertTime:chat6];
    [msgList addObject:chat6];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];
}

-(void)showPaintedGift:(NSDictionary *)giftDic {
    [self.view bringSubviewToFront:_paintedShowRegion];
    //手绘显示动画
    _paintedShowRegion.giftPathStr = minstr([giftDic valueForKey:@"gifticon"]);
    _paintedShowRegion.paintedWidth = [minstr([giftDic valueForKey:@"paintedWidth"]) floatValue];
    _paintedShowRegion.paintedHeight = [minstr([giftDic valueForKey:@"paintedHeight"]) floatValue];
    _paintedShowRegion.paintedPointArray = [NSArray arrayWithArray:[giftDic valueForKey:@"paintedPath"]];
}

//懒加载
-(NSArray *)chatModels{
    NSMutableArray *array = [NSMutableArray array];
    for (NSDictionary *dic in msgList) {
        chatModel *model = [chatModel modelWithDic:dic];
        [model setChatFrame:[_chatModels lastObject]];
        [array addObject:model];
    }
    _chatModels = array;
    return _chatModels;
}
-(void)socketShutUp:(NSString *)name andID:(NSString *)ID andType:(NSString *)type{
    [socketL shutUp:ID andName:name andType:type];
}
-(void)socketkickuser:(NSString *)name andID:(NSString *)ID{
    [socketL kickuser:ID andName:name];
}
static int startKeyboard = 0;

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView{
    _canScrollToBottom = NO;
}
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    _canScrollToBottom = YES;
}
-(void)chushihua{

    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"isLiveing"];
    [gameState saveProfile:@"0"];//重置游戏状态
    self.isRedayCloseRoom = NO;
    backTime = 0;
    _canScrollToBottom = YES;
    _voteNums = @"0";//主播一开始的收获数
    userCount = 0;//用户人数计算
    haohualiwuV.expensiveGiftCount = [NSMutableArray array];//豪华礼物数组
    platliwuV.expensiveGiftCount = [NSMutableArray array];
    titleColor = @"0";//用此字段来判别文字颜色
    msgList = [[NSMutableArray alloc] init];//聊天数组
    _chatModels = [NSArray array];//聊天模型
    _chatWordArr = [NSArray array];//屏蔽词集合
    ismessgaeshow = NO;
    isLianmai = NO;
    //预览界面的信息
    liveClassID = @"-99999999";
    selectShareName = @"";
    thumbImage = nil;
    isAnchorLink = NO;
    isTorch = NO;
    _liveGoodTipArr = [NSMutableArray array];
    _viploginArray = [NSMutableArray array];
    thumbUrlStr = @"";
    isVoiceChat = YES;

}
-(void)getUnreadCount{
    [self labeiHid];
}


-(void)viewWillAppear:(BOOL)animated{
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    self.navigationController.navigationBarHidden = YES;
    self.navigationController.interactivePopGestureRecognizer.delegate =nil;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self labeiHid];
    });
    if (huanxinviews != nil && tChatsamall != nil) {
        huanxinviews.view.frame = CGRectMake(0, _window_height*5, _window_width, _window_height*0.4);
        tChatsamall.view.frame = CGRectMake(0, _window_height*5, _window_width, _window_height*0.4);
    }
}
-(void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}
-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    self.view.backgroundColor = [UIColor whiteColor];
    //弹出麦克风权限
    [AVCaptureDevice requestAccessForMediaType:AVMediaTypeAudio completionHandler:^(BOOL granted) {
      
    }];
    isclosenetwork = NO;
    giftViewShow = NO;
    linkCount = 1;
    [self chushihua];
//    [self txRtmpPush];
    if(isVoiceChat){
        [self setBgAndPreview];

        if ([_sdkType isEqual:@"1"]) {
            [self txRTCPush];
        }else{
            [YBAgoraManager shareInstance].AgoraRole =AgoraClientRoleBroadcaster;
            [YBAgoraManager shareInstance].delegate = self;
        }
    }else{
        //弹出相机权限
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
         
        }];

    }

    __block ChatRoomViewController *weakself = self;
    managerAFH = [AFNetworkReachabilityManager sharedManager];
    [managerAFH setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
        switch (status) {
            case AFNetworkReachabilityStatusUnknown:
                NSLog(@"未识别的网络");
                isclosenetwork = YES;
                [weakself backGround];
                break;
            case AFNetworkReachabilityStatusNotReachable:
                NSLog(@"不可达的网络(未连接)");
                isclosenetwork = YES;
                [weakself backGround];
                break;
            case  AFNetworkReachabilityStatusReachableViaWWAN:
                isclosenetwork = NO;
                [weakself forwardGround];
                if (weakself.roomDic) {
                    [weakself checkLiveingStatus];
                }

                break;
            case AFNetworkReachabilityStatusReachableViaWiFi:
                isclosenetwork = NO;
                if (weakself.roomDic) {
                    [weakself checkLiveingStatus];
                }
                [weakself forwardGround];
                break;
            default:
                break;
        }
    }];
    [managerAFH startMonitoring];
#pragma mark 回到后台+来电话
    self.callCenter = [CTCallCenter new];
    
    self.callCenter.callEventHandler = ^(CTCall *call) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([call.callState isEqualToString:CTCallStateDialing]) {
                NSLog(@"电话主动拨打电话");
                [weakself reciverPhoneCall];
            } else if ([call.callState isEqualToString:CTCallStateConnected]) {
                NSLog(@"电话接通");
                [weakself reciverPhoneCall];
            } else if ([call.callState isEqualToString:CTCallStateDisconnected]) {
                NSLog(@"电话挂断");
                [weakself phoneCallEnd];
            } else if ([call.callState isEqualToString:CTCallStateIncoming]) {
                NSLog(@"电话被叫");
                [weakself reciverPhoneCall];
            } else {
                NSLog(@"电话其他状态");
            }
        });
    };
    
    [MBProgressHUD hideHUD];
    [self creatPreFrontView];
//    [self startUI];//初始化UI
    //获取所有未读消息
    [[V2TIMManager sharedInstance] addConversationListener:self];

    NSInteger deviceType = [PublicObj getDeviceType];
    //iPhone6s
    if (deviceType >= 8){
        _needScale = NO;
    }else{
        _needScale = YES;
    }
}
//杀进程
-(void)shajincheng{
    [self getCloseShow];
    [socketL closeRoom];
    [socketL colseSocket];
//    [self clearSticker];
}
-(void)backgroundselector{
    backTime +=1;
    NSLog(@"返回后台时间%d",backTime);
    if (backTime > 60) {
        [self hostStopRoom];
    }
}
-(void)backGround{
        //进入后台
        if (!backGroundTimer) {
            [self sendEmccBack];
            backGroundTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(backgroundselector) userInfo:nil repeats:YES];
        }
}
-(void)forwardGround{
    if (backTime != 0) {
        [socketL phoneCall:@"主播回来了"];
    }
    //进入前台
    if (backTime > 60) {
        [self hostStopRoom];
    }
    if (isclosenetwork == NO) {
        [backGroundTimer invalidate];
        backGroundTimer  = nil;
        backTime = 0;
    }
}
-(void)appactive{
    NSLog(@"哈哈哈哈哈哈哈哈哈哈哈哈 app回到前台");
    if ([_sdkType isEqual:@"1"]) {
        [[YBLiveRTCManager shareInstance]resumePush];
    }else {
        //ray-声网
//        [_gpuStreamer appBecomeActive];
    }
    [self forwardGround];
}
-(void)appnoactive{
    if ([_sdkType isEqual:@"1"]) {
        [[YBLiveRTCManager shareInstance]pausePush];
    }else{
//         [_gpuStreamer appEnterBackground];
    }

    [self backGround];
    NSLog(@"0000000000000000000 app进入后台");
}
//来电话
-(void)sendEmccBack {
    [socketL phoneCall:@"主播离开一下，精彩不中断，不要走开哦"];
}
-(void)startUI{
    frontView = [[UIView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height)];
    frontView.clipsToBounds = YES;
    [self.view addSubview:frontView];
    [self.view insertSubview:frontView atIndex:3];
    
    UIImageView *backImg = [[UIImageView alloc]init];
    backImg.frame = CGRectMake(0, 0, _window_width, _window_height);
    backImg.image = [UIImage imageNamed:@"chatbackgroundImg"];
    backImg.contentMode = UIViewContentModeScaleAspectFill;
    backImg.userInteractionEnabled = YES;
    [frontView addSubview:backImg];

    listView = [[ListCollection alloc]initWithListArray:nil andID:[Config getOwnID] andStream:[NSString stringWithFormat:@"%@",[_roomDic valueForKey:@"stream"]]andFixWidth:50];
    listView.frame = CGRectMake(130,20+statusbarHeight,_window_width-130-50,40);
    listView.delegate = self;
    [frontView addSubview:listView];
    dispatch_async(dispatch_get_main_queue(), ^{
        [self setView];//加载信息页面
    });
    //倒计时动画
    backView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    backView.opaque = YES;
    label1 = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2 -100, _window_height/2-200, 100, 100)];
    label1.textColor = [UIColor whiteColor];
    label1.font = [UIFont systemFontOfSize:90];
    label1.text = @"3";
    label1.textAlignment = NSTextAlignmentCenter;
    label1.center = backView.center;
    label2 = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2 -100, _window_height/2-200, 100, 100)];
    label2.textColor = [UIColor whiteColor];
    label2.font = [UIFont systemFontOfSize:90];
    label2.text = @"2";
    label2.textAlignment = NSTextAlignmentCenter;
    label2.center = backView.center;
    label3 = [[UILabel alloc]initWithFrame:CGRectMake(_window_width/2 -100, _window_height/2-200, 100, 100)];
    label3.textColor = [UIColor whiteColor];
    label3.font = [UIFont systemFontOfSize:90];
    label3.text = @"1";
    label3.textAlignment = NSTextAlignmentCenter;
    label3.center = backView.center;
    label1.hidden = YES;
    label2.hidden = YES;
    label3.hidden = YES;
    [backView addSubview:label3];
    [backView addSubview:label1];
    [backView addSubview:label2];
    [frontView addSubview:backView];
    [self creatLiveTimeView];
    [self kaishidonghua];
    self.view.backgroundColor = [UIColor clearColor];
}
//开始321
-(void)kaishidonghua{
    [self hideBTN];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        label1.hidden = NO;
        [self donghua:label1];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        label1.hidden = YES;
        label2.hidden = NO;
        [self donghua:label2];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        label2.hidden = YES;
        label3.hidden = NO;
        [self donghua:label3];
    });
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        label3.hidden = YES;
        backView.hidden = YES;
        [backView removeFromSuperview];
        [self showBTN];
        [self getStartShow];//请求直播
    });
}
-(void)hidebeautifulgirl
{
    beautifulgirl.hidden = YES;
}
//ray-声网
//- (void) addObservers {
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(onStreamStateChange:)
//                                                 name:KSYStreamStateDidChangeNotification
//                                               object:nil];
//    
//    
//    [[NSNotificationCenter defaultCenter] addObserver:self
//                                             selector:@selector(onNetStateEvent:)
//                                                 name:KSYNetStateEventNotification
//                                               object:nil];
//}
- (void)dealloc
{
    //ray-声网
//    [[NSNotificationCenter defaultCenter] removeObserver:self];
//    [[NSNotificationCenter defaultCenter] removeObserver:self name:kKSYReachabilityChangedNotification object:nil];
    [backGroundTimer invalidate];
    backGroundTimer  = nil;
    NSLog(@"1212121212121212121");

}
//释放通知
- (void) rmObservers {
    //ray-声网
//    [[NSNotificationCenter defaultCenter] removeObserver:self
//                                                    name:KSYStreamStateDidChangeNotification
//                                                  object:nil];
//    [[NSNotificationCenter defaultCenter] removeObserver:self
//                                                    name:KSYNetStateEventNotification
//                                                  object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
    
//    [[NSNotificationCenter defaultCenter] removeObserver:@"wangminxindemusicplay"];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"wangminxindemusicplay" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"shajincheng" object:nil];
    //shajincheng
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"sixinok" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"fenxiang" object:nil];
    [[NSNotificationCenter defaultCenter]removeObserver:self name:@"denglushixiao" object:nil];
    
    [[NSNotificationCenter defaultCenter]removeObserver:self name:ybChatRefresh object:nil];

}
#pragma mark - UI responde
- (void)onQuit:(id)sender {
    //ray-声网
//    [_gpuStreamer stopPreview];
//    _gpuStreamer = nil;
//    [_gpuStreamer.streamerBase stopStream];
//
}
//////美颜按钮点击事件
//-(void)OnChoseFilter:(id)sender {
//
//    if (!beautifulgirl) {
//        __weak ChatRoomViewController *weakself = self;
//           beautifulgirl = [[beautifulview alloc]initWithFrame:self.view.bounds andhide:^(NSString *type) {
//               [weakself hidebeautifulgirl];
//               if (_preFrontView) {
//                   _preFrontView.hidden = NO;
//               }
//          } andslider:^(NSString *type) {
//               [weakself sliderValueChanged:[type floatValue]];
//          } andtype:^(NSString *type) {
//              [weakself setMeiYanData:[type intValue]];
//        }];
//        [self.view addSubview:beautifulgirl];
//    }
//    beautifulgirl.hidden = NO;
//    [self.view bringSubviewToFront:beautifulgirl];
//}
- (void)onStream:(id)sender {
    //ray-声网
//        [_gpuStreamer.streamerBase startStream: [NSURL URLWithString:_hostURL]];
}
//推流成功后更新直播状态 1开播
-(void)changePlayState:(int)status{
    NSDictionary *changelive = @{
                                 @"stream":urlStrtimestring,
                                 @"status":[NSString stringWithFormat:@"%d",status]
                                 };
    [YBToolClass postNetworkWithUrl:@"Live.changeLive" andParameter:changelive success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
//            [self creatLiveTimeView];
        }
    } fail:^{
        
    }];
}
#pragma mark ================ 直播时长的view ===============
- (void)creatLiveTimeView{
    
    liveTimeBGView = [[UIView alloc]initWithFrame:CGRectMake(10, 30+leftW +statusbarHeight+25, 60, 20)];
    liveTimeBGView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.2];
    liveTimeBGView.layer.cornerRadius = 10.0;
    liveTimeBGView.layer.masksToBounds = YES;
    [frontView addSubview:liveTimeBGView];
    UIView *pointView = [[UIView alloc]initWithFrame:CGRectMake(10, 8.5, 3, 3)];
    pointView.backgroundColor = normalColors;
    pointView.layer.cornerRadius = 1.5;
    pointView.layer.masksToBounds = YES;
    [liveTimeBGView addSubview:pointView];

    liveTimeLabel = [[UILabel alloc]initWithFrame:CGRectMake(pointView.right+3, 0, 31, 20)];
    liveTimeLabel.textColor = [UIColor whiteColor];
    liveTimeLabel.font = [UIFont systemFontOfSize:10];
    liveTimeLabel.textAlignment = NSTextAlignmentCenter;
    liveTimeLabel.text = @"00:00";
    [liveTimeBGView addSubview:liveTimeLabel];
    liveTime = 0;
    if (!liveTimer) {
        liveTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(liveTimeChange) userInfo:nil repeats:YES];
    }

}
- (void)liveTimeChange{
    liveTime ++;
    if (liveTime < 3600) {
        liveTimeLabel.text = [NSString stringWithFormat:@"%02d:%02d",liveTime/60,liveTime%60];
    }else{
        if (liveTimeBGView.width < 73) {
            liveTimeBGView.width = 73;
            liveTimeLabel.width = 44;
        }
        liveTimeLabel.text = [NSString stringWithFormat:@"%02d:%02d:%02d",liveTime/3600,(liveTime%3600)/60,(liveTime%3600)%60];
    }
}
//ray-声网
//#pragma mark - state handle
//- (void) onStreamError {
//    if (1 ) {
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            [_gpuStreamer.streamerBase stopStream];
//            [_gpuStreamer.streamerBase startStream:[NSURL URLWithString:_hostURL]];
//        });
//    }
//}
//- (void) onNetStateEvent:(NSNotification *)notification {
//    KSYNetStateCode netEvent = _gpuStreamer.streamerBase.netStateCode;
//    //NSLog(@"net event : %ld", (unsigned long)netEvent );
//    if ( netEvent == KSYNetStateCode_SEND_PACKET_SLOW ) {
//      
//        NSLog(@"发送包时间过长，( 单次发送超过 500毫秒 ）");
//    }
//    else if ( netEvent == KSYNetStateCode_EST_BW_RAISE ) {
//  
//        NSLog(@"估计带宽调整，上调" );
//    }
//    else if ( netEvent == KSYNetStateCode_EST_BW_DROP ) {
//
//        
//        NSLog(@"估计带宽调整，下调" );
//    }
//    else if ( netEvent == KSYNetStateCode_KSYAUTHFAILED ) {
//        
//        NSLog(@"SDK 鉴权失败 (暂时正常推流5~8分钟后终止推流)" );
//    }
//}
//- (void) onStreamStateChange:(NSNotification *)notification {
//    if ( _gpuStreamer.streamerBase.streamState == KSYStreamStateIdle) {
//        NSLog(@"推流状态:初始化时状态为空闲");
//    }
//    else if ( _gpuStreamer.streamerBase.streamState == KSYStreamStateConnected){
//        NSLog(@"推流状态:已连接");
//        [self changePlayState:1];//推流成功后改变直播状态
//        if (_gpuStreamer.streamerBase.streamErrorCode == KSYStreamErrorCode_KSYAUTHFAILED ) {
//            //(obsolete)
//            NSLog(@"推流错误:(obsolete)");
//        }
//    }
//    else if (_gpuStreamer.streamerBase.streamState == KSYStreamStateConnecting ) {
//        NSLog(@"推流状态:连接中");
//    }
//    else if (_gpuStreamer.streamerBase.streamState == KSYStreamStateDisconnecting ) {
//        NSLog(@"推流状态:断开连接中");
//        [self onStreamError];
//    }
//    else if (_gpuStreamer.streamerBase.streamState == KSYStreamStateError ) {
//        NSLog(@"推流状态:推流出错");
//        [self onStreamError];
//        return;
//    }
//}
//直播结束选择 alertview
- (void)onQuit {
    UIAlertController  *alertlianmaiVCtc = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];
    //修改按钮的颜色，同上可以使用同样的方法修改内容，样式
    UIAlertAction *defaultActionss = [UIAlertAction actionWithTitle:YZMsg(@"退出直播间") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
         [self hostStopRoom];
    }];
    UIAlertAction *cancelActionss = [UIAlertAction actionWithTitle:YZMsg(@"取消") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
    }];
    NSString *version = [UIDevice currentDevice].systemVersion;
    if (version.doubleValue < 9.0) {}
    else{
        [defaultActionss setValue:RGBA(253,59,59,1) forKey:@"_titleTextColor"];
        [cancelActionss setValue:[UIColor blackColor] forKey:@"_titleTextColor"];
    }
    [alertlianmaiVCtc addAction:defaultActionss];
    [alertlianmaiVCtc addAction:cancelActionss];
    [self presentViewController:alertlianmaiVCtc animated:YES completion:nil];
}

//关闭直播做的操作
-(void)hostStopRoom{
    [self getCloseShow];//请求关闭直播接口
}
//直播结束时 停止所有计时器
-(void)liveOver{
    if (backGroundTimer) {
        [backGroundTimer invalidate];
        backGroundTimer  = nil;
    }
    if (listTimer) {
        [listTimer invalidate];
        listTimer = nil;
    }
    if (liveTimer) {
        [liveTimer invalidate];
        liveTimer = nil;
    }
    if (hartTimer) {
        [hartTimer invalidate];
        hartTimer = nil;
    }
    if (liveingTimer) {
        [liveingTimer invalidate];
        liveingTimer  = nil;
    }
}
//直播结束时退出房间
-(void)dismissVC{
//    [self txStopRtmp];
    [self txStopRTC];
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"isLiveing"];
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationDidBecomeActiveNotification object:nil];
     [managerAFH stopMonitoring];
    if (bottombtnV) {
        [bottombtnV removeFromSuperview];
        bottombtnV = nil;
    }
    [buttleView removeFromSuperview];
    buttleView = nil;
    [managerAFH stopMonitoring];
    managerAFH = nil;
    if (continueGifts) {
        [continueGifts stopTimerAndArray];
        [continueGifts initGift];
        [continueGifts removeFromSuperview];
        continueGifts = nil;
    }
    if (_paintedShowRegion) {
        [_paintedShowRegion destroyPaitend];
    }

    if (haohualiwuV) {
        [haohualiwuV stopHaoHUaLiwu];
        [haohualiwuV removeFromSuperview];
        haohualiwuV.expensiveGiftCount = nil;
    }
    if (platliwuV) {
        [platliwuV stopHaoHUaLiwu];
        [platliwuV removeFromSuperview];
        platliwuV.expensiveGiftCount = nil;
    }
    //ray-声网
//    if (_js_playrtmp) {
//        [_js_playrtmp stopConnect];
//        [_js_playrtmp stopPush];
//        [_js_playrtmp removeFromSuperview];
//        _js_playrtmp = nil;
//    }
    if (_tx_playrtmp) {
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
    if (zhuangVC) {
        [zhuangVC dismissroom];
        [zhuangVC removeall];
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (gameVC) {
        [gameVC stopGame];
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
    }
    if (rotationV) {
        [rotationV stopRotatipnGameInt];
        [rotationV stoplasttimer];
        [socketL stopRotationGame];//关闭游戏socket
        [rotationV removeFromSuperview];
        [rotationV removeall];
        rotationV = nil;
    }
    
    NSDictionary *subdic = [NSDictionary dictionaryWithObjects:@[minstr(urlStrtimestring)] forKeys:@[@"stream"]];
    [[NSNotificationCenter defaultCenter]  postNotificationName:@"coin" object:nil userInfo:subdic];
}
/***********  以上推流  *************/

/***************     以下是信息页面          **************/
#pragma mark--------加载个人信息页面-------------
-(void)zhuboMessage{
    isMicUser = NO;
    [self showButtleView:[Config getOwnID]];
}
-(void)GetInformessage:(NSDictionary *)subdic{
    isMicUser = NO;
    [self showButtleView:[NSString stringWithFormat:@"%@",[subdic valueForKey:@"id"]]];
}
/// 动画完后销毁iamgeView
- (void)onAnimationComplete:(NSString *)animationID finished:(NSNumber *)finished context:(void *)context{
    UIImageView *imageViewsss = (__bridge UIImageView *)(context);
    [imageViewsss removeFromSuperview];
    imageViewsss = nil;
}
-(void)setView{
    www = 30;

    //左上角 直播live
    leftView = [[UIView alloc]initWithFrame:CGRectMake(10,25+statusbarHeight,115,leftW)];
    leftView.layer.cornerRadius = leftW/2;
    leftView.backgroundColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.6];
    //主播头像button
    UIButton *IconBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    [IconBTN addTarget:self action:@selector(zhuboMessage) forControlEvents:UIControlEventTouchUpInside];
    IconBTN.frame = CGRectMake(1, 1, leftW-2, leftW-2);
    IconBTN.layer.masksToBounds = YES;
    IconBTN.layer.cornerRadius = leftW/2-1;
    
    UITapGestureRecognizer *tapleft = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(zhuboMessage)];
    tapleft.numberOfTapsRequired = 1;
    tapleft.numberOfTouchesRequired = 1;
    [leftView addGestureRecognizer:tapleft];
    LiveUser *user = [[LiveUser alloc]init];
    user = [Config myProfile];
    NSString *path = user.avatar;
    NSURL *url = [NSURL URLWithString:path];
    [IconBTN sd_setBackgroundImageWithURL:url forState:UIControlStateNormal placeholderImage:[UIImage imageNamed:@"default_head.png"]];
    
    UIImageView *levelimage = [[UIImageView alloc]initWithFrame:CGRectMake(IconBTN.right - 15,IconBTN.bottom - 15,15,15)];
    NSDictionary *levelDic = [common getAnchorLevelMessage:[Config level_anchor]];
    [levelimage sd_setImageWithURL:[NSURL URLWithString:minstr([levelDic valueForKey:@"thumb_mark"])]];
    
    //直播live
    UILabel *liveLabel = [[UILabel alloc]initWithFrame:CGRectMake(leftW+4,0,65,leftW/2)];
    liveLabel.textAlignment = NSTextAlignmentLeft;
    liveLabel.text = [Config getOwnNicename];
    liveLabel.textColor = [UIColor whiteColor];
    liveLabel.shadowOffset = CGSizeMake(1,1);//设置阴影
    liveLabel.font =[UIFont systemFontOfSize:13];// fontMT(13);
    //在线人数
    onlineLabel = [[UILabel alloc]init];
    onlineLabel.frame = CGRectMake(leftW+4,leftW/2,65,leftW/2);
    onlineLabel.textAlignment = NSTextAlignmentLeft;
    onlineLabel.textColor = [UIColor whiteColor];
    onlineLabel.font = [UIFont systemFontOfSize:10];//fontMT(10);
    NSString *liangname = [NSString stringWithFormat:@"%@",[[_roomDic valueForKey:@"liang"] valueForKey:@"name"]];
    if ([liangname isEqual:@"0"]) {
        onlineLabel.text = [NSString stringWithFormat:@"ID:%@",[Config getOwnID]];
    }else{
        onlineLabel.text = [NSString stringWithFormat:@"%@:%@",YZMsg(@"靓"),liangname];
    }
    
    onlineBtn = [UIButton buttonWithType:0];
    onlineBtn.frame = CGRectMake(_window_width-90, 20+statusbarHeight, 40, 40);
    onlineBtn.backgroundColor = RGBA(1, 1, 1, 0.4);
    [onlineBtn setTitleColor:UIColor.whiteColor forState:0];
    onlineBtn.titleLabel.font = [UIFont systemFontOfSize:13];
    onlineBtn.layer.cornerRadius = 20;
    onlineBtn.layer.masksToBounds = YES;
    onlineBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
    [onlineBtn setTitle:@"0" forState:0];
    [onlineBtn addTarget:self action:@selector(onlineBtnClick) forControlEvents:UIControlEventTouchUpInside];

    //退出页面按钮
    closeLiveBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    closeLiveBtn.frame = CGRectMake(_window_width - www- 10,25+statusbarHeight, www, www);
    closeLiveBtn.tintColor = [UIColor whiteColor];
    [closeLiveBtn setImage:[UIImage imageNamed:@"cancleliveshow"] forState:UIControlStateNormal];
    [closeLiveBtn addTarget:self action:@selector(onQuit) forControlEvents:UIControlEventTouchUpInside];
   
    
    CGFloat sitTop = liveTimeBGView.bottom+80;
    
    _config = [[TXLivePlayConfig alloc] init];
    //自动模式
    _config.bAutoAdjustCacheTime   = YES;
    _config.minAutoAdjustCacheTime = 1;
    _config.maxAutoAdjustCacheTime = 5;

    if(isVoiceChat){
        //上麦用户
        CGFloat bWidth = (_window_width - 80)/4;
        sitUserArray = @[@{},@{},@{},@{},@{},@{},@{},@{}].mutableCopy;
        sitButtonArray = [NSMutableArray array];
        livePlayerArray = [NSMutableArray array];
        for (int i = 0; i < sitUserArray.count; i ++) {
            YBUserLinkButton *userButton = [[YBUserLinkButton alloc] initWithFrame:CGRectMake(10+(i%4)*(bWidth+20), sitTop+(i/4)*(bWidth + 20)+ShowDiff, bWidth, (bWidth + 20)) andIsBoos:NO andRoomMessage:_roomDic];
            userButton.roomDic = _roomDic;
            userButton.sitid = [NSString stringWithFormat:@"%d",i+1];
            userButton.userDic = sitUserArray[i];
            [frontView addSubview:userButton];
            [sitButtonArray addObject:userButton];
            userButton.delegate = self;
            
            V2TXLivePlayer *_txLivePlayer = [[V2TXLivePlayer alloc] init];
            [_txLivePlayer setObserver:self];
            [_txLivePlayer enableObserveAudioFrame:YES];
            [livePlayerArray addObject:_txLivePlayer];
        }
    }else{
        CGFloat bWidth = (_window_width - 30)/3;
        sitTop = liveTimeBGView.bottom+60;
        sitUserArray = @[@{},@{},@{},@{},@{},@{}].mutableCopy;
        sitVideoButtonArray = [NSMutableArray array];
        livePlayerArray = [NSMutableArray array];
        for (int i = 0; i < sitUserArray.count; i ++) {
            ChatVideoUserLinkView *userVideoView = [[ChatVideoUserLinkView alloc] initWithFrame:CGRectMake(10+(i%3)*(bWidth+5), sitTop+(i/3)*(bWidth + 5)+ShowDiff, bWidth, bWidth) andIsBoss:NO andRoomMessage:_roomDic];
            userVideoView.sitid = [NSString stringWithFormat:@"%d",i+1];
            userVideoView.delegate = self;
            [frontView addSubview:userVideoView];
            [sitVideoButtonArray addObject:userVideoView];
            if(i == 0){
                _anchorLinkView = userVideoView;
                userVideoView.userDic = @{@"user_nickname":[Config getOwnNicename],@"uid":[Config getOwnID]};
                userVideoView.isBoss = YES;
                if ([_sdkType isEqual:@"1"]) {
                    [[YBLiveRTCManager shareInstance]initWithChatVideoLiveModel:V2TXLiveMode_RTC andPushData:_pushSettings];
                    [[YBLiveRTCManager shareInstance]setPushView:_anchorLinkView.livePushView];
                    [[YBLiveRTCManager shareInstance] setBeautyLevel:_tx_beauty_level WhitenessLevel:_tx_whitening_level IsTXfiter:[common getIsTXfiter]];
                    [YBLiveRTCManager shareInstance].delegate = self;
                }
            }else{
                if ([_sdkType isEqual:@"1"]) {
                    V2TXLivePlayer *_txLivePlayer = [[V2TXLivePlayer alloc] init];
                    [_txLivePlayer setObserver:self];
                    [_txLivePlayer enableObserveAudioFrame:YES];
                    [livePlayerArray addObject:_txLivePlayer];
                }
            }
        }
    }

    //聊天
    self.tableView = [[UITableView alloc]initWithFrame:CGRectMake(10, _window_height - _window_height*0.2 - 50 - ShowDiff,tableWidth,_window_height*0.2) style:UITableViewStylePlain];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.clipsToBounds = YES;
    self.tableView.estimatedRowHeight = 80.0;
    //输入框
    keyField = [[UITextField alloc]initWithFrame:CGRectMake(70,7,_window_width-90 - 50, 30)];
    keyField.returnKeyType = UIReturnKeySend;
    keyField.delegate  = self;
    keyField.borderStyle = UITextBorderStyleNone;
    keyField.placeholder = YZMsg(@"和大家说些什么");
    keyField.backgroundColor = [UIColor whiteColor];
    keyField.layer.cornerRadius = 15.0;
    keyField.layer.masksToBounds = YES;
    UIView *fieldLeft = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 15, 30)];
    fieldLeft.backgroundColor = [UIColor whiteColor];
    keyField.leftView = fieldLeft;
    keyField.leftViewMode = UITextFieldViewModeAlways;
    keyField.font = [UIFont systemFontOfSize:15];

    //键盘出现
    keyBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    keyBTN.tintColor = [UIColor whiteColor];
    keyBTN.userInteractionEnabled = YES;
    [keyBTN addTarget:self action:@selector(showkeyboard:) forControlEvents:UIControlEventTouchUpInside];
    [keyBTN setTitle:YZMsg(@"  说点什么...") forState:0];
    keyBTN.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    keyBTN.titleLabel.font= [UIFont systemFontOfSize:13];
    keyBTN.titleLabel.textAlignment = NSTextAlignmentLeft;
    [keyBTN setBackgroundColor:RGBA(1, 1, 1, 0.4)];
    keyBTN.layer.cornerRadius = 15;
    keyBTN.layer.masksToBounds = YES;
    faceIcon =[[UIImageView alloc]init];
    faceIcon.image = [UIImage imageNamed:@"msg_face"];
    [keyBTN addSubview:faceIcon];

    //发送按钮
    pushBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    [pushBTN setImage:[UIImage imageNamed:@"chat_send_gray"] forState:UIControlStateNormal];
    [pushBTN setImage:[UIImage imageNamed:@"chat_send_yellow"] forState:UIControlStateSelected];
    pushBTN.imageView.contentMode = UIViewContentModeScaleAspectFit;

    pushBTN.layer.masksToBounds = YES;
    pushBTN.layer.cornerRadius = 5;
    [pushBTN addTarget:self action:@selector(pushMessage:) forControlEvents:UIControlEventTouchUpInside];
    pushBTN.frame = CGRectMake(_window_width-55,7,50,30);
    cs = [[catSwitch alloc] initWithFrame:CGRectMake(6,11,44,22)];
    cs.delegate = self;
    
    //控麦
    controlMicBtn = [UIButton buttonWithType:0];
    [controlMicBtn setImage:[UIImage imageNamed:getImagename(@"控麦")] forState:0];
    [controlMicBtn addTarget:self action:@selector(controlMicClick) forControlEvents:UIControlEventTouchUpInside];
    //上麦申请
    chatApplyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [chatApplyBtn setImage:[UIImage imageNamed:@"chatApply"] forState:0];
    [chatApplyBtn addTarget:self action:@selector(chatApplyClick) forControlEvents:UIControlEventTouchUpInside];
    //上麦红点显示
    applyTagLb = [[UILabel alloc]init];//WithFrame:CGRectMake(chatApplyBtn.width-10, 5, 6, 6)];
    applyTagLb.layer.cornerRadius = 3;
    applyTagLb.layer.masksToBounds = YES;
    applyTagLb.backgroundColor = [UIColor redColor];
    [chatApplyBtn addSubview:applyTagLb];
    applyTagLb.hidden = YES;
    //消息按钮
    messageBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    [messageBTN setImage:[UIImage imageNamed:@"live_私信"] forState:UIControlStateNormal];
    [messageBTN addTarget:self action:@selector(doMessage) forControlEvents:UIControlEventTouchUpInside];
    self.unReadLabel = [[UILabel alloc]initWithFrame:CGRectMake(13, -5, 16, 16)];
    self.unReadLabel.textAlignment = NSTextAlignmentCenter;
    self.unReadLabel.textColor = [UIColor whiteColor];
    self.unReadLabel.layer.masksToBounds = YES;
    self.unReadLabel.layer.cornerRadius = 8;
    self.unReadLabel.font = [UIFont systemFontOfSize:9];
    self.unReadLabel.backgroundColor = [UIColor redColor];
    self.unReadLabel.hidden = YES;
    [messageBTN addSubview:self.unReadLabel];
    
    //礼物
    _liwuBTN = [UIButton buttonWithType:UIButtonTypeCustom];
    _liwuBTN.tintColor = [UIColor whiteColor];
    [_liwuBTN setBackgroundImage:[UIImage imageNamed:@"live_礼物"] forState:UIControlStateNormal];
    [_liwuBTN addTarget:self action:@selector(doLiwu) forControlEvents:UIControlEventTouchUpInside];

    //更多按钮
    moreBTN = [UIButton buttonWithType:UIButtonTypeSystem];
    moreBTN.tintColor = [UIColor whiteColor];
    [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能"] forState:UIControlStateNormal];
    [moreBTN addTarget:self action:@selector(showmoreview) forControlEvents:UIControlEventTouchUpInside];
    keyField.frame = CGRectMake(cs.right+10,7,_window_width-90 - 30, 30);

    
    if (openGoodsCar.selected) {
        //camera按钮
        goodsShowBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [goodsShowBtn setBackgroundImage:[UIImage imageNamed:@"live_店铺"] forState:UIControlStateNormal];
        [goodsShowBtn addTarget:self action:@selector(showgoodsShowView) forControlEvents:UIControlEventTouchUpInside];

    }
    //红包按钮
    redBagBtn = [UIButton buttonWithType:0];
    [redBagBtn setBackgroundImage:[UIImage imageNamed:@"红包-右上角"] forState:UIControlStateNormal];
    [redBagBtn addTarget:self action:@selector(redBagBtnClick) forControlEvents:UIControlEventTouchUpInside];
    redBagBtn.hidden = YES;
    redBagBtn.frame = CGRectMake(_window_width-50, 120+statusbarHeight, 40, 40);
    /*==================  连麦  ================*/
    //tool绑定键盘
    toolBar = [[UIView alloc]initWithFrame:CGRectMake(0,_window_height+10, _window_width, 44)];
    toolBar.backgroundColor =RGBA(250, 250, 250, 0.8);// [UIColor whiteColor];
    UIView *tooBgv = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 44)];
    tooBgv.backgroundColor = [UIColor whiteColor];
    tooBgv.alpha = 0.7;
    [toolBar addSubview:tooBgv];
    
    [toolBar addSubview:pushBTN];
    [toolBar addSubview:keyField];
    [toolBar addSubview:cs];
    [frontView addSubview:keyBTN];
    //关闭连麦按钮
    //直播间按钮（竞拍，游戏，扣费，后台控制隐藏,createroom接口传进来）
    [self changeBtnFrame:_window_height - 45];
    [leftView addSubview:onlineLabel];
    [leftView addSubview:liveLabel];
    [leftView addSubview:IconBTN];
    [leftView addSubview:levelimage];
    [frontView addSubview:leftView];
    [frontView addSubview:chatApplyBtn];
    [frontView addSubview:controlMicBtn];
    [frontView addSubview:moreBTN];
    [frontView  addSubview:_liwuBTN];
    [frontView addSubview:goodsShowBtn];
    [frontView addSubview:messageBTN];
    [frontView addSubview:closeLiveBtn];
//    [frontView addSubview:linkSwitchBtn];
    [frontView addSubview:onlineBtn];

    [self.view addSubview:redBagBtn];

    [self hideBTN];
    /*==================  连麦  ================*/
    [self.view addSubview:toolBar];
    //增加监听，当键盘出现或改变时收出消息
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(ChangePushBtnState) name:UITextFieldTextDidChangeNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(denglushixiao) name:@"denglushixiao" object:nil];

    [self.view insertSubview:self.tableView atIndex:4];
    useraimation = [[userLoginAnimation alloc]init];
    useraimation.frame = CGRectMake(10,self.tableView.top - 40,_window_width,20);
    [self.view insertSubview:useraimation atIndex:4];
    danmuview = [[GrounderSuperView alloc] initWithFrame:CGRectMake(0, 100, self.view.frame.size.width, 140)];
    
    //手绘礼物
    [self.view  addSubview:self.paintedShowRegion];

    [frontView insertSubview:danmuview atIndex:5];
    liansongliwubottomview = [[UIView alloc]init];
    [self.view insertSubview:liansongliwubottomview belowSubview:frontView];
    liansongliwubottomview.frame = CGRectMake(0, self.tableView.top-150,_window_width/2,140);
//    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideSixin:)];
//
//    [self.view addGestureRecognizer:tapGesture];

}

-(void)showUserInfo:(NSString *)uidStr{
    isMicUser = YES;
    [self showButtleView:uidStr];
}

/*送礼物*/
-(void)doLiwu{
    if (giftview) {
        [giftview removeFromSuperview];
        giftview = nil;
    }
    NSMutableArray *muatArr = [NSMutableArray array];
    [muatArr addObject:@{@"uid":[Config getOwnID],@"num":YZMsg(@"主持"),@"avatar":[Config getavatar],@"sitid":@"0"}];
    if(isVoiceChat){
        for (YBUserLinkButton *btn in sitButtonArray) {
            if (btn.userDic && btn.userDic.count > 0) {
                [muatArr addObject:@{@"uid":minstr([btn.userDic valueForKey:@"uid"]),@"num":[NSString stringWithFormat:@"%@%@",[self getChinaWord:[btn.sitid intValue]],YZMsg(@"麦")],@"avatar":minstr([btn.userDic valueForKey:@"avatar"]),@"sitid":btn.sitid}];
            }
        }
    }else{
        for (ChatVideoUserLinkView *btn in sitVideoButtonArray) {
            if (btn.userDic && btn.userDic.count > 0) {
                if (![[btn.userDic valueForKey:@"uid"] isEqual:[Config getOwnID]] && btn.userDic.count>0 && ![minstr([btn.userDic valueForKey:@"uid"]) isEqual:@"0"]) {
                    [muatArr addObject:@{@"uid":minstr([btn.userDic valueForKey:@"uid"]),@"num":[NSString stringWithFormat:@"%@%@",[self getChinaWord:[btn.sitid intValue]],YZMsg(@"麦")],@"avatar":minstr([btn.userDic valueForKey:@"avatar"]),@"sitid":btn.sitid}];
                }
            }
        }

    }

    if (giftViewShow == NO) {
        giftViewShow = YES;
        if (!giftview) {
            //礼物弹窗
            NSMutableDictionary *dic = [NSMutableDictionary dictionary];
            [dic setDictionary:_roomDic];
            [dic setObject:[Config getOwnID] forKey:@"uid"];
            giftview = [[liwuview alloc]initWithDic:dic andMyDic:nil andAlluser:muatArr IsChatRoom:YES];
            giftview.giftDelegate = self;
             [self changeGiftViewFrameY:_window_height*3];
            [self.view addSubview:giftview];
        }else{
            [giftview chongzhiV:[Config getcoin]];
        }

        [self.view bringSubviewToFront:giftview];

        LiveUser *user = [Config myProfile];
        [giftview chongzhiV:user.coin];
        [UIView animateWithDuration:0.1 animations:^{
            [self changeGiftViewFrameY:_window_height - (_window_width/2+100+ShowDiff)];
        }];
        [self changecontinuegiftframeIndoliwu];
        [self showBTN];
    }

}
//改变连送礼物的frame
-(void)changecontinuegiftframeIndoliwu{
    
    liansongliwubottomview.frame = CGRectMake(_window_width, _window_height - (_window_width/2+100+ShowDiff)-140,_window_width/2,140);
}
#pragma gift delegate
//发送礼物
-(void)sendGift:(NSDictionary *)myDic andPlayDic:(NSDictionary *)playDic andData:(NSDictionary *)datas andLianFa:(NSString *)lianfa{
    haohualiwu = lianfa;
    NSString *info = [datas valueForKey:@"gifttoken"];
    NSString * level = [datas valueForKey:@"level"];
    LiveUser *users = [Config myProfile];
    users.level = level;
    [Config updateProfile:users];
    [socketL liveSendGift:level andINfo:info andlianfa:lianfa andGiftInfo:datas];
    if (rotationV) {
        [rotationV reloadcoins];
    }
    if (gameVC) {
        [gameVC reloadcoins];
    }
}
//点击礼物ye消失
-(void)zhezhaoBTNdelegate{
    giftViewShow = NO;
//    setFrontV.ZheZhaoBTN.hidden = YES;
    fenxiangV.hidden = YES;
    if (gameVC || rotationV ) {
        if (gameVC) {
            gameVC.frame = CGRectMake(_window_width, _window_height - 260, _window_width,260);
//             [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 265];
        }
        if (rotationV) {
            rotationV.frame = CGRectMake(_window_width, _window_height - _window_width/1.5, _window_width, _window_width);
//            [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - _window_width+_window_width/5];
        }
        [UIView animateWithDuration:0.5 animations:^{
            [self changeGiftViewFrameY:_window_height*3];
        }];

    }else{
        [UIView animateWithDuration:0.5 animations:^{
            [self changeGiftViewFrameY:_window_height *3];
//            [self tableviewheight:setFrontV.frame.size.height - _window_height*0.2 - 50 - ShowDiff];
        }];
    }
    keyBTN.hidden = NO;
    //wangminxinliwu
    [self changecontinuegiftframe];
    
    
    [self showBTN];
    if (huanxinviews) {
        if (sysView || tChatsamall) {
            return;
        }
        [huanxinviews.view removeFromSuperview];
        huanxinviews = nil;
        huanxinviews.view = nil;
    }
}
-(void)luckyBtnClickdelegate:(BOOL)isLuckGift{
    YBWebViewController *web = [[YBWebViewController alloc]init];
    if (isLuckGift) {
        NSString *language = [PublicObj getCurrentLanguage];
        web.urls = [NSString stringWithFormat:@"%@/portal/page/index?id=26&language=%@",h5url,language];
    }else{
        web.urls = [NSString stringWithFormat:@"%@",[common stricker_url]];
    }
    [[MXBADelegate sharedAppDelegate]pushViewController:web animated:YES];

}

-(void)changeGiftViewFrameY:(CGFloat)Y{
//    giftview.frame = CGRectMake(0,Y, _window_width, _window_width/2+100+ShowDiff);
    if (Y >= _window_height) {
        liansongliwubottomview.frame = CGRectMake(_window_width, self.tableView.top-150,300,140);
        giftview.frame = CGRectMake(0,Y, _window_width, _window_height);

    }else{
        giftview.frame = CGRectMake(0, 0, _window_width, _window_height);

    }
}

- (NSString *)getChinaWord:(int)sitid{
    NSArray *array = @[@"一",@"二",@"三",@"四",@"五",@"六",@"七",@"八"];
    if ([[Config canshu]isEqual:EN]) {
        array = @[@"1",@"2",@"3",@"4",@"5",@"6",@"7",@"8"];
    }
    NSString *string = array[sitid-1];
    NSLog(@"str = %@", string);
    return string;
}

//播放监听事件
//-(void) onPlayEvent:(int)EvtID withParam:(NSDictionary*)param
//{
//    dispatch_async(dispatch_get_main_queue(), ^{
//        if (EvtID == PLAY_EVT_CONNECT_SUCC) {
//            NSLog(@"play_linkMic已经连接服务器");
//        }
//        else if (EvtID == PLAY_EVT_RTMP_STREAM_BEGIN){
//            NSLog(@"play_linkMic已经连接服务器，开始拉流");
//        }
//        else if (EvtID == PLAY_EVT_PLAY_BEGIN){
//            NSLog(@"play_linkMic视频播放开始");
//        }
//        else if (EvtID== PLAY_WARNING_VIDEO_PLAY_LAG){
//            NSLog(@"play_linkMic当前视频播放出现卡顿（用户直观感受）");
//        }
//        else if (EvtID == PLAY_EVT_PLAY_END){
//            NSLog(@"play_linkMic视频播放结束");
//        }
//        else if (EvtID == PLAY_ERR_NET_DISCONNECT) {
//            NSLog(@"play_linkMic网络断连,且经多次重连抢救无效,可以放弃治疗,更多重试请自行重启播放");
////            if ([self.delegate respondsToSelector:@selector(tx_closeUserbyVideo:)]) {
////                [self.delegate tx_closeUserbyVideo:_subdic];
////            }
//        }
//    });
//}

#pragma mark------控麦点击--------
-(void)controlMicClick{
    if (_controlmicView) {
        [_controlmicView removeFromSuperview];
        _controlmicView = nil;
    }

    YBWeakSelf;
    _controlmicView = [[ControlMicView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)withStream:urlStrtimestring isVoiceRoom:isVoiceChat andSdkType:_sdkType];
    _controlmicView.delegate = self;
    _controlmicView.hideEvent = ^{
        [UIView animateWithDuration:0.5 animations:^{
            weakSelf.controlmicView.frame = CGRectMake(0, _window_height, _window_width, _window_height);
        } completion:^(BOOL finished) {
            [weakSelf.controlmicView removeFromSuperview];
            weakSelf.controlmicView = nil;

        }];
    };
    [self.view addSubview:_controlmicView];
}
#pragma mark------上麦申请--------
-(void)chatApplyClick{
    if (_chatapplyView) {
        [_chatapplyView removeFromSuperview];
        _chatapplyView = nil;
    }
    YBWeakSelf;
    _chatapplyView = [[ChatApplyView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)andStream:urlStrtimestring];
    _chatapplyView.delegate = self;
    _chatapplyView.hideEvent = ^{
        [UIView animateWithDuration:0.5 animations:^{
            weakSelf.chatapplyView.frame = CGRectMake(0, _window_height, _window_width, _window_height);
        } completion:^(BOOL finished) {
            [weakSelf.chatapplyView removeFromSuperview];
            weakSelf.chatapplyView = nil;

        }];
    };
    [self.view addSubview:_chatapplyView];
    applyTagLb.hidden = YES;
}
/*------------主播同意连麦------------*/
-(void)agreeApplyMicWithDic:(NSDictionary *)dic andPosition:(NSString *)position
{
    NSLog(@"chatrommviewcontroller---:%@",dic);
    NSDictionary *sendDic = @{@"touid":minstr([dic valueForKey:@"id"]),
                              @"toname":minstr([dic valueForKey:@"user_nickname"]),
                              @"avatar":minstr([dic valueForKey:@"avatar"]),
                              @"position":position
    };
    //
    [socketL agreeChatUserUp:sendDic];
    if(isVoiceChat){
        [self changeUserLinkBtn:sendDic];
    }else{
        [self changeUserVideoLinkBtn:sendDic];
    }
}


/*------------主播拒绝连麦------------*/
-(void)rejectApplyMicWithDic:(NSDictionary *)dic
{
    NSDictionary *sendDic = @{@"touid":minstr([dic valueForKey:@"id"]),
                              @"toname":minstr([dic valueForKey:@"user_nickname"]),
                              @"avatar":minstr([dic valueForKey:@"avatar"]),
                              @"position":@"-1"
    };
    //
    [socketL agreeChatUserUp:sendDic];
    [self changeUserLinkBtn:sendDic];

}
/*--------------主播把用户下麦----------*/
-(void)closeUserMic:(NSDictionary *)userDic
{
    [socketL closeUserMic:@"1" Uid:minstr([userDic valueForKey:@"id"])];
    [self removeUserLinkMic:minstr([userDic valueForKey:@"id"])];
}
//管理员下麦用户
-(void)closeUpMic:(NSString *)touid
{
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.closeUserVoiceMic"];
    
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":urlStrtimestring,
                          @"touid":touid,
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"userRoom-------:%@",data);
        [MBProgressHUD showError:msg];
        if ([code isEqual:@"0"]) {
            [socketL closeUserMic:@"2" Uid:touid];
            [self removeUserLinkMic:touid];
        }
        } Fail:^(id fail) {
            
    }];


}

-(void)socketcloseUserMic:(NSDictionary *)userDic
{
    [self removeUserLinkMic:minstr([userDic valueForKey:@"uid"])];

}

//改变麦状态
-(void)changeMicStatus:(NSDictionary *)micDic
{
    [socketL changeOnMicStatus:micDic];
    
}
-(void)socketChangeMicStatus:(NSDictionary *)dic
{
    [self changeLinkMicPostion:dic];

}
-(void)showBigEmjoe:(NSDictionary *)dic
{
    NSString *uidStr = [dic valueForKey:@"uid"];
    NSString *index = [dic valueForKey:@"face"];
    NSString *imgUrl = [NSString stringWithFormat:@"chatemoticon%@",index];
    if (isVoiceChat) {
        for (int i = 0; i < sitButtonArray.count; i ++) {
            YBUserLinkButton *btn = sitButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:uidStr]) {
                btn.imgUrl = imgUrl;
            }
        }
    }else {
        for (int i = 0; i<sitVideoButtonArray.count; i++) {
            ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:uidStr]) {
                btn.imgUrl = imgUrl;
            }
        }
    }

}
/*播上麦用户流*/
-(void)playRtmpUrl:(NSDictionary *)dic
{
    //语音聊天室
    if(isVoiceChat){
        NSString *uidStr = [dic valueForKey:@"uid"];
        for (int i = 0; i < sitButtonArray.count; i ++) {
            YBUserLinkButton *btn = sitButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:uidStr]) {
                btn.streamStr = [dic valueForKey:@"user_stream"];
                [self playRtmpDic:dic andIndex:i];
            }
        }
        [self  startHunliu];

    }else{
        //语音视频聊天室
        NSString *uidStr = [dic valueForKey:@"uid"];
        for (int i = 0; i < sitVideoButtonArray.count; i ++) {
            ChatVideoUserLinkView *userVideoView = sitVideoButtonArray [i];
            if ([[userVideoView.userDic valueForKey:@"uid"] isEqual:uidStr]) {
                userVideoView.streamStr = [dic valueForKey:@"user_stream"];
                userVideoView.pull = minstr([dic valueForKey:@"pull"]);
                [self playRtmpDic:dic andIndex:i];
            }
        }
//        [self startHunliuVideoChat];
    }
}
// 混流语音聊天室
-(void)startHunliu{
    NSMutableArray *hunliuArr = [NSMutableArray array];
    for (YBUserLinkButton *btn in sitButtonArray) {
        if (btn.userDic && btn.userDic.count > 0) {
            NSDictionary *userDic = @{@"stream":btn.streamStr,
                                      @"userid":[btn.userDic valueForKey:@"uid"]
            };
            [hunliuArr addObject:userDic];
        }
    }
    NSMutableArray *allStreams = [NSMutableArray array];
    
    V2TXLiveTranscodingConfig *config = [[V2TXLiveTranscodingConfig alloc] init];

    // 主播
    V2TXLiveMixStream *mainStream = [[V2TXLiveMixStream alloc] init];
    mainStream.streamId = nil;
    mainStream.userId = [Config getOwnID];
    mainStream.inputType = V2TXLiveMixInputTypePureAudio;
    [allStreams addObject:mainStream];
    
    for(int i = 0; i<hunliuArr.count ; i ++){
        V2TXLiveMixStream *subStream = [[V2TXLiveMixStream alloc] init];
        subStream.streamId = minstr([hunliuArr[i] valueForKey:@"stream"]);
        subStream.userId = minstr([hunliuArr[i] valueForKey:@"userid"]);
        subStream.inputType = V2TXLiveMixInputTypePureAudio;
        [allStreams addObject:subStream];

    }
    //设置混流 streams
    config.mixStreams = allStreams;
//    // 发起云端混流
    [[YBLiveRTCManager shareInstance]MixTranscoding:config];
}
//混流语音视频聊天室
-(void)startHunliuVideoChat{
    NSMutableArray *hunliuArr = [NSMutableArray array];
    for (ChatVideoUserLinkView *userVideoView in sitVideoButtonArray) {
//        if (userVideoView.userDic && userVideoView.userDic.count > 0) {
//        if (minstr([userVideoView.userDic valueForKey:@"avatar"]).length > 0 || ![minstr([userVideoView.userDic valueForKey:@"avatar"]) isEqual:@"0"]) {
        if (userVideoView.userDic && ![minstr([userVideoView.userDic valueForKey:@"avatar"]) isEqual:@"0"]) {

            NSDictionary *userDic = @{@"stream":userVideoView.streamStr,
                                      @"userid":[userVideoView.userDic valueForKey:@"uid"],
                                      @"otherUrl":userVideoView.pull
            };
            [hunliuArr addObject:userDic];
        }
    }
    CGFloat bWidth = (_window_width)/3;
//    CGFloat bWidth = (540)/3;

    CGFloat sitTop = liveTimeBGView.bottom+100;

    NSMutableArray *allStreams = [NSMutableArray array];
    
    V2TXLiveTranscodingConfig *v2Liveconfig = [[V2TXLiveTranscodingConfig alloc] init];
    v2Liveconfig.videoWidth =  _window_width;
//    v2Liveconfig.videoWidth =  540;
    v2Liveconfig.videoHeight = bWidth *2;
    v2Liveconfig.videoBitrate = 0;
    v2Liveconfig.videoFramerate  = 20;
    v2Liveconfig.backgroundImage = @"236298";
    v2Liveconfig.backgroundColor = 0x00000000;

    V2TXLiveMixStream *mainStream = [[V2TXLiveMixStream alloc] init];
    mainStream.streamId = nil;
    mainStream.userId = [Config getOwnID];
    mainStream.x = 0;
    mainStream.y = 0;
    mainStream.height = bWidth;
    mainStream.width = bWidth;
    mainStream.zOrder   = 0;
    mainStream.inputType = V2TXLiveMixInputTypeAudioVideo;
    if(hunliuArr.count > 1){
        
    }else{
        v2Liveconfig.videoWidth =  _window_width;
        v2Liveconfig.videoHeight = _window_width;
        mainStream.height = _window_width;
        mainStream.width = _window_width;

    }

    [allStreams addObject:mainStream];
    for (int i = 1; i < hunliuArr.count; i ++) {
        V2TXLiveMixStream *subStream = [[V2TXLiveMixStream alloc] init];
        
        NSString *otherUrl = minstr([hunliuArr[i] valueForKey:@"otherUrl"]);
        NSString *subStreamId = [self getStreamIDByStreamUrl:otherUrl];

        subStream.streamId = subStreamId;
        subStream.userId = minstr([hunliuArr[i] valueForKey:@"userid"]);
        subStream.height = bWidth;
        subStream.width = bWidth;
        subStream.x = (i%3)*(bWidth);
        subStream.y = (i/3)*(bWidth);
        subStream.zOrder = i;
        subStream.inputType = V2TXLiveMixInputTypeAudioVideo;
        [allStreams addObject:subStream];

    }
    //设置混流 streams
    v2Liveconfig.mixStreams = allStreams;
    // 发起云端混流
    [[YBLiveRTCManager shareInstance]MixTranscoding:v2Liveconfig];
}
-(NSString*) getStreamIDByStreamUrl:(NSString*) strStreamUrl {
    if (strStreamUrl == nil || strStreamUrl.length == 0) {
        return nil;
    }
    strStreamUrl = [strStreamUrl lowercaseString];
    //推流地址格式：rtmp://8888.livepush.myqcloud.com/live/8888_test_12345_test?txSecret=aaaa&txTime=bbbb
    NSString * strLive = @"/play/";
    NSRange range = [strStreamUrl rangeOfString:strLive];
    if (range.location == NSNotFound) {
        return nil;
    }
    NSString * strSubString = [strStreamUrl substringFromIndex:range.location + range.length];
    NSArray * array = [strSubString componentsSeparatedByCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@"?."]];
    if ([array count] > 0) {
        return [array objectAtIndex:0];
    }
    return @"";
}

- (NSString *)pictureArrayToJSON:(NSDictionary *)picArr {

    NSData *data=[NSJSONSerialization dataWithJSONObject:picArr options:NSJSONWritingPrettyPrinted error:nil];
    NSString *jsonStr=[[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    //去除空格和回车：
    jsonStr = [jsonStr stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    jsonStr = [jsonStr stringByReplacingOccurrencesOfString:@" " withString:@""];
    jsonStr = [jsonStr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    NSLog(@"jsonStr==%@",jsonStr);
    return jsonStr;
}
-(void)requestLink:(NSDictionary *)dicInfo andUrl:(NSString *)urlStr{
    YBWeakSelf;
    [YBNetworking postWithUrl:urlStr Dic:dicInfo Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        NSLog(@"===检查混流(uid%@)===code:%@==date:%@===msg:%@",[Config getOwnID],code,data,msg);
        if ([code isEqual:@"0"]) {
            NSLog(@"hunliuc成功 ");
        }else{
            if (linkCount > 3) {
                return;
            }else{
                linkCount ++;
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [weakSelf requestLink:dicInfo andUrl:urlStr];
                });

            }
        }
    } Fail:nil];

}

-(void)playRtmpDic:(NSDictionary *)dic andIndex:(int)index{
    V2TXLivePlayer *_txLivePlayer = livePlayerArray[index];

    NSString *url = [purl stringByAppendingFormat:@"?service=Live.getMicPullUrl"];
    NSDictionary *pardic = @{
                          @"uid":[Config getOwnID],
                          @"token":[Config getOwnToken],
                          @"stream":minstr([dic valueForKey:@"user_stream"])
                          };
    [YBNetworking postWithUrl:url Dic:pardic Suc:^(NSDictionary *data, NSString *code, NSString *msg) {
        if([code isEqual:@"0"]){
            
            NSDictionary *pullDic =[[data valueForKey:@"info"]firstObject];
            NSString *playUrl = minstr([pullDic valueForKey:@"play_url"]);

            if(!isVoiceChat){
                for (int i =0; i < sitVideoButtonArray.count; i ++) {
                    ChatVideoUserLinkView *userVideoView = sitVideoButtonArray[index];
                    if ([userVideoView.userDic valueForKey:@"position"]) {
                        userVideoView.pull =playUrl;
                        [_txLivePlayer setRenderView:userVideoView];
                    }
                }
            }
            [self startHunliuVideoChat];

            V2TXLiveCode result = [_txLivePlayer startLivePlay:playUrl];

            NSLog(@"chatrom--play_linkMicwangminxin%ld",result);
            if (result == -1)
            {}
            if( result != 0)
            {
                [_notification displayNotificationWithMessage:@"视频流播放失败" forDuration:5];
            }

        }else{
            [MBProgressHUD showMessage:msg];
        }
        } Fail:^(id fail) {
            
        }];

}

- (void)stopPlayRtmpIndex:(int)index{
    V2TXLivePlayer *_txLivePlayer = livePlayerArray[index];
    if(_txLivePlayer != nil)
    {
        [_txLivePlayer stopPlay];
    }
}

-(void)changeLinkMicPostion:(NSDictionary *)micDic{
    int index = [minstr([micDic valueForKey:@"position"]) intValue];
    NSString *status = minstr([micDic valueForKey:@"status"]);
    
    if(isVoiceChat){
        YBUserLinkButton *btn = sitButtonArray [index];
        [btn changeLinkBtnStatus:status];
    }else{
        if ([_sdkType isEqual:@"1"]) {
            ChatVideoUserLinkView *btn = sitVideoButtonArray [index];
            [btn changeLinkBtnStatus:status];
        }else{
            ChatVideoUserLinkView *btn = sitVideoButtonArray [index];
            [btn changeLinkBtnStatus:status];
            if ([minstr([micDic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
                if ([status isEqual:@"1"]){
                    [[YBAgoraManager shareInstance]muteLocalAudioStream:NO];
                }else if ([status isEqual:@"-1"]){
                    [[YBAgoraManager shareInstance]muteLocalAudioStream:YES];
                }
            }
        }
//        else{
//            11
//            ChatVideoUserLinkView *btn = sitVideoButtonArray [index];
//            [[YBAgoraManager shareInstance]showWithCanvasView:btn andUserId:minstr([micDic valueForKey:@"uid"]) isBroadcaster:AgoraClientRoleAudience];
//        }
    }
}
-(void)removeUserLinkMic:(NSString *)uid{
    if(isVoiceChat){
        for (int i = 0; i < sitButtonArray.count; i ++) {
            YBUserLinkButton *btn = sitButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:uid]) {
                btn.userDic = @{};
                [btn changeLinkBtnStatus:@"1"];
                if ([_sdkType isEqual:@"1"]) {
                    [self stopPlayRtmpIndex:i];
                }else{
//                    [[YBAgoraManager shareInstance]removePlayView:btn WithUserId:uid];
                }
            }
        }
    }else{
        for (int i = 0; i < sitVideoButtonArray.count; i ++) {
            ChatVideoUserLinkView *btn = sitVideoButtonArray [i];
            if ([[btn.userDic valueForKey:@"uid"] isEqual:uid]) {
                NSMutableDictionary *tempDic = [NSMutableDictionary dictionaryWithDictionary:btn.userDic];
                [tempDic setValue:@"0" forKey:@"avatar"];
                [tempDic setValue:@"0" forKey:@"id"];
                [tempDic setValue:@"0" forKey:@"uid"];

                btn.userDic = tempDic;
                if ([_sdkType isEqual:@"1"]) {
                    [self stopPlayRtmpIndex:i];
                }else{
                    [[YBAgoraManager shareInstance]removePlayView:btn WithUserId:uid];
                }
            }
        }
        [self startHunliuVideoChat];
    }
}

-(void)changeUserLinkBtn:(NSDictionary *)dic{
    NSDictionary *usseDic = @{@"uid":minstr([dic valueForKey:@"touid"]),@"user_nickname":minstr([dic valueForKey:@"toname"]),@"avatar":minstr([dic valueForKey:@"avatar"])};
    int index = [minstr([dic valueForKey:@"position"]) intValue];
    YBUserLinkButton *btn = sitButtonArray [index];
    btn.userDic = usseDic;

}
-(void)changeUserVideoLinkBtn:(NSDictionary *)dic{
    NSDictionary *usseDic = @{@"uid":minstr([dic valueForKey:@"touid"]),@"user_nickname":minstr([dic valueForKey:@"toname"]),@"avatar":minstr([dic valueForKey:@"avatar"]),@"position":minstr([dic valueForKey:@"position"])};
    int index = [minstr([dic valueForKey:@"position"]) intValue];
    ChatVideoUserLinkView *userVideoView = sitVideoButtonArray[index];
    userVideoView.userDic = usseDic;
    if(![_sdkType isEqual:@"1"]){
        //用户上麦画面
        [[YBAgoraManager shareInstance]showWithCanvasView:userVideoView.livePushView andUserId:minstr([dic valueForKey:@"touid"]) isBroadcaster:AgoraClientRoleAudience RenderMode:AgoraVideoRenderModeHidden];
    }
}
- (void)hideSixin:(UIButton *)sender{
//    [keyField resignFirstResponder];
    [self getweidulabel];

    if (tChatsamall||sysView) {
        if (tChatsamall) {
            [tChatsamall.view endEditing:YES];
        }
        return;
    }
    if (huanxinviews) {
        [huanxinviews.view removeFromSuperview];
        huanxinviews.view = nil;
        huanxinviews = nil;
        [self showBTN];
    }
    [sender removeFromSuperview];
    sender = nil;
}
- (void)doShareViewShow{
    if (!fenxiangV) {
        if (liveTitleTextView.text.length < 1) {
            liveTitleTextView.text = @"";
        }
        //分享弹窗
        fenxiangV = [[fenXiangView alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height)];
        NSDictionary *mudic = @{
                                @"user_nickname":[Config getOwnNicename],
                                @"avatar_thumb":[Config getavatarThumb],
                                @"uid":[Config getOwnID],
                                @"thumb":live_thumb,
                                @"title":liveTitleTextView.text
                                };
        
        [fenxiangV GetDIc:mudic];
        fenxiangV.roomType = @"voice";

        [self.view addSubview:fenxiangV];
    }else{
        [fenxiangV show];
    }

}
-(void)toolbarHidden
{
    [self showBTN];
    toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
    [UIView animateWithDuration:0.5 animations:^{
        tChatsamall.view.frame = CGRectMake(0, _window_height*3, _window_width, _window_height*0.4);
    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (tChatsamall) {
            [tChatsamall.view removeFromSuperview];
            tChatsamall.view = nil;
            tChatsamall = nil;
        }
    });
}
-(void)toolbarClick:(id)sender
{
    [keyField resignFirstResponder];
    toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
}

-(void)changeState{
    if (!yingpiaoLabel) {
        //魅力值//魅力值
        //修改 魅力值 适应字体 欣
        UIFont *font1 = [UIFont systemFontOfSize:12];
        NSString *str = [NSString stringWithFormat:@"%@ %@ >",[common name_votes],_voteNums];
        CGFloat width = [[YBToolClass sharedInstance] widthOfString:str andFont:font1 andHeight:20];
        yingpiaoLabel  = [[UILabel alloc]init];
        yingpiaoLabel.backgroundColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.6];
        yingpiaoLabel.font = font1;
        yingpiaoLabel.text = str;
        yingpiaoLabel.frame = CGRectMake(10,30+leftView.frame.size.height +statusbarHeight, width+30,20);
        yingpiaoLabel.textAlignment = NSTextAlignmentCenter;
        yingpiaoLabel.textColor = [UIColor whiteColor];
        yingpiaoLabel.layer.cornerRadius = 10.0;
        yingpiaoLabel.layer.masksToBounds  =YES;
        UITapGestureRecognizer *yingpiaoTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(yingpiao)];
        [yingpiaoLabel addGestureRecognizer:yingpiaoTap];
        yingpiaoLabel.userInteractionEnabled = YES;
        [frontView addSubview:yingpiaoLabel];
    }else{
        UIFont *font1 = [UIFont systemFontOfSize:12];
        NSString *str = [NSString stringWithFormat:@"%@ %@ >",[common name_votes],_voteNums];
        CGFloat width = [[YBToolClass sharedInstance] widthOfString:str andFont:font1 andHeight:20]+30;
        yingpiaoLabel.width = width;
        yingpiaoLabel.text = str;
        guardBtn.frame = CGRectMake(yingpiaoLabel.right+5, yingpiaoLabel.top, guardWidth+20, yingpiaoLabel.height);
    }
}
- (void)changeGuardNum:(NSString *)nums{
    if (!guardBtn) {
        guardWidth = [[YBToolClass sharedInstance] widthOfString:YZMsg(@"守护 虚位以待>") andFont:[UIFont systemFontOfSize:12] andHeight:20];
        guardBtn = [UIButton buttonWithType:0];
        guardBtn.frame = CGRectMake(yingpiaoLabel.right+5, yingpiaoLabel.top, guardWidth+20, yingpiaoLabel.height);
        guardBtn.backgroundColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.6];
        [guardBtn addTarget:self action:@selector(guardBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [guardBtn setTitle:YZMsg(@"守护 虚位以待>") forState:0];
        guardBtn.layer.cornerRadius = 10;
        guardBtn.layer.masksToBounds = YES;
        guardBtn.titleLabel.font = [UIFont systemFontOfSize:12];
        [frontView addSubview:guardBtn];
    }
    if (![nums isEqual:@"0"]) {
        [guardBtn setTitle:[NSString stringWithFormat:@"%@ %@%@ >",YZMsg(@"守护"),nums,YZMsg(@"人")] forState:0];
        guardWidth = [[YBToolClass sharedInstance] widthOfString:guardBtn.titleLabel.text andFont:[UIFont systemFontOfSize:12] andHeight:20];
        guardBtn.frame = CGRectMake(yingpiaoLabel.right+5, yingpiaoLabel.top, guardWidth+20, yingpiaoLabel.height);
    }
}
//跳往魅力值界面
-(void)yingpiao{
    NSString *language = [PublicObj getCurrentLanguage];

    YBWebViewController *jumpC = [[YBWebViewController alloc]init];
    jumpC.urls = [NSString stringWithFormat:@"%@/appapi/contribute/index?uid=%@&language=%@",h5url,[Config getOwnID],language];
    [[MXBADelegate sharedAppDelegate]pushViewController:jumpC animated:YES];
}
-(void)showmoreviews{
    NSArray * moreArr;
    if ([_dailytask_switch isEqual:@"0"]) {
        moreArr= @[@"分享",@"红包"];
    }else{
        moreArr= @[@"分享",@"红包",@"每日任务"];
    }

    YBWeakSelf;
    _bottomView = [[ChatBottomView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) Music:^(NSString *type) {
        if (isLianmai) {
            [MBProgressHUD showError:YZMsg(@"连麦时无法开启伴奏")];
            return;
        }

        [weakSelf justplaymusic];//播放音乐
    } Share:^(NSString *type) {
        [weakSelf doShareViewShow];//分享
    } Redbag:^(NSString *type) {
        //红包
        [weakSelf showRedView];

    } Task:^(NSString *type) {
        //每日任务
        if (!_taskView) {
            _taskView = [[DayTaskView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)andLiveUid:[Config getOwnID]];
//            _taskView.liveUid = [Config getOwnID];
            _taskView.closeEvent = ^{
                [weakSelf.taskView removeFromSuperview];
                weakSelf.taskView = nil;
            };
            [self.view addSubview:_taskView];
        }

    } Jackpot:^(NSString *type) {
        //奖池
        [self showJackpotView];

    }BigEmo:^(NSString *type) {
        
    } SMsg:^(NSString *type) {
        
    } MsgCount:@"0" UserLink:^(NSString *type) {
        
    }Hideself:^(NSString *type) {
        //隐藏
        [UIView animateWithDuration:0.4 animations:^{
            _bottomView.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
        }];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能"] forState:UIControlStateNormal];
            [_bottomView removeFromSuperview];
            _bottomView = nil;
        });

    }andDataArray:moreArr];//,@"幸运奖池"
        UIWindow *window = [UIApplication sharedApplication].delegate.window;
        [window addSubview:_bottomView];
        _bottomView.hidden = YES;
//        [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能_S"] forState:UIControlStateNormal];
    [moreBTN setBackgroundImage:[UIImage imageNamed:@"功能"] forState:UIControlStateNormal];

}
-(void)justplaymusic{
    musicView *music = [[musicView alloc]init];
    music.modalPresentationStyle = UIModalPresentationFullScreen;
    self.animator = [[ZFModalTransitionAnimator alloc] initWithModalViewController:music];
    self.animator.bounces = NO;
    self.animator.behindViewAlpha = 1;
    self.animator.behindViewScale = 0.5f;
    self.animator.transitionDuration = 0.4f;
    music.transitioningDelegate = self.animator;
    self.animator.dragable = YES;
    self.animator.direction = ZFModalTransitonDirectionRight;
    [self presentViewController:music animated:YES completion:nil];
}
-(void)showmoreview{
    
    if (!_bottomView) {
        [self showmoreviews];
    }
    
    if (_bottomView.hidden == YES) {
        _bottomView.hidden = NO;
        [UIView animateWithDuration:0.4 animations:^{
            _bottomView.frame = CGRectMake(0,0, _window_width, _window_height);
        }];
        
    }else{
        [UIView animateWithDuration:0.4 animations:^{
            _bottomView.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
        }];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            _bottomView.hidden = YES;
        });
    }
}
-(void)donghua:(UILabel *)labels{
    CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"transform"];
    animation.duration = 0.8;
    NSMutableArray *values = [NSMutableArray array];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(4.0, 4.0, 4.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(3.0, 3.0, 3.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(2.0, 2.0, 2.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(1.0, 1.0, 1.0)]];
    [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeScale(0.1, 0.1, 0.1)]];
    animation.values = values;
    animation.removedOnCompletion = NO;//是不是移除动画的效果
    animation.fillMode = kCAFillModeForwards;//保持最新状态
    [labels.layer addAnimation:animation forKey:nil];
}
#pragma mark ---- 私信方法
-(void)nsnotifition{
    //注册进入后台的处理
    NSNotificationCenter* notification = [NSNotificationCenter defaultCenter];
    [notification addObserver:self
           selector:@selector(appactive)
               name:UIApplicationDidBecomeActiveNotification
             object:nil];
    [notification addObserver:self
           selector:@selector(appnoactive)
               name:UIApplicationWillResignActiveNotification
             object:nil];

    [notification addObserver:self selector:@selector(shajincheng) name:@"shajincheng" object:nil];
    //@"shajincheng"
    [notification addObserver:self selector:@selector(forsixin:) name:@"sixinok" object:nil];
    [notification addObserver:self selector:@selector(getweidulabel) name:@"gengxinweidu" object:nil];
    [notification addObserver:self selector:@selector(toolbarHidden) name:@"toolbarHidden" object:nil];
    
    [notification addObserver:self selector:@selector(getChatRefresh:) name:ybChatRefresh object:nil];

}
-(void)getChatRefresh:(NSNotification*)noti {
    NSDictionary *notiDic = noti.userInfo;
    NSString *add_time = minstr([notiDic valueForKey:@"add_time"]);
    NSArray *curArray = [NSArray arrayWithArray:msgList];
    BOOL have = NO;
    for (int i = 0; i<curArray.count; i++) {
        NSDictionary *subDic = curArray[i];
        if ([minstr([subDic valueForKey:@"add_time"]) isEqual:add_time]) {
            have = YES;
            NSMutableDictionary *m_dic = [NSMutableDictionary dictionaryWithDictionary:subDic];
            [m_dic setObject:@"0" forKey:@"is_first"];
            [msgList replaceObjectAtIndex:i withObject:m_dic.mutableCopy];
        }
    }
    if (have) {
        NSLog(@"======图片加载......refresh");
        [_tableView reloadData];
    }
}
//更新未读消息
-(void)getweidulabel{
    [self labeiHid];
}
- (void)onTotalUnreadMessageCountChanged:(UInt64)totalUnreadCount {
    [self labeiHid];
}
-(void)labeiHid{
    __block NSInteger unRead = 0;
    [[YBImManager shareInstance]getAllUnredNumExceptUser:@[@"dsp_fans",@"dsp_like",@"dsp_at",@"dsp_comment"] complete:^(int allUnread) {
        unRead = allUnread;
        dispatch_async(dispatch_get_main_queue(), ^{
            self.unReadLabel.text = [NSString stringWithFormat:@"%ld",unRead];
            if ([self.unReadLabel.text isEqual:@"0"] || unRead <= 0) {
                self.unReadLabel.hidden =YES;
            }else {
                self.unReadLabel.hidden = NO;
            }
        });

    }];
}
//跳往消息列表
-(void)doMessage{
    [self hideBTN];
    [tChatsamall.view removeFromSuperview];
    tChatsamall = nil;
    tChatsamall.view = nil;
    [huanxinviews.view removeFromSuperview];
    huanxinviews = nil;
    huanxinviews.view = nil;
    if (!huanxinviews) {
        UIButton *btn = [UIButton buttonWithType:0];
        btn.frame = CGRectMake(0, 0, _window_width, _window_height*0.6);
        [btn addTarget:self action:@selector(hideSixin:) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:btn];
        huanxinviews = [[huanxinsixinview alloc]init];
        huanxinviews.view.frame = CGRectMake(0, _window_height*3, _window_width, _window_height*0.4);
        huanxinviews.zhuboID = @"";
        [huanxinviews forMessage];
        [self.view insertSubview:huanxinviews.view atIndex:9];

    }
    [UIView animateWithDuration:0.2 animations:^{
        huanxinviews.view.frame = CGRectMake(0, _window_height - _window_height*0.4,_window_width, _window_height*0.4);
    }];
}
//点击用户聊天
-(void)forsixin:(NSNotification *)ns{
    
    NSMutableDictionary *dic = [[ns userInfo] mutableCopy];
    if (sysView.view) {
        [sysView.view removeFromSuperview];
        sysView = nil;
        sysView.view = nil;
        
    }
    __weak ChatRoomViewController *wSelf = self;

    if ([[dic valueForKey:@"id"] isEqual:@"1"]) {
        if (liansongliwubottomview) {
            [self.view insertSubview:liansongliwubottomview belowSubview:frontView];
        }

        sysView = [[MsgSysVC alloc]init];
        sysView.view.frame = CGRectMake(_window_width, _window_height-_window_height*0.4, _window_width, _window_height*0.4);
        sysView.block = ^(int type) {
            if (type == 0) {
                [wSelf hideSysTemView];
            }
        };
        [sysView reloadSystemView];
        
        [self.view insertSubview:sysView.view atIndex:10];
        [UIView animateWithDuration:0.5 animations:^{
            sysView.view.frame = CGRectMake(0, _window_height-_window_height*0.4, _window_width, _window_height*0.4);
        }];
        return;
    }
    if([[dic valueForKey:@"id"] isEqual:@"goodsorder_admin"]){
        orderChat = [[OrderMessageVC alloc]init];
        [[MXBADelegate sharedAppDelegate]pushViewController:orderChat animated:YES];
        return;

    }

    [tChatsamall.view removeFromSuperview];
    tChatsamall = nil;
    tChatsamall.view = nil;
    
    if (!tChatsamall) {
        tChatsamall = [[TChatC2CController alloc]init];
        [dic setObject:minstr([dic valueForKey:@"name"]) forKey:@"user_nickname"];
        TConversationCellData *conv =[dic valueForKey:@"conversation"];
        tChatsamall.conversation = conv;
        tChatsamall.block = ^(int type) {
            if (type == 0) {
                [wSelf hideChatMall];
            }
            if (type == 1) {
//                if ([conv.convId isEqual:minstr([wSelf.playDoc valueForKey:@"uid"])]) {
//                    [wSelf isAttentionLive:@"1"];
//                }
            }
        };
        [tChatsamall reloadSamllChtaView:@"0"];
        [self.view insertSubview:tChatsamall.view atIndex:10];
        [self.view bringSubviewToFront:tChatsamall.view];

        if (liansongliwubottomview) {
            [self.view insertSubview:liansongliwubottomview atIndex:8];
        }
    }
    tChatsamall.view.hidden = NO;
}
- (void)hideSysTemView{
    [sysView.view removeFromSuperview];
    sysView = nil;
    sysView.view = nil;
    
}

-(void)siXin:(NSString *)icon andName:(NSString *)name andID:(NSString *)ID andIsatt:(NSString *)isatt{
    [tChatsamall.view removeFromSuperview];
    tChatsamall = nil;
    tChatsamall.view = nil;
    [huanxinviews.view removeFromSuperview];
    huanxinviews = nil;
    huanxinviews.view = nil;
    YBWeakSelf;
    if (!tChatsamall) {
        tChatsamall = [[TChatC2CController alloc]init];
        TConversationCellData *data = [[TConversationCellData alloc] init];
        data.convId = minstr(ID);
        data.convType = TConv_Type_C2C;
        data.userHeader = minstr(icon);
        data.userName = minstr(name);
        tChatsamall.conversation = data;

        tChatsamall.block = ^(int type) {
            if (type == 0) {
                [weakSelf hideChatMall];
            }
//            if (type == 1) {
//                if ([data.convId isEqual:minstr([wSelf.playDoc valueForKey:@"uid"])]) {
//                    [weakSelf isAttentionLive:@"1"];
//                }
//            }
        };
        [tChatsamall reloadSamllChtaView:@"0"];

        [self.view insertSubview:tChatsamall.view atIndex:10];
        if (liansongliwubottomview) {
            [self.view insertSubview:liansongliwubottomview atIndex:8];
        }
    }
    tChatsamall.view.hidden = NO;
}
- (void)hideChatMall{
    if (huanxinviews) {
        [huanxinviews forMessage];
        CATransition *transition = [CATransition animation];    //创建动画效果类
        transition.duration = 0.3; //设置动画时长
        transition.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];  //设置动画淡入淡出的效果
        transition.type = kCATransitionPush;//{kCATransitionMoveIn, kCATransitionPush, kCATransitionReveal, kCATransitionFade};设置动画类型，移入，推出等
        //更多私有{@"cube",@"suckEffect",@"oglFlip",@"rippleEffect",@"pageCurl",@"pageUnCurl",@"cameraIrisHollowOpen",@"cameraIrisHollowClose"};
        transition.subtype = kCATransitionFromLeft;//{kCATransitionFromLeft, kCATransitionFromRight, kCATransitionFromTop, kCATransitionFromBottom};
        [tChatsamall.view.layer addAnimation:transition forKey:nil];       //在图层增加动画效果
        [tChatsamall.view removeFromSuperview];
        tChatsamall.view = nil;
        tChatsamall = nil;
        
    }else{
        [UIView animateWithDuration:0.3 animations:^{
            tChatsamall.view.frame = CGRectMake(_window_width, _window_height*0.6, _window_width, _window_height*0.4);
        } completion:^(BOOL finished) {
            [tChatsamall.view removeFromSuperview];
            tChatsamall.view = nil;
            tChatsamall = nil;
        }];
    }
}

-(void)pushZhuYe:(NSString *)IDS{
    PersonHomeVC  *person = [[PersonHomeVC alloc]init];
    person.userID = IDS;
    [[MXBADelegate sharedAppDelegate]pushViewController:person animated:YES];

}
-(void)sendAtMsgClick:(NSString *)nameStr
{
    if (tChatsamall) {
        tChatsamall.view.hidden = YES;
        [tChatsamall.view removeFromSuperview];
        tChatsamall.view = nil;
        tChatsamall = nil;
    }
    ismessgaeshow = YES;

    [keyField becomeFirstResponder];
    keyField.text = [NSString stringWithFormat:@"@ %@",nameStr];
}

//键盘弹出隐藏下面四个按钮
-(void)hideBTN{
//    closeLiveBtn.hidden = YES;
    keyBTN.hidden = YES;
    messageBTN.hidden = YES;
    chatApplyBtn.hidden = YES;
    controlMicBtn.hidden = YES;
    moreBTN.hidden = YES;
    _liwuBTN.hidden = YES;
    bottombtnV.hidden = YES;
//    linkSwitchBtn.hidden = YES;
    if (goodsShowBtn) {
        goodsShowBtn.hidden = YES;
    }
}
-(void)showBTN{
//    closeLiveBtn.hidden = NO;
    keyBTN.hidden = NO;
    messageBTN.hidden = NO;
    chatApplyBtn.hidden = NO;
    controlMicBtn.hidden = NO;
    moreBTN.hidden = NO;
    _liwuBTN.hidden = NO;
//    linkSwitchBtn.hidden = NO;
    if (goodsShowBtn) {
        goodsShowBtn.hidden = NO;
    }
}
- (void)keyboardWillShow:(NSNotification *)aNotification
{
    //防止填写竞拍信息的时候弹出私信
//    startPKBtn.hidden = YES;
    if (!ismessgaeshow) {
        
        return;
    }
    if (startKeyboard == 1) {
        return;
    }
    if (gameVC) {
        gameVC.hidden = YES;
    }
    if (rotationV) {
        rotationV.hidden = YES;
    }

    [self hideBTN];
    //获取键盘的高度
    NSDictionary *userInfo = [aNotification userInfo];
    NSValue *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardRect = [aValue CGRectValue];
    CGFloat height = keyboardRect.origin.y;
    [UIView animateWithDuration:0.3 animations:^{
        toolBar.frame = CGRectMake(0,height-44,_window_width,44);
        frontView.frame = CGRectMake(0,-height+toolBar.height+ShowDiff+120, _window_width, _window_height);
        [self tableviewheight:_window_height - _window_height*0.2 - keyboardRect.size.height - 40];
        [self.view bringSubviewToFront:toolBar];
        [self.view insertSubview:self.tableView atIndex:4];
        [self changecontinuegiftframe];
        if (zhuangVC) {
            zhuangVC.frame =  CGRectMake(10,20, _window_width/4, _window_width/4 + 20 + _window_width/8);
        }

    }];
}
- (void)keyboardWillHide:(NSNotification *)aNotification
{
//    startPKBtn.hidden = NO;

    ismessgaeshow = NO;
    [self showBTN];
    if (gameVC) {
        gameVC.hidden = NO;
    }
    if (rotationV) {
        rotationV.hidden = NO;
    }

    [UIView animateWithDuration:0.1 animations:^{
        toolBar.frame = CGRectMake(0, _window_height+10, _window_width, 44);
        if (gameVC) {
            [self tableviewheight:_window_height - _window_height*0.2 - 240-www];

        }else if (rotationV){
            [self tableviewheight:_window_height - _window_height*0.2 - _window_width/1.8 - www];
        }
        else{
            [self tableviewheight:_window_height - _window_height*0.2 - 50 - ShowDiff];
        }
        frontView.frame = CGRectMake(0, 0, _window_width, _window_height);
        [self changecontinuegiftframe];
        if (zhuangVC) {
            zhuangVC.frame =  CGRectMake(10,90, _window_width/4, _window_width/4 + 20 + _window_width/8);
        }

    }];
}
-(void)adminZhezhao{
    zhezhaoList.view.hidden = YES;
    self.tableView.hidden = NO;
    [UIView animateWithDuration:0.3 animations:^{
        adminlist.view.frame = CGRectMake(0,_window_height*2, _window_width, _window_height*0.3);
    }];
}
//管理员列表
-(void)adminList{
    if (!adminlist) {
        //管理员列表
        zhezhaoList  = [[UIViewController alloc]init];
        zhezhaoList.view.frame = CGRectMake(0, 0, _window_width, _window_height);
        [self.view addSubview:zhezhaoList.view];
        zhezhaoList.view.hidden = YES;
        adminlist = [[adminLists alloc]init];
        adminlist.delegate = self;
        adminlist.view.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
        [self.view addSubview:adminlist.view];
        UITapGestureRecognizer *tapAdmin = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(adminZhezhao)];
        [zhezhaoList.view addGestureRecognizer:tapAdmin];
    }
    [[NSNotificationCenter defaultCenter]postNotificationName:@"adminlist" object:nil];
    self.tableView.hidden = YES;
    [UIView animateWithDuration:0.3 animations:^{
        zhezhaoList.view.hidden = NO;
        adminlist.view.frame = CGRectMake(0,0, _window_width, _window_height);
    }];
}
-(void)setAdminSuccess:(NSString *)isadmin andName:(NSString *)name andID:(NSString *)ID{
//    NSString *cts;
//    if ([isadmin isEqual:@"0"]) {
//        //不是管理员
//         cts = @"被取消管理员";
//        [MBProgressHUD showError:YZMsg(@"取消管理员成功")];
//    }else{
//        //是管理员
//          cts = @"被设为管理员";
//        [MBProgressHUD showError:@"设置管理员成功"];
//    }
     [socketL setAdminID:ID andName:name andCt:isadmin];
}
-(void)superStopRoom:(NSString *)state{
    [self hostStopRoom];
}
//发送消息
-(void)sendBarrage
{
    //屏蔽词过滤
    NSString *sendMsg = keyField.text;
    for (NSString *str in self.chatWordArr) {
        if ([sendMsg containsString:str]) {
           sendMsg =  [sendMsg  stringByReplacingOccurrencesOfString:str withString:@"***"];
        }
    }

    /*******发送弹幕开始 **********/
    NSString *url = [purl stringByAppendingFormat:@"?service=Live.sendBarrage"];
    NSDictionary *subdic = @{
                             @"liveuid":[Config getOwnID],
                             @"stream":urlStrtimestring,
                             @"giftid":@"1",
                             @"giftcount":@"1",
                             @"content":sendMsg
                             };
    [YBToolClass postNetworkWithUrl:@"Live.sendBarrage" andParameter:subdic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSString *barragetoken = [[info firstObject] valueForKey:@"barragetoken"];
            //刷新本地魅力值
            LiveUser *liveUser = [Config myProfile];
            liveUser.coin = [NSString stringWithFormat:@"%@",[[info firstObject] valueForKey:@"coin"]];
            [Config updateProfile:liveUser];
            [socketL sendBarrage:barragetoken];

        }
    } fail:^{
        
    }];
    
    /*********************发送礼物结束 ************************/
}
-(void)pushMessage:(UITextField *)sender{
    if (keyField.text.length >50) {
        [MBProgressHUD showError:YZMsg(@"字数最多50字")];
        return;
    }
    pushBTN.enabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        pushBTN.enabled = YES;
    });
    NSCharacterSet *set = [NSCharacterSet whitespaceAndNewlineCharacterSet];
    NSString *trimedString = [keyField.text stringByTrimmingCharactersInSet:set];
    if ([trimedString length] == 0) {
        
        return ;
    }
    if(cs.state == YES)//发送弹幕
    {
        
        if (keyField.text.length <=0) {
            return;
        }
        
        [self sendBarrage];
        keyField.text = @"";
        pushBTN.selected = NO;
        return;
    }
    //屏蔽词过滤
    NSString *sendMsg = keyField.text;
    for (NSString *str in self.chatWordArr) {
        if ([sendMsg containsString:str]) {
           sendMsg =  [sendMsg  stringByReplacingOccurrencesOfString:str withString:@"***"];
        }
    }

    [socketL sendMessage:sendMsg];
    keyField.text = @"";
    pushBTN.selected = NO;
}
//聊天输入框
-(void)showkeyboard:(UIButton *)sender{
    if (tChatsamall) {
        tChatsamall.view.hidden = YES;
        [tChatsamall.view removeFromSuperview];
        tChatsamall.view = nil;
        tChatsamall = nil;
    }
    ismessgaeshow = YES;
    [keyField becomeFirstResponder];
    
}
// 以下是 tableview的方法
///*******    连麦 注意下面的tableview方法    *******/
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return UITableViewAutomaticDimension;
}
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return self.chatModels.count;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return 1;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
       [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    chatMsgCell *cell = [tableView dequeueReusableCellWithIdentifier:@"chatMsgCELL"];
    if (!cell) {
        cell = [[[NSBundle mainBundle] loadNibNamed:@"chatMsgCell" owner:nil options:nil] lastObject];
    }
    cell.model = self.chatModels[indexPath.section];
    return cell;
}
- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 5;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *view = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, 5)];
    view.backgroundColor = [UIColor clearColor];
    return view;
}

-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];

    chatModel *model = self.chatModels[indexPath.section];
    [keyField resignFirstResponder];
    if ([model.userName isEqual:YZMsg(@"直播间消息")]) {
        return;
    }
    NSString *IsUser = [NSString stringWithFormat:@"%@",model.userID];
    if (IsUser.length > 1) {
        NSDictionary *subdic = @{@"id":model.userID,
                             @"name":model.userName
                             };
        [self GetInformessage:subdic];
    }
}
//请求直播
-(void)getStartShow
{
    _hostURL = minstr([_roomDic valueForKey:@"push"]);
    urlStrtimestring = [_roomDic valueForKey:@"stream"];
    _socketUrl = [_roomDic valueForKey:@"chatserver"];
    _danmuPrice = [_roomDic valueForKey:@"barrage_fee"];
//    if (![jackpot_level isEqual:@"-1"]) {
//        [self JackpotLevelUp:@{@"uplevel":jackpot_level}];
//    }

//    [self onStream:nil];
    _voteNums = [NSString stringWithFormat:@"%@",[_roomDic valueForKey:@"votestotal"]];
    [self changeState];
    [self changeGuardNum:minstr([_roomDic valueForKey:@"guard_nums"])];
    socketL = [[socketLive alloc]init];
    socketL.delegate = self;
    socketL.zhuboDic = _roomDic;
    [socketL getshut_time:_shut_time];//获取禁言时间
    [socketL addNodeListen:_socketUrl andTimeString:urlStrtimestring];
    userlist_time = [[_roomDic valueForKey:@"userlist_time"] intValue];
    if (!listTimer) {
        listTimer = [NSTimer scheduledTimerWithTimeInterval:userlist_time target:self selector:@selector(reloadUserList) userInfo:nil repeats:YES];
    }
    if (!hartTimer) {
        hartTimer = [NSTimer scheduledTimerWithTimeInterval:0.8 target:self selector:@selector(socketLight) userInfo:nil repeats:YES];
    }

    if (!liveingTimer) {
        liveingTimer = [NSTimer scheduledTimerWithTimeInterval:30 target:self selector:@selector(checkLiveingStatus) userInfo:nil repeats:YES];
    }
    //语音直播间
    if(isVoiceChat){
        if([_sdkType isEqual:@"1"]){
            [self txStartRTC];
        }else{
            [[YBAgoraManager shareInstance]joinChannelWithChannelId:urlStrtimestring andUserToken:minstr([_roomDic valueForKey:@"user_sw_token"]) WithModel:RtcMode_Chat isBroadcaster:AgoraClientRoleBroadcaster isGameLive:NO];
            [[YBAgoraManager shareInstance]showWithCanvasView:_pushPreview andUserId:[Config getOwnID] isBroadcaster:AgoraClientRoleBroadcaster RenderMode:AgoraVideoRenderModeHidden];
        }
    }else{
        if([_sdkType isEqual:@"1"]){
            //语音视频直播间
            [[YBLiveRTCManager shareInstance]startPush:_hostURL isGameLive:NO];
            [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
        }else{
            [[YBAgoraManager shareInstance]joinChannelWithChannelId:urlStrtimestring andUserToken:minstr([_roomDic valueForKey:@"user_sw_token"]) WithModel:RtcMode_Living isBroadcaster:AgoraClientRoleBroadcaster isGameLive:NO];
            [[YBAgoraManager shareInstance]showWithCanvasView:_anchorLinkView.livePushView andUserId:[Config getOwnID] isBroadcaster:AgoraClientRoleBroadcaster RenderMode:AgoraVideoRenderModeHidden];

        }
    }
}
#pragma mark - 加入频道成功
-(void)joinChannelSuc{
    [self changePlayState:1];
}
//改变tableview高度
-(void)tableviewheight:(CGFloat)h{
    self.tableView.frame = CGRectMake(10,h,tableWidth,_window_height*0.2);
    useraimation.frame = CGRectMake(10,self.tableView.top - 40,_window_width,20);
}
//改变连送礼物的frame
-(void)changecontinuegiftframe{
    liansongliwubottomview.frame = CGRectMake(0, self.tableView.top - 150,_window_width/2,140);
}
-(void)changeBtnFrame:(CGFloat)bottombtnH{
    //bottombtnH = bottombtnH-ShowDiff;
    if(bottombtnH == _window_height - 45){
        bottombtnH = bottombtnH-ShowDiff;
    }
    keyBTN.frame = CGRectMake(10, bottombtnH, 130, www);
    faceIcon.frame = CGRectMake(keyBTN.width-26, 4, 22, 22);

    controlMicBtn.frame = CGRectMake(_window_width - www*5-50,bottombtnH, www, www);
    chatApplyBtn.frame= CGRectMake(_window_width - www*4-40,bottombtnH, www, www);
    applyTagLb.frame = CGRectMake(chatApplyBtn.width-10, 5, 6, 6);
    messageBTN.frame =CGRectMake(_window_width - www*3-30,bottombtnH, www, www);
    moreBTN.frame =CGRectMake(_window_width - www*2 - 20,bottombtnH, www, www);
    _liwuBTN.frame =CGRectMake(_window_width - www - 10,bottombtnH, www, www);
    if (goodsShowBtn) {
        goodsShowBtn.frame = CGRectMake(_window_width - www*4-40,bottombtnH, www, www);
//        startPKBtn.frame = CGRectMake(_window_width - www*6-50 - 20,bottombtnH, www*2, www);

    }else{
//        startPKBtn.frame = CGRectMake(_window_width - www*5-40 - 20,bottombtnH, www*2, www);
    }

//    linkSwitchBtn.frame = CGRectMake(_window_width - www*1.5-10,bottombtnH-10-www*1.5, www*1.5, www*1.5);

    [frontView insertSubview:keyBTN atIndex:6];
//    [frontView insertSubview:closeLiveBtn atIndex:6];
    [frontView insertSubview:messageBTN atIndex:6];
    [frontView insertSubview:moreBTN atIndex:6];
    [frontView insertSubview:_liwuBTN atIndex:6];
    [frontView insertSubview:chatApplyBtn atIndex:6];
    [frontView insertSubview:controlMicBtn atIndex:6];

    [self showBTN];
}
-(void)pushCoinV{
//    CoinVeiw *coin = [[CoinVeiw alloc] init];
//    [self presentViewController:coin animated:YES completion:nil];
    [self requestPayList];

}
#pragma mark ============充值=============
- (void)requestPayList{
    if (!payView) {
        [YBToolClass postNetworkWithUrl:@"User.GetBalance" andParameter:@{@"type":@"1"} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
            if (code == 0) {
                NSDictionary *infoDic = [info firstObject];
                if (!payView) {
                    payView = [[roomPayView alloc]initWithMsg:infoDic andFrome:1];
                    [self.view addSubview:payView];
                }
                [payView show];
                [self.view bringSubviewToFront:payView];
            }
        } fail:^{
            
        }];
    }else{
        [payView show];
        [self.view bringSubviewToFront:payView];
        
    }
    
}

//********************************转盘*******************************************************************//
-(void)reloadUserList{
    if (listView) {
        [listView listReloadNoew];
    }
}
- (void)loginOnOtherDevice{
    
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:YZMsg(@"当前账号已在其他设备登录") message:nil preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:YZMsg(@"确定") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [self hostStopRoom];
    }]];
    [self presentViewController:alert animated:YES completion:nil];
}

//请求关闭直播
-(void)getCloseShow
{
    if([_sdkType isEqual:@"1"]){
        for (int i = 0; i < livePlayerArray.count; i ++) {
            V2TXLivePlayer *_txLivePlayer = livePlayerArray[i];
            if(_txLivePlayer != nil)
            {
                [_txLivePlayer stopPlay];
                _txLivePlayer = nil;
            }
        }
    }else{
        [[YBAgoraManager shareInstance]leaveChannel:nil];
    }
    [self liveOver];//停止计时器
    NSDictionary *signdic = @{@"uid":[Config getOwnID],@"token":[Config getOwnToken],@"time":[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],@"stream":urlStrtimestring};
    NSString *sign = [YBToolClass dynamicSortString:signdic];

    NSString *url = [NSString stringWithFormat:@"Live.stopRoom&uid=%@&token=%@&stream=%@&time=%@&sign=%@",[Config getOwnID],[Config getOwnToken],urlStrtimestring,[NSNumber numberWithLong: (long)[[NSDate date] timeIntervalSince1970]],sign];
    [YBToolClass postNetworkWithUrl:url andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        [MBProgressHUD hideHUD];
        [socketL closeRoom];//发送关闭直播的socket

        [self dismissVC];
        [socketL colseSocket];//注销socket
        socketL = nil;//注销socket
        //直播结束
        [self onQuit:nil];//停止音乐、停止推流
        [self rmObservers];//释放通知
        //            [self.navigationController popViewControllerAnimated:YES];
        [self requestLiveAllTimeandVotes];
    } fail:^{
        [MBProgressHUD hideHUD];
        [socketL closeRoom];//发送关闭直播的socket
        [self dismissVC];
        [socketL colseSocket];//注销socket
        socketL = nil;//注销socket
        //直播结束
        [self onQuit:nil];//停止音乐、停止推流
        [self rmObservers];//释放通知
        //        [self.navigationController popViewControllerAnimated:YES];
        [self requestLiveAllTimeandVotes];

    }];
}

//-(void)clearSticker{
//    [[NSUserDefaults standardUserDefaults]setBool:NO forKey:HAVESTICKER];
//    [[NSUserDefaults standardUserDefaults]setObject:@"" forKey:STICKERKEY];
//    [[NSUserDefaults standardUserDefaults]setBool:NO forKey:STICKERNOTUSE];
//
//}
//礼物效果
/************ 礼物弹出及队列显示开始 *************/
//红包
-(void)redbag{
    
}

//全站飘屏礼物
-(void)platGiftdelegate:(NSDictionary *)giftData
{
    
    if (!platliwuV) {
        platliwuV = [[PlatGiftView alloc]initWithIsPlat:YES];
        platliwuV.delegate = self;
//        [self.view insertSubview:platliwuV atIndex:8];
        if (_tx_playrtmp) {
            [self.view insertSubview:platliwuV aboveSubview:_tx_playrtmp];
        }else {
            //ray-声网
//            [self.view insertSubview:platliwuV aboveSubview:_js_playrtmp];

        }
    }
    if (giftData == nil) {
        
        
    }
    else
    {
        [platliwuV addArrayCount:giftData];
    }
    if(platliwuV.haohuaCount == 0){
        [platliwuV enGiftEspensive:YES];
    }

}
-(void)platGift:(NSDictionary *)giftData{
    if (!platliwuV) {
        platliwuV = [[PlatGiftView alloc]initWithIsPlat:YES];
        platliwuV.delegate = self;
        [self.view insertSubview:platliwuV atIndex:8];

//        [self.view addSubview:platliwuV];
//        [backScrollView insertSubview:platliwuV atIndex:8];
//        CGAffineTransform t = CGAffineTransformMakeTranslation(_window_width, 0);
//        platliwuV.transform = t;
    }
    if (giftData == nil) {
        
    }else
    {
        [platliwuV addArrayCount:giftData];
    }
    if(platliwuV.haohuaCount == 0){
        [platliwuV enGiftEspensive:YES];
    }

}

-(void)expensiveGiftdelegate:(NSDictionary *)giftData{
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]initWithIsPlat:NO];
        haohualiwuV.delegate = self;
        [self.view insertSubview:haohualiwuV atIndex:8];
        if (_tx_playrtmp) {
            [self.view insertSubview:haohualiwuV aboveSubview:_tx_playrtmp];
        }
        //ray-声网
//        if (_js_playrtmp) {
//            [self.view insertSubview:haohualiwuV aboveSubview:_js_playrtmp];
//        }
    }
    if (giftData == nil) {
        
        
        
    }
    else
    {
        [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive:NO];
    }
}
-(void)expensiveGift:(NSDictionary *)giftData isPlatGift:(BOOL)isPlat{
    
    
    if (!haohualiwuV) {
        haohualiwuV = [[expensiveGiftV alloc]initWithIsPlat:isPlat];
        haohualiwuV.delegate = self;
        [self.view insertSubview:haohualiwuV atIndex:8];
        if (_tx_playrtmp) {
            [self.view insertSubview:haohualiwuV aboveSubview:_tx_playrtmp];
        }
        //ray-声网
//        if (_js_playrtmp) {
//            [self.view insertSubview:haohualiwuV aboveSubview:_js_playrtmp];
//        }
    }
    if (giftData == nil) {
    }
    else
    {
        [haohualiwuV addArrayCount:giftData];
    }
    if(haohualiwuV.haohuaCount == 0){
        [haohualiwuV enGiftEspensive:isPlat];
    }
}
/*
 *添加魅力值数
 */
-(void)addCoin:(long)coin
{
    long long ordDate = [_voteNums longLongValue];
    long long newDate = ordDate + coin;
    _voteNums = [NSString stringWithFormat:@"%lld",newDate];
    [self changeState];
}
-(void)addvotesdelegate:(NSString *)votes{
    [self addCoin:[votes longLongValue]];
}
-(void)switchState:(BOOL)state
{
    NSLog(@"%d",state);
    if(!state)
    {
        keyField.placeholder = YZMsg(@"和大家说些什么");
    }
    else
    {
        keyField.placeholder = [NSString stringWithFormat:@"%@，%@%@/%@",YZMsg(@"开启弹幕"),_danmuPrice,[common name_coin],YZMsg(@"条")];
    }
}
- (BOOL)shouldAutorotate {
    return YES;
}
-(void)setBgAndPreview {
    pushbottomV = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    pushbottomV.backgroundColor = [UIColor clearColor];
    [self.view addSubview:pushbottomV];
    
    pkBackImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0,0,_window_width,_window_height)];
    pkBackImgView.image = [UIImage imageNamed:@"pk背景"];
    pkBackImgView.userInteractionEnabled = YES;
    pkBackImgView.hidden = YES;
    [pushbottomV addSubview:pkBackImgView];
    
    _pushPreview = [[UIView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    _pushPreview.backgroundColor = [UIColor clearColor];
    [pushbottomV addSubview:_pushPreview];
}
//设置videoview拖拽点击
-(void)addvideoswipe{
    [self showBTN];
    if (!videopan) {
        videopan = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(handlepanss:)];
        [pushbottomV addGestureRecognizer:videopan];
    }
    if (!videotap) {
        videotap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(videotap)];
    }
    
}
-(void)videotap{
    useraimation.hidden = NO;
    vipanimation.hidden = NO;
    [pushbottomV removeGestureRecognizer:videopan];
    videopan = nil;
    [UIView beginAnimations:nil context:nil];
    [UIView setAnimationDuration:0.1];
    pushbottomV.transform = CGAffineTransformScale(CGAffineTransformIdentity, 1.0f, 1.0f);
    [UIView commitAnimations];
    pushbottomV.frame = CGRectMake(0,0,_window_width,_window_height);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.tableView.hidden = NO;
        listView.hidden       = NO;
        frontView.hidden      = NO;
        danmuview.hidden      = NO;
        liansongliwubottomview.hidden = NO;
        haohualiwuV.hidden = NO;
        platliwuV.hidden = NO;
        [self showBTN];
    });
}
- (void) handlepanss: (UIPanGestureRecognizer *)sender{
    CGPoint point = [sender translationInView:sender.view];
    CGPoint center = sender.view.center;
    center.x += point.x/3;
    center.y += point.y/3;
    if (center.x <0 ) {
        center.x = 0;
    }
    if (center.x >_window_width) {
        center.x = _window_width - _window_width*0.3;
    }
    if (center.y <0) {
        center.y = 0;
    }
    if ( center.y > _window_height ) {
        center.y = _window_height - _window_height*0.3;
    }
    pushbottomV.center = center;
    //清空
    [sender setTranslation:CGPointZero inView:sender.view];
}
- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self.view endEditing:YES];
    CGPoint origin = [[touches anyObject] locationInView:self.view];
    CGPoint location;
    location.x = origin.x/self.view.frame.size.width;
    location.y = origin.y/self.view.frame.size.height;
    [self onSwitchRtcView:location];
    
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField;
{
    if (textField == keyField) {
        [self pushMessage:nil];
    }
    return YES;
}
-(void) onSwitchRtcView:(CGPoint)location
{
    
}
- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
}

#pragma mark ================ 连麦 ===============
/**
 连麦成功，拉取连麦用户的流
 
 @param playurl 流地址
 @param userid 用户ID
 */
-(void)getSmallRTMP_URL:(NSString *)playurl andUserID:(NSString *)userid{
    isAnchorLink = NO;
    if ([_sdkType isEqual:@"1"]) {
        if (_tx_playrtmp) {
            [_tx_playrtmp stopConnect];
            [_tx_playrtmp stopPush];
            [_tx_playrtmp removeFromSuperview];
            _tx_playrtmp = nil;
        }
        _tx_playrtmp = [[TXPlayLinkMic alloc]initWithRTMPURL:@{@"playurl":playurl,@"pushurl":@"0",@"userid":userid} andFrame:CGRectMake(_window_width - 100, _window_height - 110 -statusbarHeight - 150 , 100, 150) andisHOST:YES andAnToAn:NO];
        _tx_playrtmp.delegate = self;
        _tx_playrtmp.tag = 1500 + [userid intValue];
        [self.view addSubview:_tx_playrtmp];
        [self.view insertSubview:_tx_playrtmp aboveSubview:self.tableView];
        //混流
        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":playurl};
        [_tx_playrtmp hunliu:hunDic andHost:NO];
        [self huanCunLianMaiMessage:playurl andUserID:userid];
    }else{
        //ray-声网
//        if (_js_playrtmp) {
//            [_js_playrtmp stopConnect];
//            [_js_playrtmp stopPush];
//            [_js_playrtmp removeFromSuperview];
//            _js_playrtmp = nil;
//        }
//        _js_playrtmp = [[JSPlayLinkMic alloc]initWithRTMPURL:@{@"playurl":playurl,@"pushurl":@"0",@"userid":userid} andFrame:CGRectMake(_window_width - 100, _window_height - 110 -statusbarHeight - 150 , 100, 150) andisHOST:YES];
//        _js_playrtmp.delegate = self;
//        _js_playrtmp.tag = 1500 + [userid intValue];
//        [self.view addSubview:_js_playrtmp];
//        [self.view insertSubview:_js_playrtmp aboveSubview:self.tableView];
//        [self huanCunLianMaiMessage:playurl andUserID:userid];
    }
    
    
    
}
#pragma mark -  腾讯连麦start
-(void)tx_closeuserconnect:(NSString *)uid{
//    if (pkAlertView) {
//        return;
//    }
    if ([_sdkType isEqual:@"1"] && _tx_playrtmp) {
        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":@""};
        [_tx_playrtmp hunliu:hunDic andHost:YES];
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
    //主播端
    if (isAnchorLink) {
        [socketL anchor_DuankaiLink];
    }else{
        [socketL closeconnectuser:uid];
    }
    [self changeLivebroadcastLinkState:NO];
}

#pragma mark -  腾讯连麦end

/**
 有人下麦
 
 @param uid UID
 */
-(void)usercloseConnect:(NSString *)uid{
    //ray-声网
//    if (_js_playrtmp) {
//        [_js_playrtmp stopConnect];
//        [_js_playrtmp stopPush];
//        [_js_playrtmp removeFromSuperview];
//        _js_playrtmp = nil;
//    }
    if (_tx_playrtmp) {
        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":@""};
        [_tx_playrtmp hunliu:hunDic andHost:NO];
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
    [UIApplication sharedApplication].idleTimerDisabled = YES;
}
//主播关闭某人的连麦
-(void)js_closeuserconnect:(NSString *)uid{
    if (pkAlertView) {
        return;
    }
    if ([_sdkType isEqual:@"1"] && _tx_playrtmp) {
        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":@""};
        [_tx_playrtmp hunliu:hunDic andHost:YES];
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
    if (isAnchorLink) {
        [socketL anchor_DuankaiLink];
    }else{
        [socketL closeconnectuser:uid];
    }
    [self changeLivebroadcastLinkState:NO];
}
//请求接口，服务器缓存连麦者信息
- (void)huanCunLianMaiMessage:(NSString *)playurl andUserID:(NSString *)touid{
    
    NSDictionary *parameterDic = @{
                                   @"pull_url":playurl,
                                   @"touid":touid
                                   };
    [YBToolClass postNetworkWithUrl:@"Live.showVideo" andParameter:parameterDic success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            
        }
    } fail:^{
        
    }];
    

}
/**
 更改Livebroadcast中的连麦状态
 
 @param islianmai 是否在连麦
 */
- (void)changeLivebroadcastLinkState:(BOOL)islianmai{
    isLianmai = islianmai;
}
#pragma mark ================ 改变发送按钮图片 ===============
- (void)ChangePushBtnState{
    if (keyField.text.length > 0) {
        pushBTN.selected = YES;
    }else{
        pushBTN.selected = NO;
    }
}
#pragma mark ================ 守护 ===============
- (void)guardBtnClick{
    gShowView = [[guardShowView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) andUserGuardMsg:nil andLiveUid:[Config getOwnID]];
    gShowView.delegate = self;
    [self.view addSubview:gShowView];
    [gShowView show];
}
- (void)removeShouhuView{
    if (gShowView) {
        [gShowView removeFromSuperview];
        gShowView = nil;
    }
    if (anchorView) {
        [anchorView removeFromSuperview];
        anchorView = nil;
    }
    if (redList) {
        [redList removeFromSuperview];
        redList = nil;
    }
    if (pkAlertView) {
        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
//        startPKBtn.hidden = NO;
//        [frontView addSubview:startPKBtn];

    }

}
- (void)updateGuardMsg:(NSDictionary *)dic{
    _voteNums = minstr([dic valueForKey:@"votestotal"]);
    [self changeState];
    [self changeGuardNum:minstr([dic valueForKey:@"guard_nums"])];
    if (listView) {
        [listView listReloadNoew];
    }
    
}
#pragma mark ================ 主播连麦 ===============
- (void)showAnchorView{
    anchorView = [[anchorOnline alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    anchorView.delegate = self;
    anchorView.myStream = minstr([_roomDic valueForKey:@"stream"]);
    [self.view addSubview:anchorView];
    [anchorView show];
}
- (void)startLink:(NSDictionary *)dic andMyInfo:(NSDictionary *)myInfo{
        [MBProgressHUD showError:YZMsg(@"连麦请求已发送")];
        [socketL anchor_startLink:dic andMyInfo:myInfo];
}
//- (void)anchor_agreeLink:(NSDictionary *)dic{
//    [MBProgressHUD showError:YZMsg(@"对方主播接受了您的连麦请求，开始连麦")];
//
//    isAnchorLink = YES;
//    pkBackImgView.hidden = NO;
//    isLianmai = YES;
//    [UIView animateWithDuration:0.3 animations:^{
//        _pushPreview.frame = CGRectMake(0, 130+statusbarHeight, _window_width/2, _window_width*2/3);
//        if (![_sdkType isEqual:@"1"]) {
//            _gpuStreamer.preview.size = CGSizeMake(_window_width/2, _window_width*2/3);
//        }
//
//    }];
//    if ([_sdkType isEqual:@"1"]) {
//        if (_tx_playrtmp) {
//            [_tx_playrtmp stopConnect];
//            [_tx_playrtmp stopPush];
//            [_tx_playrtmp removeFromSuperview];
//            _tx_playrtmp = nil;
//        }
//        _tx_playrtmp = [[TXPlayLinkMic alloc]initWithRTMPURL:@{@"playurl":minstr([dic valueForKey:@"pkpull"]),@"pushurl":@"0",@"userid":minstr([dic valueForKey:@"pkuid"])} andFrame:CGRectMake(_window_width/2, 130+statusbarHeight , _window_width/2, _window_width*2/3) andisHOST:YES];
//        _tx_playrtmp.delegate = self;
//        _tx_playrtmp.tag = 1500 + [minstr([dic valueForKey:@"pkuid"]) intValue];
//        [self.view addSubview:_tx_playrtmp];
//        [self.view insertSubview:redBagBtn aboveSubview:_tx_playrtmp];
//        [self.view insertSubview:toolBar aboveSubview:_tx_playrtmp];
//        if (musicV) {
//            [self.view insertSubview:musicV aboveSubview:_tx_playrtmp];
//        }
//        if (haohualiwuV) {
//            [self.view insertSubview:haohualiwuV aboveSubview:_js_playrtmp];
//        }
//
//        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":minstr([dic valueForKey:@"pkpull"])};
//        [_tx_playrtmp hunliu:hunDic andHost:YES];
//        [self closeBgm];
//    }else{
//        if (_js_playrtmp) {
//            [_js_playrtmp stopConnect];
//            [_js_playrtmp stopPush];
//            [_js_playrtmp removeFromSuperview];
//            _js_playrtmp = nil;
//        }
//        _js_playrtmp = [[JSPlayLinkMic alloc]initWithRTMPURL:@{@"playurl":minstr([dic valueForKey:@"pkpull"]),@"pushurl":@"0",@"userid":minstr([dic valueForKey:@"pkuid"])} andFrame:CGRectMake(_window_width/2, 130+statusbarHeight , _window_width/2, _window_width*2/3) andisHOST:YES];
//        _js_playrtmp.delegate = self;
//        _js_playrtmp.tag = 1500 + [minstr([dic valueForKey:@"pkuid"]) intValue];
//        [self.view addSubview:_js_playrtmp];
//        [self.view insertSubview:redBagBtn aboveSubview:_js_playrtmp];
//        [self.view insertSubview:toolBar aboveSubview:_js_playrtmp];
//        if (musicV) {
//            [self.view insertSubview:musicV aboveSubview:_js_playrtmp];
//        }
//        if (haohualiwuV) {
//            [self.view insertSubview:haohualiwuV aboveSubview:_js_playrtmp];
//
//        }
//    }
//    startPKBtn.hidden = NO;
//    [frontView addSubview:startPKBtn];
//
//}
//
//断开连麦
- (void)anchor_stopLink:(NSDictionary *)dic{
    if (pkAlertView) {
        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
    //ray-声网
//    if (_js_playrtmp) {
//        [_js_playrtmp stopConnect];
//        [_js_playrtmp stopPush];
//        [_js_playrtmp removeFromSuperview];
//        _js_playrtmp = nil;
//    }
    if (_tx_playrtmp) {
        NSDictionary *hunDic = @{@"selfUrl":_hostURL,@"otherUrl":@""};
        [_tx_playrtmp hunliu:hunDic andHost:YES];
        [_tx_playrtmp stopConnect];
        [_tx_playrtmp stopPush];
        [_tx_playrtmp removeFromSuperview];
        _tx_playrtmp = nil;
    }
    if (pkView) {
        [pkView removeTimer];
        [pkView removeFromSuperview];
        pkView = nil;
    }

    [UIView animateWithDuration:0.3 animations:^{
        _pushPreview.frame = CGRectMake(0, 0, _window_width, _window_height);
        if (![_sdkType isEqual:@"1"]) {
            //ray-声网
//             _gpuStreamer.preview.size = CGSizeMake(_window_width, _window_height);
        }
    }];
    isAnchorLink = NO;
    [self changeLivebroadcastLinkState:NO];
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    
}
#pragma mark ================ PK ===============
-(void)removePKView{
    if (pkView) {
        [pkView removeTimer];
        [pkView removeFromSuperview];
        pkView = nil;
        if (isAnchorLink) {
            //            startPKBtn.hidden = NO;
//            [frontView addSubview:startPKBtn];
            
        }
    }
}
//- (void)startPKBtnClick{
////    startPKBtn.hidden = YES;
//    [startPKBtn removeFromSuperview];
//    if (pkAlertView) {
//        [pkAlertView removeFromSuperview];
//        pkAlertView = nil;
//    }
//    pkAlertView = [[anchorPKAlert alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight+_window_width*2/6, _window_width, 70) andIsStart:YES];
//    pkAlertView.delegate = self;
//    [self.view addSubview:pkAlertView];
//    [socketL launchPK];
//    [socketL setWaitPKTime:10];
//    [MBProgressHUD showError:YZMsg(@"PK请求已发送")];
//}
-(void)sendWaitPkTime:(int)time
{
    [socketL setWaitPKTime:time];
}
- (void)showPKView{
    if (pkAlertView) {
        [socketL setWaitPKTime:0];

        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;

    }

//    startPKBtn.hidden = YES;
//    [startPKBtn removeFromSuperview];
    if (pkView) {
        [pkView removeFromSuperview];
        pkView = nil;
    }
    pkView = [[anchorPKView alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight, _window_width, _window_width*2/3+20) andTime:@"300"];
    pkView.delegate = self;
    [self.view addSubview:pkView];
}
- (void)showPKButton{
    if (pkAlertView) {
        [socketL setWaitPKTime:0];

        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
//    [frontView addSubview:startPKBtn];
//    startPKBtn.hidden = NO;

}
-(void)hidePKButton{
    if (pkAlertView) {
        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
//    startPKBtn.hidden = YES;

}
- (void)showPKResult:(NSDictionary *)dic{
    int win;
    if ([minstr([dic valueForKey:@"win_uid"]) isEqual:@"0"]) {
        win = 0;
    }else if ([minstr([dic valueForKey:@"win_uid"]) isEqual:[Config getOwnID]]) {
        win = 1;
    }else{
        win = 2;
    }

    [pkView showPkResult:dic andWin:win];
}
- (void)changePkProgressViewValue:(NSDictionary *)dic{
    NSString *blueNum;
    NSString *redNum;
    CGFloat progress = 0.0;
    if ([minstr([dic valueForKey:@"pkuid1"]) isEqual:[Config getOwnID]]) {
        blueNum = minstr([dic valueForKey:@"pktotal1"]);
        redNum = minstr([dic valueForKey:@"pktotal2"]);
    }else{
        redNum = minstr([dic valueForKey:@"pktotal1"]);
        blueNum = minstr([dic valueForKey:@"pktotal2"]);
    }
    if ([blueNum isEqual:@"0"]) {
        progress = 0.2;
    }else if ([redNum isEqual:@"0"]) {
        progress = 0.8;
    }else{
        CGFloat ppp = [blueNum floatValue]/([blueNum floatValue] + [redNum floatValue]);
        if (ppp < 0.2) {
            progress = 0.2;
        }else if (ppp > 0.8){
            progress = 0.8;
        }else{
            progress = ppp;
        }
    }

    [pkView updateProgress:progress withBlueNum:blueNum withRedNum:redNum];
}
- (void)duifangjujuePK{
    if (pkAlertView) {
        [pkAlertView removeTimer];
        [pkAlertView removeFromSuperview];
        pkAlertView = nil;
    }
//    startPKBtn.hidden = NO;
//    [frontView addSubview:startPKBtn];

}
#pragma mark ================ 红包 ===============
- (void)showRedView{
    redBview = [[redBagView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
    __weak ChatRoomViewController *wSelf = self;
    redBview.block = ^(NSString *type) {
        [wSelf sendRedBagSuccess:type];
    };
    redBview.zhuboDic = _roomDic;
    [self.view addSubview:redBview];
}
- (void)sendRedBagSuccess:(NSString *)type{
    [redBview removeFromSuperview];
    redBview = nil;
    if ([type isEqual:@"909"]) {
        return;
    }
    [socketL fahongbaola];
}
- (void)showRedbag:(NSDictionary *)dic{
    redBagBtn.hidden = NO;
    NSString *uname;
    if ([minstr([dic valueForKey:@"uid"]) isEqual:[Config getOwnID]]) {
        uname = YZMsg(@"主播");
    }else{
        uname = minstr([dic valueForKey:@"uname"]);
    }
    NSString *levell = @" ";
    NSString *ID = @" ";
    NSString *vip_type = @"0";
    NSString *liangname = @"0";
    NSString *language = [PublicObj getCurrentLanguage];
    NSString *l_ct;
    if ([language isEqual:@"en"]) {
        l_ct = minstr([dic valueForKey:@"ct_en"]);
    }else{
        l_ct =minstr([dic valueForKey:@"ct"]);
    }

    NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",l_ct,@"contentChat",levell,@"levelI",ID,@"id",@"redbag",@"titleColor",vip_type,@"vip_type",liangname,@"liangname",nil];
    chat = [YBToolClass roomChatInsertTime:chat];
    [msgList addObject:chat];
    titleColor = @"0";
    if(msgList.count>30)
    {
        [msgList removeObjectAtIndex:0];
    }
    [self.tableView reloadData];
    [self jumpLast:self.tableView];

}
- (void)redBagBtnClick{
    redList = [[redListView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height) withZHuboMsg:_roomDic];
    redList.delegate =self;
    [self.view addSubview:redList];
}

#pragma mark ============连麦开关n按钮点击=============
- (void)linkSwitchBtnClick:(UIButton *)sender{
    [YBToolClass postNetworkWithUrl:@"Linkmic.setMic" andParameter:@{@"ismic":@(!sender.selected)} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            sender.selected = !sender.selected;
        }
        [MBProgressHUD showError:msg];
        
    } fail:^{
        
    }];
}
#pragma mark ===========================   腾讯推流start   =======================================
-(void)txRTCPush{
    [[YBLiveRTCManager shareInstance]initWithChatLiveMode:V2TXLiveMode_RTC];
    [[YBLiveRTCManager shareInstance]setPushView:_pushPreview];
    [YBLiveRTCManager shareInstance].delegate = self;
}
-(void)txStartRTC{
    [[YBLiveRTCManager shareInstance]startPush:_hostURL isGameLive:NO];
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
}
-(void)txStopRTC{
    [[YBLiveRTCManager shareInstance]stopPushIsGameLive:NO];
    [[UIApplication sharedApplication] setIdleTimerDisabled:NO];
    for (TXPlayLinkMic *playv in self.view.subviews) {
        if ([playv isKindOfClass:[TXPlayLinkMic class]]) {
            [playv stopConnect];
            [playv stopPush];
            [playv removeFromSuperview];
        }
    }

}
#pragma mark -美狐回调
-(void)MHBeautyBlock:(V2TXLiveVideoFrame *)srcFrame dstFrame:(V2TXLiveVideoFrame *)dstFrame
{
    //暂时用腾讯的美颜
    float _tx_beauty_level = 9;
    float _tx_whitening_level = 3;
    [[YBLiveRTCManager shareInstance]setBeautyLevel:_tx_beauty_level WhitenessLevel:_tx_whitening_level];
//    GLuint dstTextureId = renderItemWithTexture(srcFrame.textureId, srcFrame.width, srcFrame.height);
    dstFrame.textureId =srcFrame.textureId;// dstTextureId;

//    dstFrame.textureId= [self.beautyManager getTextureProcessWithTexture:srcFrame.textureId width:(GLint)srcFrame.width height:(GLint)srcFrame.height mirror:YES];
//    dispatch_async(dispatch_get_main_queue(), ^{
//        if (self.menusView) {
//            if (!isLoadWebSprout) {
//                isLoadWebSprout = YES;
//                [self.menusView setupDefaultBeautyAndFaceValue];
//            }
//        }
//    });
}
#pragma mark ================ TXVideoProcessDelegate ===============
- (MHBeautyManager *)beautyManager {
    if (!_beautyManager) {
        _beautyManager = [[MHBeautyManager alloc] init];
        [_beautyManager setWatermarkRect:CGRectMake(0.2, 0.1, 0.1, 0)];
    }
    return _beautyManager;
}
#pragma mark - MHMenuViewDelegate
- (void)beautyEffectWithLevel:(NSInteger)beauty whitenessLevel:(NSInteger)white ruddinessLevel:(NSInteger)ruddiness {
    //暂时用腾讯的美颜
    _tx_beauty_level = 9;
    _tx_whitening_level = 3;
    
//    NSString *mopi =  [sproutCommon getYBskin_smooth];//磨皮数值
//    NSString *white = [sproutCommon getYBskin_whiting];//美白数值
//    NSString *hongrun = [sproutCommon getYBskin_tenderness];//红润数值

//    [_txLivePublisher setBeautyStyle:0 beautyLevel:beauty whitenessLevel:white ruddinessLevel:ruddiness];
}
#pragma mark -美狐回调
//-(void)MHBeautyBlock:(V2TXLiveVideoFrame *)srcFrame dstFrame:(V2TXLiveVideoFrame *)dstFrame;
//{
//    //暂时用腾讯的美颜
//    _tx_beauty_level = 9;
//    _tx_whitening_level = 3;
////    [anchorLinkView. setBeautyStyle:0 beautyLevel:beauty whitenessLevel:white ruddinessLevel:ruddiness];
////    [[YBLiveRTCManager shareInstance]setBeautyLevel:_tx_beauty_level WhitenessLevel:_tx_whitening_level];
////    GLuint dstTextureId = renderItemWithTexture(srcFrame.textureId, srcFrame.width, srcFrame.height);
////    dstFrame.textureId = dstTextureId;
//}
#pragma mark -初始化美颜UI
- (void)initMeiyanFaceUI{
    _menusView = [[MHMeiyanMenusView alloc] initWithFrame:CGRectMake(0, window_height - MHMeiyanMenuHeight - BottomIndicatorHeight, window_width, MHMeiyanMenuHeight) superView:self.view  beautyManager:self.beautyManager];
}


- (void)onTextureDestoryed{
    NSLog(@"[self.tiSDKManager destroy];");
}

#pragma mark  --RTC推流回调
/**
 * 推流器连接状态回调通知
 *
 * @param status    推流器连接状态 {@link V2TXLivePushStatus}。
 * @param msg       连接状态信息。
 * @param extraInfo 扩展信息。
 */
-(void)ybRTCPushStatusUpdate:(V2TXLivePushStatus)status message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (status == V2TXLivePushStatusDisconnected) {
            /// 与服务器断开连接
            NSLog(@"PUSH_EVT_PUSH_BEGIN网络断连,且经多次重连抢救无效,可以放弃治疗,更多重试请自行重启推流");
            [_notification displayNotificationWithMessage:@"网络断连" forDuration:5];
            [self hostStopRoom];

        }else if(status == V2TXLivePushStatusConnecting){
            /// 正在连接服务器
             if (isPUSH_WARNING_RECONNECT) {
                isPUSH_WARNING_RECONNECT = NO;
                [self checkLiveingStatus];
             }

        }else if(status == V2TXLivePushStatusConnectSuccess){
            /// 连接服务器成功
            [self changePlayState:1];

        }else if(status == V2TXLivePushStatusConnectSuccess){
            ///  重连服务器中
            [_notification displayNotificationWithMessage:@"网络断连, 已启动自动重连" forDuration:5];
        }
    });
}
-(void)ybPushLiveStatus:(V2TXLiveCode)pushStatus
{
    if (pushStatus == V2TXLIVE_OK) {
        NSLog(@"LIVEBROADCAST --:推流成功、停止推流");
    }else if (pushStatus == V2TXLIVE_ERROR_INVALID_PARAMETER){
        [_notification displayNotificationWithMessage:@"操作失败，url 不合法" forDuration:5];
        NSLog(@"推流器启动失败");
    }else if (pushStatus == V2TXLIVE_ERROR_INVALID_LICENSE){
        [_notification displayNotificationWithMessage:@"操作失败，license 不合法，鉴权失败" forDuration:5];
        NSLog(@"推流器启动失败");
    }else if (pushStatus == V2TXLIVE_ERROR_REFUSED){
        [_notification displayNotificationWithMessage:@"操作失败，RTC 不支持同一设备上同时推拉同一个 StreamId" forDuration:5];
        NSLog(@"推流器启动失败");
    }else if (pushStatus == V2TXLIVE_WARNING_NETWORK_BUSY){
        [_notification displayNotificationWithMessage:
            @"您当前的网络环境不佳，请尽快更换网络保证正常直播" forDuration:5];
    }
}

#pragma tx_play_linkmic 代理
-(void)tx_closeUserbyVideo:(NSDictionary *)subdic{
    [MBProgressHUD showError:@"播放失败"];
}
-(void) onNetStatus:(NSDictionary*) param{
    
}
//-(void) onPushEvent:(int)EvtID withParam:(NSDictionary*)param {
//    dispatch_async(dispatch_get_main_queue(), ^{
//        NSLog(@"onPushEvent:(int)EvtID withParam:(NSDictionary*)param = \n%d",EvtID);
//        if (EvtID >= 0) {
//            if (EvtID == PUSH_WARNING_HW_ACCELERATION_FAIL) {
//                _txLivePublisher.config.enableHWAcceleration = false;
//                NSLog(@"PUSH_EVT_PUSH_BEGIN硬编码启动失败，采用软编码");
//            }else if (EvtID == PUSH_EVT_CONNECT_SUCC) {
//                // 已经连接推流服务器
//                NSLog(@" PUSH_EVT_PUSH_BEGIN已经连接推流服务器");
//                if (isPUSH_WARNING_RECONNECT) {
//                    isPUSH_WARNING_RECONNECT = NO;
//                    [self checkLiveingStatus];
//                }
//            }else if (EvtID == PUSH_EVT_PUSH_BEGIN) {
//                // 已经与服务器握手完毕,开始推流
//                [self changePlayState:1];
//                NSLog(@"liveshow已经与服务器握手完毕,开始推流");
//            }else if (EvtID == PUSH_WARNING_RECONNECT){
//                isPUSH_WARNING_RECONNECT = YES;
//                // 网络断连, 已启动自动重连 (自动重连连续失败超过三次会放弃)
//                NSLog(@"网络断连, 已启动自动重连 (自动重连连续失败超过三次会放弃)");
//            }else if (EvtID == PUSH_WARNING_NET_BUSY) {
//                [_notification displayNotificationWithMessage:@"您当前的网络环境不佳，请尽快更换网络保证正常直播" forDuration:5];
//            }else if (EvtID == WARNING_RTMP_SERVER_RECONNECT) {
//                [_notification displayNotificationWithMessage:@"网络断连, 已启动自动重连" forDuration:5];
//            }
//        }else {
//            if (EvtID == PUSH_ERR_NET_DISCONNECT) {
//                NSLog(@"PUSH_EVT_PUSH_BEGIN网络断连,且经多次重连抢救无效,可以放弃治疗,更多重试请自行重启推流");
//                [_notification displayNotificationWithMessage:@"网络断连" forDuration:5];
//                [self hostStopRoom];
//            }
//        }
//    });
//}
#pragma mark ===========================   腾讯推流end   =======================================

//-(void)addCycleScroll:(NSArray *)titleArr{
//    NSArray * sliderMuArr = @[@"每日任务",@"Jackpot_btnBack"];
//    if (_cycleScroll) {
//        [_cycleScroll removeFromSuperview];
//        _cycleScroll = nil;
//    }
//    _cycleScroll = [[SDCycleScrollView alloc]initWithFrame: CGRectMake(10, statusbarHeight + 135, 80, 60)];
//    _cycleScroll.backgroundColor = [UIColor clearColor];
//    _cycleScroll.bannerImageViewContentMode = UIViewContentModeScaleAspectFit;
//    _cycleScroll.delegate = self;
//    _cycleScroll.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
//    [self.view addSubview:_cycleScroll];
//    _cycleScroll.scrollDirection = UICollectionViewScrollDirectionHorizontal;
//    _cycleScroll.autoScrollTimeInterval = 3.0;//轮播时间间隔，默认1.0秒，可自定义
//    _cycleScroll.currentPageDotColor = [UIColor whiteColor];
//    _cycleScroll.pageDotColor = [[UIColor whiteColor] colorWithAlphaComponent:0.4];
//    _cycleScroll.pageControlStyle = SDCycleScrollViewPageContolStyleNone;
//    _cycleScroll.imageURLStringsGroup = sliderMuArr;
//    _cycleScroll.titlesGroup = titleArr;
//    _cycleScroll.titleLabelBackgroundColor = [UIColor clearColor];
//    _cycleScroll.titleLabelHeight = 40;
//    _cycleScroll.titleLabelTextFont = SYS_Font(12);
//
//    if (sysPageControl) {
//        [sysPageControl removeFromSuperview];
//        sysPageControl =  nil;
//    }
//    sysPageControl  = [[UIPageControl alloc]initWithFrame:CGRectMake(_cycleScroll.left, _cycleScroll.bottom+2, 20, 5)];
//    sysPageControl.pageIndicatorTintColor = [UIColor grayColor];
//    sysPageControl.currentPageIndicatorTintColor = [UIColor whiteColor];
//    sysPageControl.numberOfPages = sliderMuArr.count;
//    sysPageControl.transform=CGAffineTransformScale(CGAffineTransformIdentity, 0.7, 0.7);
//
//    [self.view addSubview:sysPageControl];
//    sysPageControl.centerX = _cycleScroll.centerX;
//
//}
///** 点击图片回调 */
//- (void)cycleScrollView:(SDCycleScrollView *)cycleScrollView didSelectItemAtIndex:(NSInteger)index;
//{
//    NSLog(@"点击轮播----------index:%ld",index);
//    if (index == 0) {
//        YBWeakSelf;
//        if (!_taskView) {
//            _taskView = [[DayTaskView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)];
//            _taskView.closeEvent = ^{
//                [weakSelf.taskView removeFromSuperview];
//                weakSelf.taskView = nil;
//            };
//            [self.view addSubview:_taskView];
//        }
//    }else if (index == 1) {
//        [self showJackpotView];
//    }
//}
//
#pragma mark ============奖池View=============
- (void)JackpotLevelUp:(NSDictionary *)dic{

}
- (void)WinningPrize:(NSDictionary *)dic{
//    if (winningView) {
//        [winningView removeFromSuperview];
//        winningView = nil;
//    }
//    winningView = [[WinningPrizeView alloc]initWithFrame:CGRectMake(0, 130+statusbarHeight, _window_width, _window_width) andMsg:dic];
//    [self.view addSubview:winningView];
//    [self.view bringSubviewToFront:winningView];
    
}
#pragma mark------幸运奖池---------
- (void)showJackpotView{
    if (jackV) {
        [jackV removeFromSuperview];
        jackV = nil;
    }
    [YBToolClass postNetworkWithUrl:@"Jackpot.getJackpot" andParameter:nil success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *infoDic = [info firstObject];
            jackV = [[[NSBundle mainBundle]loadNibNamed:NSStringFromClass([JackpotView class]) owner:nil options:nil] lastObject];
            jackV.delegate = self;
            jackV.levelL.text = [NSString stringWithFormat:@"Lv.%@",minstr([infoDic valueForKey:@"level"])];
            jackV.coinL.text = minstr([infoDic valueForKey:@"total"]);
            jackV.frame = CGRectMake(_window_width*0.2, 135+statusbarHeight, _window_width*0.6+20, _window_width*0.6);
            [self.view addSubview:jackV];
        }else{
            [MBProgressHUD  showError:msg];
        }
    } fail:^{

    }];
}
-(void)jackpotViewClose{
    [jackV removeFromSuperview];
    jackV = nil;
}
#pragma mark ============幸运礼物全站效果=============
- (void)showAllLuckygift:(NSDictionary *)dic{
    if (!luckyGift) {
        luckyGift = [[AllRoomShowLuckyGift alloc]initWithFrame:CGRectMake(guardBtn.right+5, guardBtn.top-2.5, _window_width-(guardBtn.right+5), guardBtn.height+5)];
        [frontView addSubview:luckyGift];
    }
    [luckyGift addLuckyGiftMove:dic];
}


#pragma mark ============显示用户弹窗选项卡=============
- (void)showButtleView:(NSString *)touid{
    buttleView = [[UserBulletWindow alloc]initWithUserID:touid andIsAnchor:YES andAnchorID:[Config getOwnID]];
    buttleView.delegate  = self;
    buttleView.isChatLive = @"1";
    buttleView.micUserBool = isMicUser;
    [self.view addSubview:buttleView];
}
- (void)removeButtleView{
    [buttleView removeFromSuperview];
    buttleView = nil;
    self.tableView.userInteractionEnabled = YES;

}

#pragma mark ============检查开播状态=============
- (void)checkLiveingStatus{
    [YBToolClass postNetworkWithUrl:@"Live.checkLiveing" andParameter:@{@"stream":minstr([_roomDic valueForKey:@"stream"]),@"voice_type":isVoiceChat ? @"0":@"1"} success:^(int code, id  _Nonnull info, NSString * _Nonnull msg) {
        if (code == 0) {
            NSDictionary *dic = [info firstObject];
            NSLog(@"info=%@",info);
            if ([minstr([dic valueForKey:@"status"]) isEqual:@"0"]) {
                [MBProgressHUD showError:@"连接已断开，请重新开播"];
                [self getCloseShow];
            }
        }
    } fail:^{

    }];
}
#pragma mark ============登录失效=============
- (void)denglushixiao{
    dispatch_async(dispatch_get_main_queue(), ^{
        [self getCloseShow];
    });
}

#pragma mark ============电话监听=============
- (void)reciverPhoneCall{
    if ([_sdkType isEqual:@"1"]) {
        [self appnoactive];
    }else{
        //ray-声网
//        [_gpuStreamer stopPreview];
    }
}
- (void)phoneCallEnd{
    if ([_sdkType isEqual:@"1"]) {
        [self appactive];
    }else{
        //ray-声网
//        [_gpuStreamer startPreview:_pushPreview];
//        if (_roomDic) {
//            [_gpuStreamer.streamerBase startStream: [NSURL URLWithString:_hostURL]];
//        }
    }

}

#pragma mark ============店铺相关=============
- (void)showgoodsShowView{
    if (roomGoodsV) {
        [roomGoodsV removeFromSuperview];
        roomGoodsV = nil;
    }
    roomGoodsV = [[roomShowGoodsView alloc]initWithFrom:YES andZhuboMsg:_roomDic];
    roomGoodsV.showEvent = ^(NSDictionary * _Nonnull infoDic, RelationGoodsModel * _Nonnull models) {
        NSLog(@"-----broadcast:%@", infoDic);
        if ([minstr([infoDic valueForKey:@"status"]) isEqual:@"1"]) {
            [socketL goodsLiveShow:models.goodsid andThumb:models.thumb andName:models.name andPrice:models.price andType:models.type saleNums:models.sale_nums];
        }else{
            [socketL hideLiveGoodsShow];
        }
    };
    [self.view addSubview:roomGoodsV];
}

#pragma mark -----获取道具礼物------------------
-(void)getStickerList:(void(^)(NSArray * stickerlist))success{
    NSDictionary *dic = @{@"package_name":[self bundleName],
                          @"source":@"ios",

    };
    
    NSString *sign = [YBToolClass stickerSortString:dic];

    NSString *urlssss =[NSString stringWithFormat:@"%@package_name=%@&source=%@&sign=%@",PropUrl,[self bundleName],@"ios",sign] ;

    
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:urlssss] cachePolicy:NSURLRequestReloadIgnoringLocalCacheData timeoutInterval:10];
    request.HTTPMethod = @"POST";
    [request setValue:@"application/json" forHTTPHeaderField:@"content-Type"];
    [request setValue:@"application/json;charset=utf-8" forHTTPHeaderField:@"Accept"];

    [[self.session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if (error) {
            //failed(self.sticker, self.index, self);
            dispatch_async(dispatch_get_main_queue(), ^{
                [MBProgressHUD showError:YZMsg(@"获取道具礼物失败")];
            });
        } else {
            NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
            NSArray *infoarr = [dic valueForKey:@"info"];
            if (success) {
                success(infoarr);
            }
        }

    }] resume];

}
#pragma mark - NSURLSession delegate
-(void)URLSession:(NSURLSession *)session didReceiveChallenge:(NSURLAuthenticationChallenge *)challenge completionHandler:(void (^)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler {
    SecTrustRef serverTrust = challenge.protectionSpace.serverTrust;
    SecCertificateRef certificate = SecTrustGetCertificateAtIndex(serverTrust, 0);
    
    // Set SSL policies for domain name check
    NSMutableArray *policies = [NSMutableArray array];
    [policies addObject:(__bridge_transfer id)SecPolicyCreateSSL(true, (__bridge CFStringRef)challenge.protectionSpace.host)];
    SecTrustSetPolicies(serverTrust, (__bridge CFArrayRef)policies);
    
    // Evaluate server certificate
    SecTrustResultType result;
    SecTrustEvaluate(serverTrust, &result);
    // BOOL certificateIsValid = (result == kSecTrustResultUnspecified || result == kSecTrustResultProceed);
    
    // Get local and remote cert data
    NSData *remoteCertificateData = CFBridgingRelease(SecCertificateCopyData(certificate));
    NSString *pathToCert = [[NSBundle mainBundle] pathForResource:@"server" ofType:@"cer"];
    NSData *localCertificate = [NSData dataWithContentsOfFile:pathToCert];
    
    // The pinnning check
    if ([remoteCertificateData isEqualToData:localCertificate]) {
        NSURLCredential *credential = [NSURLCredential credentialForTrust:serverTrust];
        completionHandler(NSURLSessionAuthChallengeUseCredential, credential);
    } else {
        completionHandler(NSURLSessionAuthChallengeCancelAuthenticationChallenge, NULL);
    }

}
- (NSURLSession *)session {
    if (!_session) {
        NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
        // config.requestCachePolicy = NSURLRequestReloadIgnoringLocalCacheData;
        _session =
        [NSURLSession sessionWithConfiguration:config delegate:self delegateQueue:[[NSOperationQueue alloc] init]];
    }
    return _session;
}

- (NSString *)bundleName {
    NSString *bundleName = [[NSBundle mainBundle] bundleIdentifier];
    
    return bundleName;
}




//********************************炸金花*******************************************************************//
-(void)startgamepagev{
    [UIView animateWithDuration:0.4 animations:^{
        bottombtnV.frame = CGRectMake(0, _window_height*2, _window_width, _window_height);
        
    }];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        bottombtnV.hidden = YES;
    });
    self.tableView.hidden = YES;
    if (!gameselectedVC) {
        gameselectedVC = [[gameselected alloc]initWithFrame:CGRectMake(0,0, _window_width, _window_height) andArray:_game_switch];
        gameselectedVC.delegate = self;
        [self.view insertSubview:gameselectedVC atIndex:10];
    }
    gameselectedVC.hidden = NO;
    
}
-(void)reloadcoinsdelegate{
    if (gameVC) {
        [gameVC reloadcoins];
    }
}
//更换庄家信息
-(void)changebank:(NSDictionary *)subdic{
    [zhuangVC getNewZhuang:subdic];
}
-(void)changebankall:(NSDictionary *)subdic{
      [socketL getzhuangjianewmessagem:subdic];
      [zhuangVC getNewZhuang:subdic];
}
-(void)getzhuangjianewmessagedelegate:(NSDictionary *)subdic{
    [zhuangVC getNewZhuang:subdic];
}
-(void)gameselect:(int)action{
    //1炸金花  2海盗  3转盘  4牛牛  5二八贝
    self.tableView.hidden = NO;
    switch (action) {
        case 1:
            _method = @"startGame";
            _msgtype = @"15";
            [self startGame];
            break;
        case 2:
            _method = @"startLodumaniGame";
            _msgtype = @"18";
            [self startGame];
            break;
        case 3:
             [self getRotation];
            break;
        case 4:
            _method = @"startCattleGame";
            _msgtype = @"17";
            [self startGame];
            break;
        case 5:
            _method = @"startShellGame";
            _msgtype = @"19";
            [self startsheelGame];
            break;
            
        default:
            break;
    }
    gameselectedVC.hidden = YES;
}
-(void)hideself{

    self.tableView.hidden = NO;
    gameselectedVC.hidden = YES;
}
//********************************转盘*******************************************************************//
-(void)stopRotationgameBySelf{
    [rotationV stopRotatipnGameInt];
     [rotationV stoplasttimer];
    [socketL stopRotationGame];//关闭游戏socket
    [rotationV removeFromSuperview];
    [rotationV removeall];
    rotationV = nil;
    [gameState saveProfile:@"0"];
    [self changeBtnFrame:_window_height - 45];
    [self tableviewheight: _window_height - _window_height*0.2 - 50 - ShowDiff];
}
-(void)getRotation{
  
    NSString *games = [NSString stringWithFormat:@"%@",[gameState getGameState]];
    if ([games isEqual:@"1"] ) {
        [MBProgressHUD showError:YZMsg(@"请等待游戏结束")];
        return;
    }
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (gameVC) {
        [gameVC stopGame];
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
        [gameState saveProfile:@"0"];//保存游戏开始状态
    }
    if (rotationV) {
        [rotationV stopRotatipnGameInt];
         [rotationV stoplasttimer];
        [socketL stopRotationGame];//关闭游戏socket
        [rotationV removeFromSuperview];
        [rotationV removeall];
        rotationV = nil;
        [gameState saveProfile:@"0"];
    }
    rotationV = [WPFRotateView rotateView];
    [rotationV setlayoutview];
    [rotationV isHost:YES andHostDic:[_roomDic valueForKey:@"stream"]];
    [rotationV hostgetstart];
    rotationV.delegate = self;
    rotationV.frame = CGRectMake(0, _window_height - _window_width/1.8, _window_width, _window_width);
    [self.view insertSubview:rotationV atIndex:6];
    [self changeBtnFrame:_window_height - _window_width/1.8 - www];
    [self tableviewheight: _window_height - _window_height*0.2 - _window_width/1.8 - www];
    [socketL prepRotationGame];
    [self changecontinuegiftframe];
}
//更新押注数量
-(void)getRotationCoin:(NSString *)type andMoney:(NSString *)money{
    [rotationV getRotationCoinType:type andMoney:money];
}
-(void)getRotationResult:(NSArray *)array{
    [rotationV getRotationResult:array];
}
//开始倒计时
-(void)startRotationGameSocketToken:(NSString *)token andGameID:(NSString *)ID andTime:(NSString *)time{
    [socketL RotatuonGame:ID andTime:time androtationtoken:token];
}
//二八贝游戏********************************************************************************************
-(void)startsheelGame{

    NSString *games = [NSString stringWithFormat:@"%@",[gameState getGameState]];
    if ([games isEqual:@"1"] ) {
        [MBProgressHUD showError:YZMsg(@"请等待游戏结束")];
        return;
    }
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (rotationV) {
        [rotationV stopRotatipnGameInt];
         [rotationV stoplasttimer];
        [socketL stopRotationGame];//关闭游戏socket
        [rotationV removeFromSuperview];
        [rotationV removeall];
        rotationV = nil;
        [gameState saveProfile:@"0"];
    }
    if (gameVC) {
        [gameVC stopGame];
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
        [gameState saveProfile:@"0"];//保存游戏开始状态
    }

}
//********************************转盘*******************************************************************//
//********************************炸金花   牛仔*******************************************************************//
-(void)startGame{
 
    NSString *games = [NSString stringWithFormat:@"%@",[gameState getGameState]];
    if ([games isEqual:@"1"] ) {
        [MBProgressHUD showError:YZMsg(@"请等待游戏结束")];
        return;
    }
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (rotationV) {
        [rotationV stopRotatipnGameInt];
         [rotationV stoplasttimer];
        [socketL stopRotationGame];//关闭游戏socket
        [rotationV removeFromSuperview];
        [rotationV removeall];
        rotationV = nil;
    }
    if (gameVC) {
        [gameVC stopGame];
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
    }
    
    
    [gameState saveProfile:@"0"];//保存游戏开始状态
    
    
    //出现游戏界面
    gameVC = [[gameBottomVC alloc]initWIthDic:_roomDic andIsHost:YES andMethod:_method andMsgtype:_msgtype];
    [socketL prepGameandMethod:_method andMsgtype:_msgtype];
    //判断开始哪个游戏
    gameVC.delagate = self;
    gameVC.frame = CGRectMake(0, _window_height - 230, _window_width,230);
    [self.view insertSubview:gameVC atIndex:5];
    [self changeBtnFrame:_window_height - 230-www];
    [self tableviewheight:_window_height - _window_height*0.2 -230-www];
    [self changecontinuegiftframe];
    if ([_method isEqual:@"startCattleGame"]) {
        //上庄玩法
        zhuangVC = [[shangzhuang alloc]initWithFrame:CGRectMake(10,90, _window_width/4, _window_width/4 + 20 + _window_width/8) ishost:YES withstreame:[_roomDic valueForKey:@"stream"]];
        zhuangVC.deleagte = self;
        [self.view insertSubview:zhuangVC atIndex:6];
        [zhuangVC addPoker];
        [zhuangVC addtableview];
        [zhuangVC getbanksCoin:_zhuangDic];
        [self changecontinuegiftframe];
    }
}
//主播广播准备开始游戏
-(void)prepGame:(NSString *)gameid ndMethod:(NSString *)method andMsgtype:(NSString *)msgtype andBanklist:(NSDictionary *)banklist{
    [socketL takePoker:gameid ndMethod:method andMsgtype:msgtype andBanklist:banklist];
}
//游戏开始，开始倒数计时
-(void)startGameSocketToken:(NSString *)token andGameID:(NSString *)ID andTime:(NSString *)time ndMethod:(NSString *)method andMsgtype:(NSString *)msgtype{
    [socketL zhaJinHua:ID andTime:time andJinhuatoken:token ndMethod:method andMsgtype:msgtype];
}
-(void)skate:(NSString *)type andMoney:(NSString *)money andMethod:(NSString *)method andMsgtype:(NSString *)msgtype{
    [socketL stakePoke:type andMoney:money andMethod:method andMsgtype:msgtype];
}
-(void)getCoin:(NSString *)type andMoney:(NSString *)money{
    [gameVC getCoinType:type andMoney:money];
}
//得到游戏结果
-(void)getResult:(NSArray *)array{
    [gameVC getResult:array];
    if (zhuangVC) {
        [zhuangVC getresult:array];
    }
}
-(void)stopGamendMethod:(NSString *)method andMsgtype:(NSString *)msgtype{
    [socketL stopGamendMethod:method andMsgtype:msgtype];
 
    if (zhuangVC) {
        [zhuangVC remopokers];
        [zhuangVC removeFromSuperview];
        zhuangVC = nil;
    }
    if (gameVC) {
        [gameVC releaseAll];
        [gameVC removeFromSuperview];
        gameVC = nil;
    }
    [self changeBtnFrame:_window_height - 45];
    [self tableviewheight:_window_height - _window_height*0.2 - 50 - ShowDiff];
    [self changecontinuegiftframe];
}


- (RKShowPaintedView *)paintedShowRegion {
    if (!_paintedShowRegion) {
        _paintedShowRegion = [[RKShowPaintedView alloc]init];
        _paintedShowRegion.frame = CGRectMake(0, 0, _window_width, _window_height*0.6);
        //_paintedShowRegion.backgroundColor = UIColor.redColor;
    }
    return _paintedShowRegion;
}

//点击购物车展示
-(void)showLiveGooodTips:(NSDictionary *)msg{
    NSString *userName = minstr([msg valueForKey:@"uname"]);
    NSDictionary *userDic = @{@"uname":userName};
    [_liveGoodTipArr addObject:userDic];
    [self beginShowLiveGoodTips];
    
}
-(void)beginShowLiveGoodTips{
    YBWeakSelf;
    if (_liveGoodTipArr.count > 0) {
        NSDictionary *infoDic = [_liveGoodTipArr firstObject];
        NSString *userName = minstr([infoDic valueForKey:@"uname"]);
        NSString*newStr = [userName substringWithRange:NSMakeRange(0,1)];
        NSString *ssss = [NSString stringWithFormat:@"***%@",YZMsg(@"前去购买")];
        newStr = [newStr stringByAppendingString:ssss];
        CGSize sizee = [PublicObj sizeWithString:newStr andFont:SYS_Font(14)];
        
        if (!_liveTipLabel) {
            _liveTipLabel = [[UILabel alloc]init];
            _liveTipLabel.frame = CGRectMake(_window_width, _tableView.frame.origin.y-50, sizee.width+20, 32);
            _liveTipLabel.font = [UIFont systemFontOfSize:14];
            _liveTipLabel.textColor = [UIColor whiteColor];
            _liveTipLabel.textAlignment = NSTextAlignmentCenter;
            _liveTipLabel.text = newStr;
            _liveTipLabel.layer.cornerRadius = 16;
            _liveTipLabel.layer.masksToBounds = YES;
            _liveTipLabel.backgroundColor = RGBA(248,152,38, 0.55);
            [self.view addSubview:_liveTipLabel];
            
            [UIView animateWithDuration:1.5 animations:^{
                _liveTipLabel.frame = CGRectMake(20, _tableView.frame.origin.y-50, sizee.width+20, 32);
            } completion:^(BOOL finished) {
                
                [UIView animateWithDuration:1 animations:^{
                    _liveTipLabel.frame = CGRectMake(0, _tableView.frame.origin.y-50, sizee.width+20, 32);

                } completion:^(BOOL finished) {
                    
                    [UIView animateWithDuration:1 animations:^{
                        _liveTipLabel.frame = CGRectMake(-_window_width, _tableView.frame.origin.y-50, sizee.width+20, 32);

                    } completion:^(BOOL finished) {
                        [_liveTipLabel removeFromSuperview];
                        _liveTipLabel = nil;
                        [_liveGoodTipArr removeObjectAtIndex:0];
                        [weakSelf beginShowLiveGoodTips];
                    }];
                }];
                
            }];
        }
    }

}

-(void)userApplyUpMic
{
    applyTagLb.hidden = NO;
    
}
#pragma mark---liveplayObserver
- (void)onError:(id<V2TXLivePlayer>)player code:(V2TXLiveCode)code message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo;
{
    NSLog(@"liveplay-error");
}
- (void)onWarning:(id<V2TXLivePlayer>)player code:(V2TXLiveCode)code message:(NSString *)msg extraInfo:(NSDictionary *)extraInfo;
{
    NSLog(@"liveplay-onWarning");
}
/**
 * 已经成功连接到服务器
 *
 * @param player    回调该通知的播放器对象。
 * @param extraInfo 扩展信息。
 */

- (void)onVideoPlaying:(id<V2TXLivePlayer>)player firstPlay:(BOOL)firstPlay extraInfo:(NSDictionary *)extraInfo;
{
    NSLog(@"liveplay-VideoPlaying");
}
/*
* 直播播放器分辨率变化通知
*
* @param player    回调该通知的播放器对象。
* @param width     视频宽。
* @param height    视频高。
*/
- (void)onVideoResolutionChanged:(id<V2TXLivePlayer>)player width:(NSInteger)width height:(NSInteger)height;
{
    
}
#pragma mark -------------在线人数--------------
-(void)onlineBtnClick{
    NSDictionary *dataDic = @{
        @"uid":[Config getOwnID],
        @"stream":minstr([self.roomDic valueForKey:@"stream"])
    };
    YBWeakSelf;
    _onlineView  =  [[OnlineUserView alloc]initWithFrame:CGRectMake(0, 0, _window_width, _window_height)withInfoDic:dataDic];
    _onlineView.btnEvent = ^(NSString *status, NSDictionary *userDic) {
        if([status isEqual:@"关闭"]){
            [weakSelf.onlineView removeFromSuperview];
            weakSelf.onlineView = nil;
        }else{
            [weakSelf showButtleView:minstr([userDic valueForKey:@"id"])];
        }
    };
    [self.view addSubview:_onlineView];
}




-(void) txsliderValueChange:(UISlider*) obj {
    if (obj.tag == 1) { //美颜
        _tx_beauty_level = obj.value;
        [[YBLiveRTCManager shareInstance]setBeautyLevel:_tx_beauty_level WhitenessLevel:_tx_whitening_level];
    } else if (obj.tag == 0) { //美白
        _tx_whitening_level = obj.value;
        [[YBLiveRTCManager shareInstance]setBeautyLevel:_tx_beauty_level WhitenessLevel:_tx_whitening_level];
    } else if (obj.tag == 2) { //大眼
        _tx_eye_level = obj.value;
        [[YBLiveRTCManager shareInstance]setYBEyeScaleLevel:_tx_eye_level];
    } else if (obj.tag == 3) { //瘦脸
        _tx_face_level = obj.value;
        [[YBLiveRTCManager shareInstance]setYBFaceScaleLevel:_tx_face_level];
    } else if (obj.tag == 4) {// 背景音乐音量
        [[YBLiveRTCManager shareInstance]setYBBGMVolume:(obj.value/obj.maximumValue)];
    } else if (obj.tag == 5) { // 麦克风音量
//        [_txLivePublisher setMicVolume:(obj.value/obj.maximumValue)];
    }
}

-(void)selectBeauty:(UIButton *)button{
    switch (button.tag) {
        case 0: {
            _sdWhitening.hidden = NO;
            _sdBeauty.hidden    = NO;
            _beautyLabel.hidden = NO;
            _whiteLabel.hidden  = NO;
            _bigEyeLabel.hidden = NO;
            _sdBigEye.hidden    = NO;
            _slimFaceLabel.hidden = NO;
            _sdSlimFace.hidden    = NO;
            _beautyBtn.selected  = YES;
            _filterBtn.selected = NO;
            _filterPickerView.hidden = YES;
            _vBeauty.frame = CGRectMake(0, self.view.height-185-statusbarHeight, self.view.width, 185+statusbarHeight);
        }break;
        case 1: {
            _sdWhitening.hidden = YES;
            _sdBeauty.hidden    = YES;
            _beautyLabel.hidden = YES;
            _whiteLabel.hidden  = YES;
            _bigEyeLabel.hidden = YES;
            _sdBigEye.hidden    = YES;
            _slimFaceLabel.hidden = YES;
            _sdSlimFace.hidden    = YES;
            _beautyBtn.selected  = NO;
            _filterBtn.selected = YES;
            _filterPickerView.hidden = NO;
            [_filterPickerView scrollToElement:_filterType animated:NO];
        }
            _beautyBtn.center = CGPointMake(_beautyBtn.center.x, _vBeauty.frame.size.height - 35-statusbarHeight);
            _filterBtn.center = CGPointMake(_filterBtn.center.x, _vBeauty.frame.size.height - 35-statusbarHeight);
    }
}
////设置美颜滤镜
//#pragma mark - HorizontalPickerView DataSource Methods/Users/<USER>/Work/RTMPDemo_PituMerge/RTMPSDK/webrtc
//- (NSInteger)numberOfElementsInHorizontalPickerView:(V8HorizontalPickerView *)picker {
//    return [_filterArray count];
//}
//#pragma mark - HorizontalPickerView Delegate Methods
//- (UIView *)horizontalPickerView:(V8HorizontalPickerView *)picker viewForElementAtIndex:(NSInteger)index {
//    
//    V8LabelNode *v = [_filterArray objectAtIndex:index];
//    return [[UIImageView alloc] initWithImage:v.face];
//    
//}
//- (NSInteger) horizontalPickerView:(V8HorizontalPickerView *)picker widthForElementAtIndex:(NSInteger)index {
//    
//    return 90;
//}
//- (void)horizontalPickerView:(V8HorizontalPickerView *)picker didSelectElementAtIndex:(NSInteger)index {
//    _filterType = index;
//    [self filterSelected:index];
//}
//- (void)filterSelected:(NSInteger)index {
//    NSString* lookupFileName = @"";
//    switch (index) {
//        case FilterType_None:
//            break;
//        case FilterType_white:
//            lookupFileName = @"filter_white";
//            break;
//        case FilterType_langman:
//            lookupFileName = @"filter_langman";
//            break;
//        case FilterType_qingxin:
//            lookupFileName = @"filter_qingxin";
//            break;
//        case FilterType_weimei:
//            lookupFileName = @"filter_weimei";
//            break;
//        case FilterType_fennen:
//            lookupFileName = @"filter_fennen";
//            break;
//        case FilterType_huaijiu:
//            lookupFileName = @"filter_huaijiu";
//            break;
//        case FilterType_landiao:
//            lookupFileName = @"filter_landiao";
//            break;
//        case FilterType_qingliang:
//            lookupFileName = @"filter_qingliang";
//            break;
//        case FilterType_rixi:
//            lookupFileName = @"filter_rixi";
//            break;
//        default:
//            break;
//    }
//    NSString * path = [[NSBundle mainBundle] pathForResource:lookupFileName ofType:@"png"];
//    if (path != nil && index != FilterType_None ) {
//        UIImage *image = [UIImage imageWithContentsOfFile:path];
//        [[YBLiveRTCManager shareInstance]setYBFilter:image];
//    }
//    else {
//        [[YBLiveRTCManager shareInstance]setYBFilter:nil];
//    }
//}
-(void)showXQTBMsgList:(NSDictionary *)msg;
{
    NSArray *getList = [msg valueForKey:@"list"];
    for (int i = 0; i <getList.count;i ++ ){
        titleColor = @"firstlogin";
        NSString *ct;
        NSString *languageStr= [PublicObj getCurrentLanguage];
        if ([languageStr isEqual:@"en"]) {
            ct = minstr([getList[i] valueForKey:@"title_en"]);
        }else{
            ct = minstr([getList[i] valueForKey:@"title"]);
        }
        NSString *uname = YZMsg(@"直播间消息");
        NSString *ID = @"";
        NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",ID,@"id",titleColor,@"titleColor",nil];
        chat = [YBToolClass roomChatInsertTime:chat];
        [msgList addObject:chat];
        titleColor = @"0";
        if(msgList.count>30)
        {
            [msgList removeObjectAtIndex:0];
        }
        [self.tableView reloadData];
        [self jumpLast:self.tableView];

    }

}
-(void)showXYDZPMsgList:(NSDictionary *)msg
{
    NSArray *getList = [msg valueForKey:@"list"];
    for (int i = 0; i <getList.count;i ++ ){
        titleColor = @"firstlogin";
        NSString *ct;
        NSString *languageStr= [PublicObj getCurrentLanguage];
        if ([languageStr isEqual:@"en"]) {
            ct = minstr([getList[i] valueForKey:@"title_en"]);
        }else{
            ct = minstr([getList[i] valueForKey:@"title"]);
        }
        NSString *uname = YZMsg(@"直播间消息");
        NSString *ID = @"";
        NSDictionary *chat = [NSDictionary dictionaryWithObjectsAndKeys:uname,@"userName",ct,@"contentChat",ID,@"id",titleColor,@"titleColor",nil];
        chat = [YBToolClass roomChatInsertTime:chat];
        [msgList addObject:chat];
        titleColor = @"0";
        if(msgList.count>30)
        {
            [msgList removeObjectAtIndex:0];
        }
        [self.tableView reloadData];
        [self jumpLast:self.tableView];

    }

}

@end
