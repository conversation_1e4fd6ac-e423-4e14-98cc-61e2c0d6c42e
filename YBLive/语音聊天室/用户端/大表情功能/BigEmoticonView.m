//
//  BigEmoticonView.m
//  YBLive
//
//  Created by ybRRR on 2021/1/12.
//  Copyright © 2021 cat. All rights reserved.
//

#import "BigEmoticonView.h"
#import "BigEmoticonCell.h"
#define chatCell @"chatGiftCell"

@interface CollectionCellWhite : UICollectionViewCell
@end

@implementation BigEmoticonView

-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self) {
        _allArray = [NSMutableArray array];
        for (int i = 0; i < 16; i++) {
            NSString *faceName = [NSString stringWithFormat:@"chatemoticon%d",i];
            [_allArray addObject:faceName];
        }
        UITapGestureRecognizer *tips = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideSelf)];
        tips.delegate = self;
        [self addGestureRecognizer:tips];
        [self creatUI];
    }
    return self;
}
-(void)hideSelf{
    if (self.emotionHide) {
        self.emotionHide(@"0", @"0");
    }
}
-(void)creatUI{
    
    bottomView = [[UIView alloc]init];
    bottomView.frame = CGRectMake(0, _window_height-(_window_width/2)-ShowDiff-20, _window_width, _window_width/2+ShowDiff+20);
    bottomView.backgroundColor = [UIColor blackColor];
    [self addSubview:bottomView];
    
    LWLCollectionViewHorizontalLayout *Flowlayout =[[LWLCollectionViewHorizontalLayout alloc]init];
    Flowlayout.itemCountPerRow = 4;
    Flowlayout.rowCount = 2;
    Flowlayout.minimumLineSpacing = 0;
    Flowlayout.minimumInteritemSpacing = 0;
    Flowlayout.scrollDirection = UICollectionViewScrollDirectionHorizontal;

    self.collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0,0,_window_width, _window_width/2) collectionViewLayout:Flowlayout];
    self.collectionView.pagingEnabled = YES;
    //注册cell
    self.collectionView.backgroundColor = [UIColor clearColor];
    [self.collectionView registerNib:[UINib nibWithNibName:@"BigEmoticonCell" bundle:nil] forCellWithReuseIdentifier:chatCell];
    self.collectionView.dataSource = self;
    self.collectionView.delegate = self;
    self.collectionView.multipleTouchEnabled = NO;
    [bottomView addSubview:self.collectionView];
    
    //page
    UIPageControl *pageControl = [[UIPageControl alloc] init];
    pageControl.frame = CGRectMake(0,_collectionView.bottom,_window_width,20);
    pageControl.pageIndicatorTintColor = [UIColor grayColor];
    pageControl.currentPageIndicatorTintColor = [UIColor whiteColor];
    pageControl.enabled = NO;
    [bottomView addSubview:pageControl];
    _pageControl = pageControl;

}
- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset{
    //水平滑动时 判断是右滑还是左滑
    if(velocity.x>0){
        //右滑
        NSLog(@"右滑");
        isRight = YES;
    }else{
        //左滑
        NSLog(@"左滑");
        isRight = NO;
    }
    NSLog(@"scrollViewWillEndDragging");
    if (isRight) {
        self.pageControl.currentPage+=1;
    }
    else{
        self.pageControl.currentPage-=1;
    }
}
//展示cell的个数
-(NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return _allArray.count;
}
//定义section的个数
-(NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}
-(UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView selectItemAtIndexPath:indexPath animated:NO scrollPosition:UICollectionViewScrollPositionNone];
    BigEmoticonCell *cell = (BigEmoticonCell *)[collectionView dequeueReusableCellWithReuseIdentifier:chatCell forIndexPath:indexPath];
    cell.backgroundColor = [UIColor clearColor];
    cell.imgUrl = _allArray[indexPath.item];
    return cell;
}
//每个cell的大小
-(CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    return CGSizeMake(_window_width/4-0.01, _window_width/4-0.01);
}
//定义每个UICollectionView 的 margin
-(UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section
{
    return UIEdgeInsetsMake(0,0,0,0);
}
//cell的最小行间距
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section{
    return 0;
}
//cell的最小列间距
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section{
    return 0;
}
-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
    NSString *index = [NSString stringWithFormat:@"%ld", indexPath.item];
    if (self.emotionHide) {
        self.emotionHide(@"1", index);
    }

}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch{
    if ([NSStringFromClass([touch.view class]) isEqual:@"UIView"]) {
        return NO;
    }
    return YES;
}

@end
