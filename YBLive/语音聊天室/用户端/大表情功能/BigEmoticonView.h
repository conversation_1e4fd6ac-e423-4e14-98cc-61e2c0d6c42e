//
//  BigEmoticonView.h
//  YBLive
//
//  Created by ybRRR on 2021/1/12.
//  Copyright © 2021 cat. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "LWLCollectionViewHorizontalLayout.h"

typedef void(^emotionEvent)(NSString *type, NSString *index);
@interface BigEmoticonView : UIView<UICollectionViewDataSource, UICollectionViewDelegate,UIGestureRecognizerDelegate>
{
    BOOL isRight;
    NSInteger _pageCount;
    NSMutableArray *_allArray;
    UIView *bottomView;
}
@property(nonatomic,strong)UICollectionView *collectionView;
@property (nonatomic , strong) UIPageControl *pageControl;
@property (nonatomic, copy)emotionEvent emotionHide;
@end

