<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="BigEmoticonCell">
            <rect key="frame" x="0.0" y="0.0" width="65" height="65"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="65" height="65"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Ykl-XL-Neo" customClass="YYAnimatedImageView">
                        <rect key="frame" x="10" y="10" width="45" height="45"/>
                    </imageView>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="Ykl-XL-Neo" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="10" id="A66-Wt-Ond"/>
                <constraint firstItem="Ykl-XL-Neo" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="aLJ-SN-5wK"/>
                <constraint firstAttribute="bottom" secondItem="Ykl-XL-Neo" secondAttribute="bottom" constant="10" id="ihd-Iq-IxX"/>
                <constraint firstAttribute="trailing" secondItem="Ykl-XL-Neo" secondAttribute="trailing" constant="10" id="rOh-yl-Xbb"/>
            </constraints>
            <size key="customSize" width="65" height="65"/>
            <connections>
                <outlet property="animationImageView" destination="Ykl-XL-Neo" id="Gx7-YA-TOs"/>
            </connections>
            <point key="canvasLocation" x="-135.50724637681159" y="62.611607142857139"/>
        </collectionViewCell>
    </objects>
</document>
