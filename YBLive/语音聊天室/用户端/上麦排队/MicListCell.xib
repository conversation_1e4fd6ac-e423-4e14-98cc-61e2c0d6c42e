<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="KGk-i7-Jjw" customClass="MicListCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PoB-yS-E9u">
                        <rect key="frame" x="10" y="21.5" width="25" height="17"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="25" id="thP-rb-OzN"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ki6-zR-IkP">
                        <rect key="frame" x="35" y="10" width="40" height="40"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="40" id="E8P-jp-i9u"/>
                            <constraint firstAttribute="height" constant="40" id="Igs-EA-fjn"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="20"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kjd-FE-XfN">
                        <rect key="frame" x="81" y="10" width="35.5" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HTc-dF-wfR">
                        <rect key="frame" x="6" y="58" width="308" height="1"/>
                        <color key="backgroundColor" systemColor="secondarySystemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="IUd-O2-Mhf"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ogt-6n-H9q">
                        <rect key="frame" x="81" y="36" width="16" height="14"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="14" id="nPi-jb-sSE"/>
                            <constraint firstAttribute="width" constant="16" id="vUe-OJ-V4g"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="1aF-oq-lZU">
                        <rect key="frame" x="105" y="35" width="21" height="16"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="16" id="A0f-yR-WNs"/>
                            <constraint firstAttribute="width" constant="21" id="K5a-ML-HgH"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="PoB-yS-E9u" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="1So-L5-4OI"/>
                    <constraint firstItem="PoB-yS-E9u" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="7t9-s5-8dC"/>
                    <constraint firstAttribute="trailing" secondItem="HTc-dF-wfR" secondAttribute="trailing" constant="6" id="9xo-Dq-dYH"/>
                    <constraint firstItem="ogt-6n-H9q" firstAttribute="bottom" secondItem="ki6-zR-IkP" secondAttribute="bottom" id="Gqz-g3-cVC"/>
                    <constraint firstAttribute="bottom" secondItem="HTc-dF-wfR" secondAttribute="bottom" constant="1" id="Luy-O7-LsR"/>
                    <constraint firstItem="1aF-oq-lZU" firstAttribute="leading" secondItem="ogt-6n-H9q" secondAttribute="trailing" constant="8" id="Og7-00-RvZ"/>
                    <constraint firstItem="HTc-dF-wfR" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="6" id="OvZ-Wr-IfQ"/>
                    <constraint firstItem="kjd-FE-XfN" firstAttribute="top" secondItem="ki6-zR-IkP" secondAttribute="top" id="SAy-5P-ddg"/>
                    <constraint firstItem="ki6-zR-IkP" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="Sh0-kd-mal"/>
                    <constraint firstItem="1aF-oq-lZU" firstAttribute="centerY" secondItem="ogt-6n-H9q" secondAttribute="centerY" id="YWS-mp-yRj"/>
                    <constraint firstItem="ki6-zR-IkP" firstAttribute="leading" secondItem="PoB-yS-E9u" secondAttribute="trailing" id="cyk-GL-FZl"/>
                    <constraint firstItem="kjd-FE-XfN" firstAttribute="leading" secondItem="ki6-zR-IkP" secondAttribute="trailing" constant="6" id="dDj-Nz-HVp"/>
                    <constraint firstItem="ogt-6n-H9q" firstAttribute="leading" secondItem="ki6-zR-IkP" secondAttribute="trailing" constant="6" id="s3c-no-vg9"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="indexLb" destination="PoB-yS-E9u" id="uhQ-Qm-OTd"/>
                <outlet property="levelImg" destination="1aF-oq-lZU" id="j9F-OU-bCb"/>
                <outlet property="nameLb" destination="kjd-FE-XfN" id="AzK-3B-uDk"/>
                <outlet property="sexImg" destination="ogt-6n-H9q" id="cUZ-qq-azz"/>
                <outlet property="thumbImg" destination="ki6-zR-IkP" id="Bjp-b8-tMC"/>
            </connections>
            <point key="canvasLocation" x="128.98550724637681" y="69.642857142857139"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="secondarySystemBackgroundColor">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
