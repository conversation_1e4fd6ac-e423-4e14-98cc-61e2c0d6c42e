//
//  MicListCell.m
//  YBLive
//
//  Created by ybRR<PERSON> on 2021/1/9.
//  Copyright © 2021 cat. All rights reserved.
//

#import "MicListCell.h"

@implementation MicListCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}
+(MicListCell*)cellWithTab:(UITableView *)tableView andIndexPath:(NSIndexPath *)indexPath{
    MicListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"MicListCell"];
    if (!cell) {
        cell = [[[NSBundle mainBundle]loadNibNamed:@"MicListCell" owner:nil options:nil]objectAtIndex:0];
    }
    return cell;
}
-(void)setCellData:(NSDictionary *)cellData
{
    _cellData = cellData;
    [self.thumbImg sd_setImageWithURL:[NSURL URLWithString:minstr([cellData valueForKey:@"avatar"])]];
    self.nameLb.text = minstr([cellData valueForKey:@"user_nickname"]);
    //性别 1男
     if ([[cellData valueForKey:@"sex"] isEqual:@"1"])
    {
        self.sexImg.image = [UIImage imageNamed:@"sex_man"];
    }
    else
    {
        self.sexImg.image = [UIImage imageNamed:@"sex_woman"];
    }
    //级别
    NSDictionary *userLevel = [common getUserLevelMessage:minstr([cellData valueForKey:@"level"])];
    [self.levelImg sd_setImageWithURL:[NSURL URLWithString:minstr([userLevel valueForKey:@"thumb"])]];
}
@end
