//
//  MicListView.m
//  YBLive
//
//  Created by ybRRR on 2021/1/7.
//  Copyright © 2021 cat. All rights reserved.
//

#import "MicListView.h"
#import "MicListCell.h"
@interface MicListView ()<UITableViewDelegate, UITableViewDataSource,UIGestureRecognizerDelegate>
{
    UIView *backview;
    UILabel *titleLb;
}
@property (nonatomic, strong)UITableView *tableview;
@property (nonatomic, strong)NSArray *listArr;
@end

@implementation MicListView

-(instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.listArr = [NSArray array];
        UITapGestureRecognizer *hideTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideSelf)];
        hideTap.delegate = self;
        [self addGestureRecognizer:hideTap];
        [self createUI];

    }
    return self;
}
-(void)hideSelf{
    if (self.hideEvent) {
        self.hideEvent();
    }
}
-(void)setDataDic:(NSDictionary *)dataDic
{
    YBWeakSelf;

    NSArray *arr = [dataDic valueForKey:@"apply_list"];
    weakSelf.listArr = arr;
    [weakSelf.tableview reloadData];
    titleLb.text = [NSString stringWithFormat:@"%@(%@)",YZMsg(@"您当前的顺位"), minstr([dataDic valueForKey:@"position"])];

}
-(void)createUI{
    backview = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height *0.5+ShowDiff, _window_width, _window_height *0.5-ShowDiff)];
    backview.backgroundColor = [UIColor whiteColor];
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:backview.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)];
           CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
           maskLayer.frame = backview.bounds;
           maskLayer.path = maskPath.CGPath;
           backview.layer.mask = maskLayer;
    [self addSubview:backview];
    titleLb = [[UILabel alloc]init];
    titleLb.frame = CGRectMake(10, 10, _window_width-20, 20);
    titleLb.font = [UIFont systemFontOfSize:14];
    titleLb.textColor = [UIColor blackColor];
    titleLb.textAlignment = NSTextAlignmentCenter;
    titleLb.text = YZMsg(@"您当前的顺位(0)");
    [backview addSubview:titleLb];
    
    [[YBToolClass sharedInstance]lineViewWithFrame:CGRectMake(0, titleLb.bottom +9, _window_width, 1) andColor:RGB(238, 238, 238) andView:backview];
    
    self.tableview.frame = CGRectMake(0, titleLb.bottom+10, _window_width, backview.height-100);
    [backview addSubview:self.tableview];
    
    [[YBToolClass sharedInstance]lineViewWithFrame:CGRectMake(0, backview.height-50-ShowDiff, _window_width, 1) andColor:RGB(238, 238, 238) andView:backview];
    
    UIButton *applyBtn = [UIButton buttonWithType:0];
    applyBtn.frame = CGRectMake(0, backview.height-49-ShowDiff, _window_width, 49);
    [applyBtn setTitle:YZMsg(@"取消排队") forState:0];
    [applyBtn setTitleColor:RGBA(200,200,200,1) forState:0];
    applyBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [applyBtn addTarget:self action:@selector(cancelClick) forControlEvents:UIControlEventTouchUpInside];
    [backview addSubview:applyBtn];
}
-(void)cancelClick{
    if (self.cancelEvent) {
        self.cancelEvent();
    }
}
-(UITableView *)tableview
{
    if (!_tableview) {
        _tableview = [[UITableView alloc]initWithFrame:CGRectMake(0, 0,0,0) style:UITableViewStylePlain];
        _tableview.delegate = self;
        _tableview.dataSource = self;
        _tableview.separatorStyle = UITableViewCellSeparatorStyleNone;
        
    }
    return _tableview;
}
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}
-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.listArr.count;
}
-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 60;
}
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    MicListCell *cell = [MicListCell cellWithTab:tableView andIndexPath:indexPath];
    [cell setCellData:self.listArr[indexPath.row]];
    cell.indexLb.text = [NSString stringWithFormat:@"%ld",indexPath.row+1];
    return cell;
}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch{
    NSLog(@"s0000000000:%@",NSStringFromClass([touch.view class]));
    if ([NSStringFromClass([touch.view class]) isEqual:@"UITableViewCellContentView"]) {
        return NO;
    }
    return YES;
}

@end
