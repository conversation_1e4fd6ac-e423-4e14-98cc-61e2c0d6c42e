//
//  ApplyUpMicView.m
//  YBLive
//
//  Created by ybRRR on 2021/1/7.
//  Copyright © 2021 cat. All rights reserved.
//

#import "ApplyUpMicView.h"

@interface ApplyUpMicView ()<UIGestureRecognizerDelegate>
{
    UIView *backview;
}
@end

@implementation ApplyUpMicView

-(instancetype)initWithFrame:(CGRect)frame ApplyClick:(upMicEvent)applyClick HideClick:(upMicEvent)hideSelf
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.applyEvent = applyClick;
        self.hideEvent = hideSelf;
        
        UITapGestureRecognizer *hideTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(hideSelf)];
        hideTap.delegate = self;
        [self addGestureRecognizer:hideTap];
        [self createUI];

    }
    return self;
}
-(void)hideSelf{
    if (self.hideEvent) {
        self.hideEvent();
    }
}
-(void)createUI{
    backview = [[UIView alloc]initWithFrame:CGRectMake(0, _window_height *0.5-ShowDiff, _window_width, _window_height *0.5+ShowDiff)];
    backview.backgroundColor = [UIColor whiteColor];
    UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:backview.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)];
           CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
           maskLayer.frame = backview.bounds;
           maskLayer.path = maskPath.CGPath;
           backview.layer.mask = maskLayer;
    [self addSubview:backview];
    UILabel *titleLb = [[UILabel alloc]init];
    titleLb.frame = CGRectMake(10, 10, _window_width-20, 20);
    titleLb.font = [UIFont systemFontOfSize:14];
    titleLb.textColor = [UIColor blackColor];
    titleLb.textAlignment = NSTextAlignmentCenter;
    titleLb.text = YZMsg(@"上麦互动");
    [backview addSubview:titleLb];
    
    [[YBToolClass sharedInstance]lineViewWithFrame:CGRectMake(0, titleLb.bottom +9, _window_width, 1) andColor:RGB(238, 238, 238) andView:backview];
    
    UIImageView *headImg = [[UIImageView alloc]init];
    headImg.frame = CGRectMake(_window_width/2-40, backview.height*0.25, 72, 72);
    headImg.layer.cornerRadius = 36;
    headImg.layer.masksToBounds = YES;
    headImg.contentMode = UIViewContentModeScaleAspectFill;
    [headImg sd_setImageWithURL:[NSURL URLWithString:[Config getavatar]]];
    [backview addSubview:headImg];
    
    UILabel *nameLb = [[UILabel alloc]init];
    nameLb.frame = CGRectMake(10, headImg.bottom+5, _window_width-20, 20);
    nameLb.font = [UIFont systemFontOfSize:14];
    nameLb.textColor = [UIColor blackColor];
    nameLb.text = [Config getOwnNicename];
    nameLb.textAlignment = NSTextAlignmentCenter;
    [backview addSubview:nameLb];
    
    [[YBToolClass sharedInstance]lineViewWithFrame:CGRectMake(0, backview.height-50-ShowDiff, _window_width, 1) andColor:RGB(238, 238, 238) andView:backview];
    
    UIButton *applyBtn = [UIButton buttonWithType:0];
    applyBtn.frame = CGRectMake(0, backview.height-49-ShowDiff, _window_width, 49);
    [applyBtn setTitle:YZMsg(@"申请上麦") forState:0];
    [applyBtn setTitleColor:RGBA(31,200,248,1) forState:0];
    applyBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [applyBtn addTarget:self action:@selector(applyBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [backview addSubview:applyBtn];
}
-(void)applyBtnClick{
    [[AVAudioSession sharedInstance] requestRecordPermission:^(BOOL granted) {
        
        if (granted) {
            
            // 用户同意获取麦克风
            if (self.applyEvent) {
                self.applyEvent();
            }

            
        } else {
            
            UIAlertView *alert = [[UIAlertView alloc]initWithTitle:YZMsg(@"权限受阻") message:YZMsg(@"请在设置中开启麦克风权限") delegate:self cancelButtonTitle:YZMsg(@"确定") otherButtonTitles:nil, nil];
            
            [alert show];
            
            return ;
            
        }
        
    }];

}
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch{
    NSLog(@"s0000000000:%@",NSStringFromClass([touch.view class]));
    if ([NSStringFromClass([touch.view class]) isEqual:@"UIView"]) {
        return NO;
    }
    return YES;
}

@end
