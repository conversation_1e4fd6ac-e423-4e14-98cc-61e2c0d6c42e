#ifndef PrefixHeader_pch
#define PrefixHeader_pch

#ifdef __OBJC__

#import "MBProgressHUD.h"
#import "MBProgressHUD+MJ.h"
#import "liveCity.h"
#import "cityDefault.h"
#import "AFNetworking.h"
#import "UIImageView+WebCache.h"
//#import "UIImage+AFNetworking.h"
#import "UIButton+WebCache.h"
#import "SBJson.h"
#import "AFHTTPSessionManager.h"
#import "Config.h"
#import "common.h"
#import "Masonry.h"
#import "liveCommon.h"
#import "UIView+AdditionsX12.h"
#import "UIImageView+YBImageView.h"
#import "MXBADelegate.h"
#import "UIView+ITTAdditions.h"
#import "UIView+WPFExtension.h"
#import "YBNavi.h"
#import "YBToolClass.h"
#import <MJRefresh/MJRefresh.h>
#import "YBNoWordView.h"
#import "PublicView.h"
#import "YBNetworking.h"
#import "PublicObj.h"
#import "YBNavi.h"
#import "MyTextView.h"
#import <SDWebImage/UIImage+GIF.h>
#import <SDWebImage/SDWebImageManager.h>
#import <SDWebImage/SDWebImageDownloader.h>
#import <RKKeepAlive/RKKeepAlive.h>
#import "RKLBSManager.h"
#import "RookieTools.h"
#import "YBWebViewController.h"
#import "gameState.h"
#import "YBStorageManage.h"
#import "YBYoungManager.h"
#import "YBImManager.h"
#import "YBSmallLiveWindow.h"
#import <ImSDK_Plus/ImSDK_Plus.h>
#import <JKCategories.h>
#import "IYZPlayPreHeader.h"
#import <UIScrollView+EmptyDataSet.h>
#import "YBAgoraManager.h"
#endif



// 域名 正式站
#define purl @"https://ep-xipai.horyer.net/appapi/"
#define h5url @"https://您的域名.com"

#pragma mark - 初始三方
// com.yunbao.zb
// 短视频的
#define LicenceURL @"https://license.vod2.myqcloud.com/license/v2/6666/v_cube.license"
#define LicenceKey @"6666"
// 直播的
#define TXPushLicenceURL @"https://license.vod2.myqcloud.com/license/v2/6666/v_cube.license"
#define TXPushLicenceKey @"6666"
// Bugly
#define BuglyId @"6666"
// 腾讯地图key
#define TencentKey @"NY6BZ-J6666662E-4ABSL"
// 百度语音识别 "请在官网新建应用，配置包名，并在此填写应用的 api key, secret key, appid(即appcode)"
#define ASR_API_KEY @"666666"
#define ASR_SECRET_KEY @"66666"
#define ASR_APP_ID @"6666"
// 友盟
#define youmengKey @"66666"
#define youmengChannel @""
// 声网
#define agoraAppid @"6666"
// 腾讯IM
#define TXIMSdkAppid          6666
/*
 腾讯tpns推送
 //腾讯tpns推送服务接入点
 上海     @"tpns.sh.tencent.com"
 新加坡   @"tpns.sgp.tencent.com"
 香港     @"tpns.hk.tencent.com"
 广州     @"tpns.tencent.com"
 */
#define TXPushAccessID 666
#define TXPushAccessKey @"6666"
#define TXPushClusterDomain   @"tpns.sh.tencent.com"

// 录屏包名
// 使用腾讯sdk的时候改一下这里，同时根目录/YBLiveScreen/SampleHandler.m里也改一下
#define TxAppGroupId    @"group.com.yunbao.zb.livescreen"

#pragma mark - 三方接入信息 注意 在info 中也要配置 QQ要配置两个（tencent+QQAppId || QQ+QQAppId的16进制 ）
//QQ
#define QQAppId @"6666"
#define QQAppKey @"66666"
#define QQLinks @"https://ylusw.share2dlink.com/qq_conn/101551222"
//wechat
#define WechatAppId @"666"
#define WechatAppSecret @"6666"
#define WechatUniversalLink @"https://ylusw.share2dlink.com/"
#define WechatUsername @"gh_e0130aea3153"
//facebook
#define FacebookApiKey @"6666"
#define FacebookAppSecret @"6666"
//twitter
#define TwitterKey @"6666"
#define TwitterSecret @"6666"
#define TwitterRedirectUri @"https://域名.com"



#pragma mark -
#define PropUrl @"https://data.facegl.com/appapi/gift/index?"

//短视频录制时长控制
#define MAX_RECORD_TIME             15
#define MIN_RECORD_TIME             5

// 语言
#define YZMsg(key) [[RookieTools shareInstance] getStringForKey:key withTable:@"InfoPlist"]
#define CurrentLanguage @"will_show_language"
#define getImagename(a) [NSString stringWithFormat:@"%@%@",a,[Config canshu]]
#define lagType [[NSUserDefaults standardUserDefaults] objectForKey:@"will_show_language"]
#define ZH_CN @"zh-Hans"
#define EN @"en"

#define ybShowLiveSmallWindow     @"SHOWSMALLLIVEWINDOW"
#define ybChangeLiveSmallWindow     @"CHANGESMALLLIVEWINDOW"
// IM主动刷新
#define ybImNeedRefresh                 @"imNeedRefresh"
// 邀请、退出、撤回
#define ybImConveEvent                  @"ybImConveEvent"
// 等级图片加载完毕
#define ybChatRefresh   @"yb_room_chat_level_finish"
// 购买成功
#define ybBuySucSendSoc @"yb_room_buy_suc_send_socket"
// 更新销售数量
#define ybUpdateSaleNums    @"yb_room_update_sale_nums"
// 游客离开直播间
#define ybUserDestroyRoom   @"yb_user_destroy_room_noti"

#define BundleIdfier [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"]
//适配iphoneX
#define iPhoneX (_window_width== 375.f && _window_height == 812.f)||(_window_width== 414.f && _window_height == 896.f)
#define ShowDiff (iPhoneX ? 34: 0)
#define statusbarHeight ([[UIApplication sharedApplication] statusBarFrame].size.height-20)
#define naviHight                   (statusbarHeight + naviContentHeight)
#define naviContentHeight           44.0

//app主色调s
//#define normalColors [UIColor colorWithRed:255/255.0 green:97/255.0 blue:49/255.0 alpha:1]
#define normalColors [UIColor colorWithRed:255/255.0 green:88/255.0 blue:120/255.0 alpha:1]
#define oldNormalColors [UIColor colorWithRed:255/255.0 green:221/255.0 blue:0/255.0 alpha:1]
#define Line_Cor [UIColor colorWithRed:230/255.0 green:230/255.0 blue:230/255.0 alpha:1]
#define RGBA(r, g, b, a)    [UIColor colorWithRed:(r)/255.0f green:(g)/255.0f blue:(b)/255.0f alpha:a]
// 随机色
#define YBRandomColor [UIColor colorWithRed:arc4random_uniform(256) / 255.0 green:arc4random_uniform(256) / 255.0 blue:arc4random_uniform(256) / 255.0 alpha:1.0]

//各种字体颜色
#define color32 RGB_COLOR(@"#323232",1)
#define color64 RGB_COLOR(@"#646464",1)
#define colorCC RGB_COLOR(@"#cccccc",1)
#define color96 RGB_COLOR(@"#969696",1)
#define color99 RGB_COLOR(@"#999999",1)
#define colorf0 RGB_COLOR(@"#f0f0f0",1)
#define colorf5 RGB_COLOR(@"#f5f5f5",1)

//顶部导航栏背景色
#define navigationBGColor [UIColor whiteColor]
//顶部导航栏字体颜色
#define navtionTitleColor [UIColor blackColor]
//顶部导航栏字体字号
#define navtionTitleFont [UIFont boldSystemFontOfSize:16]

#define gameWait 8 //游戏等待时间

//直播间聊天区域宽度
#define tableWidth  _window_width*0.75 - 15

#define minstr(a)    [NSString stringWithFormat:@"%@",a]
#define YBValue(dic,key) [NSString stringWithFormat:@"%@",[dic valueForKey:key]]

// 随机色
#define WPFRandomColor [UIColor colorWithRed:arc4random_uniform(256) / 255.0 green:arc4random_uniform(256) / 255.0 blue:arc4random_uniform(256) / 255.0 alpha:1.0]

//#ifndef __OPTIMIZE__
//# define NSLog(...) NSLog(__VA_ARGS__)
//#else
//# define NSLog(...)
//#endif


#if DEBUG
//#import <UIKit/UIKit.h>
#define NSLog(FORMAT, ...) fprintf(stderr,"\n【DEBUG】[%s %s:%d行] %s\n",\
[((id(*)(void))method_getImplementation(class_getClassMethod(NSObject.class, @selector(rk_getConsoleLogOfTime))))() UTF8String], \
[[[NSString stringWithUTF8String:__FILE__] lastPathComponent] UTF8String], \
__LINE__, \
[[NSString stringWithFormat:FORMAT, ##__VA_ARGS__] UTF8String]);

#else
//#import <UIKit/UIKit.h>
#define NSLog(FORMAT, ...) nil

#endif



//RGB(246, 246, 246)


#define fontThin(sizeThin) [UIFont fontWithName:@"HelveticaNeue-Thin" size:(sizeThin)]
#define fontMT(sizeThin)   [UIFont fontWithName:@"Arial-ItalicMT" size:(sizeThin)]

#define MAGIN_W ([UIScreen mainScreen].bounds.size.width / 3)

//pageviewcontroller宽度
#define _pageBarWidth  _window_width *0.6
#define  _window_width  [UIScreen mainScreen].bounds.size.width
#define _window_height [UIScreen mainScreen].bounds.size.height
#define backColor [UIColor colorWithRed:65/255.0 green:212/255.0 blue:131/255.0 alpha:1]
//255 211 80
#define UIColorFromRGB(rgbValue) [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16))/255.0 \
green:((float)((rgbValue & 0xFF00) >> 8))/255.0 \
blue:((float)(rgbValue & 0xFF))/255.0 \
alpha:1.0]
#define FNOT [UIFont systemFontOfSize:16];
#define leftW 35  //直播间左上角头像
#define ios8 [[UIDevice currentDevice].systemVersion floatValue] <9
#define IS_IPAD (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad)
#define IS_IPHONE (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone)
#define IS_RETINA ([[UIScreen mainScreen] scale] >= 2.0)
#define SCREEN_WIDTH ([[UIScreen mainScreen] bounds].size.width)
#define SCREEN_HEIGHT ([[UIScreen mainScreen] bounds].size.height)
#define SCREEN_MAX_LENGTH (MAX(SCREEN_WIDTH, SCREEN_HEIGHT))
#define SCREEN_MIN_LENGTH (MIN(SCREEN_WIDTH, SCREEN_HEIGHT))
#define IS_IPHONE_4_OR_LESS (IS_IPHONE && SCREEN_MAX_LENGTH < 568.0)
#define IS_IPHONE_5 (IS_IPHONE && SCREEN_MAX_LENGTH == 568.0)
#define IS_IPHONE_6 (IS_IPHONE && SCREEN_MAX_LENGTH == 667.0)
#define IS_IPHONE_6P (IS_IPHONE && SCREEN_MAX_LENGTH == 736.0)

#define IS_BIG_SCREEN (IS_IPHONE && SCREEN_MAX_LENGTH > 667.0)
//首页高亮
#define RGB(r,g,b)          [UIColor colorWithRed:(r)/255.f \
green:(g)/255.f \
blue:(b)/255.f \
alpha:1.f]
#endif
#define RGB_COLOR(_STR_,a) ([UIColor colorWithRed:[[NSString stringWithFormat:@"%lu", strtoul([[_STR_ substringWithRange:NSMakeRange(1, 2)] UTF8String], 0, 16)] intValue] / 255.0 green:[[NSString stringWithFormat:@"%lu", strtoul([[_STR_ substringWithRange:NSMakeRange(3, 2)] UTF8String], 0, 16)] intValue] / 255.0 blue:[[NSString stringWithFormat:@"%lu", strtoul([[_STR_ substringWithRange:NSMakeRange(5, 2)] UTF8String], 0, 16)] intValue] / 255.0 alpha:a])

//颜色
#define Pink_Cor normalColors
#define Black_Cor RGB_COLOR(@"#f4f5f6",1)
#define YBWeakSelf __weak typeof(self) weakSelf = self;
#define CellRow_Cor Black_Cor
#define SelCell_Col RGB_COLOR(@"#f4f5f6", 1)
#define GrayText [UIColor colorWithRed:150/255.f green:150/255.f blue:150/255.f alpha:1.f]
#define AtCol RGB_COLOR(@"#FF5878", 1)

//字体
#define SYS_Font(a) [UIFont systemFontOfSize:(a)]

//@规则
#define kATRegular @"@[\\u4e00-\\u9fa5\\w\\-\\_]+ "
//emoji规则
#define emojiPattern @"\\[\\w+\\]"

//图片
#define FOREGROUND_STAR_IMAGE_NAME @"start_select_评价"
#define BACKGROUND_STAR_IMAGE_NAME @"start_normal_评价"

#define Comm_FOREGROUND_STAR_IMAGE_NAME @"comm_star_实心"
#define Comm_BACKGROUND_STAR_IMAGE_NAME @"comm_star_空心"

#define video_FOREGROUND_STAR_IMAGE_NAME @"videocontent_评价sel"
#define video_BACKGROUND_STAR_IMAGE_NAME @"videocontent_评价normal"


#define ImageBundlePath [[NSBundle mainBundle] pathForResource:@"MHSDK" ofType:@"bundle"]
#define Bundle [NSBundle bundleWithPath:ImageBundlePath]
#define BundleImg(Name) [UIImage imageNamed:Name inBundle:Bundle compatibleWithTraitCollection:nil];
#define kBeautyAuthKey @"kBeautyAuthKey"

#define kHighLevelSticker @"kHighLevelSticker"

#define kBeautyVersion @"kBeautyVersion"

#define HAVESTICKER    @"haveSticker"
#define STICKERKEY     @"stickerKey"
#define STICKERCANUSE  @"stickerCanUse"
#define STICKERNOTUSE  @"stickerNotUse"
#define VOICEPAUSE     @"voicePause"

//Notification
#define kAlertToSendImage @"AlertToSendImage"
#define kDeleteMessage @"DeleteMessage"
#define kRetractMsg @"RetractMesg"

//....

/* PrefixHeader_pch */
