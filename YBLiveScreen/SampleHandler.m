//
//  SampleHandler.m
//  YbReplayScreen
//
//  Created by y<PERSON><PERSON>01 on 2023/9/22.
//  Copyright © 2023 IOS1. All rights reserved.
//


#import "SampleHandler.h"
//#import <AgoraRtcKit/AgoraRtcKit.h>

//@import TXLiteAVSDK_ReplayKitExt;
@import AgoraReplayKitExtension;
#define APPGROUP @"group.com.yunbao.zb.livescreen"

//TXReplayKitExtDelegate
@interface SampleHandler ()<AgoraReplayKitExtDelegate>

@end
@implementation SampleHandler

- (void)broadcastStartedWithSetupInfo:(NSDictionary<NSString *,NSObject *> *)setupInfo {
    // User has requested to start the broadcast. Setup info from the UI extension can be supplied but optional.
//    [[TXReplayKitExt sharedInstance] setupWithAppGroup:APPGROUP delegate:self];
    // 用户请求开始广播
    [[AgoraReplayKitExt shareInstance] start:self];
}

- (void)broadcastPaused {
    // User has requested to pause the broadcast. Samples will stop being delivered.
//    [[TXReplayKitExt sharedInstance] broadcastPaused];
    [[AgoraReplayKitExt shareInstance] pause];
}

- (void)broadcastResumed {
    // User has requested to resume the broadcast. Samples delivery will resume.
//    [[TXReplayKitExt sharedInstance] broadcastResumed];
    [[AgoraReplayKitExt shareInstance] resume];
}

- (void)broadcastFinished {
    // User has requested to finish the broadcast.
    NSLog(@"7777777777777777777777777777777777");

//    [[TXReplayKitExt sharedInstance] broadcastFinished];
    [[AgoraReplayKitExt shareInstance] stop];

}
- (void)processSampleBuffer:(CMSampleBufferRef)sampleBuffer withType:(RPSampleBufferType)sampleBufferType {
//    [[TXReplayKitExt sharedInstance] sendSampleBuffer:sampleBuffer withType:sampleBufferType];
    [[AgoraReplayKitExt shareInstance]pushSampleBuffer:sampleBuffer withType:sampleBufferType];
}

#pragma mark - TXReplayKitExtDelegate
/*
- (void)broadcastFinished:(TXReplayKitExt *)broadcast reason:(TXReplayKitExtReason)reason
{
    NSString *tip = @"";
    switch (reason) {
        case TXReplayKitExtReasonRequestedByMain:
            tip = NSLocalizedString(@"MLVB-API-Example.liveStop", "");
            break;
        case TXReplayKitExtReasonDisconnected:
            tip = NSLocalizedString(@"MLVB-API-Example.appReset", "");
            break;
        case TXReplayKitExtReasonVersionMismatch:
            tip = NSLocalizedString(@"MLVB-API-Example.sdkError", "");
            break;
    }

    NSError *error = [NSError errorWithDomain:NSStringFromClass(self.class)
                                         code:0
                                     userInfo:@{
                                         NSLocalizedFailureReasonErrorKey:tip
                                     }];
    [self finishBroadcastWithError:error];
}
*/
- (void)broadcastFinished:(AgoraReplayKitExt* _Nonnull)broadcast
                   reason:(AgoraReplayKitExtReason)reason
{
    NSString *tip = @"";
    switch (reason) {
        case AgoraReplayKitExtReasonInitiativeStop:
            tip = NSLocalizedString(@"主播直播已关闭", "");
            break;
        case AgoraReplayKitExtReasonConnectFail:
            tip = NSLocalizedString(@"App已断开连接", "");
            break;
        case AgoraReplayKitExtReasonDisconnect:
            tip = NSLocalizedString(@"App进程已停止", "");
            break;
    }

    NSError *error = [NSError errorWithDomain:NSStringFromClass(self.class)
                                         code:0
                                     userInfo:@{
                                         NSLocalizedFailureReasonErrorKey:tip
                                     }];
    NSLog(@"rk===>[录屏]错误:%@",error);
    [self finishBroadcastWithError:error];

}

@end
