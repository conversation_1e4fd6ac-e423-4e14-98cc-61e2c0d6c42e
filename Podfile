source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
source 'https://gitlab.horyer.net:8383/ios/pod/Specs.git'
platform :ios, '13.0'
inhibit_all_warnings!

target "YBLive" do
#图片
pod 'SDWebImage'
#网络请求
pod 'MJRefresh','3.2.0'
pod 'AFNetworking','4.0.1'

pod 'ZFPlayer','4.1.4'
pod 'ZFPlayer/ControlView','4.1.4'
pod 'ZFPlayer/ijkplayer','4.1.4'
pod 'ZFPlayer/AVPlayer','4.1.4'
#日志打印
pod 'CocoaLumberjack','3.4.2'
#bugly
pod 'Bugly', '2.4.7'
#支付宝
pod 'AliPay','********'
pod 'OpenSSL-for-iOS', '1.0.2.d.1'
#UI
pod 'Masonry'

pod 'DZNEmptyDataSet','1.8.1'

#分享
pod 'mob_sharesdk','4.4.34' 
pod 'mob_sharesdk/ShareSDKPlatforms/QQ'
pod 'mob_sharesdk/ShareSDKPlatforms/SinaWeibo'
#pod 'mob_sharesdk/ShareSDKPlatforms/WeChat'   #（微信sdk不带支付的命令）
pod 'mob_sharesdk/ShareSDKPlatforms/WeChatFull' #（微信sdk带支付的命令，和上面不带支付的不能共存，只能选择一个）
pod 'mob_sharesdk/ShareSDKPlatforms/Facebook'
pod 'mob_sharesdk/ShareSDKPlatforms/Twitter'
pod 'mob_sharesdk/ShareSDKPlatforms/Apple' #苹果登录

#头部滑动
pod 'TYPagerController','2.1.2'

#SVGAPlayer
pod 'SVGAPlayer','2.3.5'
pod 'Protobuf', '3.12.0'
#弹窗
pod 'CWStatusBarNotification', '2.3.5'
#七牛
pod 'Qiniu','7.4.3'
#腾讯SDK
#pod 'TXLiteAVSDK_UGC', '3.9.2749'
pod 'TXLiteAVSDK_Professional'
pod 'TXIMSDK_Plus_iOS'
pod 'TPNS-iOS'
pod 'TXLiteAVSDK_Live/ReplayKitExt'

pod 'RTCRoomEngine/Professional'
pod 'TUICore'
pod 'TUIChat'
pod 'TXAppBasic'
pod 'TIMCommon'
pod 'TUIBeauty'
pod 'TIMPush'

#声网
pod 'AgoraRtcEngine_Special_iOS', '*********'


pod 'AGMCapturer_iOS'

#腾讯上传
pod 'QCloudCOSXML','5.7.4'

#AT 功能
pod 'HPGrowingTextView','1.1'
#YYImage用来加载gif
pod 'YYWebImage','1.0.5'
pod 'YYText','1.0.7'

#友盟统计
pod 'UMCCommon','2.1.1'
pod 'UMCAnalytics','5.5.2'
#pod 'UMCSecurityPlugins'
#  轮播图
pod 'SDCycleScrollView','1.82'
#动画
pod 'lottie-ios', '2.5.3'

use_frameworks!
#socket.io
pod 'Socket.IO-Client-Swift','15.1.0'
pod 'MBProgressHUD','1.1.0'
pod 'SnapKit'
pod 'Then'
pod 'XLPagerTabStrip', '9.1.0'
pod 'Kingfisher', '8.3.2'
pod 'SwiftRichString', '3.7.2'
pod 'RxSwift', '6.9.0'
pod 'RxCocoa', '6.9.0'
pod 'SwiftEntryKit', '2.0.0'
pod 'MBCircularProgressBar'
pod 'MXParallaxHeader', '1.1.0'

#pod 'EBBannerView','1.0.8'
pod 'EBBannerView', '~> 1.1.2'

#图片选择器
pod 'TZImagePickerController', '~> 3.8.6'
pod 'SPPageMenu', '3.5.0'
#Braintree
pod 'BraintreeDropIn', "8.1.2"
#openinstall
pod 'libOpenInstallSDK','2.8.3'

pod 'XHWebImageAutoSize', "1.1.2"
pod 'JKCategories','1.9.3'

post_install do |installer|
  installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['ENABLE_BITCODE'] = 'NO'
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
      end
    end
  end
end
end
